package com.hzjm.task.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.product.entity.KnetProductPrice;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.mapper.ShopUserMapper;
import com.hzjm.service.service.SendEmailService;
import com.hzjm.task.service.TaskSendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class TaskSendEmailServiceImpl implements TaskSendEmailService {

    @Autowired
    private ShopUserMapper shopUserMapper;

    @Resource
    SendEmailService sendEmailService;

    /**
     * orderTask 定时任务执行完毕后，给订单的用户发送邮件，告知用户成功卖出
     */
    @Override
    @Async
    public void sendEmailForOrderTask(List<PlatformOrder> platformOrders) throws Exception {

        log.info("TaskSendEmailServiceImpl.sendEmailForOrderTask  start email:" + JSON.toJSONString(platformOrders));

        for (PlatformOrder platformOrder : platformOrders) {

            if (ObjectUtils.isEmpty(platformOrder.getOrderNumber()) || ObjectUtils.isEmpty(platformOrder.getShopId())) {
                continue;
            }

            ShopUser shopUser = shopUserMapper.selectOne(Wrappers.<ShopUser>lambdaQuery().eq(ShopUser::getDelFlag, 0).eq(ShopUser::getId, platformOrder.getShopId()));
            if (ObjectUtils.isEmpty(shopUser) || ObjectUtils.isEmpty(shopUser.getEmail()) || ObjectUtils.isEmpty(shopUser.getRealname())) {
                continue;
            }

            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("email", shopUser.getEmail());
            dataMap.put("userName", shopUser.getRealname());
            dataMap.put("platform", platformOrder.getPlatform().stringFormatToEmail());
            dataMap.put("imageUrl", platformOrder.getImg());
            dataMap.put("itemName", platformOrder.getProductName());
            dataMap.put("sku", platformOrder.getSku());
            dataMap.put("size", platformOrder.getSize());
            Integer amount = platformOrder.getSalePrice();
            dataMap.put("amount", new KnetProductPrice(amount).getAmount());
            Integer owning = platformOrder.getSellerOwning();
            dataMap.put("owning", new KnetProductPrice(owning).getAmount());
            dataMap.put("orderNumber", platformOrder.getOrderNumber());


            sendEmailService.productSoldSendEmail(dataMap);

        }

    }


}
