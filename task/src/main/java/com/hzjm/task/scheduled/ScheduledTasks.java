package com.hzjm.task.scheduled;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.model.touch.TouchProductStatusResponse;
import com.hzjm.service.service.*;
import com.hzjm.service.service.impl.AsyncImpl;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("ScheduledTasks")
@Configurable
@EnableScheduling
public class ScheduledTasks {

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdTransferService iSysProdTransferService;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysProdCashService iSysProdCashService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysWareInService iSysWareInService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private ISysWareOutBatchService iSysWareOutBatchService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private ISysStatisticDataService iSysStatisticDataService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private ISysProdSalePoolService iSysProdSalePoolService;

    @Autowired
    private ISysProdSaleValidService iSysProdSaleValidService;

    @Autowired
    private AsyncImpl async;

    @Autowired
    private TouchUtils touchUtils;

    @Autowired
    private SendEmailService sendEmailService;

    @Resource
    private ISysOperatedMetricsService sysOperatedMetricsService;


    @AcquireTaskLock(name = "secondTask")
    public void second() {
//        Calendar c = Calendar.getInstance();
//c.setTime(DateTimeUtils.getNow());
        Date now = DateTimeUtils.getNow();

        // 支付过期：平台内转移
        List<SysProdTransfer> transferList = iSysProdTransferService.list(Wrappers.<SysProdTransfer>lambdaQuery()
                .select(SysProdTransfer::getId)
                .le(SysProdTransfer::getGmtPayValid, now)
                .eq(SysProdTransfer::getStatus, 3));
        if (!ObjectUtils.isEmpty(transferList)) {
            iSysProdService.release(transferList.stream().map(SysProdTransfer::getId).collect(Collectors.toList()), SysProdEvent.TypeTransfer);
        }

        // 支付过期：转运&代发
        List<SysProdTransport> transportList = iSysProdTransportService.list(Wrappers.<SysProdTransport>lambdaQuery()
                .select(SysProdTransport::getId, SysProdTransport::getType)
                .le(SysProdTransport::getGmtPayValid, now)
                .eq(SysProdTransport::getStatus, 3));
        if (!ObjectUtils.isEmpty(transportList)) {
            iSysProdService.release(transportList.stream().filter(a -> a.getType() == 3).map(SysProdTransport::getId).collect(Collectors.toList()), SysProdEvent.TypeSend);
            iSysProdService.release(transportList.stream().filter(a -> a.getType() == 4).map(SysProdTransport::getId).collect(Collectors.toList()), SysProdEvent.TypeTransport);
        }

        // 支付过期：套现
        List<SysProdCash> cashList = iSysProdCashService.list(Wrappers.<SysProdCash>lambdaQuery()
                .select(SysProdCash::getId)
                .le(SysProdCash::getGmtPayValid, now)
                .eq(SysProdCash::getStatus, 3));
        if (!ObjectUtils.isEmpty(cashList)) {
            iSysProdService.release(cashList.stream().map(SysProdCash::getId).collect(Collectors.toList()), SysProdEvent.TypeCash);
        }

        // 寄售打款
        salePay(now);

    }

    private void salePay(Date now) {
        List<SysProdSalePool> poolList = iSysProdSalePoolService.list(Wrappers.<SysProdSalePool>lambdaQuery()
                .isNull(SysProdSalePool::getGmtPay).le(SysProdSalePool::getGmtSettle, now)
                .orderByAsc(SysProdSalePool::getBillId)
        );
        if (ObjectUtils.isEmpty(poolList)) {
            log.info("ScheduledTasks salePay poolList is null ");
            return;
        }

        // 按照 bill_Id 的从小到大顺序插入 poolMap
        Map<Integer, SysProdSalePool> poolMap = new LinkedHashMap<>();
        for (SysProdSalePool pool : poolList) {
            poolMap.put(pool.getDealId(), pool);
        }

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .in(SysProdDeal::getId, poolList.stream().map(SysProdSalePool::getDealId).collect(Collectors.toList())));

        // dealList的排序更改为poolMap的顺序
        dealList.sort((a, b) -> {
            SysProdSalePool poolA = poolMap.get(a.getId());
            SysProdSalePool poolB = poolMap.get(b.getId());
            return poolA.getBillId().compareTo(poolB.getBillId());
        });

        poolList.clear();

        List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                .in(SysWareOut::getRelationId, dealList.stream().map(SysProdDeal::getSaleId).collect(Collectors.toList()))
                .eq(SysWareOut::getType, SysProdEvent.TypeSale));
        Map<Integer, Integer> outIdMap = outList.stream().collect(Collectors.toMap(SysWareOut::getRelationId, SysWareOut::getId));
        outList.clear();

        List<Integer> shopIdList = dealList.stream().map(SysProdDeal::getShopId).collect(Collectors.toList());
        List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIdList));
        Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));

        dealList.forEach(deal -> {
            SysProdSalePool pool = poolMap.get(deal.getId());
            if (iSysProdSalePoolService.update(Wrappers.<SysProdSalePool>lambdaUpdate()
                    .set(SysProdSalePool::getGmtPay, now)
                    .isNull(SysProdSalePool::getGmtPay)
                    .eq(SysProdSalePool::getId, pool.getId()))) {
                Integer shopId = deal.getShopId();
                BigDecimal getPlatSoldPrice = deal.getPlatSoldPrice();

                // 给商家发钱
                iSysMoneyService.change(5, shopId, getPlatSoldPrice);

                if (ObjectUtils.isEmpty(pool.getBillId())) {
                    // 生成流水：寄售收入
                    SysBill bill = new SysBill();
                    bill.setStatus(2);
                    bill.setUserId(shopId);
                    bill.setUserType(5);
                    bill.setIeType(1);
                    bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysProdEvent.TypeSale, 4));
                    bill.setPayType(4);
                    bill.setTotalFee(getPlatSoldPrice);
                    bill.setRelationType(SysProdEvent.TypeSale);

                    Map<String, String> attach = new HashMap<>();
                    attach.put("platOrderNo", deal.getPlatOrderNo());
                    SysWare ware = iSysWareService.getById(deal.getWareId());
                    if (!ObjectUtils.isEmpty(ware)) {
                        attach.put("wareName", ware.getName());
                    }
                    if (!ObjectUtils.isEmpty(deal.getSalePrice())) {
                        attach.put("salePrice", deal.getSalePrice().toString());
                    }
                    if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
                        attach.put("platSoldPrice", deal.getPlatSoldPrice().toString());
                    }
                    if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice()) && !ObjectUtils.isEmpty(deal.getSoldPrice())) {
                        attach.put("serviceFee", deal.getPlatSoldPrice().subtract(deal.getSoldPrice()).toString());
                    }
                    SysProd prod = iSysProdService.getById(deal.getProdId());
                    if (!ObjectUtils.isEmpty(prod)) {
                        attach.put("remarks", prod.getRemarks());
                        attach.put("sku", prod.getSku());
                        attach.put("spec", prod.getSpec());
                        attach.put("oneId", prod.getOneId());
                    }
                    bill.setAttach(BaseUtils.mapToQuery(attach) + "&");

                    bill.setRelationId(outIdMap.get(deal.getSaleId()));
                    iSysBillService.saveSysBill(bill);
                } else {
                    // 更新流水状态：寄售收入
                    SysBill bill = new SysBill();
                    bill.setId(pool.getBillId());
                    bill.setStatus(2);
                    iSysBillService.saveSysBill(bill);
                }

                // 通知商家，订单完成
                ShopUser shop = shopMap.get(deal.getShopId());
                if (!ObjectUtils.isEmpty(shop) && !ObjectUtils.isEmpty(shop.getEmail())) {
                    SysProdSale sale = iSysProdSaleService.getById(deal.getSaleId());
                    if (!ObjectUtils.isEmpty(sale)) {
                        String shopName = ObjectUtils.isEmpty(shop.getRealname()) ? "" : shop.getRealname();
                        Map<String,String> map = new HashMap<>();
                        map.put("email", shop.getEmail()); // 邮箱
                        map.put("userName", shopName); // 用户名
                        map.put("orderNo", sale.getOddNo()); // 编号
                        sendEmailService.fundAreAvailableSendEmail(map);
//                        String content =
//                                "Hi, Dear " + shopName + "\n" +
//                                        "\n" +
//                                        "We would like to inform you that your order #" + sale.getOddNo() + " has been successfully processed " +
//                                        "and completed. You can now access and view the funds in your wallets. " +
//                                        "If desired, you may proceed with cashing out at your convenience.\n" +
//                                        "\n" +
//                                        "If you have any questions regarding your balance, please contact our customer service.\n" +
//                                        "\n" +
//                                        "We appreciate your business!\n" +
//                                        "\n" +
//                                        "KNETGROUP";
//                        async.sendMail(shop.getEmail(), content, "Your order #" + sale.getOddNo() + " has been completed. - KNETGROUP");
                    }
                }
            }
        });
        dealList.clear();
    }

    @AcquireTaskLock(name = "refreshMetrics")
    public void refreshMetrics() {
        log.info("ScheduledTasks refreshMetrics start");
        sysOperatedMetricsService.refreshMetrics();
        log.info("ScheduledTasks refreshMetrics end");
    }


    @AcquireTaskLock(name = "dayTask")
    public void day(Date date) {
        log.info("日级任务....");
        Date lastDay ;
        if(ObjectUtils.isEmpty(date)){
            Calendar c = Calendar.getInstance();
            c.setTime(DateTimeUtils.getNow());
            Date now = c.getTime();

            c.add(Calendar.DATE, -1);
            lastDay = c.getTime();
        }else{
            lastDay = date;
        }

        // 清理小于当天的编号
//        String dateStr = DateTimeUtils.format(DateTimeUtils.sdfCode, lastDay);
//        iSysCodePoolService.hardDelete(Wrappers.<SysCodePool>lambdaQuery().lt(SysCodePool::getDateStr, dateStr));

        // 统计前一天的数据
        saveDayStatistic(DateTimeUtils.getDateStart(lastDay), DateTimeUtils.getDateEnd(lastDay));
    }

    private void saveDayStatistic(Date beginDate, Date endDate) {
        log.info("ScheduledTasks.saveDayStatistic start beginDate={},endDate={} ", beginDate, endDate);

        List<SysStatisticData> dataList = new ArrayList<>();

        String dateStr = DateTimeUtils.format(DateTimeUtils.sdfDay, beginDate);

        // 商品总数
        SysStatisticData prodAll = new SysStatisticData();
        prodAll.setGmtCreate(endDate);
        prodAll.setDataId(null);
        prodAll.setDataType(SysStatisticData.TypeProd);
        prodAll.setDataNum(iSysProdService.count() + "");
        prodAll.setDateType(3);
        prodAll.setDateStr(dateStr);
        dataList.add(prodAll);

        // 在仓总数
        QueryWrapper<SysProd> prodOnQw = new QueryWrapper();
        prodOnQw.select("count(0) status, ifnull(shop_id, 0) shop_id");
        prodOnQw.ne("status", 6);
        prodOnQw.eq("del_flag", 0);
        prodOnQw.groupBy("shop_id");
        List<SysProd> onList = iSysProdService.list(prodOnQw);
        Integer onAllNum = 0;

        log.info("ScheduledTasks.saveDayStatistic SysProd onListSize={} ", onList.size());
        log.info("ScheduledTasks.saveDayStatistic SysProd onListSize={} ", onList.size());

        for (SysProd prod : onList) {
            SysStatisticData prodSale = new SysStatisticData();
            prodSale.setGmtCreate(endDate);
            prodSale.setDataId(prod.getShopId());
            prodSale.setDataType(SysStatisticData.TypeProdOn);
            prodSale.setDataNum(prod.getStatus() + "");
            prodSale.setDateType(3);
            prodSale.setDateStr(dateStr);
            dataList.add(prodSale);

            onAllNum = onAllNum + prod.getStatus();
        }
        onList.clear();
        SysStatisticData prodOn = new SysStatisticData();
        prodOn.setGmtCreate(endDate);
        prodOn.setDataId(null);
        prodOn.setDataType(SysStatisticData.TypeProdOn);
        prodOn.setDataNum(onAllNum + "");
        prodOn.setDateType(3);
        prodOn.setDateStr(dateStr);
        dataList.add(prodOn);

        log.info("ScheduledTasks.saveDayStatistic onAllNum dataListSize={} ", dataList.size());

        // 寄售数量
        QueryWrapper<SysProdDeal> saleQw = new QueryWrapper();
        saleQw.select("count(0) status, ifnull(shop_id, 0) shop_id");
        saleQw.eq("type", SysProdEvent.TypeSale);
        saleQw.eq("status", 1);
        saleQw.eq("del_flag", 0);
        saleQw.groupBy("shop_id");
        List<SysProdDeal> saleList = iSysProdDealService.list(saleQw);
        Integer saleProdAllNum = 0;

        log.info("ScheduledTasks.saveDayStatistic saleListsize ={} ", saleList.size());

        for (SysProdDeal sale : saleList) {
            SysStatisticData prodSale = new SysStatisticData();
            prodSale.setGmtCreate(endDate);
            prodSale.setDataId(sale.getShopId());
            prodSale.setDataType(SysStatisticData.TypeProdSale);
            prodSale.setDataNum(sale.getStatus() + "");
            prodSale.setDateType(3);
            prodSale.setDateStr(dateStr);
            dataList.add(prodSale);

            saleProdAllNum = saleProdAllNum + sale.getStatus();
        }
        saleList.clear();
        SysStatisticData prodSale = new SysStatisticData();
        prodSale.setGmtCreate(endDate);
        prodSale.setDataId(null);
        prodSale.setDataType(SysStatisticData.TypeProdSale);
        prodSale.setDataNum(saleProdAllNum + "");
        prodSale.setDateType(3);
        prodSale.setDateStr(dateStr);
        dataList.add(prodSale);

        log.info("ScheduledTasks.saveDayStatistic saleList dataListSize={} ", dataList.size());

        // sku种类
        QueryWrapper<SysProdSearch> skuQw = new QueryWrapper();
        skuQw.select("count(distinct sku) status, ifnull(shop_id, 0) shop_id");
        skuQw.eq("del_flag", 0);
        skuQw.groupBy("shop_id");
        List<SysProdSearch> skuList = iSysProdSearchService.list(skuQw);
        for (SysProdSearch prod : skuList) {
            SysStatisticData prodSku = new SysStatisticData();
            prodSku.setGmtCreate(endDate);
            prodSku.setDataId(prod.getShopId());
            prodSku.setDataType(SysStatisticData.TypeProdSku);
            prodSku.setDataNum(prod.getStatus() + "");
            prodSku.setDateType(3);
            prodSku.setDateStr(dateStr);
            dataList.add(prodSku);
        }
        skuList.clear();
        SysStatisticData prodSku = new SysStatisticData();
        prodSku.setGmtCreate(endDate);
        prodSku.setDataId(null);
        prodSku.setDataType(SysStatisticData.TypeProdSku);
        QueryWrapper<SysProdSearch> skuAllQw = new QueryWrapper();
        skuAllQw.select("distinct sku");
        skuAllQw.eq("del_flag", 0);
        prodSku.setDataNum(iSysProdSearchService.count(skuAllQw) + "");
        prodSku.setDateType(3);
        prodSku.setDateStr(dateStr);
        dataList.add(prodSku);

        // 瑕疵数量
        QueryWrapper<SysProdSearch> brokenQw = new QueryWrapper();
        brokenQw.select("count(0) status, ifnull(shop_id, 0) shop_id");
        brokenQw.in("check_result", 2, 3);
        brokenQw.eq("search_type", 1);
        brokenQw.ne("status", 6);
        brokenQw.eq("del_flag", 0);
        brokenQw.groupBy("shop_id");
        List<SysProdSearch> brokenList = iSysProdSearchService.list(brokenQw);
        Integer brokenAllNum = 0;
        for (SysProdSearch prod : brokenList) {
            SysStatisticData prodBroken = new SysStatisticData();
            prodBroken.setGmtCreate(endDate);
            prodBroken.setDataId(prod.getShopId());
            prodBroken.setDataType(SysStatisticData.TypeProdBroken);
            prodBroken.setDataNum(prod.getStatus() + "");
            prodBroken.setDateType(3);
            prodBroken.setDateStr(dateStr);
            dataList.add(prodBroken);

            brokenAllNum = brokenAllNum + prod.getStatus();
        }
        SysStatisticData prodBroken = new SysStatisticData();
        prodBroken.setGmtCreate(endDate);
        prodBroken.setDataId(null);
        prodBroken.setDataType(SysStatisticData.TypeProdBroken);
        prodBroken.setDataNum(brokenAllNum + "");
        prodBroken.setDateType(3);
        prodBroken.setDateStr(dateStr);
        dataList.add(prodBroken);

        // 入库商品数量
        QueryWrapper<SysWareInProd> inProdQw = new QueryWrapper<>();
        inProdQw.select("ifnull(count(0),0) check_result, ware_id, in_type");
        inProdQw.ge("gmt_create", beginDate);
        inProdQw.le("gmt_create", endDate);
        inProdQw.eq("del_flag", 0);
        inProdQw.groupBy("ware_id, in_type");
        List<SysWareInProd> inProdList = iSysWareInProdService.list(inProdQw);
        if (!ObjectUtils.isEmpty(inProdList)) {
            Integer allNum = 0;
            Integer num1 = 0;
            Integer num2 = 0;

            Map<Integer, SysStatisticData> wareDataMap = new HashMap<>();
            for (SysWareInProd group : inProdList) {// 全部
                SysStatisticData wareData = wareDataMap.get(group.getWareId());
                if (ObjectUtils.isEmpty(wareData)) {
                    wareData = new SysStatisticData();
                    wareData.setDataNum("0");
                    wareData.setGmtCreate(endDate);
                    wareData.setDataId(group.getWareId());
                    wareData.setDataType(SysStatisticData.TypeWareInProd);
                    wareData.setDateType(3);
                    wareData.setDateStr(dateStr);
                    dataList.add(wareData);

                    wareDataMap.put(group.getWareId(), wareData);
                }
                wareData.setDataNum(group.getCheckResult() + Integer.valueOf(wareData.getDataNum()) + "");
                allNum = allNum + group.getCheckResult();

                // 按类型
                SysStatisticData typeData = new SysStatisticData();
                typeData.setGmtCreate(endDate);
                typeData.setDataId(group.getWareId());
                typeData.setDataNum(group.getCheckResult() + "");
                typeData.setDateType(3);
                typeData.setDateStr(dateStr);
                switch (group.getInType()) {
                    case 1:
                        num1 = num1 + group.getCheckResult();
                        typeData.setDataType(SysStatisticData.TypeWareInProdShipping);
                        break;
                    case 2:
                        num2 = num2 + group.getCheckResult();
                        typeData.setDataType(SysStatisticData.TypeWareInProdDropoff);
                        break;
                }
                dataList.add(typeData);
            }

            SysStatisticData data0 = new SysStatisticData();
            data0.setGmtCreate(endDate);
            data0.setDataNum(allNum + "");
            data0.setDateType(3);
            data0.setDataType(SysStatisticData.TypeWareInProd);
            data0.setDateStr(dateStr);
            dataList.add(data0);

            SysStatisticData data1 = new SysStatisticData();
            data1.setGmtCreate(endDate);
            data1.setDataNum(num1 + "");
            data1.setDateType(3);
            data1.setDataType(SysStatisticData.TypeWareInProdShipping);
            data1.setDateStr(dateStr);
            dataList.add(data1);

            SysStatisticData data2 = new SysStatisticData();
            data2.setGmtCreate(endDate);
            data2.setDataNum(num2 + "");
            data2.setDateType(3);
            data2.setDataType(SysStatisticData.TypeWareInProdDropoff);
            data2.setDateStr(dateStr);
            dataList.add(data2);
        }

        // 入库单数量
        QueryWrapper<SysWareIn> inQw = new QueryWrapper<>();
        inQw.select("ifnull(count(0),0) num, ware_id, type");
        inQw.ge("gmt_modify", beginDate);
        inQw.le("gmt_modify", endDate);
        inQw.eq("del_flag", 0);
        inQw.eq("status", 3); // 完成验货
        inQw.groupBy("ware_id, type");
        List<SysWareIn> inList = iSysWareInService.list(inQw);
        if (!ObjectUtils.isEmpty(inList)) {
            Integer allNum = 0;
            Integer num1 = 0;
            Integer num2 = 0;

            Map<Integer, SysStatisticData> wareDataMap = new HashMap<>();
            for (SysWareIn group : inList) {
                // 全部
                SysStatisticData wareData = wareDataMap.get(group.getWareId());
                if (ObjectUtils.isEmpty(wareData)) {
                    wareData = new SysStatisticData();
                    wareData.setDataNum("0");
                    wareData.setGmtCreate(endDate);
                    wareData.setDataId(group.getWareId());
                    wareData.setDataType(SysStatisticData.TypeWareIn);
                    wareData.setDateType(3);
                    wareData.setDateStr(dateStr);
                    dataList.add(wareData);

                    wareDataMap.put(group.getWareId(), wareData);
                }
                wareData.setDataNum(group.getNum() + Integer.valueOf(wareData.getDataNum()) + "");
                allNum = allNum + group.getNum();

                // 按类型
                SysStatisticData typeData = new SysStatisticData();
                typeData.setGmtCreate(endDate);
                typeData.setDataId(group.getWareId());
                typeData.setDataNum(group.getNum() + "");
                typeData.setDateType(3);
                typeData.setDateStr(dateStr);
                dataList.add(typeData);

                switch (group.getType()) {
                    case 1:
                        num1 = num1 + group.getNum();
                        typeData.setDataType(SysStatisticData.TypeWareInShipping);
                        break;
                    case 2:
                        num2 = num2 + group.getNum();
                        typeData.setDataType(SysStatisticData.TypeWareInDropoff);
                        break;
                }
            }

            SysStatisticData data0 = new SysStatisticData();
            data0.setGmtCreate(endDate);
            data0.setDataNum(allNum + "");
            data0.setDateType(3);
            data0.setDataType(SysStatisticData.TypeWareIn);
            data0.setDateStr(dateStr);
            dataList.add(data0);

            SysStatisticData data1 = new SysStatisticData();
            data1.setGmtCreate(endDate);
            data1.setDataNum(num1 + "");
            data1.setDateType(3);
            data1.setDataType(SysStatisticData.TypeWareInShipping);
            data1.setDateStr(dateStr);
            dataList.add(data1);

            SysStatisticData data2 = new SysStatisticData();
            data2.setGmtCreate(endDate);
            data2.setDataNum(num2 + "");
            data2.setDateType(3);
            data2.setDataType(SysStatisticData.TypeWareInDropoff);
            data2.setDateStr(dateStr);
            dataList.add(data2);
        }

        // 出库商品数量
        QueryWrapper outBatchProdQw = new QueryWrapper<>();
//        outQw.select("ifnull(count(0),0) status, t1.ware_id outId, t1.type prodId");
        outBatchProdQw.ge("t.gmt_out", beginDate);
        outBatchProdQw.le("t.gmt_out", endDate);
        outBatchProdQw.eq("t.del_flag", 0);
        outBatchProdQw.eq("t.status", 4); // 完成出库
        outBatchProdQw.groupBy("t1.ware_id, t1.type");
        List<SysWareOutBatchProd> outBatchProdList = iSysWareOutBatchProdService.statistic(outBatchProdQw);
        if (!ObjectUtils.isEmpty(outBatchProdList)) {
            Integer allNum = 0;
            Integer num1 = 0;
            Integer num2 = 0;
            Integer num3 = 0;

            Map<Integer, SysStatisticData> wareDataMap = new HashMap<>();
            for (SysWareOutBatchProd group : outBatchProdList) {// 全部
                SysStatisticData wareData = wareDataMap.get(group.getOutId());
                if (ObjectUtils.isEmpty(wareData)) {
                    wareData = new SysStatisticData();
                    wareData.setDataNum("0");
                    wareData.setGmtCreate(endDate);
                    wareData.setDataId(group.getOutId());
                    wareData.setDataType(SysStatisticData.TypeWareOutProd);
                    wareData.setDateType(3);
                    wareData.setDateStr(dateStr);
                    dataList.add(wareData);

                    wareDataMap.put(group.getOutId(), wareData);
                }
                wareData.setDataNum(group.getStatus() + Integer.valueOf(wareData.getDataNum()) + "");
                allNum = allNum + group.getStatus();

                // 按类型
                SysStatisticData typeData = new SysStatisticData();
                typeData.setGmtCreate(endDate);
                typeData.setDataId(group.getOutId());
                switch (group.getProdId()) {
                    case SysProdEvent.TypeSend:
                        typeData.setDataType(SysStatisticData.TypeWareOutProdSend);
                        num1 = num1 + group.getStatus();
                        break;
                    case SysProdEvent.TypeTransport:
                        typeData.setDataType(SysStatisticData.TypeWareOutProdTransport);
                        num2 = num2 + group.getStatus();
                        break;
                    case SysProdEvent.TypeSale:
                        typeData.setDataType(SysStatisticData.TypeWareOutProdSale);
                        num3 = num3 + group.getStatus();
                        break;
                }
                typeData.setDataNum(group.getStatus() + "");
                typeData.setDateType(3);
                typeData.setDateStr(dateStr);
                dataList.add(typeData);
            }

            SysStatisticData data0 = new SysStatisticData();
            data0.setGmtCreate(endDate);
            data0.setDataNum(allNum + "");
            data0.setDateType(3);
            data0.setDataType(SysStatisticData.TypeWareOutProd);
            data0.setDateStr(dateStr);
            dataList.add(data0);

            SysStatisticData data1 = new SysStatisticData();
            data1.setGmtCreate(endDate);
            data1.setDataNum(num1 + "");
            data1.setDateType(3);
            data1.setDataType(SysStatisticData.TypeWareOutProdSend);
            data1.setDateStr(dateStr);
            dataList.add(data1);

            SysStatisticData data2 = new SysStatisticData();
            data2.setGmtCreate(endDate);
            data2.setDataNum(num2 + "");
            data2.setDateType(3);
            data2.setDataType(SysStatisticData.TypeWareOutProdTransport);
            data2.setDateStr(dateStr);
            dataList.add(data2);

            SysStatisticData data3 = new SysStatisticData();
            data3.setGmtCreate(endDate);
            data3.setDataNum(num3 + "");
            data3.setDateType(3);
            data3.setDataType(SysStatisticData.TypeWareOutProdSale);
            data3.setDateStr(dateStr);
            dataList.add(data3);
        }

        // 出库批次数量
        QueryWrapper<SysWareOutBatch> outBatchQw = new QueryWrapper<>();
        outBatchQw.select("ifnull(count(0),0) status, ware_id");
        outBatchQw.ge("gmt_modify", beginDate);
        outBatchQw.le("gmt_modify", endDate);
        outBatchQw.eq("del_flag", 0);
        outBatchQw.eq("status", 2); // 完成出库
        outBatchQw.groupBy("ware_id");
        List<SysWareOutBatch> outBatchList = iSysWareOutBatchService.list(outBatchQw);
        if (!ObjectUtils.isEmpty(outBatchList)) {
            Integer allNum = 0;
            for (SysWareOutBatch group : outBatchList) {
                SysStatisticData wareData = new SysStatisticData();
                wareData.setGmtCreate(endDate);
                wareData.setDataId(group.getWareId());
                wareData.setDataType(SysStatisticData.TypeWareOutBatch);
                wareData.setDateType(3);
                wareData.setDateStr(dateStr);
                dataList.add(wareData);
                wareData.setDataNum(group.getStatus() + "");

                allNum = allNum + group.getStatus();
            }

            SysStatisticData data0 = new SysStatisticData();
            data0.setGmtCreate(endDate);
            data0.setDataNum(allNum + "");
            data0.setDateType(3);
            data0.setDataType(SysStatisticData.TypeWareOutBatch);
            data0.setDateStr(dateStr);
            dataList.add(data0);
        }

        // 出库单数量
        QueryWrapper<SysWareOut> outQw = new QueryWrapper<>();
        outQw.select("ifnull(count(0),0) prodNum, ware_id, type");
        outQw.ge("gmt_modify", beginDate);
        outQw.le("gmt_modify", endDate);
        outQw.eq("del_flag", 0);
        outQw.eq("status", 5); // 完成出库
        outQw.groupBy("ware_id, type");
        List<SysWareOut> outList = iSysWareOutService.list(outQw);
        if (!ObjectUtils.isEmpty(outList)) {
            Integer allNum = 0;
            Integer num1 = 0;
            Integer num2 = 0;
            Integer num3 = 0;

            Map<Integer, SysStatisticData> wareDataMap = new HashMap<>();
            for (SysWareOut group : outList) {// 全部
                SysStatisticData wareData = wareDataMap.get(group.getWareId());
                if (ObjectUtils.isEmpty(wareData)) {
                    wareData = new SysStatisticData();
                    wareData.setDataNum("0");
                    wareData.setGmtCreate(endDate);
                    wareData.setDataId(group.getWareId());
                    wareData.setDataType(SysStatisticData.TypeWareOut);
                    wareData.setDateType(3);
                    wareData.setDateStr(dateStr);
                    dataList.add(wareData);

                    wareDataMap.put(group.getWareId(), wareData);
                }
                wareData.setDataNum(group.getProdNum() + Integer.valueOf(wareData.getDataNum()) + "");
                allNum = allNum + group.getProdNum();

                // 按类型
                SysStatisticData typeData = new SysStatisticData();
                typeData.setGmtCreate(endDate);
                typeData.setDataId(group.getWareId());
                switch (group.getType()) {
                    case SysProdEvent.TypeSend:
                        typeData.setDataType(SysStatisticData.TypeWareOutSend);
                        num1 = num1 + group.getProdNum();
                        break;
                    case SysProdEvent.TypeTransport:
                        typeData.setDataType(SysStatisticData.TypeWareOutTransport);
                        num2 = num2 + group.getProdNum();
                        break;
                    case SysProdEvent.TypeSale:
                        typeData.setDataType(SysStatisticData.TypeWareOutSale);
                        num3 = num3 + group.getProdNum();
                        break;
                }
                typeData.setDataNum(group.getProdNum() + "");
                typeData.setDateType(3);
                typeData.setDateStr(dateStr);
                dataList.add(typeData);
            }

            SysStatisticData data0 = new SysStatisticData();
            data0.setGmtCreate(endDate);
            data0.setDataNum(allNum + "");
            data0.setDateType(3);
            data0.setDataType(SysStatisticData.TypeWareOut);
            data0.setDateStr(dateStr);
            dataList.add(data0);

            SysStatisticData data1 = new SysStatisticData();
            data1.setGmtCreate(endDate);
            data1.setDataNum(num1 + "");
            data1.setDateType(3);
            data1.setDataType(SysStatisticData.TypeWareOutSend);
            data1.setDateStr(dateStr);
            dataList.add(data1);

            SysStatisticData data2 = new SysStatisticData();
            data2.setGmtCreate(endDate);
            data2.setDataNum(num2 + "");
            data2.setDateType(3);
            data2.setDataType(SysStatisticData.TypeWareOutTransport);
            data2.setDateStr(dateStr);
            dataList.add(data2);

            SysStatisticData data3 = new SysStatisticData();
            data3.setGmtCreate(endDate);
            data3.setDataNum(num3 + "");
            data3.setDateType(3);
            data3.setDataType(SysStatisticData.TypeWareOutSale);
            data3.setDateStr(dateStr);
            dataList.add(data3);
        }

        // 商家余额
        List<SysMoney> moneyList = iSysMoneyService.list();
        for (SysMoney money : moneyList) {
            SysStatisticData shopMoney = new SysStatisticData();
            shopMoney.setGmtCreate(endDate);
            shopMoney.setDataId(money.getUserId());
            shopMoney.setDataType(SysStatisticData.TypeShopMoney);
            shopMoney.setDataNum(money.getMoney() + "");
            shopMoney.setDateType(3);
            shopMoney.setDateStr(dateStr);
            dataList.add(shopMoney);
        }

        // 商家入库商品数量
        QueryWrapper<SysProdSearch> inProdShopQw = new QueryWrapper<>();
        inProdShopQw.select("count(0) status, ifnull(sum(cost_price),0) cost_price, shop_id");
        inProdShopQw.ge("gmt_in", beginDate);
        inProdShopQw.le("gmt_in", endDate);
        inProdShopQw.eq("del_flag", 0);
        inProdShopQw.groupBy("shop_id");
        List<SysProdSearch> inProdShopList = iSysProdSearchService.list(inProdShopQw);
        if (!ObjectUtils.isEmpty(inProdShopList)) {
            Integer num1 = 0;
            BigDecimal num2 = SysConstants.zero;

            for (SysProdSearch group : inProdShopList) {
                SysStatisticData numData = new SysStatisticData();
                numData.setDataNum(group.getStatus() + "");
                numData.setGmtCreate(endDate);
                numData.setDataId(group.getShopId());
                numData.setDataType(SysStatisticData.TypeShopInProd);
                numData.setDateType(3);
                numData.setDateStr(dateStr);
                dataList.add(numData);
                num1 = num1 + group.getStatus();

                SysStatisticData costData = new SysStatisticData();
                costData.setDataNum(group.getCostPrice() + "");
                costData.setGmtCreate(endDate);
                costData.setDataId(group.getShopId());
                costData.setDataType(SysStatisticData.TypeShopInCost);
                costData.setDateType(3);
                costData.setDateStr(dateStr);
                dataList.add(costData);
                num2 = num2.add(group.getCostPrice());
            }

            SysStatisticData data1 = new SysStatisticData();
            data1.setGmtCreate(endDate);
            data1.setDataNum(num1 + "");
            data1.setDateType(3);
            data1.setDataType(SysStatisticData.TypeShopInProd);
            data1.setDateStr(dateStr);
            dataList.add(data1);

            SysStatisticData data2 = new SysStatisticData();
            data2.setGmtCreate(endDate);
            data2.setDataNum(num2.toString());
            data2.setDateType(3);
            data2.setDataType(SysStatisticData.TypeShopInCost);
            data2.setDateStr(dateStr);
            dataList.add(data2);

        }

        // 商家出库商品数量
        QueryWrapper outProdShopQw = new QueryWrapper<>();
        outProdShopQw.select(
                "count(0) status, " +
//                        "ifnull(sum(ifnull(case type when " + SysProdEvent.TypeSale + " then sold_price when " + SysProdEvent.TypeCash + " then sale_price else out_price end, 0)),0) out_price, " +
                        "ifnull(sum(cost_price),0) cost_price, " +
                        "shop_id");
        outProdShopQw.ge("gmt_out", beginDate);
        outProdShopQw.le("gmt_out", endDate);
        outProdShopQw.eq("del_flag", 0);
        outProdShopQw.groupBy("shop_id");
        List<SysProdSearch> outProdShopList = iSysProdSearchService.list(outProdShopQw);
        if (!ObjectUtils.isEmpty(outProdShopList)) {
            Integer num1 = 0;
            BigDecimal num2 = SysConstants.zero;

            for (SysProdSearch group : outProdShopList) {
                SysStatisticData numData = new SysStatisticData();
                numData.setDataNum(group.getStatus() + "");
                numData.setGmtCreate(endDate);
                numData.setDataId(group.getShopId());
                numData.setDataType(SysStatisticData.TypeShopOutProd);
                numData.setDateType(3);
                numData.setDateStr(dateStr);
                dataList.add(numData);
                num1 = num1 + group.getStatus();

                SysStatisticData priceData = new SysStatisticData();
                priceData.setDataNum(group.getCostPrice() + "");
                priceData.setGmtCreate(endDate);
                priceData.setDataId(group.getShopId());
                priceData.setDataType(SysStatisticData.TypeShopOutCost);
                priceData.setDateType(3);
                priceData.setDateStr(dateStr);
                dataList.add(priceData);
                num2 = num2.add(group.getCostPrice());
            }

            SysStatisticData data1 = new SysStatisticData();
            data1.setGmtCreate(endDate);
            data1.setDataNum(num1 + "");
            data1.setDateType(3);
            data1.setDataType(SysStatisticData.TypeShopOutProd);
            data1.setDateStr(dateStr);
            dataList.add(data1);

            SysStatisticData data2 = new SysStatisticData();
            data2.setGmtCreate(endDate);
            data2.setDataNum(num2.toString());
            data2.setDateType(3);
            data2.setDataType(SysStatisticData.TypeShopOutCost);
            data2.setDateStr(dateStr);
            dataList.add(data2);
        }

        // 包裹
        QueryWrapper<ShopPack> packShopQw = new QueryWrapper<>();
        packShopQw.select("count(0) status, ifnull(shop_id, 0) shop_id");
//        packShopQw.ge("gmt_create", beginDate);
//        packShopQw.le("gmt_create", endDate);
        packShopQw.eq("del_flag", 0);
        packShopQw.groupBy("shop_id");
        List<ShopPack> packList = iShopPackService.list(packShopQw);
        if (!ObjectUtils.isEmpty(packList)) {
            Integer packNum = 0;
            SysStatisticData data = new SysStatisticData();
            data.setGmtCreate(endDate);
            data.setDataType(SysStatisticData.TypePack);
            data.setDateType(3);
            data.setDateStr(dateStr);
            dataList.add(data);

            for (ShopPack group : packList) {
                SysStatisticData numData = new SysStatisticData();
                numData.setDataNum(group.getStatus() + "");
                numData.setGmtCreate(endDate);
                numData.setDataId(group.getShopId());
                numData.setDataType(SysStatisticData.TypePack);
                numData.setDateType(3);
                numData.setDateStr(dateStr);
                dataList.add(numData);

                packNum = packNum + group.getStatus();
            }

            data.setDataNum(packNum + "");
        }

        log.info("ScheduledTasks.saveDayStatistic dataList={} ", dataList);

        if (!ObjectUtils.isEmpty(dataList)) {
            iSysStatisticDataService.insertList(dataList);
        }

        log.info("ScheduledTasks.saveDayStatistic end ");
    }

    @AcquireTaskLock(name = "minuteTask")
    public void minute() {
        Calendar c = Calendar.getInstance();
        c.setTime(DateTimeUtils.getNow());
        c.add(Calendar.MINUTE, -5);
        Date gmtValid = c.getTime();

        List<SysProdSaleValid> validList = iSysProdSaleValidService.list(Wrappers.<SysProdSaleValid>lambdaQuery().le(SysProdSaleValid::getGmtCreate, gmtValid));
        if (!ObjectUtils.isEmpty(validList)) {
            int length = 50;
            for (int i = 0; i < validList.size(); i = i + length) {
                int endIndex = validList.size() <= i + length ? validList.size() : i + length;
                List<String> oneIdList = validList.subList(i, endIndex).stream().map(SysProdSaleValid::getOneId).collect(Collectors.toList());

                JSONObject res = touchUtils.checkStatus(oneIdList);
                if ("success".equals(res.getString("status"))) {
                    List<TouchProductStatusResponse> dataList = res.getJSONArray("data").toJavaList(TouchProductStatusResponse.class);
                    List<String> successList = dataList.stream().filter(a -> a.getIsSellingInTouch()).map(TouchProductStatusResponse::getErpProductId).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(successList)) {
                        iSysProdService.fixProd(successList);
                    }

                    iSysProdSaleValidService.remove(Wrappers.<SysProdSaleValid>lambdaQuery().in(SysProdSaleValid::getOneId, oneIdList));
                }
            }
        }

    }

}


