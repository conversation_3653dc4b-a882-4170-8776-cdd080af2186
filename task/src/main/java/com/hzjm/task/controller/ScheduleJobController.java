package com.hzjm.task.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.annotation.JsonView;
import com.hzjm.common.model.HttpPageResult;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ScheduleJob;
import com.hzjm.service.model.DTO.PageBaseSearchDto;
import com.hzjm.service.service.BaseSendEmailService;
import com.hzjm.service.service.IScheduleJobService;
import com.hzjm.service.service.SendEmailService;
import com.hzjm.task.scheduled.ScheduledTasks;
import com.hzjm.task.utils.QuartzUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.quartz.CronTrigger;
import org.quartz.Scheduler;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 定时任务 前端控制器
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Api(tags = "定时任务")
@Slf4j
@RestController
@RequestMapping("/schedule_job")
public class ScheduleJobController {

    @Autowired
    private IScheduleJobService iScheduleJobService;

    @Autowired
    private Scheduler scheduler;

    @Autowired
    ScheduledTasks scheduledTasks;

    // 发送邮件
    @Resource
    BaseSendEmailService baseSendEmailService;

    @Resource
    SendEmailService sendEmailService;
    /**
     * 项目启动时，初始化定时器
     */
    @PostConstruct
    public void init() {
        List<ScheduleJob> scheduleJobList = iScheduleJobService.list(Wrappers.<ScheduleJob>lambdaQuery()
                .ge(ScheduleJob::getGmtEnd, DateTimeUtils.getNow()).or(o -> o.isNull(ScheduleJob::getGmtEnd)));
        for (ScheduleJob scheduleJob : scheduleJobList) {
            CronTrigger cronTrigger = QuartzUtils.getCronTrigger(scheduler, scheduleJob.getJobId());
            // 如果不存在，则创建
            if (cronTrigger == null) {
                QuartzUtils.createScheduleJob(scheduler, scheduleJob);
            } else {
                QuartzUtils.updateScheduleJob(scheduler, scheduleJob);
            }
        }
    }

    @ApiOperation("重新加载定时任务配置")
    @GetMapping("/reload")
    public HttpResult<Boolean> reload() {
        log.info("ScheduleJob: Reload schedule jobs from database.");
        init();

        return HttpResult.ok(true);
    }

    @ApiOperation("保存定时任务")
    @PostMapping("/save")
    public HttpResult<Boolean> save(@RequestBody ScheduleJob dto) {
        Boolean rs;

        if (ObjectUtils.isEmpty(dto.getJobId())) {
            rs = iScheduleJobService.saveScheduleJob(dto);
            QuartzUtils.createScheduleJob(scheduler, dto);
        } else {
            ScheduleJob job = iScheduleJobService.getById(dto.getJobId());
            rs = iScheduleJobService.saveScheduleJob(dto);

            if (job.getStatus().intValue() != dto.getStatus()) {
                if (QuartzUtils.statusPause == dto.getStatus()) {
                    // 开始的任务无法停止，因此已移除任务的形式，强制中止任务的进行
                    dto.setDelFlag(-1);
                    QuartzUtils.deleteScheduleJob(scheduler, dto.getJobId());
                }

                if (QuartzUtils.statusNormal == dto.getStatus()) {
                    // 暂停中的任务因已被移除，因此重新开启时需要添加新的任务
                    QuartzUtils.createScheduleJob(scheduler, dto);
                }
            } else if (!ObjectUtils.isEmpty(dto.getDelFlag()) && dto.getDelFlag() == -1) {
                // 直接移除
                QuartzUtils.deleteScheduleJob(scheduler, dto.getJobId());
            } else {
                // 修改任务
                QuartzUtils.updateScheduleJob(scheduler, dto);
            }
        }
        return HttpResult.ok(rs);
    }

    @ApiOperation("立即执行定时任务")
    @GetMapping("/run")
    public HttpResult<Boolean> run(@RequestParam Long jobId) {
        ScheduleJob job = iScheduleJobService.getByIdWithoutLogic(jobId);

        QuartzUtils.run(scheduler, job);
        return HttpResult.ok(true);
    }

    @ApiOperation("查看定时任务")
    @GetMapping("/get")
    @JsonView({ScheduleJob.ScheduleJobInfoView.class})
    public HttpResult<ScheduleJob> get(@RequestParam Long id) {
        return HttpResult.ok(iScheduleJobService.getDetail(id));
    }

    @ApiOperation("查询定时任务列表")
    @PostMapping("/list")
    @JsonView(ScheduleJob.ScheduleJobListView.class)
    public HttpResult<HttpPageResult<ScheduleJob>> list(@RequestBody PageBaseSearchDto dto) {
        Date endTime = dto.dealEndTime();

        LambdaQueryWrapper<ScheduleJob> qw = Wrappers.<ScheduleJob>lambdaQuery().orderByDesc(ScheduleJob::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ScheduleJob::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ScheduleJob::getGmtCreate, endTime);

        HttpPageResult<ScheduleJob> pageResult = new HttpPageResult();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            IPage<ScheduleJob> iPage = iScheduleJobService.page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
            BeanUtils.copyProperties(iPage, pageResult);
        } else {
            pageResult.setRecords(iScheduleJobService.list(qw));
        }

        return HttpResult.ok(pageResult);
    }

    /**
     *  统计面板数据，例如统计当天数据，传入第二天的凌晨五点 ，统计 20240327 ，传入 2024032805
     */
    @GetMapping("/day")
    public HttpResult<String> day(@RequestParam String dateStr) {
        if (ObjectUtils.isEmpty(dateStr)){
            return HttpResult.error("The dateStr is incorrect");
        }

        Date date = DateTimeUtils.getDateToString(dateStr);

        if (ObjectUtils.isEmpty(date)){
            return HttpResult.error("The datestr is not yyyyMMddHH ");
        }

        try {
            scheduledTasks.day(date);
        }catch (Exception e){
            log.error("task day is error e={},e.msg={}", e, e.getMessage());
        }

        return  HttpResult.ok("sueccss ok!");

    }


    @GetMapping("/sendEmail")
    public HttpResult<String> sendEmail(@RequestParam String email) {
        // 发送到货邮件
        Map<String, String> map = new HashMap<>();
//        map.put("email", "<EMAIL>"); // 邮箱
//        map.put("email", "<EMAIL>"); // 邮箱
//        map.put("email", "<EMAIL>"); // 邮箱
//        map.put("email", "<EMAIL>"); // 邮箱
//        map.put("email", "<EMAIL>"); // 邮箱
        map.put("email", "<EMAIL>"); // 邮箱
        map.put("userName", "lucy"); // 用户名

        map.put("trackingNo","121234443435rerer");
//        sendEmailService.incomingShipmentSendEmail(map);
        // 查验
        map.put("logNo","121214460443423234");
//        sendEmailService.oncomingShipmentSendEmail(map);
        // 到货
        Map<String, Object> dataMap = new HashMap<>();
//        dataMap.put("email", "<EMAIL>");
//        dataMap.put("email", "<EMAIL>");
//        dataMap.put("email", "<EMAIL>");
//        dataMap.put("email", "<EMAIL>");
        dataMap.put("email", "<EMAIL>");
//        dataMap.put("email", "<EMAIL>");
        dataMap.put("userName", "lucy");
        dataMap.put("platform","KNET");
        dataMap.put("imageUrl","https://image.goat.com/750/attachments/product_template_pictures/images/084/941/046/original/1103104_00.png.png");
        dataMap.put("itemName","yuguyggui");
        dataMap.put("sku","DZ5485 051");
        dataMap.put("size","13");
        dataMap.put("amount","100");
        dataMap.put("owning","80");
        dataMap.put("orderNumber","12121644jg2247984243");
        sendEmailService.productSoldSendEmail(dataMap);
        // 提现
        map.put("orderNo","12212744831212143");
//        sendEmailService.fundAreAvailableSendEmail(map);

//        SysEmailHistory sysEmailHistory = new SysEmailHistory();
//        sysEmailHistory.setBusinessId(UUID.randomUUID().toString());
//        sysEmailHistory.setBusinessType(EmailTypeEnum.ADMIN_SEND_EMAIL.value);
//
//        baseSendEmailService.sendEmail("<EMAIL>", "", "内容2", "主题2", sysEmailHistory);

        return HttpResult.ok();
    }

}
