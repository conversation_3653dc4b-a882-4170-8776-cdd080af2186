package com.hzjm.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.model.DTO.SysWareOutPageDto;
import com.hzjm.service.model.DTO.SysWareOutUserSaveDto;
import com.hzjm.service.model.VO.SysWareOutListVo;
import com.hzjm.service.model.VO.SysWareOutUserCountVo;
import com.hzjm.service.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 出库单任务情况 前端控制器
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Api(tags = "出库单任务情况")
@Slf4j
@RestController
@RequestMapping("/sys_ware_out_user")
public class SysWareOutUserController {

    @Autowired
    private ISysWareOutUserService iSysWareOutUserService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @ApiOperation(value = "保存出库单任务情况", notes = "说明：\n" +
            "添加：不传id和delFlag\n" +
            "修改：传id，不传delFlag\n" +
            "删除：传id，delFlag固定传-1")
    @PostMapping("/save")
    public HttpResult<Boolean> save(@RequestBody SysWareOutUserSaveDto dto) {
        if (ObjectUtils.isEmpty(dto.getOutIdList()) && !ObjectUtils.isEmpty(dto.getOutId())) {
            dto.setOutIdList(new ArrayList<>(Arrays.asList(dto.getOutId())));
        }

        if (ObjectUtils.isEmpty(dto.getUserId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意账号"));
        }

        if (ObjectUtils.isEmpty(dto.getOutIdList())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意批次"));
        }

        if (iSysWareOutService.count(Wrappers.<SysWareOut>lambdaQuery()
                .eq(SysWareOut::getStatus, 5)
                .in(SysWareOut::getId, dto.getOutIdList())) > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("存在已出库的出库单"));
        }

        if (dto.getType() == 2 && iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .ne(SysWareOutBatchProd::getBatchStatus, 2)
                .in(SysWareOutBatchProd::getOutId, dto.getOutIdList())) > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("存在处理中的出库单，无法撤回"));
        }

        iSysWareOutUserService.remove(Wrappers.<SysWareOutUser>lambdaQuery().in(SysWareOutUser::getOutId, dto.getOutIdList()));
        // 更新 出库单状态为已分配
        iSysWareOutService.update(Wrappers.<SysWareOut>lambdaUpdate()
                .in(SysWareOut::getId, dto.getOutIdList())
                .set(SysWareOut::getAssigned, false)
        );
        // 设置分配任务
        if (ObjectUtils.isEmpty(dto.getType()) || dto.getType() == 1) {
            Integer userId = JwtContentHolder.getUserId();
            List<SysWareOutUser> outUserList = new ArrayList<>();

            dto.getOutIdList().forEach(outId -> {
                SysWareOutUser batchUser = new SysWareOutUser();
                batchUser.setOutId(outId);
                batchUser.setUserId(dto.getUserId());
                batchUser.setCreateById(userId);
                outUserList.add(batchUser);
            });

            // 更新 出库单状态为已分配
            iSysWareOutService.update(Wrappers.<SysWareOut>lambdaUpdate()
                    .in(SysWareOut::getId, dto.getOutIdList())
                    .set(SysWareOut::getAssigned, true)
            );

            return HttpResult.ok(iSysWareOutUserService.insertList(outUserList));
            // 取消分配任务
        } else {
            return HttpResult.ok(true);
        }
    }

    @ApiOperation("出库单任务统计")
    @PostMapping("/count")
    @TrimParam
    public HttpResult<SysWareOutUserCountVo> count(@RequestBody SysWareOutPageDto dto) {
        SysWareOutUserCountVo vo = new SysWareOutUserCountVo();
        dto.setCurrent(0);
        dto.setSize(0);
        dto.setStatus(null);

        List<Integer> batchIdList = BaseUtils.initList();
        if (!ObjectUtils.isEmpty(dto.getExecutor())) {
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery().like(SysUser::getNickname, dto.getExecutor()));
            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(userList.stream().map(SysUser::getId).collect(Collectors.toList()));

            List<SysWareOutUser> outUserList = iSysWareOutUserService.list(Wrappers.<SysWareOutUser>lambdaQuery().in(SysWareOutUser::getUserId, userIdList));
            batchIdList.addAll(outUserList.stream().map(SysWareOutUser::getOutId).collect(Collectors.toList()));
            dto.setIdList(batchIdList);
        }
        List<SysWareOutUser> outUserList = iSysWareOutUserService.list(Wrappers.<SysWareOutUser>lambdaQuery().select(SysWareOutUser::getOutId));
        List<Integer> assignIdList = outUserList.stream().map(SysWareOutUser::getOutId).collect(Collectors.toList());

        SysWareOutPageDto dto1 = new SysWareOutPageDto();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setAssign(false);
        //dto1.setNotIdList(assignIdList);
        vo.setDealingNum((int) iSysWareOutService.searchList(dto1).getTotal());
        vo.setDealingProdNum(iSysWareOutService.sumProdNum(dto1));

        SysWareOutPageDto dto2 = new SysWareOutPageDto();
        BeanUtils.copyProperties(dto, dto2);
        dto2.setAssign(true);
//        List<Integer> assignedIdList = BaseUtils.initList();
//        assignedIdList.addAll(assignIdList);
//        if (!ObjectUtils.isEmpty(dto2.getExecutor())) {
//            assignedIdList.retainAll(batchIdList);
//        }
//        dto2.setNotIdList(null);
//        dto2.setIdList(assignedIdList);
        vo.setAssignedNum((int) iSysWareOutService.searchList(dto2).getTotal());
        vo.setAssignedProdNum(iSysWareOutService.sumProdNum(dto2));

        dto2.setStatus(5);
        vo.setFinishNum((int) iSysWareOutService.searchList(dto2).getTotal());
        vo.setFinishProdNum(iSysWareOutService.sumProdNum(dto2));

        return HttpResult.ok(vo);
    }

    @ApiOperation("查询出库单任务列表")
    @PostMapping("/list")
    @TrimParam
    public HttpResult<IPage<SysWareOutListVo>> list(@RequestBody SysWareOutPageDto dto) {
        IPage<SysWareOutListVo> iPage = null;

        List<Integer> outIdList = BaseUtils.initList();
        if (!ObjectUtils.isEmpty(dto.getExecutor())) {
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery().like(SysUser::getNickname, dto.getExecutor()));
            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(userList.stream().map(SysUser::getId).collect(Collectors.toList()));

            List<SysWareOutUser> outUserList = iSysWareOutUserService.list(Wrappers.<SysWareOutUser>lambdaQuery().in(SysWareOutUser::getUserId, userIdList));
            outIdList.addAll(outUserList.stream().map(SysWareOutUser::getOutId).collect(Collectors.toList()));
            dto.setIdList(outIdList);
        }

//        if (!ObjectUtils.isEmpty(dto.getAssign())) {
//            List<SysWareOutUser> outUserList = iSysWareOutUserService.list(Wrappers.<SysWareOutUser>lambdaQuery().select(SysWareOutUser::getOutId));
//            List<Integer> batchIdList = BaseUtils.initList();
//            batchIdList.addAll(outUserList.stream().map(SysWareOutUser::getOutId).collect(Collectors.toList()));
//            if (dto.getAssign()) {
//                List<Integer> assignedIdList = BaseUtils.initList();
//                assignedIdList.addAll(batchIdList);
//                if (!ObjectUtils.isEmpty(dto.getExecutor())) {
//                    assignedIdList.retainAll(outIdList);
//                }
//                dto.setIdList(assignedIdList);
//            } else {
//                dto.setNotIdList(batchIdList);
//            }
//        }

        if (!ObjectUtils.isEmpty(dto.getSortOrder())) {
            iPage = iSysWareOutService.searchSale(dto);
        } else {
            iPage = iSysWareOutService.searchList(dto);
        }

        outIdList = iPage.getRecords().stream().map(SysWareOutListVo::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(outIdList)) {
            List<SysWareOutUser> outUserList = iSysWareOutUserService.list(Wrappers.<SysWareOutUser>lambdaQuery().in(SysWareOutUser::getOutId, outIdList));
            Map<Integer, SysWareOutUser> outUserMap = outUserList.stream().collect(Collectors.toMap(SysWareOutUser::getOutId, a -> a));
            outUserList.clear();

            List<SysUser> userList = iSysUserService.listAll();
            Map<Integer, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getId, a -> a));
            userList.clear();

            List<SysWare> wareList = iSysWareService.list(Wrappers.<SysWare>lambdaQuery());
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
            wareList.clear();

            List<Integer> saleIdList = BaseUtils.initList();

            saleIdList.addAll(iPage.getRecords().stream().filter(a -> {
                return a.getType() == SysProdEvent.TypeSale;
            }).map(SysWareOutListVo::getRelationId).collect(Collectors.toList()));
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
            Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
            saleList.clear();

            iPage.getRecords().forEach(data -> {
                data.setWareName(wareMap.get(data.getWareId()));

                if (data.getType() == 6) {
                    SysProdSale sale = saleMap.get(data.getRelationId());
                    if (!ObjectUtils.isEmpty(sale)) {
                        data.setPlatName(sale.getPlatName());
                        data.setPlatOrderNo(sale.getPlatOrderNo());
                    }
                }

                SysWareOutUser outUser = outUserMap.get(data.getId());
                if (!ObjectUtils.isEmpty(outUser)) {
                    SysUser executor = userMap.get(outUser.getUserId());
                    if (!ObjectUtils.isEmpty(executor)) {
                        data.setExecutor(executor.getNickname());
                        data.setExecutorId(executor.getId());
                    }
                    SysUser operator = userMap.get(outUser.getCreateById());
                    if (!ObjectUtils.isEmpty(operator)) {
                        data.setOperator(operator.getNickname());
                    }
                    data.setGmtAssign(outUser.getGmtCreate());
                }
            });
        }

        return HttpResult.ok(iPage);
    }

}
