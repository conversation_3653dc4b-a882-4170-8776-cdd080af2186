package com.hzjm.tts;

import com.hzjm.common.model.HttpResult;
import com.hzjm.tts.auth.TTSAuthShopManager;
import com.hzjm.tts.auth.data.response.AuthShop;
import com.hzjm.tts.product.service.ITTSProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/tts")
public class TestController {

    @Autowired
    private ITTSProductService ttsProductService;

    @Autowired
    private TTSAuthShopManager authShopManager;

    @PostMapping("/test")
    public String test() {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail("7495886818156513965", "<EMAIL>");
        return HttpResult.ok(authShop.getCipher()).toString();
    }

}
