package com.hzjm.tts.product.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AuthorizedStatus {
    AUTHORIZED("AUTHORIZED"),
    UNAUTHORIZED("UNAUTHORIEZD"); //  这里 tts 打错了。。。。。

    @JSONField
    @EnumValue
    private final String rawValue;

    public static AuthorizedStatus createFrom(String rawValue) {
        switch (rawValue) {
            case "AUTHORIZED": return AUTHORIZED;
            case "UNAUTHORIEZD": return UNAUTHORIZED;
            default: throw new IllegalArgumentException("Unsupported authorized status string value, value: " + rawValue);
        }
    }

}
