package com.hzjm.tts.product.data.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TTSUpdateProductPriceRequest implements Serializable {
    private List<Sku> skus;

    @Data
    public static class Sku implements Serializable {
        private String id;
        private List<ExternalListPrice> external_list_prices;
        private ListPrice list_price;
        private Price price;

        @Data
        public static class ExternalListPrice implements Serializable {
            private String amount;
            private String currency;
            private String source;
        }

        @Data
        public static class ListPrice implements Serializable {
            private String amount;
            private String currency;
        }

        @Data
        public static class Price implements Serializable {
            private String amount;
            private String currency;
        }
    }
}
