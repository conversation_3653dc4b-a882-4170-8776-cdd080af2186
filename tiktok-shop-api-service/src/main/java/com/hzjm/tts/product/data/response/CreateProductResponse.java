package com.hzjm.tts.product.data.response;

import com.hzjm.tts.product.data.request.TTSSku;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {
 *     "product_id": "1729592969712207008",
 *     "skus": [
 *       {
 *         "external_sku_id": "1729592969712207234",
 *         "id": "1729592969712207012",
 *         "sales_attributes": [
 *           {
 *             "id": "100000",
 *             "value_id": "1729592969712207123"
 *           }
 *         ],
 *         "seller_sku": "Color-Red-XM001"
 *       }
 *     ],
 *     "warnings": [
 *       {
 *         "message": "The [brand_id]:123 field is incorrect and has been automatically cleared by the system. Reason: [Brand does not exist]. You can edit it later."
 *       }
 *     ]
 *   }
 */
@Data
public class CreateProductResponse implements Serializable {
    private String product_id;
    private List<Sku> skus;
    private List<Warning> warnings;

    @Data
    public static class Sku {
        private String external_sku_id;
        private String id;
        private List<SalesAttribute> sales_attributes;
        private String seller_sku;

        @Data
        public static class SalesAttribute {
            private String id;
            private String value_id;
        }
    }

    @Data
    public static class Warning {
        private String message;
    }
}
