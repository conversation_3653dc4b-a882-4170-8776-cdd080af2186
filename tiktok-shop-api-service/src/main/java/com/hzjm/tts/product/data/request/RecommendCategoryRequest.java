package com.hzjm.tts.product.data.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RecommendCategoryRequest implements Serializable {
    public String product_title;
    public String description;
    public List<ImagesItem> images;
    public String category_version = "v2";
    public String listing_platform = "TIKTOK_SHOP";

    public static class ImagesItem {
        String uri;
    }
}
