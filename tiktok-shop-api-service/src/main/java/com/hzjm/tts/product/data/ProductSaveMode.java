package com.hzjm.tts.product.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Indicates how the product should be saved.
 * Possible values:
 * - AS_DRAFT: Save the product as a draft for future editing.
 * - LISTING: Immediately list the product in the shop.
 * Default: LISTING
 *
 */
@Getter
@AllArgsConstructor
public enum ProductSaveMode {

    AS_DRAFT("AS_DRAFT"),
    LISTING("LISTING");

    @EnumValue
    @JsonValue
    private final String rawValue;
}
