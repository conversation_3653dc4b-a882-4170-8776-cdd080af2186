package com.hzjm.tts.product.data.response;

import com.hzjm.tts.product.data.request.CreateProductRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PackageDimension implements Serializable {
    String length;
    String width;
    String height;

    /**
     * The unit for the package dimensions.
     * Possible values based on region:
     * - US: CENTIMETER, INCH
     * - Other  regions: CENTIMETER
     */
    String unit;

    private PackageDimension(Builder builder) {
        this.length = builder.length;
        this.width = builder.width;
        this.height = builder.height;
        this.unit = builder.unit;
    }

    public static class Builder {
        private String length;
        private String width;
        private String height;
        private String unit;

        public Builder length(String length) {
            this.length = length;
            return this;
        }

        public Builder width(String width) {
            this.width = width;
            return this;
        }

        public Builder height(String height) {
            this.height = height;
            return this;
        }

        public Builder unit(String unit) {
            this.unit = unit;
            return this;
        }

        public PackageDimension build() {
            if (length == null || width == null || height == null || unit == null) {
                throw new IllegalArgumentException("All dimensions and unit must be provided");
            }
            return new PackageDimension(this);
        }
    }

    public static PackageDimension DEFAULT_SNEAKER_DIMENSION() {
        return new PackageDimension.Builder()
                .length("6")
                .width("10")
                .height("14")
                .unit("INCH")
                .build();
    }

}
