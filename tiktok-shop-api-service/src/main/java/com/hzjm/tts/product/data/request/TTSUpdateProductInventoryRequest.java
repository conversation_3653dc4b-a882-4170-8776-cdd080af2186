package com.hzjm.tts.product.data.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class TTSUpdateProductInventoryRequest implements Serializable {

    private List<Sku> skus;

    @Data
    public static class Sku implements Serializable {
        private String id;
        private List<Inventory> inventory;

        @Data
        public static class Inventory implements Serializable {
            private int quantity;
            private String warehouse_id;
        }
    }
}
