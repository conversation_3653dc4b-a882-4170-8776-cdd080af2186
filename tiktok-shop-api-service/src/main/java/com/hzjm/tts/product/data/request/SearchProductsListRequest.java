package com.hzjm.tts.product.data.request;

import com.hzjm.tts.product.data.TTSProductStatus;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SearchProductsListRequest implements Serializable {
    TTSProductStatus status;
    List<String> seller_skus;
    int create_time_ge;
    int create_time_le;
    int update_time_ge;
    int update_time_le;
    String category_version;
    List<String> listing_quality_tiers;
    List<String> listing_platforms;
    List<String> audit_status;
}
