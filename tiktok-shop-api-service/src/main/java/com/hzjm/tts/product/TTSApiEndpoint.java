package com.hzjm.tts.product;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.hzjm.common.infrastructure.HttpMethod;
import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.utils.ObjectConvertUtils;
import com.hzjm.tts.common.TTSBaseResponse;
import com.hzjm.tts.order.data.request.*;
import com.hzjm.tts.order.data.response.*;
import com.hzjm.tts.product.data.request.*;
import com.hzjm.tts.product.data.response.*;

import java.util.HashMap;
import java.util.Map;

public class TTSApiEndpoint {

    private static final String HOST = "https://open-api.tiktokglobalshop.com";

    private static Map<String, String> defaultHeader() {
        HashMap<String, String> defaultHeaders = new HashMap<String, String>();
        defaultHeaders.put("Charset", "UTF-8");
        defaultHeaders.put("Content-Type", "application/json");
        return defaultHeaders;
    }

    public static HttpRequest<TTSBaseResponse<TTSCheckPrerequisiteResult>> getCheckPrerequisite(String shopCipher) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);
        return new HttpRequest<>(
                HOST,
                "/product/202312/prerequisites",
                HttpMethod.GET,
                defaultHeader(),
                queryMap,
                null,
                new TypeReference<TTSBaseResponse<TTSCheckPrerequisiteResult>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSCategoriesResponse>> getCategories(GetCategoryRequest request) {
        return new HttpRequest<>(
                HOST,
                "/product/202309/categories",
                HttpMethod.GET,
                defaultHeader(),
                ObjectConvertUtils.convertMap(ObjectConvertUtils.objectToMap(request)),
                null,
                new TypeReference<TTSBaseResponse<TTSCategoriesResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSCategoriesResponse>> getRecommendCategories(RecommendCategoryRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/categories/recommend",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<TTSCategoriesResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSBrandPaginationResponse>> getBrands(GetBrandsRequest query) {
        return new HttpRequest<>(
                HOST,
                "/product/202309/brands",
                HttpMethod.GET,
                defaultHeader(),
                ObjectConvertUtils.convertMap(ObjectConvertUtils.objectToMap(query)),
                null,
                new TypeReference<TTSBaseResponse<TTSBrandPaginationResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<UploadImageResponse>> uploadImage(UploadImageRequest request) {
        String bodyJson = JSON.toJSONString(request);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "multipart/form-data");

        return new HttpRequest<>(
                HOST,
                "/product/202309/images/upload",
                HttpMethod.POST,
                headers,
                null,
                bodyJson,
                new TypeReference<TTSBaseResponse<UploadImageResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSWareHouseResponse>> getWareHouses(String shopCipher) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);
        return new HttpRequest<>(
                HOST,
                "/logistics/202309/warehouses",
                HttpMethod.GET,
                defaultHeader(),
                queryMap,
                null,
                new TypeReference<TTSBaseResponse<TTSWareHouseResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSProduct>> getProductDetail(String productId, String shopCipher) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);
        return new HttpRequest<>(
                HOST,
                "/product/202309/products/" + productId,
                HttpMethod.GET,
                defaultHeader(),
                queryMap,
                null,
                new TypeReference<TTSBaseResponse<TTSProduct>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<CreateProductResponse>> createProduct(CreateProductRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/products",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<CreateProductResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<UpdateProductPriceResponse>> updateProductPrice(UpdateProductPriceRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request.getRequest());
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/products/" + request.getProductId()+ "/prices/update",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<UpdateProductPriceResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<UpdateProductInventoryResponse>> updateProductInventory(UpdateProductInventoryRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request.getRequest());
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/products/" + request.getProductId() + "/inventory/update",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<UpdateProductInventoryResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<String>> getProductDescription(String productTitle) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "text/plain");
        headers.put("Authorization", "Bearer sk-iKFGU+waolPvw/3NKIC6C+LY4Z77ilIU+r89KP5ZLeayrVarolcomHEY");

        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("text", productTitle);

        return new HttpRequest<>(
                "https://producthelper.knetgroup.com",
                "/product_helper",
                HttpMethod.POST,
                headers,
                null,
                JSON.toJSONString(bodyMap),
                new TypeReference<TTSBaseResponse<String>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSOrderResponse>> getOrderList(Map<String, String> query, GetOrderListRequest request) {
        String bodyJson = JSON.toJSONString(request);

        return new HttpRequest<>(
                HOST,
                "/order/202309/orders/search",
                HttpMethod.POST,
                defaultHeader(),
                query,
                bodyJson,
                new TypeReference<TTSBaseResponse<TTSOrderResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<ReturnsResponse>> getReturnsList(Map<String, String> query, SearchReturnsRequest request) {
        String bodyJson = JSON.toJSONString(request);

        return new HttpRequest<>(
                HOST,
                "/return_refund/202309/returns/search",
                HttpMethod.POST,
                defaultHeader(),
                query,
                bodyJson,
                new TypeReference<TTSBaseResponse<ReturnsResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSOrderResponse>> getOrderDetail(Map<String, Object> query) {
        return new HttpRequest<>(
                HOST,
                "/order/202309/orders",
                HttpMethod.GET,
                defaultHeader(),
                ObjectConvertUtils.convertMap(query),
                null,
                new TypeReference<TTSBaseResponse<TTSOrderResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<EligibleShippingResponse>> getEligibleShippingService(String orderId,
                                                                                                    String shopCipher,
                                                                                                    GetEligibleShippingServiceRequest request) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/fulfillment/202309/orders/" + orderId + "/shipping_services/query",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<EligibleShippingResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<CreatePackageResponse>> createPackages(CreatePackageRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/fulfillment/202309/packages",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<CreatePackageResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<GetPackageDocResponse>> getPackageDocument(String packageId, Map<String, String> query) {
        return new HttpRequest<>(
                HOST,
                "/fulfillment/202309/packages/" + packageId + "/shipping_documents",
                HttpMethod.GET,
                defaultHeader(),
                query,
                null,
                new TypeReference<TTSBaseResponse<GetPackageDocResponse>>() {}
        );
    }

    /**
     *
     * @param request
     * @param shopCipher
     * @return
     */
    public static HttpRequest<TTSBaseResponse<TTSProduct>> editProduct(String productId, CreateProductRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/products/" + productId,
                HttpMethod.PUT,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<TTSProduct>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<OperateProductResponse>> activateProduct(OperateProductRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/products/activate",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<OperateProductResponse>>() {}
        );
    }


    public static HttpRequest<TTSBaseResponse<OperateProductResponse>> deactivateProduct(OperateProductRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/product/202309/products/deactivate",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<OperateProductResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<SearchProductResponse>>  getAllProducts(SearchProductsListRequest request, Map<String, String> queryMap) {
        String bodyJson = JSON.toJSONString(request);

        return new HttpRequest<>(
                HOST,
                "/product/202502/products/search",
                HttpMethod.GET,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<SearchProductResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<TTSOrderRevenueResponse>> getOrderSettlement(String orderId, String shopCipher) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/finance/202501/orders/" + orderId + "/statement_transactions",
                HttpMethod.GET,
                defaultHeader(),
                queryMap,
                null,
                new TypeReference<TTSBaseResponse<TTSOrderRevenueResponse>>() {}
        );
    }

    public static HttpRequest<TTSBaseResponse<CancelOrderResponse>> cancelOrder(CancelOrderRequest request, String shopCipher) {
        String bodyJson = JSON.toJSONString(request);
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("shop_cipher", shopCipher);

        return new HttpRequest<>(
                HOST,
                "/return_refund/202309/cancellations",
                HttpMethod.POST,
                defaultHeader(),
                queryMap,
                bodyJson,
                new TypeReference<TTSBaseResponse<CancelOrderResponse>>() {}
        );
    }
}
