package com.hzjm.tts.product.service;

import com.hzjm.tts.product.data.request.*;
import com.hzjm.tts.product.data.response.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface ITTSProductService {

    /**
     * 检查是否满足上架条件
     * @return
     */
    TTSCheckPrerequisiteResult checkListingPrerequisites(String accountEmail);

    /**
     *  搜索 可销售类别
     * @param accountEmail
     * @return
     */
    List<TTSCategory> getCategories(String accountEmail);

    /**
     *  输入商品描述来获取 一个推荐的分类
     * @param request
     * @param accountEmail
     * @return
     */
    List<TTSCategory> getRecommendCategories(RecommendCategoryRequest request, String accountEmail);

    /**
     *  获取商店 授权的品牌
     * @param request
     * @param accountEmail
     * @return
     */
    List<TTSBrand> getBrands(GetBrandsRequest request, String accountEmail);

    /**
     * 获取所有仓库
     * @param accountEmail
     * @return
     */
    List<TTSWareHouseResponse.WareHouse> getWareHouses(String accountEmail);

    /**
     * 上传商品图片
     * @param remoteImageUrl
     * @param request
     * @param accountEmail
     * @return
     */
    UploadImageResponse uploadImageByRemoteResource(String remoteImageUrl, UploadImageRequest request, String accountEmail);


    /**
     * 上传商品图片
     * @param imageFile
     * @param request
     * @param accountEmail
     * @return
     */
    UploadImageResponse uploadImageByMultipartFile(MultipartFile imageFile, UploadImageRequest request, String accountEmail);


    /**
     *  获取商品描述
     * @param productTitle
     * @return
     */
    String getProductDescription(String productTitle);

    /**
     *  创建 商品
     * @param request
     * @param accountEmail
     * @return
     */
    CreateProductResponse createProduct(CreateProductRequest request, String accountEmail);

    /**
     *  更新 商品价格
     * @param request
     * @param accountEmail
     * @return
     */
    Boolean updateProductPrice(UpdateProductPriceRequest request, String accountEmail);


    /**
     *  更新商品库存
     * @param request
     * @param accountEmail
     * @return
     */
    UpdateProductInventoryResponse updateProductInventory(UpdateProductInventoryRequest request, String accountEmail);

    /**
     *
     * @param productId
     * @param accountEmail
     * @return
     */
    TTSProduct getProductDetail(String productId, String accountEmail);

    /**
     *  编辑 TTS 商品信息
     * @param productId
     * @param request
     * @param accountEmail
     * @return
     */
    TTSProduct editProductDetail(String productId, CreateProductRequest request, String accountEmail);


    /**
     *  激活 TTS 商品
     * @param request
     * @param accountEmail
     * @return
     */
    OperateProductResponse activateProduct(OperateProductRequest request, String accountEmail);

    /**
     *  下架 TTS 商品
     * @param request
     * @param accountEmail
     * @return
     */
    OperateProductResponse deactivateProduct(OperateProductRequest request, String accountEmail);


    List<TTSProduct> getAllProducts(SearchProductsListRequest request, String accountEmail);

}
