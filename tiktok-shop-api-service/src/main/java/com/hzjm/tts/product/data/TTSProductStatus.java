package com.hzjm.tts.product.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * - DRAFT
 * - PENDING
 * - FAILED
 * - ACTIVATE
 * - SELLER_DEACTIVATED
 * - PLATFORM_DEACTIVATED
 * - FREEZE
 * - DELETED
 */
@Getter
@AllArgsConstructor
public enum TTSProductStatus {

    DRAFT("DRAFT"),
    PENDING("PENDING"),
    FAILED("FAILED"),
    ACTIVATE("ACTIVATE"),
    SELLER_DEACTIVATED("SELLER_DEACTIVATED"),
    PLATFORM_DEACTIVATED("PLATFORM_DEACTIVATED"),
    FREEZE("FREEZE"),
    DELETED("DELETED");

    @EnumValue
    @JsonValue
    private final String rawValue;
}
