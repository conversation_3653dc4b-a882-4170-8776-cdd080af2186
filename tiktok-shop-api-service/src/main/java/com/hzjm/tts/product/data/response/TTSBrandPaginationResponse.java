package com.hzjm.tts.product.data.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {
 *     "brands": [
 *       {
 *         "authorized_status": "AUTHORIZED",
 *         "brand_status": "AVAILABLE",
 *         "id": "7082427311584347905",
 *         "is_t1_brand": true,
 *         "name": "Teas"
 *       }
 *     ],
 *     "next_page_token": "b2Zmc2V0PTAK",
 *     "total_count": 10000
 *   }
 */
@Data
public class TTSBrandPaginationResponse implements Serializable {
    List<TTSBrand> brands;
    String next_page_token;
    Integer total_count;
}
