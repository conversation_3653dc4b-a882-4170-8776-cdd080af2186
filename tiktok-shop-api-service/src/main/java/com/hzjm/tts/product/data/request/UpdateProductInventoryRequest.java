package com.hzjm.tts.product.data.request;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class UpdateProductInventoryRequest implements Serializable {
    private TTSUpdateProductInventoryRequest request;
    private String productId;

    public static UpdateProductInventoryRequest create(String productId, String variantId, String warehouseId, int quantity) {
        UpdateProductInventoryRequest request = new UpdateProductInventoryRequest();
        TTSUpdateProductInventoryRequest ttsRequest = new TTSUpdateProductInventoryRequest();
        TTSUpdateProductInventoryRequest.Sku sku = new TTSUpdateProductInventoryRequest.Sku();
        sku.setId(variantId);

        TTSUpdateProductInventoryRequest.Sku.Inventory inventory = new TTSUpdateProductInventoryRequest.Sku.Inventory();
        inventory.setQuantity(quantity);
        inventory.setWarehouse_id(warehouseId);

        List<TTSUpdateProductInventoryRequest.Sku.Inventory> inventoryList = new ArrayList<>();
        inventoryList.add(inventory);

        sku.setInventory(inventoryList);

        List<TTSUpdateProductInventoryRequest.Sku> skus = new ArrayList<>();
        skus.add(sku);

        ttsRequest.setSkus(skus);

        request.setRequest(ttsRequest);
        request.setProductId(productId);

        return request;
    }
}
