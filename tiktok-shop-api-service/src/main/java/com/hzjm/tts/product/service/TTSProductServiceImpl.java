package com.hzjm.tts.product.service;

import com.hzjm.common.model.BaseException;
import com.hzjm.tts.auth.TTSAuthShopManager;
import com.hzjm.tts.auth.data.response.AuthShop;
import com.hzjm.tts.common.TTSApiRequestServiceImpl;
import com.hzjm.tts.order.data.TTSOrder;
import com.hzjm.tts.order.data.request.GetOrderListRequest;
import com.hzjm.tts.order.data.response.TTSOrderResponse;
import com.hzjm.tts.product.TTSApiEndpoint;
import com.hzjm.tts.product.data.AuthorizedStatus;
import com.hzjm.tts.product.data.request.*;
import com.hzjm.tts.product.data.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TTSProductServiceImpl implements ITTSProductService {

    @Autowired
    TTSApiRequestServiceImpl apiRequestService;

    @Autowired
    TTSAuthShopManager authShopManager;

    /**
     * 检查是否满足上架条件
     * @return
     */
    public TTSCheckPrerequisiteResult checkListingPrerequisites(String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);

        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Categories error, no available auth shop info to request.");
        }

        return Objects.requireNonNull(apiRequestService.doRequest(TTSApiEndpoint.getCheckPrerequisite(authShop.getCipher()))).getData();
    }

    /**
     *  搜索 分类
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSCategory> getCategories(String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);

        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Categories error, no available auth shop info to request.");
        }

        GetCategoryRequest categoryRequest = new GetCategoryRequest();
        categoryRequest.setShop_cipher(authShop.getCipher());

        TTSCategoriesResponse response = apiRequestService.doRequestWithAccount(TTSApiEndpoint.getCategories(categoryRequest), accountEmail).getData();

        return response.getCategories();
    }

    /**
     * 输入商品描述来获取 一个推荐的分类
     *
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSCategory> getRecommendCategories(RecommendCategoryRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Categories error, no available auth shop info to request.");
        }

        TTSCategoriesResponse response = apiRequestService.doRequestWithAccount(TTSApiEndpoint.getRecommendCategories(request, authShop.getCipher()), accountEmail).getData();
        return response.getCategories();
    }

    /**
     * 获取商店 授权的品牌
     *
     * @param initialRequest
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSBrand> getBrands(GetBrandsRequest initialRequest, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Brands error, no available auth shop info to request.");
        }
        initialRequest.setShop_cipher(authShop.getCipher());
        initialRequest.setPage_size(100); // 设置每页的大小

        List<TTSBrand> brands = new ArrayList<>();
        GetBrandsRequest request = initialRequest; // 当前请求对象
        String nextPageToken = null; // 分页token

        do {
            // 如果有下一页token，则设置到请求中
            if (nextPageToken != null && !nextPageToken.isEmpty()) {
                request.setPage_token(nextPageToken);
            }

            // 发送请求并获取响应
            TTSBrandPaginationResponse response = apiRequestService
                    .doRequestWithAccount(TTSApiEndpoint.getBrands(request), accountEmail)
                    .getData();

            // 添加当前页的品牌到总列表中
            if (response.getBrands() != null) {
                brands.addAll(response.getBrands());
            }

            // 检查是否有任何品牌的持有状态为 UNAUTHORIZED
            boolean containsUnauthorized = response.getBrands().stream()
                    .anyMatch(brand -> brand.getAuthorizedStatus() == AuthorizedStatus.UNAUTHORIZED);

            if (containsUnauthorized) {
                log.warn("Some brands are unauthorized, stopping further requests.");
                break; // 如果有未授权的品牌，停止请求
            }

            // 获取下一页token
            nextPageToken = response.getNext_page_token();

        } while (nextPageToken != null && !nextPageToken.isEmpty());

        return brands.stream().filter(brand -> brand.getAuthorizedStatus() == AuthorizedStatus.AUTHORIZED).collect(Collectors.toList());
    }

    /**
     * 获取所有仓库
     *
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSWareHouseResponse.WareHouse> getWareHouses(String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get WareHouses error, no available auth shop info to request.");
        }
        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.getWareHouses(authShop.getCipher()), accountEmail).getData().getWarehouses();
    }

    /**
     * 上传商品图片
     *
     * @param remoteImageUrl
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public UploadImageResponse uploadImageByRemoteResource(String remoteImageUrl, UploadImageRequest request, String accountEmail) {
        // 1.首先根据 传入的 远程 图片 地址下载图片
        byte[] imageData = new byte[0];
        try {
            imageData = apiRequestService.download(remoteImageUrl, null);
        }catch (Exception e) {
            log.error("Download image error: " + e.getMessage() + ", url: " + remoteImageUrl);
        }

        if (ObjectUtils.isEmpty(imageData)) {
            throw new BaseException("Download image error, image data is empty.");
        }

        log.info("Download image data: " + imageData.length + ", url: " + remoteImageUrl);

        return uploadProductImage(imageData, request, accountEmail, remoteImageUrl);
    }

    /**
     * 上传商品图片
     *
     * @param imageFile
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public UploadImageResponse uploadImageByMultipartFile(MultipartFile imageFile, UploadImageRequest request, String accountEmail) {
        // 1. 检查上传的文件是否为空
        if (imageFile.isEmpty()) {
            throw new BaseException("Uploaded image file is empty.");
        }

        // 2. 将文件数据转为 byte 数组
        byte[] imageData;
        try {
            imageData = imageFile.getBytes();
        } catch (IOException e) {
            log.error("Error reading uploaded image file: " + e.getMessage());
            throw new BaseException("Error reading uploaded image file.");
        }

        return uploadProductImage(imageData, request, accountEmail, imageFile.getName());
    }

    private UploadImageResponse uploadProductImage(byte[] imageData, UploadImageRequest request, String accountEmail, String imageFileName) {
        // 判断 imageFileName 的结尾
        String fixedImageFileName;
        if (imageFileName.endsWith(".png")) {
            fixedImageFileName = "temp_image.png"; // 对于 PNG 文件
        } else if (imageFileName.endsWith(".jpg") || imageFileName.endsWith(".jpeg")) {
            fixedImageFileName = "temp_image.jpg"; // 对于 JPG 文件
        } else {
            throw new BaseException("Unsupported image format: " + imageFileName);
        }
        // 1. 将图片数据写入本地文件
        String savePath = "downloads/tts_resource/" + fixedImageFileName; // 假设 remoteImageUrl 是文件名
        Path path = Paths.get(savePath);

        // 获取目录路径并创建目录
        Path directory = path.getParent();
        if (directory != null) { // 检查目录是否为 null
            try {
                // 创建目录（如果不存在的话）
                Files.createDirectories(directory);
            } catch (IOException e) {
                throw new BaseException("Failed to create directories: " + e.getMessage());
            }
        }

        try {
            // 写入文件数据
            Files.write(path, imageData);
            log.debug("文件已成功写入: " + path.toAbsolutePath());
        } catch (IOException e) {
            throw new BaseException("Write image error: " + e.getMessage() + ", url: " + fixedImageFileName);
        }

        File savedFile = new File(savePath);

        // 3. 上传图片
        UploadImageResponse response = apiRequestService.uploadFileWithAccount(
                TTSApiEndpoint.uploadImage(request), savedFile, accountEmail).getData(); // 确保使用 new File 且传递正确的路径
        log.info("Upload image response: " + response);

        return response;
    }

    /**
     * 获取商品描述
     *
     * @param productTitle
     * @return
     */
    @Override
    public String getProductDescription(String productTitle) {
        return Objects.requireNonNull(apiRequestService.doRequest(TTSApiEndpoint.getProductDescription(productTitle))).getMessage();
    }

    /**
     * 创建商品
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public CreateProductResponse createProduct(CreateProductRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Create TTS Product error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.createProduct(request, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * 更新 商品价格
     *
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public Boolean updateProductPrice(UpdateProductPriceRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Update TTS Product Price error, no available auth shop info to request.");
        }
        String result = apiRequestService.doRequestWithAccount(TTSApiEndpoint.updateProductPrice(request, authShop.getCipher()), accountEmail).getMessage();

        return Objects.equals(result, "Success");
    }

    /**
     * 更新商品库存
     *
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public UpdateProductInventoryResponse updateProductInventory(UpdateProductInventoryRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Update Product Inventory error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.updateProductInventory(request, authShop.getCipher()), accountEmail).getData();
    }


    /**
     * @param productId
     * @param accountEmail
     * @return
     */
    @Override
    public TTSProduct getProductDetail(String productId, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Product error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.getProductDetail(productId, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * 编辑 TTS 商品信息
     *
     * @param productId
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public TTSProduct editProductDetail(String productId, CreateProductRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Product error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.editProduct(productId, request, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * 激活 TTS 商品
     *
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public OperateProductResponse activateProduct(OperateProductRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Activate Product error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.activateProduct(request, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * 下架 TTS 商品
     *
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public OperateProductResponse deactivateProduct(OperateProductRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Activate Product error, no available auth shop info to request.");
        }

        return apiRequestService.doRequestWithAccount(TTSApiEndpoint.deactivateProduct(request, authShop.getCipher()), accountEmail).getData();
    }

    /**
     * @param request
     * @param accountEmail
     * @return
     */
    @Override
    public List<TTSProduct> getAllProducts(SearchProductsListRequest request, String accountEmail) {
        AuthShop authShop = authShopManager.getAuthShopByAccountEmail(accountEmail);
        if (ObjectUtils.isEmpty(authShop) || ObjectUtils.isEmpty(authShop.getCipher())) {
            throw new BaseException("Get Brands error, no available auth shop info to request.");
        }

        Map<String, String> query = new HashMap<>();
        query.put("shop_cipher", authShop.getCipher());
        query.put("page_size", String.valueOf(100));

        SearchProductsListRequest pageRequest = request;

        String nextPageToken = null; // 分页token

        List<TTSProduct> productList = new ArrayList<>();

        do {
            // 如果有下一页token，则设置到请求中
            if (!ObjectUtils.isEmpty(nextPageToken)) {
                query.put("page_token", nextPageToken);
            }

            // 发送请求并获取响应
            SearchProductResponse productResponse = apiRequestService.doRequestWithAccount(TTSApiEndpoint.getAllProducts(pageRequest, query), accountEmail).getData();

            // 添加当前页的品牌到总列表中
            if (!ObjectUtils.isEmpty((productResponse.getProducts()))) {
                productList.addAll(productResponse.getProducts());
            }

            // 获取下一页token
            nextPageToken = productResponse.getNext_page_token();

        } while (nextPageToken != null && !nextPageToken.isEmpty());


        return productList;
    }

}
