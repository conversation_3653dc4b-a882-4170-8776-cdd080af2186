package com.hzjm.tts.product.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * The usage scenario of the image.
 * Possible values:
 * - MAIN_IMAGE: An image displayed in the product image gallery.
 * - ATTRIBUTE_IMAGE: An image that represents a product variant (e.g. color).
 * - DESCRIPTION_IMAGE: An image used within the product description.
 * - CERTIFICATION_IMAGE: An image to provide supporting information to meet TikTok Shop requirements for listing restricted products (e.g., images of certifications, product packaging, labeling).
 * - SIZE_CHART_IMAGE: An image that displays the product's measurement details.
 */

@Getter
@AllArgsConstructor
public enum TTSUseCase {
    MAIN_IMAGE("MAIN_IMAGE"),
    ATTRIBUTE_IMAGE("ATTRIBUTE_IMAGE"),
    DESCRIPTION_IMAGE("DESCRIPTION_IMAGE"),
    CERTIFICATION_IMAGE("CERTIFICATION_IMAGE"),
    SIZE_CHART_IMAGE("SIZE_CHART_IMAGE");

    @EnumValue
    @JsonValue
    private final String rawValue;
}
