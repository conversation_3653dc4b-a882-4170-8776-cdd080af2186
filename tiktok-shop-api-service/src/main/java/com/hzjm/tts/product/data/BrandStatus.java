package com.hzjm.tts.product.data;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BrandStatus {
    AVAILABLE("AVAILABLE"),
    UNAVAILABLE("UNAVAILABLE");

    @EnumValue
    @JsonValue
    private final String rawValue;

    public static BrandStatus createFrom(String rawValue) {
        switch (rawValue) {
            case "AVAILABLE": return AVAILABLE;
            case "UNAVAILABLE": return UNAVAIL<PERSON>LE;
            default: throw new IllegalArgumentException("Unsupported brand status string value, value: " + rawValue);
        }
    }

}
