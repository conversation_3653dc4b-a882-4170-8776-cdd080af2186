package com.hzjm.tts.product.data.request;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class UpdateProductPriceRequest implements Serializable {
    String productId;
    TTSUpdateProductPriceRequest request;

    public static UpdateProductPriceRequest create(String productId, String variantId, String currency, String price) {
        UpdateProductPriceRequest request = new UpdateProductPriceRequest();

        TTSUpdateProductPriceRequest.Sku sku = new TTSUpdateProductPriceRequest.Sku();

        TTSUpdateProductPriceRequest.Sku.Price skuPrice = new TTSUpdateProductPriceRequest.Sku.Price();
        skuPrice.setCurrency(currency);
        skuPrice.setAmount(price);

        sku.setId(variantId);
        sku.setPrice(skuPrice);

        List<TTSUpdateProductPriceRequest.Sku> skus = new ArrayList<>();
        skus.add(sku);

        TTSUpdateProductPriceRequest priceRequest = new TTSUpdateProductPriceRequest();
        priceRequest.setSkus(skus);

        request.setProductId(productId);
        request.setRequest(priceRequest);

        return request;
    }

}
