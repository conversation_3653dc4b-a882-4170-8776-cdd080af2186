package com.hzjm.tts.product.data.response;

import com.alibaba.fastjson.annotation.JSONField;
import com.hzjm.tts.product.data.AuthorizedStatus;
import com.hzjm.tts.product.data.BrandStatus;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * {
 *         "authorized_status": "AUTHORIZED",
 *         "brand_status": "AVAILABLE",
 *         "id": "7082427311584347905",
 *         "is_t1_brand": true,
 *         "name": "Teas"
 *       }
 */

@Data
@AllArgsConstructor
public class TTSBrand implements Serializable {

    String id;

    private AuthorizedStatus authorizedStatus;

    @JSONField(name = "brand_status")
    private BrandStatus brandStatus;

    @JSONField(name = "is_t1_brand")
    private boolean isT1Brand;

    private String name;
}
