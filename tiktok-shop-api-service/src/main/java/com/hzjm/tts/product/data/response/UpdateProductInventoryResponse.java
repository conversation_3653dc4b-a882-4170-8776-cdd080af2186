package com.hzjm.tts.product.data.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UpdateProductInventoryResponse implements Serializable {

    private List<Error> errors;

    @Data
    public static class Error implements Serializable {
        private int code;
        private String message;

        private Detail detail;

        @Data
        public static class Detail implements Serializable {
            private String sku_id;
            private List<ExtraError> extra_errors;

            @Data
            public static class ExtraError implements Serializable {
                private int code;
                private String message;
                private String warehouse_id;
            }
        }
    }
}
