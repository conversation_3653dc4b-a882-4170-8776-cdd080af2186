package com.hzjm.tts.product.data.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * Example
 * {
 *   "code": 0,
 *   "data": {
 *     "check_results": [
 *       {
 *         "check_item": "RETURN_WAREHOUSE",
 *         "fail_reasons": [
 *           "Couldn't publish this product as you haven't set the return warehouse for your shop. Add the return warehouse information on TikTok Shop Seller Center first and try again."
 *         ],
 *         "is_failed": true
 *       }
 *     ]
 *   },
 *   "message": "Success",
 *   "request_id": "202203070749000101890810281E8C70B7"
 * }
 */
@Data
public class TTSCheckPrerequisiteResult implements Serializable {

    @JSONField(name = "check_results")
    List<CheckItem> checkItems;

    public static class CheckItem {
        @JSONField(name = "check_item")
        String checkItem;

        @JSONField(name = "fail_reasons")
        List<String> failReasons;

        @JSONField(name = "is_failed")
        boolean isFailed;
    }

}
