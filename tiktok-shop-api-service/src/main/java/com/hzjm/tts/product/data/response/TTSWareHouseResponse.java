package com.hzjm.tts.product.data.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {
 *   "code": 0,
 *   "data": {
 *     "warehouses": [
 *       {
 *         "address": {
 *           "city": "GuanZhou",
 *           "contact_person": "<PERSON>",
 *           "distict": "HuaDu",
 *           "full_address": "South Sea 11 floor",
 *           "geolocation": {
 *             "latitude": "45.41634",
 *             "longitude": "-75.6868"
 *           },
 *           "phone_number": "188****2234",
 *           "postal_code": "510000",
 *           "region": "China",
 *           "region_code": "CN",
 *           "state": "GuangDong",
 *           "town": "town"
 *         },
 *         "effect_status": "ENABLED",
 *         "id": "7000714532876273410",
 *         "is_default": true,
 *         "name": "Guangzhou",
 *         "sub_type": "DOMESTIC_WAREHOUSE",
 *         "type": "SALES_WAREHOUSE"
 *       }
 *     ]
 *   },
 *   "message": "Success",
 *   "request_id": "202203070749000101890810281E8C70B7"
 * }
 */
@Data
public class TTSWareHouseResponse implements Serializable {

    List<WareHouse> warehouses;

    @Data
    public static class WareHouse {
        Address address;
        String effect_status;
        String id;
        boolean is_default;
        String name;
        String sub_type;
        String type;

        @Data
        public static class Address {
            String city;
            String contact_person;
            String distict;
            String full_address;
            Geolocation geolocation;
            String phone_number;
            String postal_code;
            String region;
            String region_code;
            String state;
            String town;

            public static class Geolocation {
                String latitude;
                String longitude;
            }
        }
    }
}
