package com.hzjm.tts.product.data.response;

import com.hzjm.tts.product.data.TTSProductStatus;
import lombok.Data;

import java.io.Serializable;

/**
 * {
 *   "code": 0,
 *   "data": {
 *     "audit": {
 *       "status": "AUDITING"
 *     },
 *     "audit_failed_reasons": [
 *       {
 *         "listing_platform": "TIKTOK_SHOP",
 *         "position": "product",
 *         "reasons": [
 *           "violate listing rules"
 *         ],
 *         "suggestions": [
 *           "The product violates TikTok Shopping listing rules, please check and resubmit."
 *         ]
 *       }
 *     ],
 *     "brand": {
 *       "id": "7082427311584347905",
 *       "name": "brand xxx aaa"
 *     },
 *     "category_chains": [
 *       {
 *         "id": "853000",
 *         "is_leaf": true,
 *         "local_name": "Botol & Stoples Penyimpanan",
 *         "parent_id": "851848"
 *       }
 *     ],
 *     "certifications": [
 *       {
 *         "files": [
 *           {
 *             "format": "PDF",
 *             "id": "v09ea0g40000cj91373c77u3mid3g1s0",
 *             "name": "CERT_X2.PDF",
 *             "urls": [
 *               "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *             ]
 *           }
 *         ],
 *         "id": "602362",
 *         "images": [
 *           {
 *             "height": 600,
 *             "thumb_urls": [
 *               "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *             ],
 *             "uri": "tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4",
 *             "urls": [
 *               "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *             ],
 *             "width": 600
 *           }
 *         ],
 *         "title": "SNI Certificate"
 *       }
 *     ],
 *     "create_time": 1234567890,
 *     "delivery_options": [
 *       {
 *         "id": "1729592969712203232",
 *         "is_available": true,
 *         "name": "\"\""
 *       }
 *     ],
 *     "description": "<p>Please compare above detailed size with your measurement before purchase.</p>\n<ul> \n  <li>M-Size</li>\n  <li>XL-Size</li>\n</ul> \n<img src=\"https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/181595ea7d26489284b5667488d708c1~tplv-o3syd03w52-origin-jpeg.jpeg?from=1432613627\" />\n",
 *     "external_product_id": "172959296971220002",
 *     "id": "1729592969712207008",
 *     "integrated_platform_statuses": [
 *       {
 *         "platform": "TOKOPEDIA",
 *         "status": "PLATFORM_DEACTIVATED"
 *       }
 *     ],
 *     "is_cod_allowed": true,
 *     "is_not_for_sale": true,
 *     "is_pre_owned": false,
 *     "listing_quality_tier": "POOR",
 *     "main_images": [
 *       {
 *         "height": 600,
 *         "thumb_urls": [
 *           "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *         ],
 *         "uri": "tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4",
 *         "urls": [
 *           "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *         ],
 *         "width": 600
 *       }
 *     ],
 *     "manufacturer_ids": [
 *       "172959296971220002"
 *     ],
 *     "minimum_order_quantity": 1,
 *     "package_dimensions": {
 *       "height": "10",
 *       "length": "10",
 *       "unit": "CENTIMETER",
 *       "width": "10"
 *     },
 *     "package_weight": {
 *       "unit": "KILOGRAM",
 *       "value": "1.32"
 *     },
 *     "product_attributes": [
 *       {
 *         "id": "100392",
 *         "name": "Occasion",
 *         "values": [
 *           {
 *             "id": "1001533",
 *             "name": "Birthday"
 *           }
 *         ]
 *       }
 *     ],
 *     "product_types": [
 *       "COMBINED_PRODUCT"
 *     ],
 *     "recommended_categories": [
 *       {
 *         "id": "850003",
 *         "local_name": "\t\nBotol & Stoples Penyimpanan"
 *       }
 *     ],
 *     "responsible_person_ids": [
 *       "172959296971220003"
 *     ],
 *     "shipping_insurance_requirement": "OPTIONAL",
 *     "size_chart": {
 *       "image": {
 *         "height": 600,
 *         "thumb_urls": [
 *           "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *         ],
 *         "uri": "tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4",
 *         "urls": [
 *           "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *         ],
 *         "width": 600
 *       },
 *       "template": {
 *         "id": "7267563252536723205"
 *       }
 *     },
 *     "skus": [
 *       {
 *         "combined_skus": [
 *           {
 *             "product_id": "1729582718312380123",
 *             "sku_count": 1,
 *             "sku_id": "1729582718312380123"
 *           }
 *         ],
 *         "external_sku_id": "1729592969712207234",
 *         "external_urls": [
 *           "https://example.com/path1",
 *           "https://example.com/path2"
 *         ],
 *         "extra_identifier_codes": [
 *           "00012345678905",
 *           "9780596520687"
 *         ],
 *         "global_listing_policy": {
 *           "inventory_type": "SHARED",
 *           "price_sync": true,
 *           "replicate_source": {
 *             "product_id": "1729592969712203232",
 *             "shop_id": "7295929697122032321",
 *             "sku_id": "1729592969712203232"
 *           }
 *         },
 *         "id": "10001",
 *         "identifier_code": {
 *           "code": "10000000000010",
 *           "type": "GTIN"
 *         },
 *         "inventory": [
 *           {
 *             "quantity": 999,
 *             "warehouse_id": "6966568648651605766"
 *           }
 *         ],
 *         "pre_sale": {
 *           "fulfillment_type": {
 *             "handling_duration_days": 3
 *           },
 *           "type": "PRE_ORDER"
 *         },
 *         "price": {
 *           "currency": "USD",
 *           "sale_price": "117.5",
 *           "tax_exclusive_price": "110",
 *           "unit_price": "1"
 *         },
 *         "sales_attributes": [
 *           {
 *             "id": "100000",
 *             "name": "Color",
 *             "sku_img": {
 *               "height": 100,
 *               "thumb_urls": [
 *                 "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *               ],
 *               "uri": "tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4",
 *               "urls": [
 *                 "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********"
 *               ],
 *               "width": 100
 *             },
 *             "value_id": "100000",
 *             "value_name": "Red"
 *           }
 *         ],
 *         "seller_sku": "sku name",
 *         "sku_unit_count": "1.00"
 *       }
 *     ],
 *     "status": "SELLER_DEACTIVATED",
 *     "title": "Short Boat Invisible Socks",
 *     "update_time": 1234567899,
 *     "video": {
 *       "cover_url": "https://p16-oec-va.ibyteimg.com/tos-maliva-i-o3syd03w52-us/6c8519a3663a4d728c4e3c131dc914b4~tplv-o3syd03w52-resize-jpeg:300:300.jpeg?from=*********",
 *       "format": "MP4",
 *       "height": 480,
 *       "id": "v09ea0g40000cj91373c77u3mid3g1s0",
 *       "size": 1000,
 *       "url": "https://v16m-default.akamaized.net/bbae7099581b26cd340beaa7821b2d8c/64de6020/video/tos/alisg/tos-alisg-v-f466fc-sg/oMne9QuzIBN3fIDN7bFCCMbBKuGigg12ghDC8k/?a=0&ch=0&cr=0&dr=0&er=0&lr=default&cd=0%7C0%7C0%7C0&br=2212&bt=1106&cs=0&ds=3&ft=dl9~j-Inz7TKnfsfiyq8Z&mime_type=video_mp4&qs=13&rc=anR4Ojk6ZmYzbTMzODRmNEBpanR4Ojk6ZmYzbTMzODRmNEBsYWFwcjRva2NgLS1kLy1zYSNsYWFwcjRva2NgLS1kLy1zcw%3D%3D&l=202308171159498F7B108584E58B010932&btag=e00048000",
 *       "width": 1280
 *     }
 *   },
 *   "message": "Success",
 *   "request_id": "202203070749000101890810281E8C70B7"
 * }
 */
import lombok.Data;
import java.util.List;

@Data
public class TTSProduct implements Serializable {
    private Audit audit;
    private List<AuditFailedReason> audit_failed_reasons;
    private Brand brand;
    private List<CategoryChain> category_chains;
    private List<Certification> certifications;
    private long create_time;
    private List<DeliveryOption> delivery_options;
    private String description;
    private String external_product_id;
    private String id;
    private List<IntegratedPlatformStatus> integrated_platform_statuses;
    private boolean is_cod_allowed;
    private boolean is_not_for_sale;
    private boolean is_pre_owned;
    private String listing_quality_tier;
    private List<MainImage> main_images;
    private List<String> manufacturer_ids;
    private int minimum_order_quantity;
    private PackageDimension package_dimensions;
    private PackageWeight package_weight;
    private List<ProductAttribute> product_attributes;
    private List<String> product_types;
    private List<RecommendedCategory> recommended_categories;
    private List<String> responsible_person_ids;
    private String shipping_insurance_requirement;
    private SizeChart size_chart;
    private List<Sku> skus;
    private TTSProductStatus status;
    private String title;
    private long update_time;
    private Video video;

    @Data
    public static class Audit {
        private String status;
    }

    @Data
    public static class AuditFailedReason {
        private String listing_platform;
        private String position;
        private List<String> reasons;
        private List<String> suggestions;
    }

    @Data
    public static class Brand {
        private String id;
        private String name;
    }

    @Data
    public static class CategoryChain {
        private String id;
        private boolean is_leaf;
        private String local_name;
        private String parent_id;
    }

    @Data
    public static class Certification {
        private List<CertificationFile> files;
        private String id;
        private List<CertificationImage> images;
        private String title;

        @Data
        public static class CertificationFile {
            private String format;
            private String id;
            private String name;
            private List<String> urls;
        }

        @Data
        public static class CertificationImage {
            private int height;
            private List<String> thumb_urls;
            private String uri;
            private List<String> urls;
            private int width;
        }
    }

    @Data
    public static class DeliveryOption {
        private String id;
        private boolean is_available;
        private String name;
    }

    @Data
    public static class IntegratedPlatformStatus {
        private String platform;
        private String status;
    }

    @Data
    public static class MainImage {
        private int height;
        private List<String> thumb_urls;
        private String uri;
        private List<String> urls;
        private int width;
    }

    @Data
    public static class ProductAttribute {
        private String id;
        private String name;
        private List<ProductAttributeValue> values;

        @Data
        public static class ProductAttributeValue {
            private String id;
            private String name;
        }
    }

    @Data
    public static class RecommendedCategory {
        private String id;
        private String local_name;
    }

    @Data
    public static class SizeChart {
        private SizeChartImage image;
        private SizeChartTemplate template;

        @Data
        public static class SizeChartImage {
            private int height;
            private List<String> thumb_urls;
            private String uri;
            private List<String> urls;
            private int width;
        }

        @Data
        public static class SizeChartTemplate {
            private String id;
        }
    }

    @Data
    public static class Sku {
        private List<CombinedSku> combined_skus;
        private String external_sku_id;
        private List<String> external_urls;
        private List<String> extra_identifier_codes;
        private GlobalListingPolicy global_listing_policy;
        private String id;
        private IdentifierCode identifier_code;
        private List<Inventory> inventory;
        private PreSale pre_sale;
        private Price price;
        private List<SalesAttribute> sales_attributes;
        private String seller_sku;
        private String sku_unit_count;

        @Data
        public static class CombinedSku {
            private String product_id;
            private int sku_count;
            private String sku_id;
        }

        @Data
        public static class GlobalListingPolicy {
            private String inventory_type;
            private boolean price_sync;
            private ReplicateSource replicate_source;

            @Data
            public static class ReplicateSource {
                private String product_id;
                private String shop_id;
                private String sku_id;
            }
        }

        @Data
        public static class IdentifierCode {
            private String code;
            private String type;
        }

        @Data
        public static class Inventory {
            private int quantity;
            private String warehouse_id;
        }

        @Data
        public static class PreSale {
            private FulfillmentType fulfillment_type;
            private String type;

            @Data
            public static class FulfillmentType {
                private int handling_duration_days;
            }
        }

        @Data
        public static class Price {
            private String currency;
            private String sale_price;
            private String tax_exclusive_price;
            private String unit_price;
        }

        @Data
        public static class SalesAttribute {
            private String id;
            private String name;
            private SkuImg sku_img;
            private String value_id;
            private String value_name;

            @Data
            public static class SkuImg {
                private int height;
                private List<String> thumb_urls;
                private String uri;
                private List<String> urls;
                private int width;
            }
        }
    }

    @Data
    public static class Video {
        private String cover_url;
        private String format;
        private int height;
        private String id;
        private long size;
        private String url;
        private int width;
    }
}

