package com.hzjm.tts.product.data.response;

import com.hzjm.tts.product.data.request.CreateProductRequest;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
public class PackageWeight implements Serializable {
    /**
     * The package weight, which must be a positive number with up to 3 decimal places
     */
    String value;

    /**
     * The unit for the package weight.
     * Possible values based on region:
     * - US: KILOGRAM, POUND
     * - Other regions: KILOGRAM
     */
    String unit;

    private PackageWeight(Builder builder) {
        this.value = builder.value;
        this.unit = builder.unit;
    }

    public static class Builder {
        private String value;
        private String unit;

        public Builder value(String value) {
            this.value = value;
            return this;
        }

        public Builder unit(String unit) {
            this.unit = unit;
            return this;
        }

        public PackageWeight build() {
            if (value == null || unit == null) {
                throw new IllegalArgumentException("Weight value and unit must be provided");
            }
            return new PackageWeight(this);
        }
    }

    public static PackageWeight DEFAULT_SNEAKER_WEIGHT() {
        return new PackageWeight.Builder()
                .value("3")
                .unit("POUND")
                .build();
    }
}
