package com.hzjm.tts.product.data.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * {
 *         "code": 12052048,
 *         "detail": {
 *           "product_id": "1729382588639839583"
 *         },
 *         "message": "You can't edit other sellers' products"
 *       }
 */
@Data
public class OperateProductResponse implements  Serializable {

    List<ActivateProductError> errors;

    @Data
    public static class ActivateProductError implements Serializable {
        long code;
        Detail detail;
        String message;

        @Data
        public static class Detail {
            String product_id;
        }

    }
}
