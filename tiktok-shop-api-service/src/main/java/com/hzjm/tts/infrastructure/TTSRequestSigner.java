package com.hzjm.tts.infrastructure;
import com.hzjm.common.infrastructure.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import okhttp3.Request;
import okhttp3.RequestBody;
import okio.Buffer;
import org.apache.poi.ss.formula.functions.T;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 *  tts 请求 签名工具
 */
@Slf4j
public class TTSRequestSigner {

    /**
     *  TTS params 签名

     * @param secret
     * @return
     */
    public static String generateSignature(String urlString,
                                           Map<String, String> queryParams,
                                           String body,
                                           boolean isJsonType,
                                           String secret) {

        // 提取查询参数名列表
        List<String> parameterNameList = new ArrayList<>(queryParams.keySet());

        // 移除不需要的参数
        parameterNameList.removeIf(param -> "sign".equals(param) || "access_token".equals(param));

        // 按字母顺序排序参数名
        Collections.sort(parameterNameList);

        // 构建参数字符串
        StringBuilder parameterStr = new StringBuilder(urlString);
        for (String parameterName : parameterNameList) {
            // 按 {key}{value} 的格式连接所有参数
            parameterStr.append(parameterName).append(queryParams.get(parameterName));
        }

        // 检查 Content-Type，并处理请求体
        if (isJsonType) {
            if (body != null) {
                parameterStr.append(body);
            }
        }

        // 将生成的字符串包装在 App secret 中
        String signatureParams = secret + parameterStr + secret;

        log.info("signatureParams: {}", signatureParams);

        // 使用 HMAC-SHA256 编码包装后的字符串
        return HmacSha256Utils.generateSHA256(signatureParams, secret);
    }


}
