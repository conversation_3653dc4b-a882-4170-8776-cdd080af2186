package com.hzjm.tts.infrastructure;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 *  TTS HMAC SHA256 Utils
 */
public class HmacSha256Utils {

    public static String generateSHA256(String signatureParams, String secret) {
        try {
            // Get an HmacSHA256 Mac instance and initialize with the secret key
            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKeySpec);

            // Update with input data
            sha256HMAC.update(signatureParams.getBytes(StandardCharsets.UTF_8));

            // Compute the HMAC and get the result
            byte[] hashBytes = sha256HMAC.doFinal();

            // Convert to hex string
            StringBuilder sb = new StringBuilder();
            for (byte hashByte : hashBytes) {
                sb.append(String.format("%02x", hashByte & 0xff));
            }

            return sb.toString();
        } catch (Exception e) {
            throw new RuntimeException("failed to generate signature result", e);
        }
    }

}
