package com.hzjm.service.touch;

import com.alibaba.fastjson.JSONObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class TouchUtilsTest {
    @InjectMocks
    private TouchUtils touchUtils;

    @Test
    void getSkuDataByGoat() {
        JSONObject skuDataByGoat = touchUtils.getSkuDataByGoat("8224C1102S 16");
        assertNotNull(skuDataByGoat);
        assertTrue(skuDataByGoat.containsKey("productTemplates"));
        JSONObject item = skuDataByGoat.getJSONArray("productTemplates").getJSONObject(0);
        String productCategory = item.getString("productCategory");
        assertEquals("shoes", productCategory);
    }
}