package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.service.mapper.SysProdSearchMapper;
import com.hzjm.service.model.DTO.SysProdPageDto;
import com.hzjm.service.model.VO.SysProdListVo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
@ExtendWith(MockitoExtension.class)
class SysProdServiceImplTest {
    @InjectMocks
    private SysProdServiceImpl sysProdService;

    @Mock
    private SysProdSearchMapper sysProdSearchMapper;

    @Test
    void testSearchList() {
        SysProdPageDto dto = new SysProdPageDto();
        dto.setCurrent(1);
        dto.setSize(10);
        dto.setStatusList(Collections.singletonList(6));

        // Mocking the mapper method calls
        when(sysProdSearchMapper.selectSysProdSearchVOList(dto))
                .thenReturn(Collections.emptyList());
        when(sysProdSearchMapper.selectSysProdSearchVOListNum(dto))
                .thenReturn(0L);

        IPage<SysProdListVo> pageResult = sysProdService.searchList(dto);

        // Asserting the page result
        assertEquals(0, pageResult.getCurrent());
        assertEquals(0, pageResult.getTotal());
        assertEquals(Collections.emptyList(), pageResult.getRecords());
    }
}
