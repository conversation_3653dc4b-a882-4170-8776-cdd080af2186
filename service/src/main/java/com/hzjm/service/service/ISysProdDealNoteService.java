package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

import com.hzjm.service.entity.SysProdDeal;
import com.hzjm.service.entity.SysProdDealNote;
import com.hzjm.service.model.DTO.SysProdDealNotePageDto;
import com.hzjm.service.model.VO.SysProdDealNoteListVo;
import com.hzjm.service.model.VO.SysProdDealNoteVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 留言 服务类
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
public interface ISysProdDealNoteService extends IService<SysProdDealNote> {

    SysProdDealNote getByIdWithoutLogic(Integer id);

    SysProdDealNoteVo getDetail(Integer id);

    Boolean saveSysProdDealNote(SysProdDealNote dto);

    Boolean insertList(List<SysProdDealNote> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysProdDealNoteListVo> searchList(SysProdDealNotePageDto dto);

    List<SysProdDealNote> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdDealNote> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
