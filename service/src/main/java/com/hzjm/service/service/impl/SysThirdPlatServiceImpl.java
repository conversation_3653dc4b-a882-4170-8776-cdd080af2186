package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.AwsS3Utils;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ImageRotationUtils;
import com.hzjm.service.entity.ShopUserPlat;
import com.hzjm.service.entity.SysProdDeal;
import com.hzjm.service.entity.SysThirdPlat;
import com.hzjm.service.mapper.SysThirdPlatMapper;
import com.hzjm.service.model.DTO.SysThirdPlatPageDto;
import com.hzjm.service.model.VO.SysThirdPlatListVo;
import com.hzjm.service.model.VO.SysThirdPlatVo;
import com.hzjm.service.service.IShopUserPlatService;
import com.hzjm.service.service.ISysProdDealService;
import com.hzjm.service.service.ISysThirdPlatService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 收费管理 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-16
 */
@Slf4j
@Service
public class SysThirdPlatServiceImpl extends ServiceImpl<SysThirdPlatMapper, SysThirdPlat>
        implements ISysThirdPlatService {

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired

    private IShopUserPlatService iShopUserPlatService;

    @Override
    public SysThirdPlat getByIdWithoutLogic(Integer id) {
        SysThirdPlat data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该收费管理"));
        }

        return data;
    }

    @Override
    public SysThirdPlatVo getDetail(Integer id) {
        SysThirdPlat data = getByIdWithoutLogic(id);

        SysThirdPlatVo vo = new SysThirdPlatVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysThirdPlat(SysThirdPlat dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            if (iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery()
                    .eq(SysProdDeal::getThirdPlatId, dto.getId())
                    .eq(SysProdDeal::getStatus, 1)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("该平台仍存在寄售商品，无法删除"));
            }

            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysThirdPlatListVo> searchList(SysThirdPlatPageDto dto) {

        LambdaQueryWrapper<SysThirdPlat> qw = Wrappers.<SysThirdPlat>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByAsc(SysThirdPlat::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysThirdPlat::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysThirdPlat::getGmtCreate, endTime);
        // .eq(SysThirdPlat::getId, 20006);

        Integer shopId = JwtContentHolder.getShopId();
        if (!ObjectUtils.isEmpty(shopId)) {
            List<Integer> platIdList = BaseUtils.initList();

            List<ShopUserPlat> shopPlatList = iShopUserPlatService
                    .list(Wrappers.<ShopUserPlat>lambdaQuery().eq(ShopUserPlat::getShopId, shopId));
            platIdList.addAll(shopPlatList.stream().map(ShopUserPlat::getPlatId).collect(Collectors.toList()));
            qw.in(SysThirdPlat::getId, platIdList);
        }

        IPage<SysThirdPlat> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysThirdPlatListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysThirdPlatListVo vo = new SysThirdPlatListVo();
                BeanUtils.copyProperties(data, vo);
                if (!ObjectUtils.isEmpty(vo.getName()) && "GOAT IS".equals(vo.getName())) {
                    vo.setName("KNET");
                }
                voList.add(vo);
            });
        }

        IPage<SysThirdPlatListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysThirdPlat> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysThirdPlat> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysThirdPlat> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
