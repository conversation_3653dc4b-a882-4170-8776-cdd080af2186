package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.service.entity.RecapYearly;
import com.hzjm.service.mapper.RecapYearlyMapper;
import com.hzjm.service.service.IRecapYearlySaveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/12/18 14:14
 * @description: 商家年度统计报表-保存增强接口·服务实现类
 */
@Slf4j
@Service
public class RecapYearlySaveServiceImpl extends ServiceImpl<RecapYearlyMapper, RecapYearly> implements IRecapYearlySaveService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    @AcquireTaskLock(name = "saveRecapYearly", timeout = 1)
    public Boolean saveByCustom(RecapYearly recapYearly) {
        log.info("插入 商家年度统计数据");
        if (recapYearly == null) {
            log.error("商家年度统计数据为空");
            return true;
        }
        if (save(recapYearly)) {
            log.info("插入 商家年度统计成功");
            return true;
        }
        log.error("插入 商家年度统计失败");
        return false;
    }
}
