package com.hzjm.service.service.export;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.mapper.ShopUserMapper;
import com.hzjm.service.model.VO.SysUserPlatfromFeeVo;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class ShopUserExportService {


    @Resource
    public ShopUserMapper shopUserMapper;

    /**
     * 导出全部商家各个平台费率
     */
    public String exportUserPlatformRateInfo() {
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        List<String> headers = Arrays.asList(
                "商家姓名", "识别码", "账号名称", "服务费"
                , "StockX运费", "StockX手续费", "StockX提现费"
//                , "GOAT运费", "GOAT手续费", "GOAT提现费"
                , "Surge 运费", "Surge 手续费", "Surge 提现费"
                , "KNET 运费", "KNET 手续费", "KNET 提现费"
//                , "GOAT DEFECT运费", "GOAT DEFECT手续费", "GOAT DEFECT提现费"
                , "GOAT 运费", "GOAT 手续费", "GOAT 提现费"
                , "kc 运费", "kc 手续费", "kc 提现费"
                , "eBay 运费", "eBay 手续费", "eBay 提现费"
                , "POIZON 运费", "POIZON 手续费", "POIZON 提现费"
                , "TTS 运费", "TTS 手续费", "TTS 提现费"
                , "创建时间"
        );
        dataList.add(LanguageConfigService.i18nForMsg(headers));
        //填充数据
        List<SysUserPlatfromFeeVo> entityList = shopUserMapper.selectUserPlatfromFee();

        entityList.forEach(data -> {
            List<String> cowList = new ArrayList<>();
            cowList.add(data.getRealName());
            cowList.add(data.getUid());
            cowList.add(data.getAccount());
            cowList.add(String.format("%.2f", Double.parseDouble(data.getOcFee())));
            cowList.add(String.format("%.2f", Double.parseDouble(data.getStockXShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getStockXServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getStockXDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getSurgeShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getSurgeServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getSurgeDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getGoatIsShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getGoatIsServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getGoatIsDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getGoatStvDefectShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getGoatStvDefectServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getGoatStvDefectDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getKcShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getKcServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getKcDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getEbayShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getEbayServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getEbayDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getPoizonShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getPoizonServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getPoizonDrawRate())));

            cowList.add(String.format("%.2f", Double.parseDouble(data.getTTSShippingFee())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getTTSServiceRate())));
            cowList.add(String.format("%.2f%%", Double.parseDouble(data.getTTSDrawRate())));
            cowList.add(data.getGmtCreate());
            dataList.add(cowList);
        });
        String url = null;
        try {
            url = ExcelReader.generateExcelFile(dataList,
                    LanguageConfigService.i18nForMsg("用户各平台费率") + "-" + DateTimeUtils.getFileSuffix() + ".xlsx");
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }

        return url;
    }

}
