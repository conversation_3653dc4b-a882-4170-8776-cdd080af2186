package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.*;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.stream.Collectors;

import com.hzjm.common.utils.BaseUtils;
import com.hzjm.service.entity.SysPermission;
import com.hzjm.service.entity.SysWareUserPermission;
import com.hzjm.service.service.ISysPermissionService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysWareUserPermissionPageDto;
import com.hzjm.service.model.VO.SysWareUserPermissionListVo;
import com.hzjm.service.model.VO.SysWareUserPermissionVo;
import com.hzjm.service.entity.SysWareUserPermission;
import com.hzjm.service.mapper.SysWareUserPermissionMapper;
import com.hzjm.service.service.ISysWareUserPermissionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 仓库人员权限 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Slf4j
@Service
public class SysWareUserPermissionServiceImpl extends ServiceImpl<SysWareUserPermissionMapper, SysWareUserPermission> implements ISysWareUserPermissionService {

    @Autowired
    private ISysPermissionService iSysPermissionService;

    @Override
    public SysWareUserPermission getByIdWithoutLogic(Integer id) {
        SysWareUserPermission data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该仓库人员权限"));
        }

        return data;
    }

    @Override
    public SysWareUserPermissionVo getDetail(Integer id) {
        SysWareUserPermission data = getByIdWithoutLogic(id);

        SysWareUserPermissionVo vo = new SysWareUserPermissionVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareUserPermission(SysWareUserPermission dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysWareUserPermissionListVo> searchList(SysWareUserPermissionPageDto dto) {

        LambdaQueryWrapper<SysWareUserPermission> qw = Wrappers.<SysWareUserPermission>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareUserPermission::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareUserPermission::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareUserPermission::getGmtCreate, endTime);

        IPage<SysWareUserPermission> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareUserPermissionListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysWareUserPermissionListVo vo = new SysWareUserPermissionListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysWareUserPermissionListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWareUserPermission> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareUserPermission> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean reset(Integer id, Integer wareId, Integer userId, List<Integer> permissionIdList) {
        if (!ObjectUtils.isEmpty(wareId)) {
            remove(Wrappers.<SysWareUserPermission>lambdaQuery()
                    .eq(SysWareUserPermission::getWareId, wareId)
                    .eq(SysWareUserPermission::getUserId, userId));
        }

        if (!ObjectUtils.isEmpty(permissionIdList)) {
            List<SysWareUserPermission> dataList = new ArrayList<>();
            permissionIdList.forEach(permissionId -> {
                SysWareUserPermission userPermission = new SysWareUserPermission();
                userPermission.setWareUserId(id);
                userPermission.setUserId(userId);
                userPermission.setWareId(wareId);
                userPermission.setPermId(permissionId);

                dataList.add(userPermission);
            });
            saveBatch(dataList);
        }

        return true;
    }

    @Override
    public Map<Integer, List<SysPermission>> group(Integer wareId) {
        Map<Integer, List<SysPermission>> voMap = new HashMap<>();
        Map<Integer, Map<Integer, SysPermission>> tmpMap = new HashMap<>();

        List<SysPermission> permissions = iSysPermissionService.list(Wrappers.<SysPermission>lambdaQuery().eq(SysPermission::getType, 2));
        Map<Integer, SysPermission> permMap = permissions.stream().collect(Collectors.toMap(SysPermission::getId, a -> a));

        List<SysWareUserPermission> list = list(Wrappers.<SysWareUserPermission>lambdaQuery().eq(SysWareUserPermission::getWareId, wareId));
        Map<Integer, List<SysWareUserPermission>> groupMap = list.stream().collect(Collectors.groupingBy(SysWareUserPermission::getUserId));

        groupMap.keySet().forEach(userId -> {
            Map<Integer, SysPermission> permTree = new HashMap<>();

            permMap.keySet().forEach(permId -> {
                SysPermission newPerm = (SysPermission) BaseUtils.deepClone(permMap.get(permId));
                newPerm.setIsOwn(false);
                permTree.put(permId, newPerm);
            });

            List<SysWareUserPermission> ownPerms = groupMap.get(userId);
            ownPerms.forEach(item -> {
                SysPermission perm = permTree.get(item.getPermId());
                perm.setIsOwn(true);
            });

            tmpMap.put(userId, permTree);
        });

        tmpMap.keySet().forEach(userId -> {
            List<SysPermission> perms = new ArrayList<>();

            Map<Integer, SysPermission> permTree = tmpMap.get(userId);
            permTree.keySet().forEach(permId -> {
                perms.add(permTree.get(permId));
            });

            voMap.put(userId, perms.stream().sorted(new Comparator<SysPermission>() {
                @Override
                public int compare(SysPermission o1, SysPermission o2) {
                    return o1.getId().compareTo(o2.getId());
                }
            }).collect(Collectors.toList()));
        });

        return voMap;
    }

    @Override
    public List<SysWareUserPermission> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
