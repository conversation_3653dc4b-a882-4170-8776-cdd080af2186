package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysOperatedShopMembers;
import com.hzjm.service.mapper.SysOperatedShopMembersMapper;
import com.hzjm.service.model.DTO.SysOperatedShopMembersPageDto;
import com.hzjm.service.model.VO.SysOperatedShopMembersListVo;
import com.hzjm.service.model.VO.SysOperatedShopMembersVo;
import com.hzjm.service.service.ISysOperatedShopMembersService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 自营团队与商家成员关联表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Slf4j
@Service
public class SysOperatedShopMembersServiceImpl extends ServiceImpl<SysOperatedShopMembersMapper, SysOperatedShopMembers> implements ISysOperatedShopMembersService {

    @Override
    public SysOperatedShopMembers getByIdWithoutLogic(Integer id) {
        SysOperatedShopMembers data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该自营团队与商家成员关联表");
        }

        return data;
    }

    @Override
    public SysOperatedShopMembersVo getDetail(Integer id) {
        SysOperatedShopMembers data = getByIdWithoutLogic(id);

        SysOperatedShopMembersVo vo = new SysOperatedShopMembersVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysOperatedShopMembers(SysOperatedShopMembers dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysOperatedShopMembersListVo> searchList(SysOperatedShopMembersPageDto dto) {

        LambdaQueryWrapper<SysOperatedShopMembers> qw = Wrappers.<SysOperatedShopMembers>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysOperatedShopMembers::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysOperatedShopMembers::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysOperatedShopMembers::getGmtCreate, endTime);

        IPage<SysOperatedShopMembers> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysOperatedShopMembersListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysOperatedShopMembersListVo vo = new SysOperatedShopMembersListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysOperatedShopMembersListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysOperatedShopMembers> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysOperatedShopMembers> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysOperatedShopMembers> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
