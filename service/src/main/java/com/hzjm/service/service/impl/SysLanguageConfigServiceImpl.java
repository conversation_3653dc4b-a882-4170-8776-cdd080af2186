package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysLanguageConfig;
import com.hzjm.service.mapper.SysLanguageConfigMapper;
import com.hzjm.service.model.DTO.SysLanguageConfigPageDto;
import com.hzjm.service.model.VO.SysLanguageConfigListVo;
import com.hzjm.service.model.VO.SysLanguageConfigVo;
import com.hzjm.service.service.ISysLanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 多语言配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
@Slf4j
@Service
public class SysLanguageConfigServiceImpl extends ServiceImpl<SysLanguageConfigMapper, SysLanguageConfig> implements ISysLanguageConfigService {

    @Override
    public SysLanguageConfig getByIdWithoutLogic(Integer id) {
        SysLanguageConfig data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该多语言配置表");
        }

        return data;
    }

    @Override
    public SysLanguageConfigVo getDetail(Integer id) {
        SysLanguageConfig data = getByIdWithoutLogic(id);

        SysLanguageConfigVo vo = new SysLanguageConfigVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysLanguageConfig(SysLanguageConfig dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysLanguageConfigListVo> searchList(SysLanguageConfigPageDto dto) {

        LambdaQueryWrapper<SysLanguageConfig> qw = Wrappers.<SysLanguageConfig>lambdaQuery();
        qw.like(!ObjectUtils.isEmpty(dto.getZhCn()), SysLanguageConfig::getZhCn, dto.getZhCn())
                .like(!ObjectUtils.isEmpty(dto.getEnUs()), SysLanguageConfig::getEnUs, dto.getEnUs())
                .like(!ObjectUtils.isEmpty(dto.getMsg()), SysLanguageConfig::getMsg, dto.getMsg())
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysLanguageConfig::getId, dto.getIdList());

        IPage<SysLanguageConfig> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysLanguageConfigListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysLanguageConfigListVo vo = new SysLanguageConfigListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysLanguageConfigListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysLanguageConfig> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {


            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysLanguageConfig> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysLanguageConfig> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
