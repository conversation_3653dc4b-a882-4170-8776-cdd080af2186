package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysRepairProject;
import com.hzjm.service.model.DTO.SysRepairProjectPageDto;
import com.hzjm.service.model.VO.SysRepairProjectListVo;
import com.hzjm.service.model.VO.SysRepairProjectVo;

import java.util.List;

/**
 * 维修项目表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
public interface ISysRepairProjectService extends IService<SysRepairProject> {

    SysRepairProject getByIdWithoutLogic(Integer id);

    SysRepairProjectVo getDetail(Integer id);

    Boolean saveSysRepairProject(SysRepairProject dto);

    Boolean insertList(List<SysRepairProject> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysRepairProjectListVo> searchList(SysRepairProjectPageDto dto);

    List<SysRepairProject> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysRepairProject> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
