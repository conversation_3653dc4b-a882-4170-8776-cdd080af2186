package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysFile;

import java.util.List;
import java.util.Map;

/**
 * 平台文件 服务类
 *
 * <AUTHOR>
 * @since 2020-10-28
 */
public interface ISysFileService extends IService<SysFile> {

    SysFile getByIdWithoutLogic(Integer id);

    Boolean saveSysFile(SysFile dto);

    Boolean relateFile(List<String> urlList, Integer relationId, Integer relationType);

    Boolean resetFile(List<String> urlList, Integer relationId, Integer relationType);

    List<String> getFileUrl(Integer relationId, Integer relationType);

    Map<Integer, List<String>> getFileMap(List<Integer> relationIdList, Integer relationType);

}
