package com.hzjm.service.service.job.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.service.entity.DownloadFileRecord;
import com.hzjm.service.mapper.DownloadFileRecordMapper;
import com.hzjm.service.model.DTO.FileDownloadDelReqDto;
import com.hzjm.service.model.DTO.FileDownloadReqDto;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.service.job.IDownloadFileRecordService;
import com.hzjm.service.service.job.ISysTaskService;
import com.hzjm.service.utils.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

import static com.hzjm.service.constants.ServiceConstants.DOWNLOAD_FILE_TASK_ID;

/**
 * <AUTHOR>
 * @date 2024/12/26 17:52
 * @description: 文件下载记录服务实现类
 */
@Slf4j
@Service
public class DownloadFileRecordServiceImpl extends ServiceImpl<DownloadFileRecordMapper, DownloadFileRecord> implements IDownloadFileRecordService {

    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ISysTaskService iSysTaskService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @AcquireTaskLock(name = "DownloadFileRecordInsert", timeout = 1, blocking = true)
    public int create(DownloadFileRecord downloadFileRecord) {
        return baseMapper.insert(downloadFileRecord);
    }

    @Override
    @ReadOnly
    public IPage<DownloadFileRecord> queryFileRecords(FileDownloadReqDto reqDto) {
        log.info("读取用户文件下载记录 queryFileRecords reqDto: {}", reqDto);
        Integer uid = reqDto.getUid();
        if (!JwtContentHolder.getUserId().equals(uid)) {
            log.error("读取用户文件下载记录 queryFileRecords uid is not equal to current user");
            uid = JwtContentHolder.getUserId();
        }
        QueryWrapper<DownloadFileRecord> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid)
                .eq(BeanUtil.isNotEmpty(reqDto.getStatus()), "status", reqDto.getStatus())
                .eq("del_flag", 0)
                .eq("source_type", reqDto.getSourceType())
                .orderBy(true, false, "gmt_create");
        IPage<DownloadFileRecord> page = new Page<>(reqDto.getPageNo() - 1, reqDto.getPageSize());
        List<DownloadFileRecord> pages = baseMapper.selectPage(page, queryWrapper).getRecords();
        page.setRecords(pages);
        log.info("读取用户文件下载记录 queryFileRecords success 结果条数 {}", pages.size());
        return page;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @AcquireTaskLock(name = "setPendingAndReturnFileName", timeout = 1, blocking = true)
    public String setPendingAndReturnFileName(Integer taskId) {
        UpdateWrapper<DownloadFileRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id", taskId)
                .set("status", SysTaskStatus.PROCESSING)
                .set("start_time", DateUtil.date());
        if (baseMapper.update(null, updateWrapper) > 0) {
            log.info("DownloadFileRecordServiceImpl setPendingAndReturnFileName update status success, taskId: {}", taskId);
            String fileName = (String) redisUtils.getForValue(DOWNLOAD_FILE_TASK_ID.concat(String.valueOf(taskId)));
            if (StrUtil.isBlank(fileName)) {
                DownloadFileRecord record = baseMapper.selectOne(new QueryWrapper<DownloadFileRecord>().eq("task_id", taskId));
                if (BeanUtil.isNotEmpty(record)) {
                    return record.getFileName();
                }
            }
            return fileName;
        }
        log.error("DownloadFileRecordServiceImpl setPendingAndReturnFileName update status failure, taskId: {}", taskId);
        return null;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @AcquireTaskLock(name = "setDoneByTaskId", timeout = 1, blocking = true)
    public boolean setDoneByTaskId(Integer taskId, SysTaskStatus status) {
        UpdateWrapper<DownloadFileRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("task_id", taskId)
                .set("status", status)
                .set("end_time", DateUtil.date());
        return baseMapper.update(null, updateWrapper) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @AcquireTaskLock(name = "updateDownloadFileRecord", timeout = 1, blocking = true)
    public int updateDownloadFileRecord(DownloadFileRecord downloadFileRecord) {
        UpdateWrapper<DownloadFileRecord> updateWrapper = new UpdateWrapper<>();
        updateWrapper
                .eq("task_id", downloadFileRecord.getTaskId())
                .set("status", downloadFileRecord.getStatus())
                .set("file_url", downloadFileRecord.getFileUrl())
                .set("end_time", DateUtil.date());
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @AcquireTaskLock(name = "deleteDownloadFileRecord", timeout = 1, blocking = true)
    public void deleteDownloadFileRecord(FileDownloadDelReqDto reqDto) {
        if (CollUtil.isEmpty(reqDto.getTaskIds())) {
            return;
        }
        Integer uid = reqDto.getUid();
        if (!JwtContentHolder.getUserId().equals(uid)) {
            uid = JwtContentHolder.getUserId();
            log.error("删除用户文件下载记录  uid is not equal to current user, uid :{} current user{}", reqDto.getUid(), uid);
        }
        UpdateWrapper<DownloadFileRecord> wrapper = new UpdateWrapper<DownloadFileRecord>()
                .eq("uid", uid)
                .eq("source_type", reqDto.getSourceType())
                .in("task_id", reqDto.getTaskIds())
                .set("del_flag", 1);
        baseMapper.update(null, wrapper);
        iSysTaskService.setDoneTasks(reqDto.getTaskIds());
    }
}
