package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysCodePool;
import com.hzjm.service.model.DTO.SysCodePoolPageDto;
import com.hzjm.service.model.VO.SysCodePoolListVo;
import com.hzjm.service.model.VO.SysCodePoolVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 编号池（每天0点清除） 服务类
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
public interface ISysCodePoolService extends IService<SysCodePool> {

    SysCodePool getByIdWithoutLogic(Integer id);

    SysCodePoolVo getDetail(Integer id);

    Boolean saveSysCodePool(SysCodePool dto);

    Boolean insertList(List<SysCodePool> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysCodePoolListVo> searchList(SysCodePoolPageDto dto);

    List<SysCodePool> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysCodePool> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    /**
     *
     * @param type 1-oneId，2-批次编号，3-出库单号
     * @param num 生成数量
     * @return
     */
    List<String> build(Integer type, Integer num);
}
