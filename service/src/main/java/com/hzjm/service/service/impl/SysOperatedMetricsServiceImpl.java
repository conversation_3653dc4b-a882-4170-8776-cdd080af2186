package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysOperatedMetricsMapper;
import com.hzjm.service.model.DTO.SysOperatedMetricsPageDto;
import com.hzjm.service.model.VO.AdminHomeSelfOperatedDataVo;
import com.hzjm.service.model.VO.SysOperatedMetricsGroupByTeamVo;
import com.hzjm.service.model.VO.SysOperatedMetricsListVo;
import com.hzjm.service.model.VO.SysOperatedMetricsVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 自营数据表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Slf4j
@Service
public class SysOperatedMetricsServiceImpl extends ServiceImpl<SysOperatedMetricsMapper, SysOperatedMetrics> implements ISysOperatedMetricsService {

    @Resource
    private DashboardService dashboardService;
    @Resource
    private ISysBillService sysBillService;
    @Resource
    private ISysMoneyService sysMoneyService;
    @Resource
    private IShopUserService shopUserService;

    @Override
    public SysOperatedMetrics getByIdWithoutLogic(Integer id) {
        SysOperatedMetrics data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该自营数据表");
        }

        return data;
    }

    @Override
    public SysOperatedMetricsVo getDetail(Integer id) {
        SysOperatedMetrics data = getByIdWithoutLogic(id);

        SysOperatedMetricsVo vo = new SysOperatedMetricsVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysOperatedMetrics(SysOperatedMetrics dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysOperatedMetricsListVo> searchList(SysOperatedMetricsPageDto dto) {

        LambdaQueryWrapper<SysOperatedMetrics> qw = Wrappers.<SysOperatedMetrics>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysOperatedMetrics::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysOperatedMetrics::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysOperatedMetrics::getGmtCreate, endTime);

        IPage<SysOperatedMetrics> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysOperatedMetricsListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysOperatedMetricsListVo vo = new SysOperatedMetricsListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysOperatedMetricsListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysOperatedMetrics> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysOperatedMetrics> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Resource
    private ISysOperatedTeamsService sysOperatedTeamsService;
    @Resource
    private ISysOperatedShopMembersService sysOperatedShopMembersService;

    /**
     * 查询全部的自营数据
     */
    @Override
    public List<SysOperatedMetricsGroupByTeamVo> queryMetricsList() {
        // 查询团队下的成员
        List<SysOperatedShopMembers> members = sysOperatedShopMembersService.list(Wrappers.<SysOperatedShopMembers>lambdaQuery());
        // 查询所有团队
        List<SysOperatedTeams> teams = sysOperatedTeamsService.list(Wrappers.<SysOperatedTeams>lambdaQuery());
        // 查询所有商家的自营数据
        List<SysOperatedMetrics> metricsList = this.list(Wrappers.<SysOperatedMetrics>lambdaQuery());

        // 相同团队的成员在一起
        Map<Integer, List<SysOperatedShopMembers>> membersGroupByTeam = members.stream().collect(Collectors.groupingBy(SysOperatedShopMembers::getTeamId));
        // 按照团队ID对团队分组
        Map<Integer, List<SysOperatedTeams>> teamsGroupByTeam = teams.stream().collect(Collectors.groupingBy(SysOperatedTeams::getId));

        //组装数据
        List<SysOperatedMetricsGroupByTeamVo> voList = new ArrayList<>();
        membersGroupByTeam.forEach((teamId, memberMapLists) -> {
            if (ObjectUtils.isEmpty(teamId)
                    || ObjectUtils.isEmpty(memberMapLists)
                    || ObjectUtils.isEmpty(metricsList)
                    || ObjectUtils.isEmpty(teamsGroupByTeam)
                    || ObjectUtils.isEmpty(teamsGroupByTeam.get(teamId))
                    || ObjectUtils.isEmpty(teamsGroupByTeam.get(teamId).get(0))
            ) {
                log.info("SysOperatedMetricsServiceImpl.queryMetricsList() membersGroupByTeam is null teamId={} memberMapLists={} metricsList={} teamsGroupByTeam={}",
                        teamId, memberMapLists, metricsList, teamsGroupByTeam);
                return;
            }

            SysOperatedMetricsGroupByTeamVo vo = new SysOperatedMetricsGroupByTeamVo();
            vo.setTeamId(teamId);
            vo.setTeamName(teamsGroupByTeam.get(teamId).get(0).getTeamName());
            vo.setPrepayment(teamsGroupByTeam.get(teamId).get(0).getPrepayment() / 100);
            vo.setReceivables(teamsGroupByTeam.get(teamId).get(0).getReceivables() / 100);
            List<Integer> shopId = memberMapLists.stream().map(SysOperatedShopMembers::getShopId).collect(Collectors.toList());
            // 店员的自营数据
            List<SysOperatedMetrics> shopMetricsList = new ArrayList<>();

            int totalBalance = 0;
            int totalWarehouseCost = 0;
            int totalWarehouseQuantity = 0;
            int totalDefectCost = 0;
            int totalDefectQuantity = 0;
            int totalOver120DaysCost = 0;
            int totalOver120DaysQuantity = 0;

            for (SysOperatedMetrics metrics : metricsList) {
                if (shopId.contains(metrics.getShopId())) {
                    SysOperatedMetrics shopMetrics = new SysOperatedMetrics();
                    BeanUtils.copyProperties(metrics, shopMetrics);

                    // 将分转换为元，并累加到团队总计
                    totalBalance += metrics.getBalance();
                    totalWarehouseCost += metrics.getWarehouseCost();
                    totalWarehouseQuantity += metrics.getWarehouseQuantity();
                    totalDefectCost += metrics.getDefectCost();
                    totalDefectQuantity += metrics.getDefectQuantity();
                    totalOver120DaysCost += metrics.getOver120DaysCost();
                    totalOver120DaysQuantity += metrics.getOver120DaysQuantity();

                    shopMetrics.setBalance(metrics.getBalance() / 100);
                    shopMetrics.setWarehouseCost(metrics.getWarehouseCost() / 100);
                    shopMetrics.setWarehouseQuantity(metrics.getWarehouseQuantity());
                    shopMetrics.setDefectCost(metrics.getDefectCost() / 100);
                    shopMetrics.setDefectQuantity(metrics.getDefectQuantity());
                    shopMetrics.setOver120DaysCost(metrics.getOver120DaysCost() / 100);
                    shopMetrics.setOver120DaysQuantity(metrics.getOver120DaysQuantity());
                    vo.setRefreshTime(shopMetrics.getGmtCreate());
                    shopMetricsList.add(shopMetrics);
                }
            }
            // 团队的总体数据
            vo.setBalance(totalBalance / 100);
            vo.setWarehouseCost(totalWarehouseCost / 100);
            vo.setWarehouseQuantity(totalWarehouseQuantity);
            vo.setDefectCost(totalDefectCost / 100);
            vo.setDefectQuantity(totalDefectQuantity);
            vo.setOver120DaysCost(totalOver120DaysCost / 100);
            vo.setOver120DaysQuantity(totalOver120DaysQuantity);
            vo.setTemaShopMetricsList(shopMetricsList);

            voList.add(vo);
        });

        return voList;
    }

    @Override
    public List<SysOperatedMetrics> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 刷新自营数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean refreshMetrics() {
        // 查询需要更新的商家
        List<Integer> shopIds = sysOperatedShopMembersService.list(Wrappers.<SysOperatedShopMembers>lambdaQuery())
                .stream()
                .map(SysOperatedShopMembers::getShopId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (shopIds.isEmpty()) {
            log.info("SysOperatedMetricsServiceImpl refreshMetrics shopIds is null ");
            return false;
        }
        // 根据ID 查询商家信息
        List<ShopUser> shopUsers = shopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIds));
        // 查询 总在仓库存成本 总在仓库存数量 瑕疵成本 瑕疵数量 >120天 在仓库存成本 >120天 在仓库存数量
        TableDataSearchDto tableDataSearchDto = TableDataSearchDto.builder().shopIdList(shopIds).build();
        List<AdminHomeSelfOperatedDataVo> results = dashboardService.querySelfOperatedData(tableDataSearchDto);
        Map<Integer, AdminHomeSelfOperatedDataVo> resultMap = results.stream().collect(Collectors
                .toMap(AdminHomeSelfOperatedDataVo::getShopId, v -> v, (v1, v2) -> v1)
        );
        // 查询资产和余额
        List<SysMoney> sysMonies = sysMoneyService.list(Wrappers.<SysMoney>lambdaQuery()
                .in(SysMoney::getUserId, shopIds)
                .eq(SysMoney::getType, 5)
                .select(SysMoney::getUserId, SysMoney::getMoney)
        ).stream().distinct().collect(Collectors.toList());
        Map<Integer, Integer> moneyMap = sysMonies.stream().collect(Collectors.toMap(
                SysMoney::getUserId,
                money -> money.getMoney().multiply(BigDecimal.valueOf(100)).intValue() // 转换为美分并转为 Integer
        ));
        // 在途资金  select ifnull(sum(total_fee),0) total_fee from sys_bill where status =1 and relation_type in (6,24) and user_id = 1 and user_type =5,and del_flag = 0
        List<SysBill> sysBills = sysBillService.list(Wrappers.<SysBill>lambdaQuery()
                .eq(SysBill::getStatus, 1)
                .in(SysBill::getRelationType, Arrays.asList(6, 24))
                .in(SysBill::getUserId, shopIds)
                .eq(SysBill::getUserType, 5)
                .select(SysBill::getUserId, SysBill::getTotalFee)
        );
        Map<Integer, Integer> billMap = sysBills.stream()
                .filter(Objects::nonNull)
                .filter(bill -> bill.getTotalFee() != null)
                .collect(Collectors.groupingBy(
                        SysBill::getUserId,
                        Collectors.reducing(
                                0, // 初始值为 0
                                bill -> bill.getTotalFee().multiply(BigDecimal.valueOf(100)).intValue(), // 转换为美分并转为 Integer
                                Integer::sum
                        )
                ));


        // 硬删除旧数据,不使用逻辑删除
        this.hardDelete(new LambdaQueryWrapper<SysOperatedMetrics>());

        // 组装数据
        List<SysOperatedMetrics> dataList = new ArrayList<>();
        for (ShopUser shopUser : shopUsers) {
            AdminHomeSelfOperatedDataVo result = resultMap.get(shopUser.getId()) == null ? new AdminHomeSelfOperatedDataVo() : resultMap.get(shopUser.getId());
            int bill = billMap.get(shopUser.getId()) == null ? 0 : billMap.get(shopUser.getId());
            int balance = moneyMap.get(shopUser.getId()) == null ? 0 : moneyMap.get(shopUser.getId());
            balance = balance + bill;

            SysOperatedMetrics data = new SysOperatedMetrics()
                    .setShopId(shopUser.getId())
                    .setShopUid(shopUser.getUid())
                    .setShopRealname(shopUser.getRealname())
                    .setBalance(balance)
                    .setWarehouseCost(result.getTotalInventoryCost() != null ? result.getTotalInventoryCost() : 0)
                    .setWarehouseQuantity(result.getTotalInventoryNum() != null ? result.getTotalInventoryNum() : 0)
                    .setDefectCost(result.getDefectCost() != null ? result.getDefectCost() : 0)
                    .setDefectQuantity(result.getDefectNum() != null ? result.getDefectNum() : 0)
                    .setOver120DaysCost(result.getGtTotalInventoryCost() != null ? result.getGtTotalInventoryCost() : 0)
                    .setOver120DaysQuantity(result.getGtTotalInventoryNum() != null ? result.getGtTotalInventoryNum() : 0);
            dataList.add(data);
        }
        // 插入新数据
        return this.insertList(dataList);
    }

}
