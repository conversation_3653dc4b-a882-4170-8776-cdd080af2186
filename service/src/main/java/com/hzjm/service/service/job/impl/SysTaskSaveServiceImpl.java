package com.hzjm.service.service.job.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.service.entity.SysTask;
import com.hzjm.service.mapper.SysTaskMapper;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.service.job.ISysTaskSaveService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/12/27 15:49
 * @description:
 */
@Slf4j
@Service
public class SysTaskSaveServiceImpl extends ServiceImpl<SysTaskMapper, SysTask> implements ISysTaskSaveService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    @AcquireTaskLock(name = "SysTaskInsert", timeout = 1, blocking = true)
    public Integer createTask(SysTask sysTask) {
        baseMapper.insert(sysTask);
        return sysTask.getId();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @AcquireTaskLock(name = "updateTaskStatus", timeout = 1, blocking = true)
    public boolean updateTaskStatus(Integer taskId, SysTaskStatus status) {
        UpdateWrapper<SysTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", taskId).set("status", status);
        return baseMapper.update(null, updateWrapper) > 0;
    }
}
