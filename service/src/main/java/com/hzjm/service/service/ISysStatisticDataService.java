package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

import com.hzjm.common.model.TableDataListVo;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.service.entity.SysStatisticData;
import com.hzjm.service.model.DTO.SysStatisticDataPageDto;
import com.hzjm.service.model.VO.SysStatisticDataListVo;
import com.hzjm.service.model.VO.SysStatisticDataVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
public interface ISysStatisticDataService extends IService<SysStatisticData> {

    SysStatisticData getByIdWithoutLogic(Integer id);

    SysStatisticDataVo getDetail(Integer id);

    Boolean saveSysStatisticData(SysStatisticData dto);

    Boolean insertList(List<SysStatisticData> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysStatisticDataListVo> searchList(SysStatisticDataPageDto dto);

    List<SysStatisticData> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysStatisticData> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<TableDataListVo> getTableList(TableDataSearchDto query, int dataType, Integer dataId);

    List<TableDataListVo> getTableGroupList(TableDataSearchDto query, int dataType, List<Integer> dataIdList);

}
