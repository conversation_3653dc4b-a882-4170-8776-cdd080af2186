package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;

import java.math.BigDecimal;
import java.util.List;

import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.model.VO.PlatDefaultPriceVo;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.entity.SysParamSet;
import com.hzjm.service.mapper.SysParamSetMapper;
import com.hzjm.service.service.ISysParamSetService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 系统参数设置 服务实现类
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
@Slf4j
@Service
public class SysParamSetServiceImpl extends ServiceImpl<SysParamSetMapper, SysParamSet> implements ISysParamSetService {

    @Autowired
    private IShopUserService iShopUserService;

    @Override
    public SysParamSet getByIdWithoutLogic(Integer id) {
        SysParamSet data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public SysParamSet getDetail(Integer id) {
        SysParamSet data = getByIdWithoutLogic(id);

        return data;
    }

    @Override
    public Boolean saveSysParamSet(SysParamSet dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public Boolean insertList(List<SysParamSet> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }
        return baseMapper.insertList(dataList) > 0;
    }

    @Override
    public String getValue(String keyName) {
        SysParamSet set = getOne(Wrappers.<SysParamSet>lambdaQuery().eq(SysParamSet::getKeyName, keyName));
        if (ObjectUtils.isEmpty(set)) {
            set = new SysParamSet();
            switch (keyName) {
                case "delivery_fee":
                    set.setKeyValue("0");
                    break;
                case "plat_fee":
                    set.setKeyValue("5");
                    break;
                case "free_fee":
                    set.setKeyValue("0");
                    break;
            }

        }
        return set.getKeyValue();
    }

    @Override
    public PlatDefaultPriceVo defaultPrice(Integer type) {
        PlatDefaultPriceVo vo = new PlatDefaultPriceVo();
        vo.setFreeFee(new BigDecimal(getValue("free_fee")));
        vo.setDeliveryFee(new BigDecimal(getValue("delivery_fee")));
        if(!ObjectUtils.isEmpty(type) && type == SysProdEvent.TypeTransfer) {
            vo.setPlatFee(SysConstants.zero);
        } else {
            ShopUser shop = iShopUserService.getById(JwtContentHolder.getShopId());
            if (!ObjectUtils.isEmpty(shop)) {
                vo.setPlatFee(shop.getOcFee());
            } else {
                vo.setPlatFee(new BigDecimal(getValue("plat_fee")));
            }
        }
        return vo;
    }
}
