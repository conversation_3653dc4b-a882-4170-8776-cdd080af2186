package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopPre;
import com.hzjm.service.model.DTO.ShopPrePageDto;
import com.hzjm.service.model.VO.ShopPreListVo;
import com.hzjm.service.model.VO.ShopPreVo;

import java.util.List;

/**
 * 预报批次 服务类
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
public interface IShopPreService extends IService<ShopPre> {

    ShopPre getByIdWithoutLogic(Integer id);

    ShopPreVo getDetail(Integer id);

    void verifyLogNo(List<String> logNos);

    Boolean saveShopPre(ShopPre dto);

    Boolean insertList(List<ShopPre> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopPreListVo> searchList(ShopPrePageDto dto);

    List<ShopPre> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopPre> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
