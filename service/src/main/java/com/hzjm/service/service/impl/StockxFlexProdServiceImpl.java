package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.StockxFlexProd;
import com.hzjm.service.mapper.StockxFlexProdMapper;
import com.hzjm.service.service.IStockxFlexProdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class StockxFlexProdServiceImpl extends ServiceImpl<StockxFlexProdMapper, StockxFlexProd> implements IStockxFlexProdService {


    /**
     * @param ids
     * @return
     */
    @Override
    public boolean deleteBatchByIds(List<Integer> ids) {
        return getBaseMapper().deletePhysicallyByIds(ids) > 0;
    }
}
