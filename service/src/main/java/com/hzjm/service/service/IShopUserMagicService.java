package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.ShopUserMagic;
import com.hzjm.service.model.DTO.ShopUserMagicPageDto;
import com.hzjm.service.model.VO.ShopUserMagicListVo;
import com.hzjm.service.model.VO.ShopUserMagicVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商家的magic账号 服务类
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
public interface IShopUserMagicService extends IService<ShopUserMagic> {

    ShopUserMagic getByIdWithoutLogic(Integer id);

    ShopUserMagicVo getDetail(Integer id, Integer shopId);

    Boolean saveShopUserMagic(ShopUserMagic dto);

    Boolean insertList(List<ShopUserMagic> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<ShopUserMagicListVo> searchList(ShopUserMagicPageDto dto);

    List<ShopUserMagic> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopUserMagic> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
