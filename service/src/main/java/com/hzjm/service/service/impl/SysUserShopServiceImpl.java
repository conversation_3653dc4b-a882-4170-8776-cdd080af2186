package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.model.DTO.SysUserShopSaveDto;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysUserShopPageDto;
import com.hzjm.service.model.VO.SysUserShopListVo;
import com.hzjm.service.model.VO.SysUserShopVo;
import com.hzjm.service.entity.SysUserShop;
import com.hzjm.service.mapper.SysUserShopMapper;
import com.hzjm.service.service.ISysUserShopService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 商家数据权限 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Slf4j
@Service
public class SysUserShopServiceImpl extends ServiceImpl<SysUserShopMapper, SysUserShop> implements ISysUserShopService {

    @Override
    public SysUserShop getByIdWithoutLogic(Integer id) {
        SysUserShop data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商家数据权限"));
        }

        return data;
    }

    @Override
    public SysUserShopVo getDetail(Integer id) {
        SysUserShop data = getByIdWithoutLogic(id);

        SysUserShopVo vo = new SysUserShopVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysUserShop(SysUserShop dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysUserShopListVo> searchList(SysUserShopPageDto dto) {

        LambdaQueryWrapper<SysUserShop> qw = Wrappers.<SysUserShop>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysUserShop::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysUserShop::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysUserShop::getGmtCreate, endTime);

        IPage<SysUserShop> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysUserShopListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysUserShopListVo vo = new SysUserShopListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysUserShopListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysUserShop> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysUserShop> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean reset(SysUserShopSaveDto dto) {
        remove(Wrappers.<SysUserShop>lambdaQuery().eq(SysUserShop::getUserId, dto.getUserId()));

        if(!ObjectUtils.isEmpty(dto.getShopIdList())) {
            List<SysUserShop> dataList = new ArrayList<>();
            dto.getShopIdList().forEach(shopId -> {
                SysUserShop data = new SysUserShop();
                data.setShopId(shopId);
                data.setUserId(dto.getUserId());
                dataList.add(data);
            });
            insertList(dataList);
        }

        return true;
    }

    @Override
    public List<SysUserShop> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
