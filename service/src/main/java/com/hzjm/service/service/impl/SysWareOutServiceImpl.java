package com.hzjm.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysWareOutMapper;
import com.hzjm.service.model.DTO.SysProdDealPageDto;
import com.hzjm.service.model.DTO.SysWareOutPageDto;
import com.hzjm.service.model.VO.SysProdDealListVo;
import com.hzjm.service.model.VO.SysWareOutListVo;
import com.hzjm.service.model.VO.SysWareOutVo;
import com.hzjm.service.service.*;
import com.hzjm.service.utils.common.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.REGISTRATION_TIME_START;

/**
 * 出库单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
@Slf4j
@Service
public class SysWareOutServiceImpl extends ServiceImpl<SysWareOutMapper, SysWareOut> implements ISysWareOutService {

    @Resource
    ISysProdSearchService iSysProdSearchService;
    @Resource
    ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysAuditService iSysAuditService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private IShopUserAddressService iShopUserAddressService;

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysWareOutBatchService iSysWareOutBatchService;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysWareOutUserService iSysWareOutUserService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private ISysThirdPlatService iSysThirdPlatService;

    @Override
    public SysWareOut getByIdWithoutLogic(Integer id) {
        SysWareOut data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该出库单"));
        }

        return data;
    }

    @Override
    public SysWareOutVo getDetail(Integer id, String oddNo) {
        SysWareOut data = null;

        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<SysWareOut>lambdaQuery().eq(SysWareOut::getOddNo, oddNo));
            if (ObjectUtils.isEmpty(data)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该出库单"));
            }
        }

        SysWareOutVo vo = new SysWareOutVo();
        BeanUtils.copyProperties(data, vo);

        // 商家信息
        ShopUser shop = iShopUserService.getById(data.getShopId());
        if (!ObjectUtils.isEmpty(shop)) {
            vo.setShopUid(shop.getUid());
            vo.setShopName(shop.getRealname());
        }

        // 商品清单
        List<SysProdDealListVo> prodList = iSysProdDealService.dealList(data.getType() == SysProdEvent.TypeSale ? data.getId() : data.getRelationId(), data.getType(), new SysProdDealPageDto());
        prodList.forEach(item -> {
            if (ObjectUtils.isEmpty(item.getShelvesName())) {
                item.setShelvesName("");
            }
        });
        vo.setProdList(prodList.stream().sorted(Comparator.comparing(SysProdDealListVo::getShelvesName)).collect(Collectors.toList()));

        // 身份证号 : 通过收件人姓名查询
        if (!ObjectUtils.isEmpty(data.getReceiveName())) {
            String idCardNumber = iShopUserAddressService.queryIdCardByRecipientName(data.getReceiveName());
            vo.setIdCardNumber(idCardNumber);
        }

        if (iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .eq(SysWareOutBatchProd::getOutId, data.getId()).eq(SysWareOutBatchProd::getStatus, 5)) > 0) {
            vo.setStatus(7);// 丢失商品时，为异常订单
        }

        return vo;
    }

    @Override
    public Boolean saveSysWareOut(SysWareOut dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            List<Integer> prodIdList = dto.getProdIdList();
            Map<Integer, Integer> dealMap = dto.getProdDealMap();
            if (!ObjectUtils.isEmpty(dealMap)) {
                prodIdList = new ArrayList<>(dealMap.keySet());
            } else {
                dealMap = new HashMap<>();
            }

            dto.setProdNum(prodIdList.size());
            rs = baseMapper.insert(dto) > 0;

            if (!ObjectUtils.isEmpty(prodIdList)) {
                // 货架id
                List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                        .in(SysWareShelvesProd::getProdId, prodIdList));
                Map<Integer, Integer> shelvesMap = shelvesProdList.stream().collect(Collectors.toMap(SysWareShelvesProd::getProdId, SysWareShelvesProd::getShelvesId
                        // 当出现多个相同的prodID时，取最新的一条
                        , (k1, k2) -> k2
                ));

                Map<Integer, Integer> prodShopMap = new HashMap<>();
                if (ObjectUtils.isEmpty(dto.getShopId())) {
                    List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
                    prodShopMap.putAll(prodList.stream().collect(Collectors.toMap(SysProd::getId, SysProd::getShopId)));
                    prodList.clear();
                }

                List<SysWareOutProd> prodList = new ArrayList<>();
                List<SysProdEvent> eventList = new ArrayList<>();
                for (Integer prodId : prodIdList) {
                    if (prodId <= 0) {
                        continue;
                    }
                    SysWareOutProd outProd = new SysWareOutProd();
                    outProd.setProdId(prodId);
                    outProd.setOutId(dto.getId());
                    outProd.setWareId(dto.getWareId());
//                    outProd.setCheckId(userId);
                    outProd.setDealId(dealMap.get(prodId));
                    outProd.setShelvesId(shelvesMap.get(prodId));
                    prodList.add(outProd);

                    // 商品事件：生成出库申请
                    SysProdEvent event = new SysProdEvent();
                    event.setType(SysProdEvent.TypeOutApply);
                    event.setDescription("生成出库申请");
                    event.setProdId(prodId);
                    if (ObjectUtils.isEmpty(dto.getShopId())) {
                        event.setShopId(prodShopMap.get(prodId));
                    } else {
                        event.setShopId(dto.getShopId());
                    }
                    event.setRelationId(dto.getId());
                    if (event.getProdId() > 0)
                        eventList.add(event);
                }
                iSysWareOutProdService.insertList(prodList);
                iSysProdEventService.insertList(eventList);
            }
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            SysWareOut data = getById(dto.getId());
            if (!ObjectUtils.isEmpty(dto.getStatus()) && dto.getStatus() == 2) {
                if (data.getStatus() == 3 || data.getStatus() == 5) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("出库单状态已变更"));
                }

                switch (dto.getStatus()) {
                    // 成功出库
                    case 2:
                        if (iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                .isNotNull(SysWareOutBatchProd::getBatchId)
                                .eq(SysWareOutBatchProd::getOutId, dto.getId())
                                .eq(SysWareOutBatchProd::getBatchStatus, 1)) > 0) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("该出库单正在待处理的出库批次中"));
                        }
                        if (iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                .eq(SysWareOutBatchProd::getStatus, 5)
                                .eq(SysWareOutBatchProd::getOutId, dto.getId())
                                .eq(SysWareOutBatchProd::getBatchStatus, 1)) > 0) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("存在丢失的商品，无法完成出库"));
                        }

                        // 可出库商品
                        List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                .eq(SysWareOutBatchProd::getStatus, 3)
                                .isNull(SysWareOutBatchProd::getBatchId)
                                .eq(SysWareOutBatchProd::getOutId, dto.getId()));

                        // 出库单状态-是否完全出库：总数 - 已出库商品 - 即将出库数量 <= 0 ? 完全出库 : 部分出库
                        int outNum = iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                .eq(SysWareOutBatchProd::getOutId, data.getId())
                                .eq(SysWareOutBatchProd::getStatus, 4));
                        int status = data.getProdNum() - outNum - batchProdList.size() <= 0 ? 5 : 4;
                        dto.setStatus(status);

                        if (data.getProdNum() != outNum) {
                            iSysWareOutBatchService.out(batchProdList, status);

                            // 出库后静默生成已完成的出库批次
                            SysWareOutBatch batch = new SysWareOutBatch();
                            batch.setWareId(JwtContentHolder.getWareId());
                            batch.setBatchNo(iSysCodePoolService.build(-1, 1).get(0));
                            batch.setStatus(2);
                            batch.setCheckerId(JwtContentHolder.getUserId());
                            batch.insert();

                            if (!ObjectUtils.isEmpty(batchProdList)) {
                                iSysWareOutBatchProdService.update(Wrappers.<SysWareOutBatchProd>lambdaUpdate()
                                        .set(SysWareOutBatchProd::getBatchStatus, 2)
                                        .set(SysWareOutBatchProd::getBatchId, batch.getId())
                                        .in(SysWareOutBatchProd::getId, batchProdList.stream().map(SysWareOutBatchProd::getId).collect(Collectors.toList())));
                            }
                        } else {
                            Date now = DateTimeUtils.getNow();
                            if (data.getType() == SysProdEvent.TypeSale) {
                                // 流程结束：三方寄售单 完成
                                iSysProdSaleService.update(Wrappers.<SysProdSale>lambdaUpdate()
                                        .set(SysProdSale::getStatus, 5)
                                        .set(SysProdSale::getGmtModify, now)
                                        .eq(SysProdSale::getId, data.getRelationId()));
                            } else if (data.getType() == SysProdEvent.TypeTransport || data.getType() == SysProdEvent.TypeSend) {
                                // 流程结束：境外代发&转运 完成
                                iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                        .set(SysProdTransport::getStatus, 5)
                                        .set(SysProdTransport::getGmtModify, now)
                                        .eq(SysProdTransport::getId, data.getRelationId()));

                                // 更新平台审核状态：出库
                                iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                        .set(SysAudit::getGmtModify, now)
                                        .set(SysAudit::getStatus, 6)
                                        .eq(SysAudit::getRelationId, data.getRelationId()));
                            }
                        }

                        // 已拣货的记录重置
                        iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                .eq(SysWareOutBatchProd::getStatus, 2)
                                .isNull(SysWareOutBatchProd::getBatchId)
                                .eq(SysWareOutBatchProd::getOutId, dto.getId()));
                        break;
                    // 取消出库
                    case 3:
                        // 流程终止
                        iSysProdService.endEvent(data.getRelationId(), data.getType());
                        break;
                }
            }

            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysWareOutListVo> searchList(SysWareOutPageDto dto) {

        List<Integer> prodIdList = new ArrayList<>();
        LambdaQueryWrapper<SysWareOut> qw = buildQw(dto, prodIdList);
        // 输出 qw  的 where 条件 sql 语句
//        log.info("SysWareOutServiceImpl searchList qw ={}", qw.getCustomSqlSegment());
        log.info("SysWareOutServiceImpl searchList prodIdList ={}", JSON.toJSONString(prodIdList));

        IPage<SysWareOut> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareOutListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            // 商家信息
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().map(SysWareOut::getShopId).collect(Collectors.toList()));
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIdList));
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));

            // 出库商品信息
            List<Integer> outIdList = BaseUtils.initList();
            outIdList.addAll(pageResult.getRecords().stream().map(SysWareOut::getId).collect(Collectors.toList()));
            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getOutId, outIdList));
            Map<Integer, List<SysWareOutProd>> outProdMap = outProdList.stream().collect(Collectors.groupingBy(SysWareOutProd::getOutId));
            if (!ObjectUtils.isEmpty(prodIdList)) {
                prodIdList.retainAll(outProdList.stream().map(SysWareOutProd::getProdId).collect(Collectors.toList()));
            }

            // 商品清单
            Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealProdMap = iSysProdDealService.dealGroup(
                    pageResult.getRecords().stream().map(SysWareOut::getRelationId).collect(Collectors.toList()),
                    pageResult.getRecords().stream().map(SysWareOut::getType).collect(Collectors.toList()), prodIdList);

            // 出库批次
            Map<Integer, Integer> outBatchIdMap = new HashMap<>();
            Map<Integer, String> batchNoMap = new HashMap<>();
            Map<Integer, Integer> loseMap = new HashMap<>();
            List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                    .select(SysWareOutBatchProd::getOutId, SysWareOutBatchProd::getBatchId, SysWareOutBatchProd::getStatus)
                    .in(SysWareOutBatchProd::getOutId, outIdList));
            if (!ObjectUtils.isEmpty(batchProdList)) {
                // 使用最后的出库批次
                Map<Integer, List<SysWareOutBatchProd>> outTree = batchProdList.stream().collect(Collectors.groupingBy(SysWareOutBatchProd::getOutId));
                outTree.keySet().forEach(outId -> {
                    List<SysWareOutBatchProd> itemList = outTree.get(outId);
                    SysWareOutBatchProd batchProd = itemList.get(itemList.size() - 1);
                    outBatchIdMap.put(batchProd.getOutId(), batchProd.getBatchId());
                });

                List<SysWareOutBatch> batchList = iSysWareOutBatchService.list(Wrappers.<SysWareOutBatch>lambdaQuery()
                        .in(SysWareOutBatch::getId, batchProdList.stream().map(SysWareOutBatchProd::getBatchId).distinct().collect(Collectors.toList())));
                batchNoMap.putAll(batchList.stream().collect(Collectors.toMap(SysWareOutBatch::getId, SysWareOutBatch::getBatchNo)));

                Map<Integer, List<SysWareOutBatchProd>> loseTree = batchProdList.stream().filter(a -> {
                    return a.getStatus() == 5;
                }).collect(Collectors.groupingBy(SysWareOutBatchProd::getOutId));
                loseTree.keySet().forEach(outId -> {
                    loseMap.put(outId, loseTree.get(outId).size());
                });
            }

            // 寄售信息
            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(pageResult.getRecords().stream().filter(a -> a.getType() == SysProdEvent.TypeSale).map(SysWareOut::getRelationId).collect(Collectors.toList()));
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
            Map<Integer, SysProdSale> saleMap = saleList.stream().filter(e -> e.getId() != null).collect(Collectors.toMap(SysProdSale::getId, a -> a));
            saleList.clear();

            List<SysThirdPlat> platList = iSysThirdPlatService.list();
            Map<Integer, String> platMap = platList.stream().filter(e -> e.getId() != null && e.getName() != null).collect(Collectors.toMap(SysThirdPlat::getId, SysThirdPlat::getName));
            platList.clear();

            pageResult.getRecords().forEach(data -> {
                SysWareOutListVo vo = new SysWareOutListVo();
                BeanUtils.copyProperties(data, vo);

                // 商家信息
                ShopUser shop = shopMap.get(data.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    vo.setShopUid(shop.getUid());
                    vo.setShopName(shop.getRealname());
                }

                if (data.getType() == SysProdEvent.TypeSale) {
                    SysProdSale sale = saleMap.get(data.getRelationId());
                    if (!ObjectUtils.isEmpty(sale)) {
                        vo.setPlatName(sale.getPlatName());
                        vo.setPlatOrderNo(sale.getPlatOrderNo());
                    }
                }

                // 商品清单
                List<SysProdDealListVo> prodList = dealProdMap.get(data.getType()).get(data.getRelationId());
                if (!ObjectUtils.isEmpty(prodList)) {
                    if (data.getType() == SysProdEvent.TypeSale) {
                        List<SysWareOutProd> outProds = outProdMap.get(data.getId());
                        List<Integer> dealIdList = outProds.stream().map(SysWareOutProd::getDealId).collect(Collectors.toList());
                        List<SysProdDealListVo> dealList = prodList.stream().filter(a -> {
                            return dealIdList.indexOf(a.getId()) >= 0;
                        }).collect(Collectors.toList());

                        dealList.forEach(deal -> {
                            if (platMap.get(deal.getThirdPlatName()) != null && "StockX Direct".equals(platMap.get(deal.getThirdPlatName()))) {
                                deal.setThirdPlatName("SURGE");
                            } else {
                                deal.setThirdPlatName(platMap.get(deal.getThirdPlatName()));
                            }

                        });
                        saleList.clear();

                        vo.setProdList(dealList);
                    } else {
                        vo.setProdList(prodList);
                    }
                    vo.setNum(vo.getProdList().size());
                }

                if (loseMap.containsKey(data.getId())) {
                    vo.setStatus(7); // 丢失商品时，为异常订单
                }
                vo.setOutBatchNo(Optional.ofNullable(outBatchIdMap.get(data.getId())).map(batchId -> batchNoMap.get(batchId)).orElse(""));

                if (data.getStatus() == 5) {
                    vo.setGmtOut(data.getGmtModify());
                }

                voList.add(vo);
            });
        }

        IPage<SysWareOutListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    private LambdaQueryWrapper<SysWareOut> buildQw(SysWareOutPageDto dto, List<Integer> prodIdList) {
        LambdaQueryWrapper<SysWareOut> qw = Wrappers.<SysWareOut>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw
                .eq(SysWareOut::getDelFlag, 0)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysWareOut::getId, dto.getIdList())
                .notIn(!ObjectUtils.isEmpty(dto.getNotIdList()), SysWareOut::getId, dto.getNotIdList())
                .like(!ObjectUtils.isEmpty(dto.getOddNo()), SysWareOut::getOddNo, dto.getOddNo())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysWareOut::getStatus, dto.getStatus())
                .eq(!ObjectUtils.isEmpty(dto.getType()), SysWareOut::getType, dto.getType())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareOut::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareOut::getGmtCreate, endTime);

        if (!ObjectUtils.isEmpty(dto.getNumType())) {
            qw.eq(dto.getNumType() == 1, SysWareOut::getProdNum, 1);
            qw.gt(dto.getNumType() == 2, SysWareOut::getProdNum, 1);
        }
        if (!ObjectUtils.isEmpty(dto.getOutType())) {
            qw.in(dto.getOutType() == 1, SysWareOut::getStatus, 1, 2, 4);
            qw.eq(dto.getOutType() == 2, SysWareOut::getStatus, 5);
        }

        if (!ObjectUtils.isEmpty(dto.getAssign())) {
            if (!dto.getAssign()) {
                //未分配
                qw.notIn(SysWareOut::getStatus, 2, 5);
                // 搜索 不包含 已生成 批次和 完全出库的数据
            } else {
                //已分配
                qw.ne(ObjectUtils.isEmpty(dto.getStatus()), SysWareOut::getStatus, 5); // 搜索 不包含 完全出库的数据
            }
            qw.eq(SysWareOut::getAssigned, dto.getAssign());
        }

        // 仓库id
        if (ObjectUtils.isEmpty(dto.getWareId())) {
            if (JwtContentHolder.getRoleType() == 4) {
                qw.eq(SysWareOut::getWareId, JwtContentHolder.getWareId());

                // 查看权限
                List<SysWareOutUser> outUserList = iSysWareOutUserService.list(Wrappers.<SysWareOutUser>lambdaQuery()
                        .eq(SysWareOutUser::getUserId, JwtContentHolder.getUserId()));
                List<Integer> outIdList = BaseUtils.initList();
                outIdList.addAll(outUserList.stream().map(SysWareOutUser::getOutId).collect(Collectors.toList()));
                qw.in(SysWareOut::getId, outIdList);
            }
        } else {
            qw.eq(SysWareOut::getWareId, dto.getWareId());
        }

        if (!ObjectUtils.isEmpty(dto.getWareIdList())) {
            qw.in(SysWareOut::getWareId, dto.getWareIdList());
        }

        // 包含的商家 in
        if (!ObjectUtils.isEmpty(dto.getUid())) {
            List<Integer> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().select(ShopUser::getId).in(ShopUser::getUid, dto.getUid()))
                    .stream().map(ShopUser::getId).collect(Collectors.toList());

            if (!ObjectUtils.isEmpty(shopList)){
                qw.in(SysWareOut::getShopId, shopList);
            }
        }
        // 不包含的商家：需要排除 not in
        if (!ObjectUtils.isEmpty(dto.getUidListDisplay())) {
            List<Integer> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().select(ShopUser::getId).in(ShopUser::getUid, dto.getUidListDisplay()))
                    .stream().map(ShopUser::getId).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(shopList)){
                qw.notIn(SysWareOut::getShopId, shopList);
            }
        }
        // 识别码
        if (!ObjectUtils.isEmpty(dto.getShopUid())) {
            List<Integer> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().select(ShopUser::getId).like(ShopUser::getUid, dto.getShopUid()))
                    .stream().map(ShopUser::getId).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(shopList)){
                qw.in(SysWareOut::getShopId, shopList);
            }
        }

        // 寄售单号
        if (!ObjectUtils.isEmpty(dto.getPlatName())) {
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery()
                    .select(SysProdSale::getId)
                    .like(SysProdSale::getPlatName, dto.getPlatName()));
            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(saleList.stream().map(SysProdSale::getId).collect(Collectors.toList()));
            qw.and(a -> a.eq(SysWareOut::getType, SysProdEvent.TypeSale).in(SysWareOut::getRelationId, saleIdList));
            saleList.clear();
        }

        if (!ObjectUtils.isEmpty(dto.getOneId()) || !ObjectUtils.isEmpty(dto.getOneIdList())
                || !ObjectUtils.isEmpty(dto.getSku()) || !ObjectUtils.isEmpty(dto.getSkuList())
                || !ObjectUtils.isEmpty(dto.getSpec()) || !ObjectUtils.isEmpty(dto.getSpecList())) {
            prodIdList.add(0);
            LambdaQueryWrapper<SysProd> qw1 = Wrappers.<SysProd>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                    .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProd::getOneId, dto.getOneId())
                    .select(SysProd::getId);

            if (!ObjectUtils.isEmpty(dto.getSpecList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSpecList().forEach(spec -> {
                    sb.append("(spec = '" + spec + "') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            if (!ObjectUtils.isEmpty(dto.getSkuList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSkuList().forEach(sku -> {
                    sb.append("(sku = '" + sku + "') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
                StringBuffer sb = new StringBuffer();
                dto.getOneIdList().forEach(oneId -> {
                    sb.append("(one_id like '%" + oneId + "%') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            List<SysProd> prodList = iSysProdService.list(qw1);
            prodIdList.addAll(prodList.stream().map(SysProd::getId).collect(Collectors.toList()));
            prodList.clear();
        }

        List<Integer> shelvesIdList = dto.getShelvesIdList();
        if (ObjectUtils.isEmpty(shelvesIdList) && !ObjectUtils.isEmpty(dto.getShelvesNameList())) {
            StringBuffer sb = new StringBuffer();
            dto.getShelvesNameList().forEach(sku -> {
                sb.append("(name like '%" + sku + "%') or ");
            });
            List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery().apply(" (" + sb.substring(0, sb.length() - 4) + ") "));
            shelvesIdList = BaseUtils.initList();
            shelvesIdList.addAll(shelvesList.stream().map(SysWareShelves::getId).collect(Collectors.toList()));
            if (dto.getShelvesNameList().contains("未上架")) {
                shelvesIdList.add(-2);
            }
            shelvesList.clear();
        }

        // 货架id/oneId
        if (!ObjectUtils.isEmpty(shelvesIdList) || !ObjectUtils.isEmpty(prodIdList)) {
            LambdaQueryWrapper<SysWareOutProd> qw1 = Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(!ObjectUtils.isEmpty(prodIdList), SysWareOutProd::getProdId, prodIdList);
            // -2代表未上架
            if (!ObjectUtils.isEmpty(shelvesIdList)) {
                List<Integer> finalShelvesIdList = shelvesIdList;
                qw1.and(a -> a.in(SysWareOutProd::getShelvesId, finalShelvesIdList)
                        .or(finalShelvesIdList.contains(-2), o -> o.isNull(SysWareOutProd::getShelvesId)));
            }

            List<SysWareOutProd> prods = iSysWareOutProdService.list(qw1);

            List<Integer> outIdList = BaseUtils.initList();
            outIdList.addAll(prods.stream().map(SysWareOutProd::getOutId).collect(Collectors.toList()));
            prods.clear();
            qw.in(SysWareOut::getId, outIdList);
        }

        if (ObjectUtils.isEmpty(dto.getSortOrder())) {
            dto.setSortOrder("descend");
        }

        if (!ObjectUtils.isEmpty(dto.getSortField())) {
            switch (dto.getSortField()) {
                case "gmtOut":
                    qw.orderBy(true, dto.getSortOrder().equals("ascend"), SysWareOut::getGmtModify);
                    break;
            }
        }
        qw.orderByDesc(SysWareOut::getGmtCreate);

        return qw;
    }

    @Override
    public Boolean insertList(List<SysWareOut> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareOut> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    @ReadOnly
    public IPage<SysWareOutListVo> searchSale(SysWareOutPageDto dto) {
        Integer size = dto.getSize();
        Integer current = dto.getCurrent();
        dto.setSize(null);
        dto.setCurrent(null);

        if (ObjectUtils.isEmpty(dto.getSortOrder())) {
            dto.setSortOrder("ascend");
        }
        if (ObjectUtils.isEmpty(dto.getSortField())) {
            dto.setSortField("shelvesName");
        }

        List<SysWareOutListVo> records = searchList(dto).getRecords();

        Map<Integer, SysWareOutListVo> outMap = new HashMap<>();
        List<SysProdDealListVo> prodList = new ArrayList<>();
        records.forEach(out -> {
            List<SysProdDealListVo> itemList = out.getProdList();
            itemList.forEach(item -> {
                if (ObjectUtils.isEmpty(item.getShelvesName())) {
                    item.setShelvesName("");
                }
                outMap.put(item.getProdId(), out);
            });

            prodList.addAll(itemList);
        });

        // 排序方式，货架排序
        if (dto.getSortField().equals("shelvesName")) {
            List<SysProdDealListVo> tmpList;
            switch (dto.getSortOrder()) {
                case "ascend":
                    tmpList = prodList.stream().sorted(Comparator.comparing(SysProdDealListVo::getShelvesName)).collect(Collectors.toList());
                    break;
                case "descend":
                    tmpList = prodList.stream().sorted(Comparator.comparing(SysProdDealListVo::getShelvesName).reversed()).collect(Collectors.toList());
                    break;
                default:
                    tmpList = prodList.stream().collect(Collectors.toList());
            }
            records.clear();
            tmpList.forEach(tmp -> {
                records.add(outMap.get(tmp.getProdId()));
            });
            tmpList.clear();
        }

        return PageUtils.page(current, size, records.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    @ReadOnly
    public Integer sumProdNum(SysWareOutPageDto dto) {
        LambdaQueryWrapper<SysWareOut> qw = buildQw(dto, new ArrayList<>());
        return baseMapper.sumProdNum(qw);
    }

    @Override
    public Boolean resetProdNum(Integer id, Integer relationId, Integer relationType) {
        if (ObjectUtils.isEmpty(id)) {
            SysWareOut out = getOne(Wrappers.<SysWareOut>lambdaQuery().eq(SysWareOut::getRelationId, relationId).eq(SysWareOut::getType, relationType));
            if (!ObjectUtils.isEmpty(out)) {
                id = out.getId();
            }
        }

        if (ObjectUtils.isEmpty(id)) {
            return false;
        }

        return baseMapper.resetProdNum(id);
    }

    @Override
    public List<SysWareOut> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 创建 stocks flex 的出库记录
     */
    @Override
    @Transactional
    public void stocksFlexWareOut(List<String> oneIds) {
        log.info("SysWareOutServiceImpl stocksFlexWareOut start oneIds ={}", JSON.toJSONString(oneIds));

        Assert.notNull(oneIds, "oneIds is null");
        Assert.isTrue(oneIds.size() > 0, "oneIds siez is 0");

        // 拉取需要出库的商品，使用第三方平台的订单号进行分组
        List<SysProdSearch> sysProdSearchList = iSysProdSearchService.list(
                Wrappers.<SysProdSearch>lambdaQuery()
                        .in(SysProdSearch::getOneId, oneIds)
                        .eq(SysProdSearch::getSearchType, 1)
                        .select(
                                SysProdSearch::getOneId
                                , SysProdSearch::getShopId
                                , SysProdSearch::getPlatOrderNo
                                , SysProdSearch::getProdId
                        )
        );
        Map<String, List<SysProdSearch>> platOrderNoMap = sysProdSearchList.stream().collect(Collectors.groupingBy(SysProdSearch::getPlatOrderNo));

        List<Integer> prodsIdList = sysProdSearchList.stream().distinct().map(SysProdSearch::getProdId).collect(Collectors.toList());
        List<String> platOrderNoList = sysProdSearchList.stream().distinct().map(SysProdSearch::getPlatOrderNo).collect(Collectors.toList());

        Assert.notNull(prodsIdList, "prodsIdList is null");
        Assert.notNull(platOrderNoList, "platOrderNoList is null");

        List<SysProdDeal> sysProdDealList = iSysProdDealService.list(
                Wrappers.<SysProdDeal>lambdaQuery()
                        .in(SysProdDeal::getProdId, prodsIdList)
                        .eq(SysProdDeal::getType, 6)
                        .select(SysProdDeal::getSoldPrice
                                , SysProdDeal::getProdId
                                , SysProdDeal::getId
                        ));
        Assert.notNull(sysProdDealList, "sysProdDealList is null");
        Map<Integer, SysProdDeal> sysProdDealMap = sysProdDealList.stream().collect(Collectors.toMap(SysProdDeal::getProdId, a -> a, (oldValue, newValue) -> newValue));

        List<SysProdSale> sysProdSaleList = iSysProdSaleService.list(
                Wrappers.<SysProdSale>lambdaQuery()
                        .in(SysProdSale::getPlatOrderNo, platOrderNoList)
                        .select(SysProdSale::getOddNo
                                , SysProdSale::getId
                                , SysProdSale::getPlatOrderNo
                        ));
        Assert.notNull(sysProdSaleList, "sysProdSaleList is null");
        Map<String, SysProdSale> sysProdSaleMap = sysProdSaleList.stream().collect(Collectors.toMap(SysProdSale::getPlatOrderNo, a -> a, (oldValue, newValue) -> newValue));

        for (Map.Entry<String, List<SysProdSearch>> entry : platOrderNoMap.entrySet()) {
            List<SysProdSearch> sysProdSearcheList = entry.getValue();

            Assert.notNull(sysProdSearcheList, "stocksFlexWareOut sysProdSearcheList is null");
            Assert.isTrue(sysProdSearcheList.size() > 0, "stocksFlexWareOut sysProdSearcheList size is 0");
            Assert.notNull(sysProdSearcheList.get(0).getShopId(), "stocksFlexWareOut sysProdSearcheList shopId is null");
            Assert.notNull(sysProdSearcheList.get(0).getPlatOrderNo(), "stocksFlexWareOut sysProdSearcheList PlatOrderNo is null");
            Assert.notNull(sysProdSearcheList.get(0).getProdId(), "stocksFlexWareOut sysProdSearcheList PlatOrderNo is null");
            Assert.notNull(sysProdSaleMap.get(sysProdSearcheList.get(0).getPlatOrderNo()).getOddNo(), "stocksFlexWareOut sysProdSearcheList OddNo is null");

            Integer shopId = sysProdSearcheList.get(0).getShopId();
            String oddNo = sysProdSaleMap.get(sysProdSearcheList.get(0).getPlatOrderNo()).getOddNo();
            Integer relationId = sysProdSaleMap.get(sysProdSearcheList.get(0).getPlatOrderNo()).getId();
            List<Integer> prodIds = sysProdSearcheList.stream().map(SysProdSearch::getProdId).collect(Collectors.toList());

            // 入库的仓库为 20034 stockX Flex
            Integer wareId = 20034;

            // 创建出库单对象
            SysWareOut sysWareOut = new SysWareOut()
                    .setDelFlag(0)
                    .setOddNo(oddNo)
                    .setType(6)
                    .setRelationId(relationId)
                    .setWareId(wareId)
                    .setShopId(shopId)
                    .setStatus(5)
                    .setLabelImg(null)
                    .setReceiveName(null)
                    .setReceivePhone(null)
                    .setReceiveAddress(null)
                    .setProdNum(sysProdSearcheList.size())
                    .setTotalFee(null)
                    .setProdIdList(prodIds)
                    .setPlatOrderNo(sysProdSearcheList.get(0).getPlatOrderNo())
                    .setPlatName("STOCKX_FLEX");

            iSysWareOutService.save(sysWareOut);

            for (SysProdSearch searchProd : sysProdSearcheList) {
                // 创建出库商品对象
                SysWareOutProd sysWareOutProd = new SysWareOutProd()
                        .setDelFlag(0)
                        .setWareId(wareId)
                        .setOutId(sysWareOut.getId())
                        .setCheckId(1)
                        .setDealId(sysProdDealMap.get(searchProd.getProdId()).getId())
                        .setProdId(searchProd.getProdId())
                        .setShelvesId(searchProd.getShelvesId());

                iSysWareOutProdService.save(sysWareOutProd);
            }


        }

        // 更新库存和寄售的状态为已出库
        LambdaUpdateWrapper<SysProdSearch> sysProdSearchLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysProdSearchLambdaUpdateWrapper
                .in(SysProdSearch::getOneId, oneIds)
                .eq(SysProdSearch::getSearchType, 1)
                .set(SysProdSearch::getStatus, 6)
                .set(SysProdSearch::getGmtOut, new Date());
        iSysProdSearchService.update(sysProdSearchLambdaUpdateWrapper);

        LambdaUpdateWrapper<SysProd> sysProdLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        sysProdLambdaUpdateWrapper
                .in(SysProd::getOneId, oneIds)
                .set(SysProd::getStatus, 6)
                .set(SysProd::getShopId, null)
                .set(SysProd::getGmtOut, new Date());
        iSysProdService.update(sysProdLambdaUpdateWrapper);

        List<Integer> sysProdDealIdList = sysProdDealList.stream().map(SysProdDeal::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(sysProdDealIdList)) {
            LambdaUpdateWrapper<SysProdDeal> sysProdDealLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            sysProdDealLambdaUpdateWrapper
                    .in(SysProdDeal::getId, sysProdDealIdList)
                    .set(SysProdDeal::getStatus, 3)
                    .set(SysProdDeal::getGmtOut, new Date());
            iSysProdDealService.update(sysProdDealLambdaUpdateWrapper);
        } else {
            log.info("SysWareOutServiceImpl stocksFlexWareOut sysProdDealIdList is null");
        }

        List<Integer> sysProdSaleIdList = sysProdSaleList.stream().map(SysProdSale::getId).collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(sysProdSaleIdList)) {
            LambdaUpdateWrapper<SysProdSale> sysProdSaleLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            sysProdSaleLambdaUpdateWrapper
                    .in(SysProdSale::getId, sysProdSaleIdList)
                    .set(SysProdSale::getStatus, 5);
            iSysProdSaleService.update(sysProdSaleLambdaUpdateWrapper);
        } else {
            log.info("SysWareOutServiceImpl stocksFlexWareOut sysProdSaleIdList is null");

        }

        log.info("SysWareOutServiceImpl stocksFlexWareOut end");
    }

    @Override
    @ReadOnly
    public boolean queryBlockedUser(ShopUser shopUser, Date dateTime, Integer offset) {
        LambdaQueryWrapper<SysWareOut> queryWrapper = new LambdaQueryWrapper<>();
        DateTime offsetTime = DateUtil.offsetDay(dateTime, offset);
        DateTime startTime = DateUtil.parse(REGISTRATION_TIME_START);
        queryWrapper
                .eq(SysWareOut::getShopId, shopUser.getId())
                .ge(SysWareOut::getGmtCreate, offsetTime)
                .ge(SysWareOut::getGmtCreate, startTime);
        // 查询匹配的订单数量
        int count = this.count(queryWrapper);
        return count == 0;
    }

    @ReadOnly
    @Override
    public Set<Integer> queryBlockedUserReturnIds(List<ShopUser> shopUsers, Date dateTime, Integer offset) {
        LambdaQueryWrapper<SysWareOut> queryWrapper = new LambdaQueryWrapper<>();
        List<Integer> shopIds = shopUsers.stream().map(ShopUser::getId).collect(Collectors.toList());
        DateTime startTime = DateUtil.parse(REGISTRATION_TIME_START);
        DateTime offsetTime = DateUtil.offsetDay(dateTime, offset);
        queryWrapper
                .in(CollUtil.isNotEmpty(shopIds), SysWareOut::getShopId, shopIds)
                .ge(SysWareOut::getGmtCreate, offsetTime)
                .ge(SysWareOut::getGmtCreate, startTime);
        // 查询匹配的订单数量
        List<SysWareOut> wareInList = this.list(queryWrapper);
        Set<Integer> excepted = wareInList.stream()
                .map(SysWareOut::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return shopIds.stream()
                .filter(id -> !excepted.contains(id))
                .collect(Collectors.toSet());
    }
}
