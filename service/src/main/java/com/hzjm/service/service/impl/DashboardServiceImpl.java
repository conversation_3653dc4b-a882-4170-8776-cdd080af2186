package com.hzjm.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.StringProcessedUtils;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysMoney;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.mapper.DashboardAdminMapper;
import com.hzjm.service.mapper.DashboardMapper;
import com.hzjm.service.mapper.ShopUserMapper;
import com.hzjm.service.mapper.SysProdSearchMapper;
import com.hzjm.service.model.DTO.AdminHomeBiUserCashoutHistoryDTO;
import com.hzjm.service.model.DTO.SourcingOpportunitiesDto;
import com.hzjm.service.model.DTO.req.SourcingOpportunitiesDetailReq;
import com.hzjm.service.model.DTO.req.SourcingOpportunitiesReq;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.model.enums.SourcePlatformService;
import com.hzjm.service.service.DashboardService;
import com.hzjm.service.service.ISysMoneyService;
import com.hzjm.service.service.ISysSkuService;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * dashboard 实现类
 */
@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    @Resource
    DashboardAdminMapper dashboardAdminMapper;
    @Resource
    SysProdSearchMapper sysProdSearchMapper;
    @Resource
    ShopUserMapper shopUserMapper;
    @Resource
    DashboardMapper dashboardMapper;
    @Resource
    private ISysSkuService sysSkuService;
    @Resource
    private ISysMoneyService iSysMoneyService;
    @Resource
    private TouchUtils touchUtils;
    // 仓库ID，统计BI时需要忽略这些仓库中的数据
    public List<Integer> wareIds = new ArrayList<>();


    @Override
    @ReadOnly(value = "bi_read")
    public ShopInOutDashboard inOutNumTable(TableDataSearchDto dto) {
        List<Integer> ignoredWareIds = this.getIgnoredWareIdsForBI();

        ShopInOutDashboard shopInOutTable = new ShopInOutDashboard();
        List<SalesDayDataVo> inNumlist = this.getDateStringBetween(dto.getBeginDate(), dto.getEndDate(), "");
        List<SalesDayDataVo> outNumList = new ArrayList<>(inNumlist);

        // 获取有天数的数据
        List<SalesDayDataVo> inNum = dashboardMapper.selectInNum(dto, ignoredWareIds);
        List<SalesDayDataVo> outNum = dashboardMapper.selectOutNum(dto, ignoredWareIds);

        //入库： list是全量的日期列表，inNum 和 outNum 是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (inNumlist.size() != inNum.size()) {
            for (int i = 0; i < inNumlist.size(); i++) {
                SalesDayDataVo original = inNumlist.get(i);
                String originalDate = original.getDate();

                // 在 salesDayDataVos 中查找具有相同日期的对象
                for (SalesDayDataVo newObject : inNum) {
                    if (newObject.getDate().equals(originalDate)) {
                        // 将 salesDayDataVos 中的对象替换到 inNumlist 中相同位置
                        inNumlist.set(i, newObject);
                        break;
                    }
                }
            }

            shopInOutTable.setInList(inNumlist);

        } else {

            shopInOutTable.setInList(inNum);
        }

        // 出库： list是全量的日期列表，inNum 和 outNum 是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (outNumList.size() != outNum.size()) {
            for (int i = 0; i < outNumList.size(); i++) {
                SalesDayDataVo original = outNumList.get(i);
                String originalDate = original.getDate();

                // 在 salesDayDataVos 中查找具有相同日期的对象
                for (SalesDayDataVo newObject : outNum) {
                    if (newObject.getDate().equals(originalDate)) {
                        // 将 salesDayDataVos 中的对象替换到 outNumList 中相同位置
                        outNumList.set(i, newObject);
                        break;
                    }
                }
            }

            shopInOutTable.setOutList(outNumList);

        } else {

            shopInOutTable.setOutList(outNum);
        }

        return shopInOutTable;
    }

    @Override
    @ReadOnly(value = "bi_read")
    public ShopInOutDashboard inOutCostTable(TableDataSearchDto dto) {
        List<Integer> ignoredWareIds = this.getIgnoredWareIdsForBI();

        ShopInOutDashboard shopInOutTable = new ShopInOutDashboard();
        List<SalesDayDataVo> inCostlist = this.getDateStringBetween(dto.getBeginDate(), dto.getEndDate(), "");
        List<SalesDayDataVo> outCostList = new ArrayList<>(inCostlist);

        // 获取有天数的数据
        List<SalesDayDataVo> inCost = dashboardMapper.selectInCost(dto, ignoredWareIds);
        List<SalesDayDataVo> outCost = dashboardMapper.selectOutCost(dto, ignoredWareIds);

        //入库： list是全量的日期列表，inCost 和 outCost 是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (inCostlist.size() != inCost.size()) {
            for (int i = 0; i < inCostlist.size(); i++) {
                SalesDayDataVo original = inCostlist.get(i);
                String originalDate = original.getDate();

                // 在 salesDayDataVos 中查找具有相同日期的对象
                for (SalesDayDataVo newObject : inCost) {
                    if (newObject.getDate().equals(originalDate)) {
                        // 将 salesDayDataVos 中的对象替换到 inCostlist 中相同位置
                        inCostlist.set(i, newObject);
                        break;
                    }
                }
            }

            shopInOutTable.setInList(inCostlist);

        } else {

            shopInOutTable.setInList(inCost);
        }

        // 出库： list是全量的日期列表，inCost 和 outCost 是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (outCostList.size() != outCost.size()) {
            for (int i = 0; i < outCostList.size(); i++) {
                SalesDayDataVo original = outCostList.get(i);
                String originalDate = original.getDate();

                // 在 salesDayDataVos 中查找具有相同日期的对象
                for (SalesDayDataVo newObject : outCost) {
                    if (newObject.getDate().equals(originalDate)) {
                        // 将 salesDayDataVos 中的对象替换到 outCostList 中相同位置
                        outCostList.set(i, newObject);
                        break;
                    }
                }
            }

            shopInOutTable.setOutList(outCostList);

        } else {

            shopInOutTable.setOutList(outCost);
        }

        return shopInOutTable;
    }


    /**
     * 三十天内卖出去鞋子最多的商家,前十
     */
    @Override
    @ReadOnly(value = "bi_read")
    public List<ShopUserListVo> queryShopUserTop10(TableDataSearchDto dto) {
        return dashboardMapper.selectShopUserTop10(dto);
    }

    /**
     * 三十天内卖出去最多的鞋子，前二十
     */
    @Override
    @ReadOnly(value = "bi_read")
    public List<PlatformOrderVo> queryProductTop20(TableDataSearchDto dto) {
        return dashboardMapper.selectProductTop20(dto);
    }

    /**
     * 今日数据
     */
    @Override
    @ReadOnly(value = "bi_read")
    public PlatformOrderVo querySysProdDay(TableDataSearchDto dto) {
        List<Integer> ignoredWareIds = this.getIgnoredWareIdsForBI();

        dto.setTimeZoneStr(DateTimeUtils.getStartOfDay(dto.getTimeZone()));
        // 商家有多少种SKU ,今日预报的包裹数量,今日的入库数量，今日的出库数量，今日的订单数量
        return dashboardMapper.selectSysProdDay(dto, ignoredWareIds);
    }

    /**
     * 商品库存
     */
    @Override
    @ReadOnly(value = "bi_read")
    public PlatformOrderVo querySysProdCostSum(TableDataSearchDto dto) {
        List<Integer> ignoredWareIds = this.getIgnoredWareIdsForBI();

        // [合格] 商品总数量 ，合格商品总价值 ,[瑕疵] 商品总数量 ，合格商品总价值
        PlatformOrderVo platformOrderVo = dashboardMapper.selectSysProdCostSum(dto, ignoredWareIds);
        // [合格] 商家已上架数量
        PlatformOrderVo up = dashboardMapper.selectShelvesCount(dto);
        // [瑕疵] 商家已上架数量
        PlatformOrderVo down = dashboardMapper.selectUnShelvesCount(dto);

        // 合格未上架数量 = 合格商品数量 - 合格已上架数量
        platformOrderVo.setShelvesCount(up.getShelvesCount());
        platformOrderVo.setShelvesCountDown(platformOrderVo.getOneCount() - platformOrderVo.getShelvesCount());

        // 瑕疵未上架数量 = 瑕疵商品数量 - 瑕疵已上架数量
        platformOrderVo.setUnShelvesCount(down.getUnShelvesCount());
        platformOrderVo.setUnShelvesCountDown(platformOrderVo.getUnOneCount() - platformOrderVo.getUnShelvesCount());

        return platformOrderVo;
    }

    @ReadOnly(value = "bi_read")
    @TrimParam
    @Override
    public List<ShopHomeOrderPlatformVo> queryShopHomeOrderTotalGmvbyPlatform(Integer shopId
            , Date beginDate
            , Date endDate) {
        Assert.notNull(shopId, "shopId不能为空");
        Assert.notNull(beginDate, "beginDate 不能为空");
        Assert.notNull(endDate, "endDate 不能为空");
        // 获取数据库中的结果
        List<ShopHomeOrderPlatformVo> result = dashboardMapper.selectShopHomeOrderTotalGmvbyPlatform(shopId, beginDate, endDate);

        // 创建一个包含所有平台的列表
        List<ShopHomeOrderPlatformVo> completeList = new ArrayList<>();

        // 遍历所有平台枚举值
        for (SourcePlatformService platform : SourcePlatformService.values()) {
            // 查找该平台是否在结果中
            boolean exists = result.stream()
                    .anyMatch(vo -> vo.getPlatform() == platform);

            if (!exists) {
                // 如果平台不存在，创建一个新的VO对象，值设为0
                completeList.add(new ShopHomeOrderPlatformVo()
                        .setPlatform(platform)
                        .setTotalGMV(BigDecimal.ZERO)
                        .setTotalCount(0L));
            }
        }

        // 合并原有结果和补充的平台数据
        result.addAll(completeList);
        return result;
    }

    @ReadOnly(value = "bi_read")
    @TrimParam
    @Override
    public List<ShopHomeOrderPlatformVo> queryShopHomeOrderTotalGmv(Integer shopId
            , Date beginDate
            , Date endDate) {
        Assert.notNull(shopId, "shopId不能为空");
        Assert.notNull(beginDate, "beginDate 不能为空");
        Assert.notNull(endDate, "endDate 不能为空");
        return dashboardMapper.selectShopHomeOrderTotalGmvbyPlatform(shopId, beginDate, endDate);
    }

    /**
     * 商家端首页面板——销售数据
     */
    @Override
    @ReadOnly(value = "bi_read")
    public SalesDashboardDataVo querySaleNumTable(TableDataSearchDto dto) {
        /*统计销售金额和数量*/
        SalesDashboardDataVo saleSumAmount = dashboardMapper.selectSaleSumAmount(dto);

        /*统计全部商家各个订单平台的分步*/
        List<SalesPlatformDataVo> platformDataVoList = dashboardMapper.selectPlatformList(dto);

        /*统计 今日，昨日的销售量和增长比*/
        // 设置开始时间为传入时区的零点
        dto.setTimeZoneStr(DateTimeUtils.getStartOfDay(dto.getTimeZone()));
        SalesDashboardDataVo daySaleSumAmount = dashboardMapper.selectDaySaleSumAmount(dto);
        /*商家端的总利润 、 今日利润 、昨日利润 、增长比*/
        SalesDashboardDataVo dayProfitSumAmount = dashboardMapper.selectDayProfitSumAmount(dto);

        SalesDashboardDataVo salesDashboardDataVo = new SalesDashboardDataVo();
        BeanUtils.copyProperties(saleSumAmount, salesDashboardDataVo);

        salesDashboardDataVo.setDaySaleSumAmount(daySaleSumAmount.getDaySaleSumAmount());
        salesDashboardDataVo.setYesterDaySales(daySaleSumAmount.getYesterDaySales());
        salesDashboardDataVo.setIncreasePercentage(daySaleSumAmount.getIncreasePercentage());
        salesDashboardDataVo.setSaleSoldCountDay(daySaleSumAmount.getSaleSoldCountDay());

        salesDashboardDataVo.setProfitSumAmount(dayProfitSumAmount.getProfitSumAmount());
        salesDashboardDataVo.setDayProfitSumAmount(dayProfitSumAmount.getDayProfitSumAmount());
        salesDashboardDataVo.setYesterDayProfitSumAmount(dayProfitSumAmount.getYesterDayProfitSumAmount());
        salesDashboardDataVo.setProfitDiffPercentage(dayProfitSumAmount.getProfitDiffPercentage());

        salesDashboardDataVo.setPlatformList(platformDataVoList);
        salesDashboardDataVo.setPlatformMap(platformDataVoList.stream().collect(Collectors.toMap(
                SalesPlatformDataVo::getPlatform,
                SalesPlatformDataVo::getCount,
                (existingValue, newValue) -> existingValue)
        ));
        /*当天的商家各个订单平台的分步*/
        platformDataVoList = dashboardMapper.selectPlatformList(dto);
        salesDashboardDataVo.setPlatformListToday(platformDataVoList);
        salesDashboardDataVo.setPlatformMapToday(platformDataVoList.stream().collect(Collectors.toMap(
                SalesPlatformDataVo::getPlatform,
                SalesPlatformDataVo::getCount,
                (existingValue, newValue) -> existingValue)
        ));

        return salesDashboardDataVo;
    }

    /**
     * 根据传入的日期返回 日销售额
     */
    @Override
    @ReadOnly(value = "bi_read")
    public List<SalesDayDataVo> queryDaySaleNumTable(TableDataSearchDto dto) {
        Assert.notNull(dto, "请求参数不可为空");

        List<SalesDayDataVo> list = this.getDateStringBetween(dto.getBeginDate(), dto.getEndDate(), "");
        List<SalesDayDataVo> salesDayDataVos = dashboardMapper.selectSaleGroupByDay(dto);
        // list是全量的日期列表，salesDayDataVos是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (list.size() != salesDayDataVos.size()) {
            for (int i = 0; i < list.size(); i++) {
                SalesDayDataVo original = list.get(i);
                String originalDate = original.getDate();

                // 在 salesDayDataVos 中查找具有相同日期的对象
                for (SalesDayDataVo newObject : salesDayDataVos) {
                    if (newObject.getDate().equals(originalDate)) {
                        // 将 salesDayDataVos 中的对象替换到 list 中相同位置
                        list.set(i, newObject);
                        break;
                    }
                }
            }

            return list;

        } else {

            return salesDayDataVos;
        }
    }

    /**
     * 根据传入的日期返回 销售利润分组
     */
    @Override
    @ReadOnly(value = "bi_read")
    public List<SalesDayDataVo> queryProfitGroupByDay(TableDataSearchDto dto) {
        Assert.notNull(dto, "请求参数不可为空");

        List<SalesDayDataVo> list = this.getDateStringBetween(dto.getBeginDate(), dto.getEndDate(), "");
        List<SalesDayDataVo> salesDayDataVos = dashboardMapper.selectProfitGroupByDay(dto);
        // list是全量的日期列表，salesDayDataVos是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (list.size() != salesDayDataVos.size()) {
            for (int i = 0; i < list.size(); i++) {
                SalesDayDataVo original = list.get(i);
                String originalDate = original.getDate();

                // 在 salesDayDataVos 中查找具有相同日期的对象
                for (SalesDayDataVo newObject : salesDayDataVos) {
                    if (newObject.getDate().equals(originalDate)) {
                        // 将 salesDayDataVos 中的对象替换到 list 中相同位置
                        list.set(i, newObject);
                        break;
                    }
                }
            }

            return list;
        } else {

            return salesDayDataVos;
        }
    }

    /**
     * 根据传入的日期返回日销售平台分组
     */
    @Override
    @ReadOnly(value = "bi_read")
    public Map<String, List<SalesDayDataVo>> queryProfitPlatformGroupByDay(TableDataSearchDto dto) {
        Assert.notNull(dto, "请求参数不可为空");

        Map<String, List<SalesDayDataVo>> map = new HashMap<>();
        List<SalesDayDataVo> list = new ArrayList<>();
        // 处理多平台的数据初始化
        for (int i = 0; i < SysConstants.ORDER_PLATFROM.length; i++) {
            List<SalesDayDataVo> list1 = this.getDateStringBetween(dto.getBeginDate()
                    , dto.getEndDate()
                    , SysConstants.ORDER_PLATFROM[i]);
            list.addAll(list1);
        }

        List<SalesDayDataVo> salesDayDataVos = dashboardMapper.selectProfitPlatformGroupByDay(dto);
        // list是全量的日期列表，salesDayDataVos是查询出的数据，如果list和salesDayDataVos的个数不相等，则需要补全查询出来的数据
        if (list.size() != salesDayDataVos.size()) {
            for (int i = 0; i < list.size(); i++) {
                SalesDayDataVo original = list.get(i);
                String originalDate = original.getDate();
                String originalPlatform = original.getPlatform();

                // 在 salesDayDataVos 中查找具有相同日期和相同平台的对象
                for (SalesDayDataVo newObject : salesDayDataVos) {
                    if (newObject.getDate().equals(originalDate)
                            && newObject.getPlatform().equals(originalPlatform)
                    ) {
                        // 将 salesDayDataVos 中的对象替换到 list 中相同位置
                        list.set(i, newObject);
                        break; // 找到匹配的对象后退出循环
                    }
                }
            }

            map = list.stream()
                    .collect(Collectors.groupingBy(SalesDayDataVo::getPlatform));

            return map;
        } else {

            map = salesDayDataVos.stream()
                    .collect(Collectors.groupingBy(SalesDayDataVo::getPlatform));

            return map;
        }
    }


    /**
     * 根据传入的日期初始化一个按天排列的对象集合
     */
    private List<SalesDayDataVo> getDateStringBetween(Date beginDate, Date endDate, String platform) {
        List<SalesDayDataVo> dateStringList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginDate);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        while (!calendar.getTime().after(endDate)) {
            SalesDayDataVo salesDayDataVo = new SalesDayDataVo();
            salesDayDataVo.setDate(sdf.format(calendar.getTime()));
            salesDayDataVo.setAmount("0");
            salesDayDataVo.setPlatform(platform);
            salesDayDataVo.setCount("0");
            calendar.add(Calendar.DATE, 1);

            dateStringList.add(salesDayDataVo);
        }

        return dateStringList;
    }


    // region 当前最新的管理端BI查询，首页面板数据

    /**
     * 统计BI时需要忽略这些仓库中的数据
     */
    @ReadOnly(value = "bi_read")
    public List<Integer> getIgnoredWareIdsForBI() {
        if (ObjectUtils.isEmpty(this.wareIds)) {
            this.wareIds = dashboardAdminMapper.selectWareIds();
            return this.wareIds;
        }
        return this.wareIds;
    }

    /**
     * 获取总库存
     */
    @ReadOnly(value = "bi_read")
    public Long getSearchCount(Long userId, Set<Integer> shopIds) {
        List<Integer> ignoredWareIds = this.getIgnoredWareIdsForBI();

        QueryWrapper<SysProdSearch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.eq("search_type", 1);
        queryWrapper.in(!ObjectUtils.isEmpty(shopIds), "shop_id", shopIds);
        queryWrapper.in("status", 1, 2, 3, 4, 7, 8, 9, 10, 12);
        if (!ObjectUtils.isEmpty(ignoredWareIds)) {
            queryWrapper.notIn("ware_id", ignoredWareIds);
        }

        if (!ObjectUtils.isEmpty(userId)) {
            queryWrapper.eq("shop_id", userId);
        }

        // 总库存
        return sysProdSearchMapper.selectCount(queryWrapper) == null ? 0L : Long.valueOf(sysProdSearchMapper.selectCount(queryWrapper));
    }

    /**
     * 管理端首页BI - 头部默认查询，无请求参数
     * shopIds : 数据权限范围，列表内的商家可以进行统计
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:DefaultVo:'+#shopIds", unless = "#result == null")
    public AdminHomeBiDefaultVo queryAdminHomeBiDefaultVo(Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        List<Integer> ignoredWareIds = this.getIgnoredWareIdsForBI();

        // 总库存
        long searchCount = this.getSearchCount(null, shopIds);
        // 合格库存
        QueryWrapper<SysProdSearch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.eq("search_type", 1);
        queryWrapper.in("status", 1, 2, 3, 4, 7, 8, 9, 10, 12);
        queryWrapper.eq("check_result", 1);
        queryWrapper.in(!ObjectUtils.isEmpty(shopIds), "shop_id", shopIds);
        if (!ObjectUtils.isEmpty(ignoredWareIds)) {
            queryWrapper.notIn("ware_id", ignoredWareIds);
        }
        long qualifiedCount = sysProdSearchMapper.selectCount(queryWrapper);
        // 缺陷库存
        long defectCount = searchCount - qualifiedCount;
        // 总用户数
        QueryWrapper<ShopUser> shopPackQueryWrapper = new QueryWrapper<>();
        shopPackQueryWrapper.eq("del_flag", 0);
        shopPackQueryWrapper.in(!ObjectUtils.isEmpty(shopIds), "id", shopIds);
        shopPackQueryWrapper.select(" distinct uid ");
        long userCount = shopUserMapper.selectCount(shopPackQueryWrapper);

        // selectSkuSizeSize
        AdminHomeBiDefaultVo skuSizeSize = dashboardAdminMapper.selectSkuSizeSize(wareIds, shopIds);

        AdminHomeBiDefaultVo adminHomeBiDefaultVo = new AdminHomeBiDefaultVo();
        adminHomeBiDefaultVo.setTotalInventory(searchCount);
        adminHomeBiDefaultVo.setDefectInventory(defectCount);
        adminHomeBiDefaultVo.setTotalUsers(userCount);
        adminHomeBiDefaultVo.setActivePku(skuSizeSize.getActivePku());
        adminHomeBiDefaultVo.setTotalPku(skuSizeSize.getTotalPku());

        return adminHomeBiDefaultVo;
    }

    /**
     * 管理端首页BI - 头部默认查询，无请求参数 活跃用户数
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:activeUser:'+#beginDate+':'+#shopIds", unless = "#result == null")
    public AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToActiceUsers(Date beginDate, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        // 活跃用户数 : 昨天生成过订单的用户数
        long actionUser = dashboardAdminMapper.selectActionUser(wareIds, beginDate, shopIds);

        AdminHomeBiDefaultVo adminHomeBiDefaultVo = new AdminHomeBiDefaultVo();
        adminHomeBiDefaultVo.setActiveUsers(actionUser);

        return adminHomeBiDefaultVo;
    }

    /**
     * 管理端首页BI - 头部默认查询，无请求参数 昨日产生过订单的用户数量
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:acticeUsersYeaterDay:'+#beginDate+':'+#shopIds")
    public long queryAdminHomeBiDefaultVoToActiceUsersYeaterDay(Date beginDate, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        return dashboardAdminMapper.selectOrderActionUser(wareIds, beginDate, shopIds);
    }

    /**
     * 管理端首页BI - 数据对比 - 在架商品与总库存百分比
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:percentage:'+#shopIds", unless = "#result == null")
    public AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToPercentage(Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();

        // 总库存
        long searchCount = this.getSearchCount(null, shopIds);

        // 全新商品在架数量
        long newProductCount = dashboardAdminMapper.selectListedTotalInventory(null, wareIds, shopIds);

        // 瑕疵商品在架数量
        long defectProductCount = dashboardAdminMapper.selectDefectInventory(null, wareIds, shopIds);

        double percentage = 0;

        if (searchCount > 0) {
            // 列出在架商品与总库存百分比
            percentage = (double) (newProductCount + defectProductCount) / searchCount * 100;
        }

        AdminHomeBiDefaultVo adminHomeBiDefaultVo = new AdminHomeBiDefaultVo();
        adminHomeBiDefaultVo.setListedTotalInventory(String.format("%.2f", percentage));
        return adminHomeBiDefaultVo;
    }

    /**
     * 管理端首页BI - 数据对比 - 缺陷在架商品与总库存百分比
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:defectPercentage:'+#shopIds", unless = "#result == null")
    public AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToDefectPercentage(Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        // 总库存
        long searchCount = this.getSearchCount(null, shopIds);
        // 合格库存
        QueryWrapper<SysProdSearch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.eq("search_type", 1);
        queryWrapper.in("status", 1, 2, 3, 4, 7, 8, 9, 10, 12);
        queryWrapper.eq("check_result", 1);
        queryWrapper.in(!ObjectUtils.isEmpty(shopIds), "shop_id", shopIds);
        if (!ObjectUtils.isEmpty(wareIds)) {
            queryWrapper.notIn("ware_id", wareIds);
        }
        long qualifiedCount = sysProdSearchMapper.selectCount(queryWrapper);
        // 缺陷库存
        long defectCount = searchCount - qualifiedCount;

        // 瑕疵商品在架数量
        long defectProductCount = dashboardAdminMapper.selectDefectInventory(null, wareIds, shopIds);

        double defectPercentage = 0;

        if (defectCount > 0) {
            // 缺陷在架商品与总库存百分比
            defectPercentage = (double) defectProductCount / defectCount * 100;
        }

        AdminHomeBiDefaultVo adminHomeBiDefaultVo = new AdminHomeBiDefaultVo();
        adminHomeBiDefaultVo.setDefectInventoryPercentage(String.format("%.2f", defectPercentage));
        return adminHomeBiDefaultVo;
    }

    /**
     * 管理端首页BI - 数据对比 - 陈旧库存数量与总库存百分比
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:oldProductPercentage:'+#shopIds", unless = "#result == null")
    public AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToOldProductPercentage(Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();

        // 总库存
        long searchCount = this.getSearchCount(null, shopIds);

        double oldProductPercentage = 0;

        if (searchCount > 0) {
            // 陈旧库存数量与总库存百分比
            long oldProductCount = dashboardAdminMapper.selectOver120DaysInventory(null, wareIds, shopIds);
            oldProductPercentage = (double) oldProductCount / searchCount * 100;
        }

        AdminHomeBiDefaultVo adminHomeBiDefaultVo = new AdminHomeBiDefaultVo();
        adminHomeBiDefaultVo.setAgedInventoryPercentage(String.format("%.2f", oldProductPercentage));
        return adminHomeBiDefaultVo;
    }

    /**
     * 管理端首页BI - 数据对比 - 最低报价
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "FortyMinutesBiDataConfig", key = "'knet:bi:lowestAskPercentage:'+#shopIds", unless = "#result == null")
    public AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToLowestAskPercentage(Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        // 全新商品在架数量
        long newProductCount = dashboardAdminMapper.selectListedTotalInventory(null, wareIds, shopIds);
        // 瑕疵商品在架数量
        long defectProductCount = dashboardAdminMapper.selectDefectInventory(null, wareIds, shopIds);
        // 当前在架的商品数量
        long searchCount = newProductCount + defectProductCount;

        double lowestPricePercentage = 0;

        if (searchCount > 0) {
            long lowestPriceCount = this.queryLowestPriceCount(null, shopIds);
            lowestPricePercentage = (double) lowestPriceCount / searchCount * 100;
        }

        AdminHomeBiDefaultVo adminHomeBiDefaultVo = new AdminHomeBiDefaultVo();
        adminHomeBiDefaultVo.setLowestAskPercentage(String.format("%.2f", Math.min(lowestPricePercentage, 100.00)));
        return adminHomeBiDefaultVo;
    }

    /**
     * 查询上架列表中开启了最低价的商品数量
     */
    @ReadOnly(value = "bi_read")
    private long queryLowestPriceCount(Long userId, Set<Integer> shopIds) {
        AtomicLong returnNumber = new AtomicLong(0);

        // 当前上架的商品
        List<SkuSizeLowPriceVo> skuSizeLowPriceVos = dashboardAdminMapper.selectSkuSizeLowPrice(userId, shopIds);

        // 最低价
        List<String> skuList = skuSizeLowPriceVos.stream().map(SkuSizeLowPriceVo::getSku).distinct().collect(Collectors.toList());
        List<String> sizeList = skuSizeLowPriceVos.stream().map(SkuSizeLowPriceVo::getSize).distinct().collect(Collectors.toList());
        List<SkuSizeLowPriceVo> skuSizeLowPriceVoList = dashboardAdminMapper.selectSkuSizeLowPriceBySkuSize(skuList, sizeList, shopIds);
        Map<String, SkuSizeLowPriceVo> skuListAllMap = skuSizeLowPriceVoList.stream().collect(Collectors.toMap(SkuSizeLowPriceVo::getSearchKey, Function.identity()
                , (newValue, oldValue) -> newValue));
        skuSizeLowPriceVoList.clear();

        // 筛选出最低价的商品数量
        skuSizeLowPriceVos.parallelStream().forEach(productPrice -> {
            SkuSizeLowPriceVo priceVo = skuListAllMap.get(productPrice.getSearchKey());

            // 空值跳过，不进行判断
            if (ObjectUtils.isEmpty(productPrice) || ObjectUtils.isEmpty(priceVo)
                    || ObjectUtils.isEmpty(productPrice.getEbayPrice()) || ObjectUtils.isEmpty(priceVo.getEbayPrice())
                    || ObjectUtils.isEmpty(productPrice.getGoatIsPrice()) || ObjectUtils.isEmpty(priceVo.getGoatIsPrice())
                    || ObjectUtils.isEmpty(productPrice.getGoatPrice()) || ObjectUtils.isEmpty(priceVo.getGoatPrice())
                    || ObjectUtils.isEmpty(productPrice.getKcPrice()) || ObjectUtils.isEmpty(priceVo.getKcPrice())
                    || ObjectUtils.isEmpty(productPrice.getStockxPrice()) || ObjectUtils.isEmpty(priceVo.getStockxPrice())
                    || ObjectUtils.isEmpty(productPrice.getPoizonPrice()) || ObjectUtils.isEmpty(priceVo.getPoizonPrice())
                    || ObjectUtils.isEmpty(productPrice.getTtsPrice())
            ) {
                return;
            }

            if (productPrice.getStockxPrice() > 0 && priceVo.getStockxPrice() > 0
                    && productPrice.getStockxPrice() <= priceVo.getStockxPrice()) { // 上架的价格小于 stockxPrice 最低价
                returnNumber.incrementAndGet();
            } else if (productPrice.getEbayPrice() > 0 && priceVo.getEbayPrice() > 0
                    && productPrice.getEbayPrice() <= priceVo.getEbayPrice()) { // 上架的价格小于 ebay 最低价
                returnNumber.incrementAndGet();
            } else if (productPrice.getGoatIsPrice() > 0 && priceVo.getGoatIsPrice() > 0
                    && productPrice.getGoatIsPrice() <= priceVo.getGoatIsPrice()) { // 上架的价格小于 goat 最低价
                returnNumber.incrementAndGet();
            } else if (productPrice.getGoatPrice() > 0 && priceVo.getGoatPrice() > 0
                    && productPrice.getGoatPrice() <= priceVo.getGoatPrice()) { // 上架的价格小于 goat is 最低价
                returnNumber.incrementAndGet();
            } else if (productPrice.getKcPrice() > 0 && priceVo.getKcPrice() > 0
                    && productPrice.getKcPrice() <= priceVo.getKcPrice()) { // 上架的价格小于 kc 最低价
                returnNumber.incrementAndGet();
            } else if (productPrice.getPoizonPrice() > 0 && priceVo.getPoizonPrice() > 0
                    && productPrice.getPoizonPrice() <= priceVo.getPoizonPrice()) { // 上架的价格小于 poizon 最低价
                returnNumber.incrementAndGet();
            } else if (productPrice.getTtsPrice() > 0) { // TTS
                Integer ttsPrice = null;
                // 初始化完成之后，考虑一个情况，当 ttsLowestPrice 和 ttsHighestPrice 都大于零的时候，优先使用价格最低的值
                if (!ObjectUtils.isEmpty(priceVo.getTtsLowestPrice()) && !ObjectUtils.isEmpty(priceVo.getTtsHighestPrice())
                ) {
                    ttsPrice = priceVo.getTtsLowestPrice() < priceVo.getTtsHighestPrice() ? priceVo.getTtsLowestPrice() : priceVo.getTtsHighestPrice();
                }

                if (!ObjectUtils.isEmpty(ttsPrice)
                        && ttsPrice > 0
                        && productPrice.getTtsPrice() <= ttsPrice
                ) {
                    returnNumber.incrementAndGet();
                }
            }


        });

        return returnNumber.get();
    }

    /**
     * 管理端首页BI - 概述
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:Overview:'+#beginDate+':'+#endDate+':'+#userId+':'+#shopIds", unless = "#result == null")
    public AdminHomeBiOverviewVo queryAdminHomeBiOverviewVo(Date beginDate, Date endDate, Long userId, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();

        AdminHomeBiOverviewVo adminHomeBiOverviewVo = dashboardAdminMapper.selectOverview(beginDate, endDate, wareIds, userId, shopIds);
        // fake 订单率
        Long fakeOrderTotal = dashboardAdminMapper.selectFakeTotal(beginDate, endDate, userId, wareIds, shopIds);
        double fakeOrderRate = 0;
        if (!ObjectUtils.isEmpty(adminHomeBiOverviewVo.getTotalOrders())
                && adminHomeBiOverviewVo.getTotalOrders() > 0
                && fakeOrderTotal > 0) {
            fakeOrderRate = (double) fakeOrderTotal / adminHomeBiOverviewVo.getTotalOrders() * 100;
        }
        adminHomeBiOverviewVo.setFakeOrderRate(String.format("%.4f", fakeOrderRate));
        return adminHomeBiOverviewVo;
    }

    /**
     * 管理端首页BI - 数据趋势
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:TrendVo:'+#beginDate+':'+#endDate+':'+#userId+':'+#shopIds",
            unless = "#result == null || (#result != null && #result.size() == 0)")
    public List<AdminHomeBiDataTrendVo> queryAdminHomeBiDataTrendVo(Date beginDate, Date endDate, Long userId, Set<Integer> shopIds) {
        // 如果 beginDate 为空，设置为当月的第一天
        if (beginDate == null) {
            beginDate = DateTimeUtils.getFirstDayOfMonth();
        }

        // 如果 endDate 为空，设置为当月的最后一天
        if (endDate == null) {
            endDate = DateTimeUtils.getLastDayOfMonth();
        }

        List<SalesDayDataVo> dateStringBetween = this.getDateStringBetween(beginDate, endDate, "");
        List<String> dateList = dateStringBetween.stream().map(SalesDayDataVo::getDate).collect(Collectors.toList());
        return this.getAdminHomeBiDataTrendVoList(beginDate, endDate, dateList, userId, shopIds);
    }

    /**
     * 按照日期区间获取日期列表之内的销售额，订单数量，利润，转运代发数量，入库数量
     */
    @ReadOnly(value = "bi_read")
    List<AdminHomeBiDataTrendVo> getAdminHomeBiDataTrendVoList(Date beginDate, Date endDate, List<String> dateList, Long userId, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();

        List<AdminHomeBiDataTrendVo> platformOrderByDayList = dashboardAdminMapper.selectPlatformOrderByDay(beginDate, endDate, null, userId, wareIds, shopIds, null);
        List<AdminHomeBiDataTrendVo> reshipOrdersByDayList = dashboardAdminMapper.selectReshipOrdersByDay(beginDate, endDate, userId, wareIds, shopIds);
        List<AdminHomeBiDataTrendVo> inboundByDayList = dashboardAdminMapper.selectInboundByDay(beginDate, endDate, userId, wareIds, shopIds);
        // platformOrderByDay 转成map ，key 为 date，value 为 AdminHomeBiDataTrendVo
        Map<String, AdminHomeBiDataTrendVo> platformOrderByDayMap = platformOrderByDayList.stream().collect(Collectors.toMap(AdminHomeBiDataTrendVo::getDate, Function.identity(), (newValue, oldValue) -> newValue));
        Map<String, AdminHomeBiDataTrendVo> reshipOrdersByDayMap = reshipOrdersByDayList.stream().collect(Collectors.toMap(AdminHomeBiDataTrendVo::getDate, Function.identity(), (newValue, oldValue) -> newValue));
        Map<String, AdminHomeBiDataTrendVo> inboundByDayMap = inboundByDayList.stream().collect(Collectors.toMap(AdminHomeBiDataTrendVo::getDate, Function.identity(), (newValue, oldValue) -> newValue));

        List<AdminHomeBiDataTrendVo> adminHomeBiDataTrendVoList = new ArrayList<>();

        // 遍历日期列表
        for (String date : dateList) {
            AdminHomeBiDataTrendVo platformOrderByDay = new AdminHomeBiDataTrendVo();
            AdminHomeBiDataTrendVo reshipOrdersByDay = new AdminHomeBiDataTrendVo();
            AdminHomeBiDataTrendVo inboundByDay = new AdminHomeBiDataTrendVo();
            // 获取值
            if (!ObjectUtils.isEmpty(platformOrderByDayMap)) {
                platformOrderByDay = platformOrderByDayMap.get(date);
            }
            if (!ObjectUtils.isEmpty(reshipOrdersByDayMap)) {
                reshipOrdersByDay = reshipOrdersByDayMap.get(date);
            }
            if (!ObjectUtils.isEmpty(inboundByDayMap)) {
                inboundByDay = inboundByDayMap.get(date);
            }

            // 进行对象赋值
            AdminHomeBiDataTrendVo trendVo = new AdminHomeBiDataTrendVo();

            trendVo.setDate(date);

            if (!ObjectUtils.isEmpty(platformOrderByDay)) {
                trendVo.setTotalGMV(platformOrderByDay.getTotalGMV() != null ? platformOrderByDay.getTotalGMV() : new BigDecimal(0));
                trendVo.setTotalSaleOrders(platformOrderByDay.getTotalSaleOrders() != null ? platformOrderByDay.getTotalSaleOrders() : 0);
                trendVo.setTotalProfit(platformOrderByDay.getTotalProfit() != null ? platformOrderByDay.getTotalProfit() : new BigDecimal(0));
            } else {
                trendVo.setTotalGMV(new BigDecimal(0));
                trendVo.setTotalSaleOrders(0L);
                trendVo.setTotalProfit(new BigDecimal(0));
            }


            if (!ObjectUtils.isEmpty(reshipOrdersByDay)) {
                trendVo.setTotalReshipOrders(reshipOrdersByDay.getTotalReshipOrders() != null ? reshipOrdersByDay.getTotalReshipOrders() : 0L);
            } else {
                trendVo.setTotalReshipOrders(0L);
            }

            if (!ObjectUtils.isEmpty(inboundByDay)) {
                trendVo.setTotalInbound(inboundByDay.getTotalInbound() != null ? inboundByDay.getTotalInbound() : 0L);
            } else {
                trendVo.setTotalInbound(0L);
            }


            adminHomeBiDataTrendVoList.add(trendVo);
        }

        return adminHomeBiDataTrendVoList;
    }

    /**
     * 管理端首页BI - 数据对比
     */
    @Override
    @ReadOnly(value = "bi_read")
    public AdminHomeBiDataComparisonVo queryAdminHomeBiDataComparison(Date currentCycleBeginDate
            , Date currentCycleEndDate
            , Date comparedWithBeginDate
            , Date comparedWithEndDate
            , List<String> comparedWithType
            , Long userId
            , Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();

        // currentCycle 的统计
        AdminHomeBiDataTrendVo platformOrderCurrentCycle = dashboardAdminMapper.selectPlatformOrder(currentCycleBeginDate, currentCycleEndDate, userId, wareIds, shopIds);
        AdminHomeBiDataTrendVo reshipOrdersCurrentCycle = dashboardAdminMapper.selectReshipOrders(currentCycleBeginDate, currentCycleEndDate, userId, wareIds, shopIds);
        AdminHomeBiDataTrendVo inboundCurrentCycle = dashboardAdminMapper.selectInbound(currentCycleBeginDate, currentCycleEndDate, userId, wareIds, shopIds);
        // comparedWith 的统计
        AdminHomeBiDataTrendVo platformOrderComparedWith = dashboardAdminMapper.selectPlatformOrder(comparedWithBeginDate, comparedWithEndDate, userId, wareIds, shopIds);
        AdminHomeBiDataTrendVo reshipOrdersComparedWith = dashboardAdminMapper.selectReshipOrders(comparedWithBeginDate, comparedWithEndDate, userId, wareIds, shopIds);
        AdminHomeBiDataTrendVo inboundComparedWith = dashboardAdminMapper.selectInbound(comparedWithBeginDate, comparedWithEndDate, userId, wareIds, shopIds);

        // 总交易额
        AdminHomeBiDataComparisonDetailVo totalGMV = new AdminHomeBiDataComparisonDetailVo();
        totalGMV.setCurrentCycle(platformOrderCurrentCycle.getTotalGMV());
        totalGMV.setLastCycle(platformOrderComparedWith.getTotalGMV());
        totalGMV.setDifference(platformOrderCurrentCycle.getTotalGMV().subtract(platformOrderComparedWith.getTotalGMV()));
        BigDecimal percentage1 = new BigDecimal(0);
        if (percentage1.compareTo(platformOrderComparedWith.getTotalGMV()) != 0) {
            percentage1 = totalGMV.getDifference().divide(platformOrderComparedWith.getTotalGMV(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);
            if (totalGMV.getCurrentCycle().compareTo(totalGMV.getLastCycle()) > 0) {
                totalGMV.setDifferencePercentage(String.valueOf(percentage1));
            } else {
                totalGMV.setDifferencePercentage(String.valueOf(percentage1));
            }
        }

        // 总销售订单数
        AdminHomeBiDataComparisonDetailVo totalSaleOrders = new AdminHomeBiDataComparisonDetailVo();
        totalSaleOrders.setCurrentCycle(BigDecimal.valueOf(platformOrderCurrentCycle.getTotalSaleOrders()));
        totalSaleOrders.setLastCycle(BigDecimal.valueOf(platformOrderComparedWith.getTotalSaleOrders()));
        totalSaleOrders.setDifference(BigDecimal.valueOf(platformOrderCurrentCycle.getTotalSaleOrders() - platformOrderComparedWith.getTotalSaleOrders()));
        BigDecimal percentage2 = new BigDecimal(0);
        if (platformOrderComparedWith.getTotalSaleOrders() != 0) {
            // 将 Long 转换为 BigDecimal 进行计算
            BigDecimal num = totalSaleOrders.getDifference();
            BigDecimal den = new BigDecimal(platformOrderComparedWith.getTotalSaleOrders());
            percentage2 = num.divide(den, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);
            if (totalSaleOrders.getCurrentCycle().compareTo(totalSaleOrders.getLastCycle()) > 0) {
                totalSaleOrders.setDifferencePercentage(String.valueOf(percentage2));
            } else {
                totalSaleOrders.setDifferencePercentage(String.valueOf(percentage2));
            }
        }

        // 转运、代发总数量
        AdminHomeBiDataComparisonDetailVo totalReshipOrders = new AdminHomeBiDataComparisonDetailVo();
        totalReshipOrders.setCurrentCycle(BigDecimal.valueOf(reshipOrdersCurrentCycle.getTotalReshipOrders()));
        totalReshipOrders.setLastCycle(BigDecimal.valueOf(reshipOrdersComparedWith.getTotalReshipOrders()));
        totalReshipOrders.setDifference(BigDecimal.valueOf(reshipOrdersCurrentCycle.getTotalReshipOrders() - reshipOrdersComparedWith.getTotalReshipOrders()));
        BigDecimal percentage3 = new BigDecimal(0);
        if (reshipOrdersComparedWith.getTotalReshipOrders() != 0) {
            // 将 Long 转换为 BigDecimal 进行计算
            BigDecimal num = totalReshipOrders.getDifference();
            BigDecimal den = new BigDecimal(reshipOrdersComparedWith.getTotalReshipOrders());
            percentage3 = num.divide(den, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);
            if (totalReshipOrders.getCurrentCycle().compareTo(totalReshipOrders.getLastCycle()) > 0) {
                totalReshipOrders.setDifferencePercentage(String.valueOf(percentage3));
            } else {
                totalReshipOrders.setDifferencePercentage(String.valueOf(percentage3));
            }
        }

        // 总利润
        AdminHomeBiDataComparisonDetailVo totalProfit = new AdminHomeBiDataComparisonDetailVo();
        totalProfit.setCurrentCycle(platformOrderCurrentCycle.getTotalProfit());
        totalProfit.setLastCycle(platformOrderComparedWith.getTotalProfit());
        totalProfit.setDifference(platformOrderCurrentCycle.getTotalProfit().subtract(platformOrderComparedWith.getTotalProfit()));
        BigDecimal percentage4 = new BigDecimal(0);
        if (percentage4.compareTo(platformOrderComparedWith.getTotalProfit()) != 0) {
            percentage4 = totalProfit.getDifference().divide(platformOrderComparedWith.getTotalProfit(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);

            if (totalProfit.getCurrentCycle().compareTo(totalProfit.getLastCycle()) > 0) {
                totalProfit.setDifferencePercentage(String.valueOf(percentage4));
            } else {
                totalProfit.setDifferencePercentage(String.valueOf(percentage4));
            }
        }


        // 总入库量
        AdminHomeBiDataComparisonDetailVo totalInbound = new AdminHomeBiDataComparisonDetailVo();
        totalInbound.setCurrentCycle(BigDecimal.valueOf(inboundCurrentCycle.getTotalInbound()));
        totalInbound.setLastCycle(BigDecimal.valueOf(inboundComparedWith.getTotalInbound()));
        totalInbound.setDifference(BigDecimal.valueOf(inboundCurrentCycle.getTotalInbound() - inboundComparedWith.getTotalInbound()));
        BigDecimal percentage5 = new BigDecimal(0);
        if (inboundComparedWith.getTotalInbound() != 0) {
            // 将 Long 转换为 BigDecimal 进行计算
            BigDecimal num = totalInbound.getDifference();
            BigDecimal den = new BigDecimal(inboundComparedWith.getTotalInbound());
            percentage5 = num.divide(den, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"))
                    .setScale(2, RoundingMode.HALF_UP);

            if (totalInbound.getCurrentCycle().compareTo(totalInbound.getLastCycle()) > 0) {
                totalInbound.setDifferencePercentage(String.valueOf(percentage5));
            } else {
                totalInbound.setDifferencePercentage(String.valueOf(percentage5));
            }
        }


        AdminHomeBiDataComparisonVo adminHomeBiDataComparisonVo = new AdminHomeBiDataComparisonVo();
        adminHomeBiDataComparisonVo.setTotalGMV(totalGMV);
        adminHomeBiDataComparisonVo.setTotalSaleOrders(totalSaleOrders);
        adminHomeBiDataComparisonVo.setTotalReshipOrders(totalReshipOrders);
        adminHomeBiDataComparisonVo.setTotalProfit(totalProfit);
        adminHomeBiDataComparisonVo.setTotalInbound(totalInbound);

        return adminHomeBiDataComparisonVo;
    }

    /**
     * 管理端首页BI - 订单平台 - 按日期分组
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:queryAdminHomeBiOrderPlatformVo:'+#beginDate+':'+#endDate+':'+#platform+':'+#shopIds",
            unless = "#result == null || (#result != null && #result.size() == 0)")
    public List<AdminHomeBiOrderPlatformVo> queryAdminHomeBiOrderPlatformVo(Date beginDate
            , Date endDate
            , SourcePlatformService platform
            , Set<Integer> shopIds
    ) {
        // 如果 beginDate 为空，设置为当月的第一天
        if (beginDate == null) {
            beginDate = DateTimeUtils.getFirstDayOfMonth();
        }

        // 如果 endDate 为空，设置为当月的最后一天
        if (endDate == null) {
            endDate = DateTimeUtils.getLastDayOfMonth();
        }
        List<SalesDayDataVo> dateStringBetween = this.getDateStringBetween(beginDate, endDate, "");
        List<String> dateList = dateStringBetween.stream().map(SalesDayDataVo::getDate).collect(Collectors.toList());
        List<AdminHomeBiOrderPlatformVo> adminHomeBiOrderPlatformVoList = this.getAdminHomeBiDataTrendVoListByPlatform(beginDate, endDate, platform, dateList, shopIds);
        log.info("queryAdminHomeBiOrderPlatformVo adminHomeBiOrderPlatformVoList = {}", JSON.toJSONString(adminHomeBiOrderPlatformVoList));
        return adminHomeBiOrderPlatformVoList;
    }

    /**
     * 按照日期区间 和 平台
     * 获取日期列表之内的销售额，订单数量，利润
     */
    @ReadOnly(value = "bi_read")
    List<AdminHomeBiOrderPlatformVo> getAdminHomeBiDataTrendVoListByPlatform(Date beginDate, Date endDate, SourcePlatformService platform, List<String> dateList, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        String account = null;
        if (platform == SourcePlatformService.STOCKX_FLEX) {
            // 这里需要展示 stockFlex
            wareIds.removeIf(e -> e == 20034);
            platform = null;
            account = "STOCKX_FLEX";
        }

        List<AdminHomeBiDataTrendVo> platformOrderByDayList = dashboardAdminMapper.selectPlatformOrderByDay(beginDate, endDate, platform, null, wareIds, shopIds, account);
        // platformOrderByDay 转成map ，key 为 date，value 为 AdminHomeBiDataTrendVo
        Map<String, AdminHomeBiDataTrendVo> platformOrderByDayMap = platformOrderByDayList
                .stream().collect(Collectors.toMap(AdminHomeBiDataTrendVo::getDate, Function.identity(), (newValue, oldValue) -> newValue));

        List<AdminHomeBiOrderPlatformVo> adminHomeBiDataTrendVoList = new ArrayList<>();

        // 遍历日期列表
        for (String date : dateList) {
            AdminHomeBiDataTrendVo platformOrderByDay = new AdminHomeBiDataTrendVo();
            // 获取值
            if (!ObjectUtils.isEmpty(platformOrderByDayMap)) {
                platformOrderByDay = platformOrderByDayMap.get(date);
            }

            // 进行对象赋值
            AdminHomeBiOrderPlatformVo platformVo = new AdminHomeBiOrderPlatformVo();

            platformVo.setDate(date);

            if (!ObjectUtils.isEmpty(platformOrderByDay)) {
                platformVo.setTotalGMV(platformOrderByDay.getTotalGMV() != null ? platformOrderByDay.getTotalGMV() : new BigDecimal(0));
                platformVo.setTotalSaleOrders(platformOrderByDay.getTotalSaleOrders() != null ? platformOrderByDay.getTotalSaleOrders() : 0);
                platformVo.setTotalProfit(platformOrderByDay.getTotalProfit() != null ? platformOrderByDay.getTotalProfit() : new BigDecimal(0));
            } else {
                platformVo.setTotalGMV(new BigDecimal(0));
                platformVo.setTotalSaleOrders(0L);
                platformVo.setTotalProfit(new BigDecimal(0));
            }

            adminHomeBiDataTrendVoList.add(platformVo);
        }

        return adminHomeBiDataTrendVoList;
    }

    /**
     * 管理端首页BI - 用户 - 活跃列表
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:queryUserActiveListings:'+#userId+':'+#shopIds", unless = "#result == null")
    public AdminHomeBiUserActiveListingsVo queryUserActiveListings(Long userId, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();

        if (ObjectUtils.isEmpty(userId)) {
            log.info("DashboardServiceImpl queryUserActiveListings userid is null");
            return new AdminHomeBiUserActiveListingsVo();
        }

        QueryWrapper<SysProdSearch> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", 0);
        queryWrapper.eq("shop_id", userId);
        queryWrapper.eq("search_type", 1);
        queryWrapper.in("status", 1, 2, 3, 4, 7, 8, 9, 10, 12);
        queryWrapper.in(!ObjectUtils.isEmpty(shopIds), "shop_id", shopIds);
        if (!ObjectUtils.isEmpty(wareIds)) {
            queryWrapper.notIn("ware_id", wareIds);
        }
        // 总库存
        long searchCount = this.getSearchCount(userId, shopIds);

        // 合格库存
        queryWrapper.eq("check_result", 1);
        long qualifiedCount = sysProdSearchMapper.selectCount(queryWrapper);
        // 缺陷库存
        long defectCount = searchCount - qualifiedCount;
        // 全新商品在架数量
        long newProductCount = dashboardAdminMapper.selectListedTotalInventory(userId, wareIds, shopIds);
        // 瑕疵商品在架数量
        long defectProductCount = dashboardAdminMapper.selectDefectInventory(userId, wareIds, shopIds);
        // 当前上架的数量
        long listedCount = newProductCount + defectProductCount;

        double percentage = 0;
        double defectPercentage = 0;
        double lowestPricePercentage = 0;
        double oldProductPercentage = 0;
        double stockxPercentage = 0;
        double goatProductPercentage = 0;
        double knetProductPercentage = 0;
        double kcProductPercentage = 0;
        double ebayProductPercentage = 0;
        double poizonProductPercentage = 0;
        double tiktokProductPercentage = 0;
        double surgeProductPercentage = 0;

        AdminHomeBiUserPlatCountVo adminHomeBiUserPlatCountVo = dashboardAdminMapper.selectUserPlatCount(userId, wareIds, shopIds);

        if (searchCount > 0) {
            // 列出在架商品与总库存百分比
            percentage = (double) (newProductCount + defectProductCount) / searchCount * 100;
            // 缺陷在架商品与总库存百分比
            defectPercentage = (double) defectProductCount / searchCount * 100;
            // 最低报价数量与当前上架数量的百分比
            long lowestPriceCount = this.queryLowestPriceCount(userId, shopIds);
            lowestPricePercentage = (double) lowestPriceCount / listedCount * 100;
            log.info("DashboardServiceImpl queryUserActiveListings lowestPriceCount:{},listedCount:{}", lowestPriceCount, listedCount);
            // 陈旧库存数量与总库存百分比
            long oldProductCount = dashboardAdminMapper.selectOver120DaysInventory(userId, wareIds, shopIds);
            oldProductPercentage = (double) oldProductCount / searchCount * 100;

            if (!ObjectUtils.isEmpty(adminHomeBiUserPlatCountVo)) {
                // 上架商品所占的总库存百分比 - Stockx
                long stockxProductCount = adminHomeBiUserPlatCountVo.getStockxCount() != null ? adminHomeBiUserPlatCountVo.getStockxCount() : 0;
                stockxPercentage = (double) stockxProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - goat
                long goatProductCount = adminHomeBiUserPlatCountVo.getGoatCount() != null ? adminHomeBiUserPlatCountVo.getGoatCount() : 0;
                goatProductPercentage = (double) goatProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - Knet
                long knetProductCount = adminHomeBiUserPlatCountVo.getKcCount() != null ? adminHomeBiUserPlatCountVo.getKcCount() : 0;
                knetProductPercentage = (double) knetProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - KC
                long kcProductCount = adminHomeBiUserPlatCountVo.getKcCount() != null ? adminHomeBiUserPlatCountVo.getKcCount() : 0;
                kcProductPercentage = (double) kcProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - eBay
                long ebayProductCount = adminHomeBiUserPlatCountVo.getEbayCount() != null ? adminHomeBiUserPlatCountVo.getEbayCount() : 0;
                ebayProductPercentage = (double) ebayProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - POIZON
                long poizonProductCount = adminHomeBiUserPlatCountVo.getPoizonCount() != null ? adminHomeBiUserPlatCountVo.getPoizonCount() : 0;
                poizonProductPercentage = (double) poizonProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - tiktok
                long tiktokProductCount = adminHomeBiUserPlatCountVo.getTiktokCount() != null ? adminHomeBiUserPlatCountVo.getTiktokCount() : 0;
                tiktokProductPercentage = (double) tiktokProductCount / searchCount * 100;
                // 上架商品所占的总库存百分比 - surge
                long surgeProductCount = adminHomeBiUserPlatCountVo.getSurgeCount() != null ? adminHomeBiUserPlatCountVo.getSurgeCount() : 0;
                surgeProductPercentage = (double) surgeProductCount / searchCount * 100;

            }

        }

        AdminHomeBiUserActiveListingsVo adminHomeBiUserActiveListingsVo = new AdminHomeBiUserActiveListingsVo();
        adminHomeBiUserActiveListingsVo.setTotalInventory(searchCount);
        adminHomeBiUserActiveListingsVo.setListedTotalInventory(String.format("%.2f", Math.min(percentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setLowestAskPercentage(String.format("%.2f", Math.min(lowestPricePercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setDefectInventory(defectCount);
        adminHomeBiUserActiveListingsVo.setDefectInventoryPercentage(String.format("%.2f", Math.min(defectPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setAgedInventoryPercentage(String.format("%.2f", Math.min(oldProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryStockx(String.format("%.2f", Math.min(stockxPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryGoat(String.format("%.2f", Math.min(goatProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryKnet(String.format("%.2f", Math.min(knetProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryKC(String.format("%.2f", Math.min(kcProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryEBay(String.format("%.2f", Math.min(ebayProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryPOIZON(String.format("%.2f", Math.min(poizonProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventoryTIKTOK(String.format("%.2f", Math.min(tiktokProductPercentage, 100.0)));
        adminHomeBiUserActiveListingsVo.setListedTotalInventorySURGE(String.format("%.2f", Math.min(surgeProductPercentage, 100.0)));

        return adminHomeBiUserActiveListingsVo;
    }

    /**
     * 查询商家的费率
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:queryUserPlat:'+#userId+':'+#shopIds",
            unless = "#result == null || (#result != null && #result.size() == 0)")
    public List<AdminHomeBiUserPlatFeeVo> queryUserPlat(Long userId, Set<Integer> shopIds) {
        return dashboardAdminMapper.selectUserFee(userId, shopIds);
    }

    /**
     * 查询商家的提现历史记录
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:queryCashoutHistory:'+#dto+':'+#shopIds",
            unless = "#result == null || (#result != null && #result.records.size() == 0)")
    public IPage<AdminHomeBiUserCashoutHistoryVo> quereyAdminHomeBiUserCashoutHistoryVo(AdminHomeBiUserCashoutHistoryDTO dto, Set<Integer> shopIds) {
        return dashboardAdminMapper.selectSysBillByUserId(new Page<>(dto.getCurrent(), dto.getSize()), dto.getUserId(), shopIds);
    }

    /**
     * 查询用户信息
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:queryUserInfo:'+#userId+':'+#shopIds", unless = "#result == null")
    public AdminHomeBiUserInfoVo queryUserInfo(Long userId, Set<Integer> shopIds) {
        AdminHomeBiUserInfoVo adminHomeBiUserListVo = dashboardAdminMapper.selectUserInfo(userId, shopIds);

        // 用户注册年龄
        StringBuilder accountAge = new StringBuilder();
        if (!ObjectUtils.isEmpty(adminHomeBiUserListVo.getYears())
                && adminHomeBiUserListVo.getYears() > 0) {
            if (adminHomeBiUserListVo.getYears() == 1) {
                accountAge.append(adminHomeBiUserListVo.getYears()).append(" Year ");
            } else {
                accountAge.append(adminHomeBiUserListVo.getYears()).append(" Years ");
            }

        }
        if (!ObjectUtils.isEmpty(adminHomeBiUserListVo.getMonths())
                && adminHomeBiUserListVo.getMonths() > 0) {
            if (adminHomeBiUserListVo.getMonths() == 1) {
                accountAge.append(adminHomeBiUserListVo.getMonths()).append(" Month ");
            } else {
                accountAge.append(adminHomeBiUserListVo.getMonths()).append(" Months ");
            }
        }
        if (!ObjectUtils.isEmpty(adminHomeBiUserListVo.getDays())
                && adminHomeBiUserListVo.getDays() > 0) {
            if (adminHomeBiUserListVo.getDays() == 1) {
                accountAge.append(adminHomeBiUserListVo.getDays()).append(" Day ");
            } else {
                accountAge.append(adminHomeBiUserListVo.getDays()).append(" Days ");
            }

        }
        adminHomeBiUserListVo.setAccountAge(accountAge.toString());
        return adminHomeBiUserListVo;
    }

    /**
     * 查询用户信息
     */
    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:bi:queryPlatformDistribution:'+#userId+':'+#beginDate+':'+#endDate+':'+#shopIds",
            unless = "#result == null || (#result != null && #result.size() == 0)")
    public List<AdminHomeBiDataTrendVo> queryPlatformDistribution(Long userId, Date beginDate, Date endDate, Set<Integer> shopIds) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        // 这里需要展示 stockFlex
        wareIds.removeIf(e -> e == 20034);
        List<AdminHomeBiDataTrendVo> adminHomeBiDataTrendVos = dashboardAdminMapper.selectTotalByPlatform(userId, beginDate, endDate, wareIds, shopIds);
        return fillingNullData(adminHomeBiDataTrendVos);
    }

    /**
     * 填充空数据
     *
     * @param orgList 原始数据
     * @return 填充后的数据(没有数据的补零处理)
     */
    private List<AdminHomeBiDataTrendVo> fillingNullData(List<AdminHomeBiDataTrendVo> orgList) {
        List<AdminHomeBiDataTrendVo> resultList = new ArrayList<>();
        Map<String, AdminHomeBiDataTrendVo> resultMap = orgList.stream()
                .collect(Collectors.toMap(vo -> vo.getPlatform().getValue(), Function.identity()));
        for (SourcePlatformService platform : SourcePlatformService.values()) {
            AdminHomeBiDataTrendVo vo = resultMap.get(platform.getValue());
            if (vo == null) {
                vo = new AdminHomeBiDataTrendVo();
                vo.setDate(null);
                vo.setTotalGMV(BigDecimal.ZERO);
                vo.setTotalSaleOrders(0L);
                vo.setTotalReshipOrders(0L);
                vo.setTotalProfit(BigDecimal.ZERO);
                vo.setTotalInbound(0L);
                vo.setPlatform(platform);
            }
            resultList.add(vo);
        }
        return resultList;
    }

    @Override
    @ReadOnly(value = "bi_read")
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig",
            key = "'knet:bi:queryAdminHomeBiUserListVo:'+#beginDate+':'+#endDate+':'+#userName+':'+#userUid+':'+#current+':'+#size+':'+#sortField+':'+#sortOrder+':'+#customerManager+':'+#shopIds+':'+#referrerName",
            unless = "#result == null || (#result != null && #result.records.size() == 0)")
    public IPage<AdminHomeBiUserListVo> queryAdminHomeBiUserListVo(Date beginDate
            , Date endDate
            , String userName
            , String userUid
            , Integer current
            , Integer size
            , String sortField
            , String sortOrder
            , String customerManager
            , Set<Integer> shopIds
            , String referrerName) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        Integer limitStart = (current - 1) * size; // 分页的起始条数

        List<AdminHomeBiUserListVo> userInfoList = new ArrayList<>();
        List<Integer> userIds = new ArrayList<>();
        // 日期不对商家进行筛选
//        if (ObjectUtils.isEmpty(beginDate) && ObjectUtils.isEmpty(endDate)) {
        userInfoList = dashboardAdminMapper.selectUserNmaeOrUidOrAccountManager(new ArrayList<>()
                , 0
                , 99999
                , userUid
                , null
                , null
                , userName
                , customerManager
                , shopIds
                , referrerName
        );
        if (ObjectUtils.isEmpty(userInfoList)) {
            log.info("queryAdminHomeBiUserListVo userInfoList is null ,userName={},userUid={}", userName, userUid);
            return new Page<>();
        }
        userIds = userInfoList.stream().map(AdminHomeBiUserListVo::getUserId).collect(Collectors.toList());
//        }

        // 存在排序时，先按照分页的数量进行查询用户范围以及顺序
        if (!ObjectUtils.isEmpty(sortField)) {
            switch (sortField) {
                case "totalGMV":
                    List<AdminHomeBiUserListVo> totalGMVList = dashboardAdminMapper.selectUserTotalGMV(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(totalGMVList)) {
                        userIds = totalGMVList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, totalGMVList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "totalOrder":
                    List<AdminHomeBiUserListVo> totalOrderList = dashboardAdminMapper.selectUserTotalOrder(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(totalOrderList)) {
                        userIds = totalOrderList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, totalOrderList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "profitGenerated":
                    List<AdminHomeBiUserListVo> profitGeneratedList = dashboardAdminMapper.selectUserProfitGenerated(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(profitGeneratedList)) {
                        userIds = profitGeneratedList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, profitGeneratedList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "serviceRevenue":
                    List<AdminHomeBiUserListVo> serviceRevenueList = dashboardAdminMapper.selectUserServiceRevenue(userIds, limitStart, size, beginDate, endDate, sortOrder, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(serviceRevenueList)) {
                        userIds = serviceRevenueList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, serviceRevenueList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "totalInventory": // 总库存 不使用日期筛选
                    List<AdminHomeBiUserListVo> totalInventoryList = dashboardAdminMapper.selectUserTotal(userIds, limitStart, size, null, null, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(totalInventoryList)) {
                        userIds = totalInventoryList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, totalInventoryList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "inboundTotal":
                    List<AdminHomeBiUserListVo> inboundTotalList = dashboardAdminMapper.selectUserInbound(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(inboundTotalList)) {
                        userIds = inboundTotalList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, inboundTotalList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "aovPerOrder":
                    List<AdminHomeBiUserListVo> aovPerOrderList = dashboardAdminMapper.selectUserAovOrder(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(aovPerOrderList)) {
                        userIds = aovPerOrderList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, aovPerOrderList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "issueOrderRate":
                    List<AdminHomeBiUserListVo> issueOrderRateList = dashboardAdminMapper.selectUserissueOrderRate(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(issueOrderRateList)) {
                        userIds = issueOrderRateList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, issueOrderRateList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                case "fakeOrderRate":
                    List<AdminHomeBiUserListVo> fakeOrderRateList = dashboardAdminMapper.selectFakeTotalRate(userIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIds);
                    if (ObjectUtils.isEmpty(userIds) && !ObjectUtils.isEmpty(fakeOrderRateList)) {
                        userIds = fakeOrderRateList.stream().map(AdminHomeBiUserListVo::getShopId).collect(Collectors.toList());
                    }
                    return this.setAdminHomeBiUserListVo(userIds, fakeOrderRateList, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
                default:
                    break;
            }
        } else {
            // 不存在排序时，先按照分页的数量进行查询用户范围以及顺序
            return this.setAdminHomeBiUserListVo(userIds, null, beginDate, endDate, limitStart, size, sortOrder, userInfoList, userName, userUid, current, customerManager, shopIds, referrerName);
        }

        return new Page<>();
    }

    @Override
    public FinancialOverviewVo financialOverview(Set<Integer> shopIds) {
        // 钱包总金额
        QueryWrapper<SysMoney> moneyQw = new QueryWrapper<>();
        moneyQw.select("ifnull(sum(money),0) money");
        moneyQw.eq("type", 5);
        moneyQw.eq("del_flag", 0);
        moneyQw.notIn("user_id", shopIds);
        SysMoney money = iSysMoneyService.getOne(moneyQw, false);
        // 财务流水统计
        TransactionAmountVo transactionAmountVo = dashboardAdminMapper.selectTransactionAmount(shopIds);
        // 商家余额 = 商家钱包余额总和
        BigDecimal knetSellerWalletBalance = money.getMoney();
        // 商家当天进行中的金额
        BigDecimal pendingNewWalletBalanceAddedToday = transactionAmountVo.getIncomeAmountPendingDay()
                .subtract(transactionAmountVo.getExpenseAmountPendingDay());
        // 商家当天已完成的金额
        BigDecimal completedNewWalletBalanceAddedToday = transactionAmountVo.getIncomeAmountCompletedDay()
                .subtract(transactionAmountVo.getExpenseAmountCompletedDay());
        // 商家当天的总金额
        BigDecimal totalNewWalletBalanceAddedToday = pendingNewWalletBalanceAddedToday.add(completedNewWalletBalanceAddedToday);
        // touch 卖家钱包余额
        BigDecimal touchSellerWalletBalance = touchUtils.getUserBalance();
        // 组装
        FinancialOverviewVo financialOverviewVo = new FinancialOverviewVo()
                .setTouchSellerWalletBalance(touchSellerWalletBalance)
                .setKnetSellerWalletBalance(knetSellerWalletBalance)
                .setPendingNewWalletBalanceAddedToday(pendingNewWalletBalanceAddedToday)
                .setCompletedNewWalletBalanceAddedToday(completedNewWalletBalanceAddedToday)
                .setTotalNewWalletBalanceAddedToday(totalNewWalletBalanceAddedToday);
        // 总金额
        if (!ObjectUtils.isEmpty(financialOverviewVo.getTouchSellerWalletBalance())
                && !ObjectUtils.isEmpty(financialOverviewVo.getKnetSellerWalletBalance())) {
            financialOverviewVo.setTotalSellerWalletBalance(financialOverviewVo.getTouchSellerWalletBalance().add(financialOverviewVo.getKnetSellerWalletBalance()));
        }

        return financialOverviewVo;
    }

    @Override
    public FinancialStatisticsVo financialStatistics(Date beginDate, Date endDate, Set<Integer> shopIds) {

        FinancialStatisticsVo financialStatisticsVo = dashboardAdminMapper.selectFinancialStatistics(beginDate, endDate, shopIds);
        if (!ObjectUtils.isEmpty(financialStatisticsVo)
                && !ObjectUtils.isEmpty(financialStatisticsVo.getKnetDeduction())
                && !ObjectUtils.isEmpty(financialStatisticsVo.getReturnDeduction())
        ) {
            financialStatisticsVo.setTotalDeductionAmount(financialStatisticsVo.getKnetDeduction().add(financialStatisticsVo.getReturnDeduction()));
        }


        return financialStatisticsVo;
    }

    @ReadOnly(value = "bi_read")
    public IPage<AdminHomeBiUserListVo> setAdminHomeBiUserListVo(List<Integer> shopIds
            , List<AdminHomeBiUserListVo> listVos
            , Date beginDate
            , Date endDate
            , Integer limitStart
            , Integer size
            , String sortOrder
            , List<AdminHomeBiUserListVo> userListVos
            , String userName
            , String userUid
            , Integer current
            , String customerManager
            , Set<Integer> shopIdSets
            , String referrerName
    ) {
        List<Integer> wareIds = this.getIgnoredWareIdsForBI();
        int total = shopIds.size();

        Map<Integer, AdminHomeBiUserListVo> userInfoMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> totalGMVMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> totalOrderMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> profitGeneratedMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> serviceRevenueMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> totalInventoryMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> inboundTotalMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> aovPerOrderMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> issueOrderRateMap = new HashMap<>();
        Map<Integer, AdminHomeBiUserListVo> fakeOrderRateMap = new HashMap<>();

        // userListVos 为空 查询全部的用户信息
        if (ObjectUtils.isEmpty(userListVos)) {
            // 指定ID时，起始ID为0
            if (!ObjectUtils.isEmpty(shopIds)) {
                limitStart = 0;
            }

            userListVos = dashboardAdminMapper.selectUserNmaeOrUidOrAccountManager(shopIds
                    , limitStart
                    , 9999999
                    , null
                    , null
                    , null
                    , null
                    , null
                    , shopIdSets
                    , referrerName
            );
            userInfoMap = userListVos.stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getUserId, e -> e, (val1, val2) -> val1));
        } else {
            userInfoMap = userListVos.stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getUserId, e -> e, (val1, val2) -> val1));
            // listVos 更新
            if (ObjectUtils.isEmpty(listVos)) {
                listVos = userListVos;
            }
        }

        // listVos 对象空时，传入对象
        if (ObjectUtils.isEmpty(listVos)) {
            listVos = userListVos;
        }

        // shopIds 对象空时，传入对象
        if (ObjectUtils.isEmpty(shopIds)) {
            shopIds = userListVos.stream().map(AdminHomeBiUserListVo::getUserId).collect(Collectors.toList());
        }

        if (listVos.size() < size) { // 考虑不满一页的情况下，补足一页

            List<AdminHomeBiUserListVo> listAll = dashboardAdminMapper.selectUserNmaeOrUidOrAccountManager(new ArrayList<>()
                    , 0
                    , 99999
                    , userUid
                    , null
                    , null
                    , userName
                    , customerManager
                    , shopIdSets
                    , referrerName
            );

            total = listAll.size();

            List<AdminHomeBiUserListVo> list = new ArrayList<>();
            List<Integer> listVoIds = listVos.stream()
                    .filter(e -> !ObjectUtils.isEmpty(e) && !ObjectUtils.isEmpty(e.getShopId()))
                    .map(AdminHomeBiUserListVo::getShopId)
                    .collect(Collectors.toList());
            for (AdminHomeBiUserListVo vo : listAll) {
                if (list.size() > size) {
                    break;
                }
                if (!list.contains(vo) && !listVoIds.contains(vo.getUserId())) {
                    listVoIds.add(vo.getUserId());
                    list.add(vo);
                }
            }
            // 补充空闲
            int diff = size - listVos.size();
            for (int i = 0; i < diff; i++) {
                if (!ObjectUtils.isEmpty(list)
                        && i < list.size()
                        && !ObjectUtils.isEmpty(list.get(i))) {
                    listVos.add(list.get(i));
                }
            }
            // 更新 shopIds
            shopIds.addAll(listVos.stream().map(AdminHomeBiUserListVo::getUserId).collect(Collectors.toList()));
            // 更新 userInfoMap
            userInfoMap.putAll(listVos.stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getUserId, e -> e, (val1, val2) -> val1)));

        }

        if (!ObjectUtils.isEmpty(shopIds) && !ObjectUtils.isEmpty(userInfoMap) && !ObjectUtils.isEmpty(listVos) && !ObjectUtils.isEmpty(listVos.get(0))) {


            if (ObjectUtils.isEmpty(listVos.get(0).getTotalGMV())) {
                totalGMVMap = dashboardAdminMapper.selectUserTotalGMV(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                totalGMVMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getTotalOrder())) {
                totalOrderMap = dashboardAdminMapper.selectUserTotalOrder(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                totalOrderMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getProfitGenerated())) {
                profitGeneratedMap = dashboardAdminMapper.selectUserProfitGenerated(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                profitGeneratedMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getServiceRevenue())) {
                serviceRevenueMap = dashboardAdminMapper.selectUserServiceRevenue(shopIds, limitStart, size, beginDate, endDate, sortOrder, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                serviceRevenueMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getTotalInventory())) {
                // 不使用日期筛选
                totalInventoryMap = dashboardAdminMapper.selectUserTotal(shopIds, limitStart, size, null, null, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                totalInventoryMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getInboundTotal())) {
                inboundTotalMap = dashboardAdminMapper.selectUserInbound(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                inboundTotalMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getAovPerOrder())) {
                aovPerOrderMap = dashboardAdminMapper.selectUserAovOrder(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                aovPerOrderMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }
            if (ObjectUtils.isEmpty(listVos.get(0).getIssueOrderRate())) {
                issueOrderRateMap = dashboardAdminMapper.selectUserissueOrderRate(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                issueOrderRateMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }

            if (ObjectUtils.isEmpty(listVos.get(0).getFakeOrderRate())) {
                fakeOrderRateMap = dashboardAdminMapper.selectFakeTotalRate(shopIds, limitStart, size, beginDate, endDate, sortOrder, wareIds, shopIdSets)
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            } else {
                fakeOrderRateMap = listVos
                        .stream().collect(Collectors.toMap(AdminHomeBiUserListVo::getShopId, e -> e, (val1, val2) -> val1));
            }

        }

        // 组装
        for (AdminHomeBiUserListVo vo : listVos) {
            if (ObjectUtils.isEmpty(vo.getUserId()) && !ObjectUtils.isEmpty(vo.getShopId())) {
                vo.setUserId(vo.getShopId());
            }
            if (userInfoMap.containsKey(vo.getUserId())) {
                vo.setUserId(userInfoMap.get(vo.getUserId()).getUserId());
                vo.setUserName(userInfoMap.get(vo.getUserId()).getUserName());
                vo.setUserUid(userInfoMap.get(vo.getUserId()).getUserUid());
                vo.setShopId(userInfoMap.get(vo.getUserId()).getShopId());
                vo.setAccountManager(userInfoMap.get(vo.getUserId()).getAccountManager());
                vo.setReferrerName(userInfoMap.get(vo.getUserId()).getReferrerName());
            } else {
                log.info("setAdminHomeBiUserListVo for vo is null vo={} userInfoMap ={}", JSON.toJSONString(vo), JSON.toJSONString(userInfoMap));
                continue;
            }

            if (totalGMVMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(totalGMVMap.get(vo.getUserId()).getTotalGMV())) {
                vo.setTotalGMV(totalGMVMap.get(vo.getUserId()).getTotalGMV());
            } else {
                log.info("setAdminHomeBiUserListVo for vo is null vo={} userInfoMap ={}", JSON.toJSONString(vo), JSON.toJSONString(userInfoMap));
                vo.setTotalGMV("0");
            }

            if (totalOrderMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(totalOrderMap.get(vo.getUserId()).getTotalOrder())) {
                vo.setTotalOrder(totalOrderMap.get(vo.getUserId()).getTotalOrder());
            } else {
                log.info("setAdminHomeBiUserListVo for TotalOrder is null vo={} TotalOrder ={}", JSON.toJSONString(vo), JSON.toJSONString(totalOrderMap));
                vo.setTotalOrder("0");
            }

            if (profitGeneratedMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(profitGeneratedMap.get(vo.getUserId()).getProfitGenerated())) {
                vo.setProfitGenerated(profitGeneratedMap.get(vo.getUserId()).getProfitGenerated());
            } else {
                log.info("setAdminHomeBiUserListVo for ProfitGenerated is null vo={} ProfitGenerated ={}", JSON.toJSONString(vo), JSON.toJSONString(profitGeneratedMap));
                vo.setProfitGenerated("0");
            }

            if (serviceRevenueMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(serviceRevenueMap.get(vo.getUserId()).getServiceRevenue())) {
                vo.setServiceRevenue(serviceRevenueMap.get(vo.getUserId()).getServiceRevenue());
            } else {
                log.info("setAdminHomeBiUserListVo for ServiceRevenue is null vo={} ServiceRevenue ={}", JSON.toJSONString(vo), JSON.toJSONString(serviceRevenueMap));
                vo.setServiceRevenue("0");
            }

            if (totalInventoryMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(totalInventoryMap.get(vo.getUserId()).getTotalInventory())) {
                vo.setTotalInventory(totalInventoryMap.get(vo.getUserId()).getTotalInventory());
            } else {
                log.info("setAdminHomeBiUserListVo for TotalInventory is null vo={} TotalInventory ={}", JSON.toJSONString(vo), JSON.toJSONString(totalInventoryMap));
                vo.setTotalInventory("0");
            }

            if (inboundTotalMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(inboundTotalMap.get(vo.getUserId()).getInboundTotal())) {
                vo.setInboundTotal(inboundTotalMap.get(vo.getUserId()).getInboundTotal());
            } else {
                log.info("setAdminHomeBiUserListVo for InboundTotal is null vo={} InboundTotal ={}", JSON.toJSONString(vo), JSON.toJSONString(inboundTotalMap));
                vo.setInboundTotal("0");
            }

            if (aovPerOrderMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(aovPerOrderMap.get(vo.getUserId()).getAovPerOrder())) {
                vo.setAovPerOrder(aovPerOrderMap.get(vo.getUserId()).getAovPerOrder());
            } else {
                log.info("setAdminHomeBiUserListVo for AovPerOrder is null vo={} AovPerOrder ={}", JSON.toJSONString(vo), JSON.toJSONString(aovPerOrderMap));
                vo.setAovPerOrder("0");
            }

            if (issueOrderRateMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(issueOrderRateMap.get(vo.getUserId()).getIssueOrderRate())) {
                vo.setIssueOrderRate(issueOrderRateMap.get(vo.getUserId()).getIssueOrderRate());
            } else {
                log.info("setAdminHomeBiUserListVo for issueOrderRateMap is null vo={} issueOrderRateMap ={}", JSON.toJSONString(vo), JSON.toJSONString(issueOrderRateMap));
                vo.setIssueOrderRate("0");
            }

            if (fakeOrderRateMap.containsKey(vo.getUserId()) && !ObjectUtils.isEmpty(fakeOrderRateMap.get(vo.getUserId()).getFakeOrderRate())) {
                vo.setFakeOrderRate(fakeOrderRateMap.get(vo.getUserId()).getFakeOrderRate());
            } else {
                log.info("setAdminHomeBiUserListVo for fakeOrderRateMap is null vo={} fakeOrderRateMap ={}", JSON.toJSONString(vo), JSON.toJSONString(fakeOrderRateMap));
                vo.setFakeOrderRate("0");
            }

        }

        IPage<AdminHomeBiUserListVo> adminHomeBiUserListVoPage = new Page<>(
                current,
                size,
                total
        );
        adminHomeBiUserListVoPage.setRecords(listVos);

        return adminHomeBiUserListVoPage;
    }

    @Override
    @ReadOnly(value = "bi_read")
    public List<AdminHomeSelfOperatedDataVo> querySelfOperatedData(TableDataSearchDto dto) {
        dto.setWareIdList(Collections.singletonList(20034));
        return dashboardMapper.querySelfOperatedData(dto);
    }

    @Override
    @Cacheable(cacheNames = "HotSkuRankDataForB2B", key = "'knet:bi:queryProductTop20:'+#dto", unless = "#result == null || (#result != null && #result.size() == 0)")
    public List<HotSkuRankVo> queryProductTop20ByCache(TableDataSearchDto dto) {
        List<PlatformOrderVo> platformOrderVos = dashboardMapper.selectProductTop20(dto);
        return IntStream.range(0, platformOrderVos.size())
                .mapToObj(i -> {
                    PlatformOrderVo vo = platformOrderVos.get(i);
                    HotSkuRankVo hotSkuRankVo = new HotSkuRankVo();
                    hotSkuRankVo.setSku(vo.getSku());
                    hotSkuRankVo.setRank(i + 1);
                    return hotSkuRankVo;
                })
                .collect(Collectors.toList());
    }

    @ReadOnly
    @Override
    @Cacheable(value = "TikTokSourcingOpportunitiesData", key = "'cached:' + #request.bestSeller.name() + ':' + #request.sku + ':' + #request.pageNo + ':' + #request.pageSize", unless = "#result == null")
    public IPage<SourcingOpportunitiesVo> queryTiktokSourcingOpportunities(SourcingOpportunitiesReq request) {
        IPage<SourcingOpportunitiesVo> result = new Page<>(request.getQueryStartPage(), request.getPageSize());
        Integer count = dashboardMapper.queryTiktokSourcingOpportunitiesCount(request);
        List<SourcingOpportunitiesDto> opportunities = dashboardMapper.queryTiktokSourcingOpportunities(request);
        List<SourcingOpportunitiesVo> opportunitiesList = new ArrayList<>(20);
        if (CollUtil.isNotEmpty(opportunities)) {
            List<String> skuList = opportunities
                    .stream()
                    .map(SourcingOpportunitiesDto::getSku)
                    .distinct()
                    .map(StringProcessedUtils::processSkuFormat)
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(skuList)) {
                fillTikTokPriceInfo(skuList, opportunities);
            }
        }
        opportunitiesList = opportunities.stream().map(SourcingOpportunitiesVo::create).collect(Collectors.toList());
        result.setRecords(opportunitiesList);
        result.setTotal(count);
        result.setCurrent(request.getPageNo());
        return result;
    }

    /**
     * 填充 TikTok 价格信息 最低最高价
     *
     * @param skuList       skus
     * @param opportunities opportunities
     */
    private void fillTikTokPriceInfo(List<String> skuList, List<SourcingOpportunitiesDto> opportunities) {
        // 查询TikTok价格数据(最低价，最高价)
        List<Map<String, Object>> tiktokPrices = dashboardMapper.queryTiktokPrices(skuList);
        Map<String, Map<String, Integer>> tiktokPriceMap = tiktokPrices.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("sku"),
                        map -> {
                            Map<String, Integer> priceData = new HashMap<>(20);
                            priceData.put("maxPrice", (Integer) map.get("maxPrice"));
                            priceData.put("lowestPrice", (Integer) map.get("lowestPrice"));
                            return priceData;
                        }
                ));
        opportunities.forEach(dto -> {
            if (tiktokPriceMap.containsKey(dto.getSku())) {
                Map<String, Integer> priceData = tiktokPriceMap.get(dto.getSku());
                dto.setMaxPrice(String.format("%.2f", Math.ceil(priceData.get("maxPrice") / 100.0)));
                dto.setLowestPrice(String.format("%.2f", Math.ceil(priceData.get("lowestPrice") / 100.0)));
            } else {
                dto.setMaxPrice("9999.00");
                dto.setLowestPrice("9999.00");
            }
        });
    }

    @ReadOnly
    @Override
    @Cacheable(value = "TikTokSourcingOpportunitiesDetailData", key = "'cached:' + #request.sku", unless = "#result == null")
    public List<SourcingOpportunitiesDetailVo> queryTiktokSourcingOpportunitiesDetails(SourcingOpportunitiesDetailReq request) {
        List<SourcingOpportunitiesDetailVo> sourcingOpportunitiesDetailVos = dashboardMapper.queryTiktokSourcingOpportunitiesDetails(request);
        sourcingOpportunitiesDetailVos = sortBySpec(sourcingOpportunitiesDetailVos);
        return sourcingOpportunitiesDetailVos;
    }

    /**
     * 排序商品尺码
     *
     * @param originate 商品详情
     * @return 排序后的商品详情
     */
    List<SourcingOpportunitiesDetailVo> sortBySpec(List<SourcingOpportunitiesDetailVo> originate) {
        if (CollUtil.isEmpty(originate)) {
            return originate;
        }
        return originate
                .stream()
                .filter(item -> item.getSpec() != null)
                .sorted(Comparator.comparing(item -> {
                    String spec = item.getSpec();
                    try {
                        return Double.parseDouble(spec);
                    } catch (NumberFormatException e) {
                        // 非数字情况，使用字符串本身，但确保排序在数字之后
                        return Double.MAX_VALUE;
                    }
                }, Comparator.nullsLast(Double::compareTo)))
                .collect(Collectors.toList());
    }
}
