package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.pay.PayDto;
import com.hzjm.common.pay.PayService;
import com.hzjm.common.pay.PayVo;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysAccount;
import com.hzjm.service.entity.SysBill;
import com.hzjm.service.service.IPayService;
import com.hzjm.service.service.ISysAccountService;
import com.hzjm.service.service.ISysBillService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

import com.hzjm.common.utils.DateTimeUtils;

import java.util.Map;

@Slf4j
@Component
public class PayServiceImpl implements IPayService {

    @Autowired
    private ISysAccountService iSysAccountService;

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private PayService payService;

    public com.hzjm.common.pay.IPayService iPayService = new com.hzjm.common.pay.IPayService() {
        @Override
        public void dealOrderSuccess(String outTradeNo) {
            orderSuccess(outTradeNo);
        }

        @Override
        public void closeOrder(String outTradeNo) {
            orderClose(outTradeNo);
        }

        @Override
        public void refundOrder(String outTradeNo, BigDecimal refundAmount) {
            orderRefund(outTradeNo, refundAmount);
        }
    };

    @Override
    public PayVo getPayInfo(String ip, Integer id, Integer payType, Integer type, String attach) {
        PayDto dto = new PayDto();

        // 创建支付订单
        SysBill bill = new SysBill();
        bill.setIeType(-1);
        bill.setPayType(payType);
        bill.setUserId(JwtContentHolder.getUserId());

        // 支付人的微信
        if (payType == PayDto.TypeWxSmall) {
            SysAccount account = iSysAccountService.getOne(Wrappers.<SysAccount>lambdaQuery()
                    .eq(SysAccount::getAccountType, 4).eq(SysAccount::getUserType, 2)
                    .eq(SysAccount::getUserId, bill.getUserId())
                    .orderByDesc(SysAccount::getGmtModify).last("limit 1"));
            if (ObjectUtils.isEmpty(account)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("您尚未绑定微信"));
            }
            dto.setOpenId(account.getAccount());
        }

        switch (type) {
            case 1:
                /*
                // 订单信息
                SysOrder order = iSysOrderService.getById(id);
                if (ObjectUtils.isEmpty(order)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("无效的订单"));
                } else if (order.getStatus() != StatusCheck) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该订单无需支付"));
                }

                bill.setRelationId(order.getId());
                bill.setRelationType(SysBill.TypeCash);
                dto.setOutTradeNo(getOutTradeNo(payType));
                dto.setTotalFee(SysConstants.cash);
                dto.setBody("押金交付");
  */
                break;
        }

        bill.setAttach(attach);
        bill.setTotalFee(dto.getTotalFee());
        bill.setOutTradeNo(dto.getOutTradeNo());
        iSysBillService.saveSysBill(bill);

        // 下单基本信息
        dto.setIp(ip);
        dto.setNonceStr(BaseUtils.getRandomStr(32));
        dto.setPayType(payType);

        PayVo payVo = null;
        if (payType < 10) {
            throw new BaseException(LanguageConfigService.i18nForMsg("暂未开通该类支付"));
        } else if (payType < 20) {
            payVo = payService.wxPay(dto);
        } else if (payType < 30) {
            payVo = payService.aliPay(dto);
        } else if (payType < 40) {
            payVo = payService.unionPay(dto);
        } else {
            throw new BaseException(LanguageConfigService.i18nForMsg("暂未开通该类支付"));
        }
        payVo.setOutTradeNo(dto.getOutTradeNo());
        return payVo;
    }

    @Override
    public Integer getPayStatus(String outTradeNo) {
        // 根据outTradeNo查询支付类型
        SysBill bill = iSysBillService.getByOutTradeNo(outTradeNo);
        if (ObjectUtils.isEmpty(outTradeNo)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("无效的流水号"));
        }
        return payService.getPayStatus(outTradeNo, bill.getPayType(), iPayService);
    }

    @Override
    public String wxPayCallback(HttpServletRequest request) {
        return payService.wxPayCallback(request, iPayService);
    }

    @Override
    public String aliCallback(HttpServletRequest request) {
        return payService.aliCallback(request, iPayService);
    }

    @Override
    public String unionCallback(HttpServletRequest request) {
        return payService.unionCallback(request, iPayService);
    }


    @Override
    public void orderSuccess(String outTradeNo) {
        log.info("开始处理[" + outTradeNo + "]账单业务...");
        SysBill bill = iSysBillService.getByOutTradeNo(outTradeNo);
        if (ObjectUtils.isEmpty(bill) || bill.getStatus() != 1) {
            return;
        }
        bill.setStatus(2);
        bill.setGmtModify(DateTimeUtils.getNow());
        bill.updateById();

        switch (bill.getRelationType()) {
            case 1:
                break;
        }

        log.info("[" + outTradeNo + "]账单业务处理完成...");

    }

    public void orderClose(String outTradeNo) {
        // 关闭订单
        log.info("支付取消，处理业务...");
        iSysBillService.update(Wrappers.<SysBill>lambdaUpdate()
                .eq(SysBill::getOutTradeNo, outTradeNo)
                .eq(SysBill::getStatus, 1)
                .set(SysBill::getStatus, 3));
    }

    @Override
    public void orderRefund(String outTradeNo, BigDecimal refundAmount) {
        // 退款
        log.info("业务中止，流水[" + outTradeNo + "]退款...");
        SysBill bill = iSysBillService.getByOutTradeNo(outTradeNo);
        if (ObjectUtils.isEmpty(bill)) {
            log.info("账单未找到");
            return;
        }

        if (bill.getPayType() > 10 && bill.getPayType() < 20) {
            Date date = DateTimeUtils.getNow();
            Calendar c = Calendar.getInstance();
            c.setTime(bill.getGmtModify());
            c.add(Calendar.YEAR, 1);
            if (date.after(c.getTime())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("微信支付只能退一年内的款项"));
            }
        }

        refundAmount = ObjectUtils.isEmpty(refundAmount) ? bill.getTotalFee() : refundAmount;
        if (refundAmount.compareTo(SysConstants.zero) <= 0) {
            return;
        }

        String attach = bill.getAttach();
        Map<String, String> paramMap = BaseUtils.queryToMap(attach);
        String outRefundNo = !ObjectUtils.isEmpty(paramMap.get("outRefundNo")) ?
                paramMap.get("outRefundNo") : getOutTradeNo(bill.getPayType());
        paramMap.put("outRefundNo", outRefundNo);

        iSysBillService.update(Wrappers.<SysBill>lambdaUpdate()
                .eq(SysBill::getId, bill.getId())
                .set(SysBill::getAttach, BaseUtils.mapToQuery(paramMap))
                .set(SysBill::getStatus, 3));

        String reason = "支付退款";
        switch (bill.getRelationType()) {
            case 1:
//                reason = reason + "：您已成为红娘";
                break;
        }
        payService.refund(bill.getPayType(), bill.getOutTradeNo(), outRefundNo, bill.getTotalFee(),
                refundAmount, reason);
    }

    private String getOutTradeNo(Integer payType) {
        return DateTimeUtils.format(DateTimeUtils.sdfFileSuffix, DateTimeUtils.getNow()) + String.format("%02d", payType) + BaseUtils.getRandomStr(4);
    }
}
