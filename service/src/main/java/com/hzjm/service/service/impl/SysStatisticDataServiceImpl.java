package com.hzjm.service.service.impl;

import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.math.BigDecimal;
import java.util.*;

import com.hzjm.common.utils.DateTimeUtils;

import java.util.function.Function;
import java.util.stream.Collectors;

import com.hzjm.common.model.TableDataListVo;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysStatisticDataPageDto;
import com.hzjm.service.model.VO.SysStatisticDataListVo;
import com.hzjm.service.model.VO.SysStatisticDataVo;
import com.hzjm.service.entity.SysStatisticData;
import com.hzjm.service.mapper.SysStatisticDataMapper;
import com.hzjm.service.service.ISysStatisticDataService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-06
 */
@Slf4j
@Service
public class SysStatisticDataServiceImpl extends ServiceImpl<SysStatisticDataMapper, SysStatisticData> implements ISysStatisticDataService {

    @Override
    public SysStatisticData getByIdWithoutLogic(Integer id) {
        SysStatisticData data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public SysStatisticDataVo getDetail(Integer id) {
        SysStatisticData data = getByIdWithoutLogic(id);

        SysStatisticDataVo vo = new SysStatisticDataVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysStatisticData(SysStatisticData dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysStatisticDataListVo> searchList(SysStatisticDataPageDto dto) {

        LambdaQueryWrapper<SysStatisticData> qw = Wrappers.<SysStatisticData>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysStatisticData::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysStatisticData::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysStatisticData::getGmtCreate, endTime);

        IPage<SysStatisticData> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysStatisticDataListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysStatisticDataListVo vo = new SysStatisticDataListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysStatisticDataListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysStatisticData> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

//            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysStatisticData> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<TableDataListVo> getTableList(TableDataSearchDto query, int dataType, Integer dataId) {
        List<TableDataListVo> voList = new ArrayList<>();
        LambdaQueryWrapper<SysStatisticData> qw = Wrappers.<SysStatisticData>lambdaQuery()
                .orderByAsc(SysStatisticData::getDateStr)
                .eq(SysStatisticData::getDataType, dataType)
                .eq(SysStatisticData::getDateType, query.getDateType())
                .ge(SysStatisticData::getDateStr, query.getBeginDate())
                .lt(SysStatisticData::getDateStr, query.getEndDate());

        if (ObjectUtils.isEmpty(dataId)) {
            qw.isNull(SysStatisticData::getDataId);
        } else {
            qw.eq(SysStatisticData::getDataId, dataId);
        }

        List<SysStatisticData> list = list(qw);

//        Map<String, String> dataMap = list.stream().collect(Collectors.toMap(SysStatisticData::getDateStr, SysStatisticData::getDataNum));
//        query.getDateStr().forEach(dateStr -> {
//            TableDataListVo vo = new TableDataListVo();
//            String dataNum = dataMap.get(dateStr);
//            if (ObjectUtils.isEmpty(dataNum)) {
//                vo.setDataNum("0");
//            } else {
//                vo.setDataNum(dataNum);
//            }
//            vo.setDateStr(dateStr);
//            voList.add(vo);
//        });

        Map<String, SysStatisticData> dataMap = list.stream()
                .collect(Collectors.toMap(
                        SysStatisticData::getDateStr,
                        Function.identity(), // 使用SysStatisticData对象本身作为值
                        (data1, data2) -> data1.getId() > data2.getId() ? data1 : data2 // 选择id较大的对象
                ));

        Map<String, String> latestDataMap = dataMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getDataNum() // 从SysStatisticData对象中获取dataNum
                ));

        query.getDateStr().forEach(dateStr -> {
            TableDataListVo vo = new TableDataListVo();
            String dataNum = latestDataMap.getOrDefault(dateStr, "0");
            vo.setDataNum(dataNum);
            vo.setDateStr(dateStr);
            voList.add(vo);
        });

        return voList;
    }

    @Override
    public List<TableDataListVo> getTableGroupList(TableDataSearchDto query, int dataType, List<Integer> dataIdList) {
        List<TableDataListVo> voList = new ArrayList<>();
        LambdaQueryWrapper<SysStatisticData> qw = Wrappers.<SysStatisticData>lambdaQuery()
                .orderByAsc(SysStatisticData::getDateStr)
                .eq(SysStatisticData::getDataType, dataType)
                .eq(SysStatisticData::getDateType, query.getDateType())
                .ge(SysStatisticData::getDateStr, query.getBeginDate())
                .lt(SysStatisticData::getDateStr, query.getEndDate());

        if (ObjectUtils.isEmpty(dataIdList)) {
            qw.isNull(SysStatisticData::getDataId);
        } else {
            qw.in(SysStatisticData::getDataId, dataIdList);
        }

        List<SysStatisticData> list = list(qw);
        Map<String, List<SysStatisticData>> dateGroup = list.stream().collect(Collectors.groupingBy(SysStatisticData::getDateStr));
        list.clear();

        query.getDateStr().forEach(dateStr -> {
            TableDataListVo vo = new TableDataListVo();
            List<SysStatisticData> items = dateGroup.get(dateStr);

            BigDecimal dataNum = SysConstants.zero;
            if(!ObjectUtils.isEmpty(items)) {
                for (SysStatisticData item : items) {
                    dataNum = dataNum.add(new BigDecimal(item.getDataNum()));
                }
            }
            vo.setDataNum(dataNum.toString());
            vo.setDateStr(dateStr);
            voList.add(vo);
        });

        return voList;
    }

    @Override
    public List<SysStatisticData> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
