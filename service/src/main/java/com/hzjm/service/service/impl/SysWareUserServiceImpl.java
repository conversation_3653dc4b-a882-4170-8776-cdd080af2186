package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import com.hzjm.common.utils.BaseUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.service.ISysUserService;
import com.hzjm.service.service.ISysWareUserPermissionService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysWareUserPageDto;
import com.hzjm.service.model.VO.SysWareUserListVo;
import com.hzjm.service.model.VO.SysWareUserVo;
import com.hzjm.service.mapper.SysWareUserMapper;
import com.hzjm.service.service.ISysWareUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 仓库人员 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Slf4j
@Service
public class SysWareUserServiceImpl extends ServiceImpl<SysWareUserMapper, SysWareUser> implements ISysWareUserService {

    @Autowired
    private ISysWareUserPermissionService iSysWareUserPermissionService;

    @Autowired
    private ISysUserService iSysUserService;

    @Override
    public SysWareUser getByIdWithoutLogic(Integer id) {
        SysWareUser data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该仓库人员"));
        }

        return data;
    }

    @Override
    public SysWareUserVo getDetail(Integer id) {
        SysWareUser data = getByIdWithoutLogic(id);

        SysWareUserVo vo = new SysWareUserVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareUser(SysWareUser dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            SysWareUser data = getById(dto.getId());
            if(ObjectUtils.isEmpty(data)) {
               throw new BaseException(LanguageConfigService.i18nForMsg("已移出仓库"));
            }

            // 移除权限
            iSysWareUserPermissionService.remove(Wrappers.<SysWareUserPermission>lambdaQuery()
                    .eq(SysWareUserPermission::getUserId, data.getUserId())
                    .eq(SysWareUserPermission::getWareId, data.getWareId()));

            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }

        if(!isDelete) {
            // 绑定权限
            iSysWareUserPermissionService.reset(dto.getId(), dto.getWareId(), dto.getUserId(), ObjectUtils.isEmpty(dto.getPermissionList()) ?
                    null : dto.getPermissionList().stream().filter(a -> {
                return ObjectUtils.isEmpty(a.getIsOwn()) || a.getIsOwn();
            }).map(SysPermission::getId).collect(Collectors.toList()));
        }

        return rs;
    }

    @Override
    public IPage<SysWareUserListVo> searchList(SysWareUserPageDto dto) {

        LambdaQueryWrapper<SysWareUser> qw = Wrappers.<SysWareUser>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareUser::getGmtCreate)
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysWareUser::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareUser::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getUserId()), SysWareUser::getUserId, dto.getUserId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareUser::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareUser::getGmtCreate, endTime);

        IPage<SysWareUser> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareUserListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            // 权限清单
            Map<Integer, List<SysPermission>> permMap = iSysWareUserPermissionService.group(dto.getWareId());

            // 用户信息
            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(pageResult.getRecords().stream().map(SysWareUser::getUserId).collect(Collectors.toList()));
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery()
                    .in(SysUser::getId, userIdList));
            Map<Integer, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getId, a -> a));

            pageResult.getRecords().forEach(data -> {
                SysWareUserListVo vo = new SysWareUserListVo();
                BeanUtils.copyProperties(data, vo);

                SysUser user = userMap.get(data.getUserId());
                if(!ObjectUtils.isEmpty(user)) {
                    vo.setAccount(user.getAccount());
                    vo.setNickname(user.getNickname());
                }

                vo.setPermissionList(permMap.get(data.getUserId()));
                voList.add(vo);
            });
        }

        IPage<SysWareUserListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWareUser> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysWareUser> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
