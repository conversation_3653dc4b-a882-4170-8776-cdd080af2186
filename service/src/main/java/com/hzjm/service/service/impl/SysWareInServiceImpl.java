package com.hzjm.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.infrastructure.service.IApiRequestService;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.constants.UpdateOrderEndPoint;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.ShopPackMapper;
import com.hzjm.service.mapper.SysWareInMapper;
import com.hzjm.service.model.DTO.SysWareInPageDto;
import com.hzjm.service.model.DTO.SysWareInProdPageDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.REGISTRATION_TIME_START;

/**
 * 入库单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Slf4j
@Service
public class SysWareInServiceImpl extends ServiceImpl<SysWareInMapper, SysWareIn> implements ISysWareInService {

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Resource
    private ISysRepairOrderService sysReairOrderService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private IShopPreService iShopPreService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private IShopPackProdService iShopPackProdService;

    @Autowired
    private AsyncImpl async;

    @Autowired
    SendEmailService sendEmailService;

    @Autowired
    ShopPackMapper shopPackMapper;

    @Resource
    ISysWareInService iSysWareInService;

    @Resource
    ISysFileService iSysFileService;

    @Resource
    ISysWareShelvesProdService iSysWareShelvesProdService;

    @Qualifier("apiRequestServiceImpl")
    @Resource
    IApiRequestService apiRequestService;

    private final PlatformTransactionManager transactionManager;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public SysWareInServiceImpl(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
    }

    @Override
    public SysWareIn getByIdWithoutLogic(Integer id) {
        SysWareIn data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该入库单"));
        }

        return data;
    }

    @Override
    public SysWareInVo getDetail(Integer id) {
        SysWareIn data = getByIdWithoutLogic(id);

        SysWareInVo vo = new SysWareInVo();
        BeanUtils.copyProperties(data, vo);

        vo.setProdList(iSysWareInProdService.listByIn(data));

        ShopUser shop = iShopUserService.getById(data.getShopId());
        if (!ObjectUtils.isEmpty(shop)) {
            vo.setShopUid(shop.getUid());
        }

        SysUser createBy = iSysUserService.getById(data.getCreateById());
        if (!ObjectUtils.isEmpty(createBy)) {
            vo.setCreateBy(createBy.getNickname());
        }

        SysUser checker = iSysUserService.getById(data.getCheckId());
        if (!ObjectUtils.isEmpty(checker)) {
            vo.setChecker(checker.getNickname());
        }
        // 退货原因  , 根据传入的入库表ID查询
        List<ShopPack> list = iShopPackService.listWithoutLogic(Wrappers.<ShopPack>lambdaQuery()
                .eq(ShopPack::getInId, id)
                .eq(ShopPack::getDelFlag, 0)
                .last("LIMIT 1")
                .orderByDesc(ShopPack::getGmtCreate)
        );
        vo.setReason(list.size() > 0 ? list.get(0).getReason() : "");
        log.info("SysSkuServiceImpl getDetail vo ={},id={}", JSON.toJSONString(vo), id);
        return vo;
    }

    /**
     * 入库
     * @param dto 入库
     * @param returnedLogNo 退回时的物流单号
     * @return
     */
    @Override
    @AcquireTaskLock(name = "saveSysWareIn", timeout = 2, blocking = true)
    public Boolean saveSysWareIn(SysWareIn dto, String returnedLogNo) {
        boolean rs = false;
        boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        // 使用redis，加上一个幂等性锁。key 为方法名字+物流单号为唯一键，当这个物流单号在处理中，碰到相同的物流单号请求进来，报错：【物流单号】
        // 正在处理中，请稍后
        // 物流单号是 Map<String, List<SysWareInProdListVo>> prodList 的 key
        String lockKey = null;
        if (!ObjectUtils.isEmpty(dto)
                && !ObjectUtils.isEmpty(dto.getProdList())
                && !ObjectUtils.isEmpty(dto.getProdList().keySet())) {
            lockKey = "knet:saveSysWareIn:" + dto.getProdList().keySet().stream().collect(Collectors.joining(","));
            Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 1, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(lock)) {
                throw new BaseException("该物流单号【" + lockKey + "】正在处理中，请稍后");
            }
        }

        // 手动获取事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);

        try {
            if (ObjectUtils.isEmpty(dto.getId())) {
                if (ObjectUtils.isEmpty(dto.getType())) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("请选择批次类型"));
                }

                if (ObjectUtils.isEmpty(dto.getBatchNo())) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("请填入批次编号"));
                }

                if (count(Wrappers.<SysWareIn>lambdaQuery().eq(SysWareIn::getBatchNo, dto.getBatchNo())) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该批次编号已被使用"));
                }
/*
            if (dto.getType() == 1) {
                // 扫码入库：包裹变更状态
                if(!iShopPackService.update(Wrappers.<ShopPack>lambdaUpdate()
                        .set(ShopPack::getGmtWare, DateTimeUtils.getNow())
                        .set(ShopPack::getStatus, 2)
                        .eq(ShopPack::getLogNo, dto.getBatchNo()))){
                    throw new BaseException(LanguageConfigService.i18nForMsg("该包裹未曾预报"));
                }
            }*/

                if (ObjectUtils.isEmpty(dto.getWareId())) {
                    dto.setWareId(JwtContentHolder.getWareId());
                }
                if (ObjectUtils.isEmpty(dto.getWareId())) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("入库仓库不明"));
                }

                dto.setCreateById(JwtContentHolder.getUserId());
                rs = this.save(dto);

                // 直接入库没有预报的退回批次
                if (!ObjectUtils.isEmpty(returnedLogNo)
                        && dto.getType().equals(SysConstants.SHOP_PRE_TYPE_RETURNED)) {
                    iShopPreService.verifyLogNo(new ArrayList<>(Collections.singletonList(returnedLogNo)));
                    // 同步给预报批次
                    ShopPre shopPre = new ShopPre()
                            .setDelFlag(0)
                            .setType(SysConstants.SHOP_PRE_TYPE_RETURNED)
                            .setShopId(dto.getShopId())
                            .setBatchNo(iSysCodePoolService.build(2, 1).get(0))
                            .setWareId(dto.getWareId())
                            .setInId(null)
                            .setNote("没有预报，直接入库RETURNED")
                            .setStatus(SysConstants.SHOP_PRE_STATUS_1)
                            .setReason(null);
                    iShopPreService.save(shopPre);
                    // 预报包裹
                    ShopPack shopPack = new ShopPack()
                            .setDelFlag(0)
                            .setType(SysConstants.SHOP_PRE_TYPE_RETURNED)
                            .setPreId(shopPre.getId())
                            .setInId(dto.getId())
                            .setShopId(dto.getShopId())
                            .setLogNo(returnedLogNo)
                            .setLogNoSuffix(returnedLogNo.substring(returnedLogNo.length() - 9))
                            .setNum(dto.getNum())
                            .setWareId(dto.getWareId())
                            .setGmtWare(new Date())
                            .setNote("没有预报，直接入库RETURNED")
                            .setStatus(SysConstants.SHOP_PRE_STATUS_2)
                            .setReason("没有预报，直接入库RETURNED")
                            .setGmtIn(new Date());
                    iShopPackService.save(shopPack);

                    if (!ObjectUtils.isEmpty(dto.getProdList())) {
                        dto.getProdList().forEach((k, v) -> v.forEach(prod -> {
                            // 预报包裹商品
                            ShopPackProd shopPackProd = new ShopPackProd()
                                    .setDelFlag(0)
                                    .setPackId(shopPack.getId())
                                    .setShopId(dto.getShopId())
                                    .setImg(prod.getImg())
                                    .setBrand(prod.getBrand())
                                    .setRemarks(prod.getRemarks())
                                    .setSku(prod.getSku())
                                    .setSpec(prod.getSpec())
                                    .setNum(1)
                                    .setCostPrice(null)
                                    .setSupply("没有预报，直接入库RETURNED")
                                    .setReturnedReason(null);
                            iShopPackProdService.save(shopPackProd);
                        }));
                    }
                }

                // 若为预报
                if (dto.getType() == 2) {
                    bindPre(dto.getId(), dto.getBatchNo());
                }
            } else if (isDelete) {
                SysWareIn data = getById(dto.getId());
                if (ObjectUtils.isEmpty(data)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("删除已完成"));
                }
                if (data.getStatus() == 3) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("入库已完成，无法删除"));
                }
                ShopPre pre = iShopPreService.getOne(Wrappers.<ShopPre>lambdaQuery().eq(ShopPre::getBatchNo, data.getBatchNo()));
                if (!ObjectUtils.isEmpty(pre) && pre.getStatus() != 3) {
                    // 回滚预约批次的状态
                    pre.setStatus(1);
                    pre.setGmtModify(DateTimeUtils.getNow());
                    pre.updateById();
                }

                List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                        .eq(ShopPack::getStatus, 2)
                        .eq(ShopPack::getInId, dto.getId()));
                if (!ObjectUtils.isEmpty(packList)) {
                    // 回滚包裹状态
                    iShopPackService.update(Wrappers.<ShopPack>lambdaUpdate()
                            .eq(ShopPack::getInId, dto.getId())
                            .eq(ShopPack::getStatus, 2)
                            .setSql(" in_id = null, status = 1, gmt_ware = null ")
                    );

                    // 未预报过的包裹，在批次删除后，同步清理
                    List<Integer> clearIdList = packList.stream().filter(a -> {
                        return ObjectUtils.isEmpty(a.getPreId());
                    }).map(ShopPack::getId).collect(Collectors.toList());
                    if (!ObjectUtils.isEmpty(clearIdList)) {
                        iShopPackService.remove(Wrappers.<ShopPack>lambdaQuery().in(ShopPack::getId, clearIdList));
                    }
                }

           /* if (!ObjectUtils.isEmpty(pre)) {
                if (iShopPackService.count(Wrappers.<ShopPack>lambdaQuery()
                        .eq(ShopPack::getPreId, pre.getId())
                        .in(ShopPack::getStatus, 2, 4)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该批次下存在包裹，无法删除"));
                }
            }
            // 若出库单下有包裹/商品，不允许删除
            if (iSysWareInProdService.count(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getInId, dto.getId())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("该批次下存在商品，无法删除"));
            }*/

                rs = baseMapper.deleteById(dto.getId()) > 0;
            } else {
                SysWareIn data = getById(dto.getId());
                if (!ObjectUtils.isEmpty(dto.getStatus()) && data.getStatus().intValue() != dto.getStatus()) {
                    // 商品查验
                    if (dto.getStatus() == 3) {
                        if (ObjectUtils.isEmpty(dto.getProdList())) {
                            throw new BaseException("至少要有一个商品");
                        }

                        List<String> oneIdList = new ArrayList<>();
                        dto.getProdList().keySet().forEach(logNo -> {
                            oneIdList.addAll(dto.getProdList().get(logNo).stream().map(SysWareInProdListVo::getOneId).collect(Collectors.toList()));
                        });

                        List<SysProd> existOneIdList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                                .select(SysProd::getOneId)
                                .in(SysProd::getOneId, oneIdList));
                        if (existOneIdList.size() > 0) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("The entry : ")
                                    + BaseUtils.listToStr("、", existOneIdList.stream().map(SysProd::getOneId).collect(Collectors.toList()))
                                    + " already exists in the system."
                            );
                        }
                        existOneIdList.clear();

                        List<SysCodePool> codeList = iSysCodePoolService.list(Wrappers.<SysCodePool>lambdaQuery().eq(SysCodePool::getType, 1).in(SysCodePool::getCode, oneIdList));
                        oneIdList.removeAll(codeList.stream().map(SysCodePool::getCode).collect(Collectors.toList()));
                        if (oneIdList.size() > 0) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("The list of invalid one id: ") +
                                    BaseUtils.listToStr("、", oneIdList));
                        }

                        dto.setCheckId(JwtContentHolder.getUserId());
                        dto.setStaffId(JwtContentHolder.getUserId());
                        ShopPre pre = iShopPreService.getOne(Wrappers.<ShopPre>lambdaQuery().eq(ShopPre::getBatchNo, data.getBatchNo()));

                        List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                                .ne(ShopPack::getStatus, 3)
                                .in(ShopPack::getLogNo, dto.getProdList().keySet().stream().collect(Collectors.toList())));
                        Map<Integer, ShopPack> packMap = packList.stream().collect(Collectors.toMap(ShopPack::getId, a -> a));
                        Map<String, ShopPack> packLogMap = packList.stream().collect(Collectors.toMap(ShopPack::getLogNo, a -> a));
                        packList.clear();

                        // 商品正式入库
                        List<SysWareInProd> prods = new ArrayList<>();
                        dto.getProdList().keySet().forEach(logNo -> {
                            ShopPack pack = packLogMap.get(logNo);
                            if (ObjectUtils.isEmpty(pack)) {
                                pack = new ShopPack();
                                pack.setId(0);
                            }

                            List<SysWareInProdListVo> prodList = dto.getProdList().get(logNo);
                            for (SysWareInProdListVo prod : prodList) {
                                SysWareInProd item = new SysWareInProd();
                                BeanUtils.copyProperties(prod, item);

                                if (ObjectUtils.isEmpty(prod.getOneId())) {
                                    throw new BaseException(LanguageConfigService.i18nForMsg("商品缺少oneId"));
                                }
                                if (ObjectUtils.isEmpty(prod.getSku())) {
                                    throw new BaseException(LanguageConfigService.i18nForMsg("商品缺少sku"));
                                }
                                if (ObjectUtils.isEmpty(prod.getSpec())) {
                                    throw new BaseException(LanguageConfigService.i18nForMsg("商品缺少spec"));
                                }

                                // OneId 生成规则：长度为13
                                if (prod.getOneId().length() > 14) {
                                    throw new BaseException(LanguageConfigService.i18nForMsg("oneId异常"));
                                }

                                item.setSku(item.getSku().trim());
                                item.setSpec(item.getSpec().trim().toUpperCase());
                                item.setPackId(pack.getId());
                                item.setSpecNum(BaseUtils.dealSizeStr(item.getSpec()));
                                item.setCheckRemark(prod.getCheckRemark());
                                prods.add(item);
                            }
                        });
                        packLogMap.clear();

                        // 保存商品
                        this.saveProd(data, prods, packMap, dto);

                        if (!ObjectUtils.isEmpty(pre)) {
                            pre.setStatus(4);
                            pre.setGmtModify(DateTimeUtils.getNow());
                            pre.updateById();
                        }
                        iShopPackService.update(Wrappers.<ShopPack>lambdaUpdate()
                                .set(ShopPack::getStatus, 4)
                                .set(ShopPack::getGmtIn, DateTimeUtils.getNow())
                                .eq(ShopPack::getInId, data.getId()));

                        dto.setNum(iSysWareInProdService.count(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getInId, data.getId())));

                        // 仅当预报类型为return 才同步 platform order 的状态
                        if (dto.getType() == 3) {
                            // 向 订单同步 包裹已经到货状态
                            // 但此接口的失败与否都不能影响到 方法后续的执行
                            CompletableFuture.runAsync(() -> {
                                try {
                                    apiRequestService.doRequest(UpdateOrderEndPoint.confirmOrderReturned(returnedLogNo));
                                } catch (Exception e) {
                                    log.error("同步 return order 订单状态失败", e);
                                }
                            });
                        }

                        // 保存面单照片
                        if (!ObjectUtils.isEmpty(dto.getImageList())){
                            iSysFileService.resetFile(dto.getImageList(), dto.getId(), SysConstants.SYS_FILE_TYPE_8);
                        }

                        // 包裹查验通知 - 邮件
                        ShopUser shop = iShopUserService.getById(data.getShopId());
                        if (!ObjectUtils.isEmpty(shop) && !ObjectUtils.isEmpty(shop.getEmail())) {
                            String shopName = ObjectUtils.isEmpty(shop.getRealname()) ? "" : shop.getRealname();
                            switch (data.getType()) {
                                case 1:
                                    List<ShopPack> packs = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery().eq(ShopPack::getInId, data.getId()));
                                    packs.forEach(pack -> {
                                        Map<String, String> map = new HashMap<>();
                                        map.put("email", shop.getEmail()); // 邮箱
                                        map.put("userName", shopName); // 用户名
                                        map.put("logNo", pack.getLogNo()); // 物流单号
                                        sendEmailService.oncomingShipmentSendEmail(map);
                                    });
                                    break;
                                case 2:
                                    Map<String, String> map = new HashMap<>();
                                    map.put("email", shop.getEmail()); // 邮箱
                                    map.put("userName", shopName); // 用户名
                                    map.put("logNo", ""); // 没有物流单号
                                    sendEmailService.oncomingShipmentSendEmail(map);
                            }
                        }
                    }
                }
                rs = baseMapper.updateById(dto) > 0;

            }
            // 提交事务
            transactionManager.commit(status);
            return rs;
        } catch (Exception e) {
            e.printStackTrace();
            // 回滚事务
            transactionManager.rollback(status);
            throw new BaseException(e.getMessage());
        } finally {
            // 释放锁
            if (!ObjectUtils.isEmpty(lockKey)) {
                redisTemplate.delete(lockKey);
            }
        }
    }

    private void saveProd(SysWareIn in, List<SysWareInProd> prods, Map<Integer, ShopPack> packMap, SysWareIn dto) {
        Date now = DateTimeUtils.getNow();
        Integer userId = JwtContentHolder.getUserId();

        String concat = "#=#";
        Map<Integer, Map<String, JSONObject>> packProdMap = new HashMap<>();
        if (in.getType() == 1) {
            List<Integer> packIdList = new ArrayList<>();
            packIdList.addAll(packMap.keySet().stream().collect(Collectors.toList()));
            List<ShopPackProd> packProdList = iShopPackProdService.list(Wrappers.<ShopPackProd>lambdaUpdate().in(ShopPackProd::getPackId, packIdList));
            Map<Integer, List<ShopPackProd>> packProdGroup = packProdList.stream().collect(Collectors.groupingBy(ShopPackProd::getPackId));

            packProdGroup.keySet().forEach(packId -> {
                Map<String, JSONObject> prodGroup = packProdMap.get(packId);
                if (ObjectUtils.isEmpty(prodGroup)) {
                    prodGroup = new HashMap<>();
                    packProdMap.put(packId, prodGroup);
                }

                for (ShopPackProd a : packProdGroup.get(packId)) {
                    JSONObject prodInfo = prodGroup.get(a.getSku() + concat + a.getSpec());
                    if (ObjectUtils.isEmpty(prodInfo)) {
                        prodInfo = new JSONObject();
                        prodInfo.put("ban", false);
                        prodInfo.put("costPrice", a.getCostPrice());
                        prodInfo.put("supply", a.getSupply());
                    } else {
                        BigDecimal costPrice = prodInfo.getBigDecimal("costPrice");
                        if (costPrice.compareTo(a.getCostPrice()) != 0) {
                            prodInfo.put("ban", true);
                        }
                        String supply = prodInfo.getString("supply");
                        if (!supply.equals(a.getSupply())) {
                            prodInfo.put("ban", true);
                        }
                    }
                }
            });
        }

        ShopUser shop = iShopUserService.getById(in.getShopId());

        prods.forEach(item -> {
            SysProd prod = new SysProd();

            prod.setOneId(item.getOneId());
            prod.setBrand(item.getBrand());
            prod.setSku(item.getSku());
            prod.setPku(item.getPku());
            prod.setSpec(item.getSpec());
            prod.setRemarks(item.getRemarks());
            prod.setImg(item.getImg());
            prod.setWareId(in.getWareId());
            prod.setShopId(in.getShopId());

            // 默认成本/货源
            ShopPack pack = packMap.get(item.getPackId());
            if (!ObjectUtils.isEmpty(pack)) {
                Map<String, JSONObject> prodGroup = packProdMap.get(pack.getId());
                if (!ObjectUtils.isEmpty(prodGroup)) {
                    JSONObject prodInfo = prodGroup.get(item.getSku() + concat + item.getSpec());
                    if (!ObjectUtils.isEmpty(prodInfo)) {
                        if (!prodInfo.getBoolean("ban")) {
                            prod.setSupply(prodInfo.getString("supply"));
                            prod.setCostPrice(prodInfo.getBigDecimal("costPrice"));
                        }
                    }
                }
            }
            prod.setSupply(in.getSupply());

            iSysProdService.saveSysProd(prod);

            item.setInId(in.getId());
            item.setInType(in.getType());
            item.setProdId(prod.getId());
            item.setCheckId(userId);
            item.setGmtModify(now);
            item.setGmtCreate(now);
            iSysWareInProdService.saveSysWareInProd(item);

            // 存在维修项目时保存
            if (!ObjectUtils.isEmpty(item.getRepairFlag()) && item.getRepairFlag() == RepairFlagEnum.CAN_REPAIR){ // 这里对维修标记进行一次判断
                if(ObjectUtils.isEmpty(item.getRepairIdList())){
                    throw new BaseException("当前商品已标记为可维修，但未配置任何维修项目。请检查并添加相应的维修项目。");
                }
                sysReairOrderService.resetByRepairIdList(prod.getId(),item.getOneId(),item.getRepairIdList());
            }

            // search初始化
            SysProdSearch search = new SysProdSearch();
            search.setSearchType(1);
            search.setProdId(prod.getId());
            search.setOneId(prod.getOneId());
            search.setCostPrice(prod.getCostPrice());
            search.setSupply(prod.getSupply());
            search.setRemarks(prod.getRemarks());
            search.setSku(prod.getSku());
            search.setSpec(item.getSpecNum());
            search.setBrand(prod.getBrand());
            search.setStatus(1);

            search.setCheckRemark(item.getCheckRemark());// 验货备注
            search.setRepairFlag(item.getRepairFlag()); //维修标记
            // 关联的物流单号
            if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getLogNoRelated())) {
                search.setInLogNoRelated(dto.getLogNoRelated());
            }

            search.setShopId(prod.getShopId());
            if (!ObjectUtils.isEmpty(shop)) {
                search.setShopUid(shop.getUid());
                search.setShopName(shop.getRealname());
            }

            search.setWareId(prod.getWareId());
            search.setInBatchNo(in.getBatchNo());
            search.setCheckResult(item.getCheckResult());
            if (!ObjectUtils.isEmpty(pack)) {
                search.setInLogNo(pack.getLogNo());
            }
            search.setGmtIn(now);
            search.insert();

            // 商品事件：入库
            SysProdEvent event = new SysProdEvent();
            event.setProdId(prod.getId());
            event.setShopId(prod.getShopId());
            event.setDescription("商品入库");
            event.setType(SysProdEvent.TypeIn);
            event.setRelationId(item.getId());
            event.insert();
        });

    }

    @Override
    public IPage<SysWareInListVo> searchList(SysWareInPageDto dto) {
        LambdaQueryWrapper<SysWareIn> qw = Wrappers.<SysWareIn>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareIn::getGmtModify)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysWareIn::getId, dto.getIdList())
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysWareIn::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareIn::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getType()), SysWareIn::getType, dto.getType())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysWareIn::getStatus, dto.getStatus())
                .like(!ObjectUtils.isEmpty(dto.getBatchNo()), SysWareIn::getBatchNo, dto.getBatchNo());

        // isReturn 字段只有 null 和 true  ，没有 fasle
        if (ObjectUtils.isEmpty(dto.getIsReturn()) || !dto.getIsReturn()){
            qw.isNull(SysWareIn::getIsReturn);
        }else{
            qw.eq(SysWareIn::getIsReturn, true);
        }

        if (!ObjectUtils.isEmpty(dto.getBeginTime()) || !ObjectUtils.isEmpty(endTime)) {
            if (JwtContentHolder.getRoleType() == 1) {
                qw.and(a -> a.and(a1 -> a1.ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareIn::getGmtModify, dto.getBeginTime())
                        .lt(!ObjectUtils.isEmpty(endTime), SysWareIn::getGmtModify, endTime)).or().ne(SysWareIn::getStatus, 3));
            }
            if (JwtContentHolder.getRoleType() == 4) {
                qw.ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareIn::getGmtCreate, dto.getBeginTime())
                        .lt(!ObjectUtils.isEmpty(endTime), SysWareIn::getGmtCreate, endTime);
            }
        }

        Integer roleType = JwtContentHolder.getRoleType();
        if (roleType == 4) {
//            qw.eq(SysWareIn::getCreateById, JwtContentHolder.getUserId());
            qw.apply(" if(staff_id is null, create_by_id = " + JwtContentHolder.getUserId() + ",staff_id = " + JwtContentHolder.getUserId() + ") ");
            qw.eq(ObjectUtils.isEmpty(dto.getWareId()), SysWareIn::getWareId, JwtContentHolder.getWareId());
        }

        IPage<SysWareIn> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareInListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysWareInListVo vo = new SysWareInListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysWareInListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWareIn> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareIn> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    @AcquireTaskLock(name = "scanByLogNo", key = "#logNo", timeout = 3, exceptionMessage = "该物流单号尚未处理完成，请稍后重试")
    public ShopPackScanVo scanByLogNo(String logNo,Boolean isReturn) {
        Integer wareId = JwtContentHolder.getWareId();
        if (logNo.length() < 9) {
            throw new BaseException(LanguageConfigService.i18nForMsg("运单号至少要有9位"));
        }
        String logNoSuffix = logNo.substring(logNo.length() - 9);
        ShopPack pack = iShopPackService.getOne(Wrappers.<ShopPack>lambdaQuery().ne(ShopPack::getStatus, 3).eq(ShopPack::getLogNoSuffix, logNoSuffix)
                , false);
        if (ObjectUtils.isEmpty(pack)) {
            // 包裹未预报：静默生成预报批次以及相应包裹
//            throw new BaseException(1001, "包裹未预报");
            pack = new ShopPack();
            pack.setLogNo(logNo);
            pack.setType(1);
//            pack.setShopId(data.getShopId());
//            pack.setNote(data.getNote());
            pack.setWareId(wareId);
            pack.setStatus(1);
/*
            ShopPre pre = new ShopPre();
            pre.setWareId(wareId);
            pre.setStatus(2);
            pre.setBatchNo(iSysCodePoolService.build(2, 1).get(0));
            pre.setType(1);
            pre.setPackList(new ArrayList<>(Arrays.asList(pack)));
            iShopPreService.saveShopPre(pre);

            pack = iShopPackService.getOne(Wrappers.<ShopPack>lambdaQuery().eq(ShopPack::getLogNo, logNo));
            */
        } else {// 包裹已经存在时，判断是否已经入库，返回详情
            List<SysPackWareInVo> packWareInVos = baseMapper.selectWareIn(logNo);
            if (!ObjectUtils.isEmpty(packWareInVos) && packWareInVos.size() > 0) {
                // janet2024 在12月13号已经使用了9632001960960780914400282710201409物流单号做了入库，生成了 SP61210056 批次
                StringBuilder message = new StringBuilder();
                message.append(ObjectUtils.isEmpty(packWareInVos.get(0).getNickName()) ? "" : packWareInVos.get(0).getNickName());
                message.append(LanguageConfigService.i18nForMsg("在"));
                message.append(ObjectUtils.isEmpty(packWareInVos.get(0).getGmtCreate()) ? "" : packWareInVos.get(0).getGmtCreate());
                message.append(LanguageConfigService.i18nForMsg("已经使用了"));
                message.append(ObjectUtils.isEmpty(logNo) ? "" : logNo);
                message.append(LanguageConfigService.i18nForMsg("物流单号做了入库，生成了"));
                message.append(ObjectUtils.isEmpty(packWareInVos.get(0).getBatchNo()) ? "" : packWareInVos.get(0).getBatchNo());
                message.append(LanguageConfigService.i18nForMsg("批次"));
                throw new BaseException(message.toString());
            }
        }

        SysWareIn in = getById(pack.getInId());

        ShopPreVo preVo = null;
        if (!ObjectUtils.isEmpty(pack.getPreId())) {
            preVo = iShopPreService.getDetail(pack.getPreId());
        }
        /*
        if (ObjectUtils.isEmpty(preVo)) {
            // 包裹没有批次信息
//            throw new BaseException(LanguageConfigService.i18nForMsg("包裹已失效"));
            preVo = new ShopPreVo();
            preVo.setBatchNo(iSysCodePoolService.build(2, 1).get(0));
            preVo.setType(1);
            preVo.setPackList(new ArrayList<>(Arrays.asList(pack)));
        }  else if (ObjectUtils.isEmpty(in)) {
            in = getOne(Wrappers.<SysWareIn>lambdaQuery().eq(SysWareIn::getBatchNo, preVo.getBatchNo()));
        }*/

        Boolean isNew = false;
        if (!ObjectUtils.isEmpty(in)) {
//            if (pack.getStatus() != 1) {
//                throw new BaseException(LanguageConfigService.i18nForMsg("包裹无需入库"));
//            }
        } else {
            // 首次扫描包裹，根据预报批次静默生成入库单
            in = new SysWareIn();
            in.setWareId(wareId);
            in.setNum(0);
            if (isReturn){
                in.setType(SysConstants.SHOP_PRE_TYPE_RETURNED);
                in.setIsReturn(true);
            }else{
                in.setType(pack.getType());
            }
            in.setShopId(pack.getShopId());
            in.setStatus(1);
            if (!ObjectUtils.isEmpty(preVo)
                    && count(Wrappers.<SysWareIn>lambdaQuery().eq(SysWareIn::getBatchNo, preVo.getBatchNo())) == 0) {

                if (!ObjectUtils.isEmpty(pack.getType()) && pack.getType().equals(3)) {
                    in.setBatchNo(iSysCodePoolService.build(24, 1).get(0));
                }else if (isReturn){
                    in.setBatchNo(iSysCodePoolService.build(24, 1).get(0));
                }else {
                    in.setBatchNo(preVo.getBatchNo());
                }
            } else {
                int typeNumber = 21;
                if (!ObjectUtils.isEmpty(pack.getType()) && pack.getType().equals(3)) {
                    typeNumber = 24;
                }
                // 退货接口进来的，设置成为RT的批次号
                if (isReturn){
                    typeNumber = 24;
                }
                in.setBatchNo(iSysCodePoolService.build(typeNumber, 1).get(0));
            }
            saveSysWareIn(in, null);

            // 包裹入库通知,发送邮件
            ShopUser shop = iShopUserService.getById(pack.getShopId());
            if (!ObjectUtils.isEmpty(shop) && !ObjectUtils.isEmpty(shop.getEmail()) && !ObjectUtils.isEmpty(pack.getLogNo())) {
                String shopName = ObjectUtils.isEmpty(shop.getRealname()) ? "" : shop.getRealname();
                Map<String, String> map = new HashMap<>();
                map.put("email", shop.getEmail()); // 邮箱
                map.put("userName", shopName); // 用户名
                map.put("trackingNo", pack.getLogNo()); // 编号
                sendEmailService.incomingShipmentSendEmail(map);
            }

            isNew = true;
        }

        if (pack.getStatus() == 1) {
            // 扫描后包裹状态变更
            pack.setInId(in.getId());
            pack.setWareId(wareId);
            pack.setGmtWare(DateTimeUtils.getNow());
            pack.setStatus(2);
            iShopPackService.saveShopPack(pack);
        }

        if (!ObjectUtils.isEmpty(preVo)) {
            ShopPack finalPack = pack;
            if (preVo.getPackList().stream().filter(a -> {
                return a.getStatus() == 1 && a.getId() != finalPack.getId().intValue();
            }).collect(Collectors.toList()).size() == 0) {
                // 若为最后一个待扫描的包裹，则更新预报批次&入库批次的状态
                iShopPreService.update(Wrappers.<ShopPre>lambdaUpdate()
                        .eq(ShopPre::getId, preVo.getId())
                        .eq(ShopPre::getStatus, 1)
                        .set(ShopPre::getStatus, 2));
            }
        }
        // 包裹入库，直接更新入库批次的状态
        if (ObjectUtils.isEmpty(in.getStatus()) || in.getStatus() == 1) {
            in.setStatus(2);
            in.updateById();
        }

        ShopPackScanVo vo = new ShopPackScanVo();
        BeanUtils.copyProperties(pack, vo);

//        vo.setProdList(iShopPackProdService.list(Wrappers.<ShopPackProd>lambdaQuery()
//                .eq(ShopPackProd::getPackId, pack.getId())));
        vo.setBatchNo(in.getBatchNo());
        vo.setInId(in.getId());
        vo.setStatus(in.getStatus());
        vo.setIsNew(isNew);
        return vo;
    }

    @Override
    public Boolean bindShop(Integer inId, Integer shopId) {
        SysWareIn in = getById(inId);
        if (ObjectUtils.isEmpty(in) || !ObjectUtils.isEmpty(in.getShopId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("该批次已有归属人"));
        }
        ShopPre pre = iShopPreService.getOne(Wrappers.<ShopPre>lambdaQuery().eq(ShopPre::getBatchNo, in.getBatchNo()));
        if (!ObjectUtils.isEmpty(pre)) {
            iShopPreService.update(Wrappers.<ShopPre>lambdaUpdate()
                    .eq(ShopPre::getBatchNo, in.getBatchNo())
                    .set(ShopPre::getShopId, shopId));

            iShopPackService.update(Wrappers.<ShopPack>lambdaUpdate()
                    .eq(ShopPack::getPreId, pre.getId())
                    .set(ShopPack::getShopId, shopId));
        }

        update(Wrappers.<SysWareIn>lambdaUpdate().eq(SysWareIn::getId, inId).isNull(SysWareIn::getShopId)
                .set(SysWareIn::getShopId, shopId));
        iShopPackService.update(Wrappers.<ShopPack>lambdaUpdate()
                .isNull(ShopPack::getShopId)
                .eq(ShopPack::getInId, inId)
                .set(ShopPack::getShopId, shopId));

        List<SysWareInProd> inProds = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getInId, in.getId()));
        if (!ObjectUtils.isEmpty(inProds)) {
            List<Integer> prodIdList = inProds.stream().map(SysWareInProd::getProdId).collect(Collectors.toList());

            ShopUser shop = iShopUserService.getById(shopId);
            if (!ObjectUtils.isEmpty(shop)) {
                iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                        .in(SysProd::getId, prodIdList)
                        .isNull(SysProd::getShopId)
                        .set(SysProd::getShopId, shop.getId()));

                iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                        .in(SysProdSearch::getProdId, prodIdList)
                        .eq(SysProdSearch::getSearchType, 1)
                        .isNull(SysProdSearch::getShopId)
                        .set(SysProdSearch::getShopId, shop.getId())
                        .set(SysProdSearch::getShopUid, shop.getUid())
                        .set(SysProdSearch::getShopName, shop.getRealname()));
            }
        }

        // 包裹入库通知
        ShopUser shop = iShopUserService.getById(shopId);
        if (!ObjectUtils.isEmpty(shop) && !ObjectUtils.isEmpty(shop.getEmail())) {
            String shopName = ObjectUtils.isEmpty(shop.getRealname()) ? "" : shop.getRealname();
            List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery().eq(ShopPack::getInId, inId));
            packList.forEach(pack -> {
                if (!ObjectUtils.isEmpty(pack.getLogNo())) {

                    Map<String, String> map = new HashMap<>();
                    map.put("email", shop.getEmail()); // 邮箱
                    map.put("userName", shopName); // 用户名
                    map.put("trackingNo", pack.getLogNo()); // 编号
                    sendEmailService.incomingShipmentSendEmail(map);
//                    String content =
//                            "Hi, Dear " + shopName + ",\n\n" +
//                                    "Your package has been delivered and well-received by our warehouse.\n" +
//                                    "Received tracking ID: " + pack.getLogNo() + ". \n" +
//                                    "\n" +
//                                    "Hang tie! We will examine your item and post its status to your account shortly!\n" +
//                                    "\n" +
//                                    "KNETGROUP";
//                    async.sendMail(shop.getEmail(), content, "Package Received Notification - KNETGROUP");
                }
            });
        }

        return true;
    }

    @Override
    public Boolean bindPre(Integer inId, String preBatchNo) {
        ShopPre pre = iShopPreService.getOne(Wrappers.<ShopPre>lambdaQuery().eq(ShopPre::getBatchNo, preBatchNo));
        if (ObjectUtils.isEmpty(pre)) {
//            throw new BaseException(LanguageConfigService.i18nForMsg("该批次未预报"));
            return false;
        }

        if (!ObjectUtils.isEmpty(pre.getInId())) {
//            throw new BaseException(LanguageConfigService.i18nForMsg("该批次已绑定入库批次"));
            return false;
        }

        iShopPreService.update(Wrappers.<ShopPre>lambdaUpdate()
                .eq(ShopPre::getInId, inId)
                .eq(ShopPre::getId, pre.getId()));

        return true;
    }

    /**
     * 清除 stockx flex 预报生成的对应数据
     */
    @Override
    @Transactional
    public void removeStockxFlexWareIn(String trackingNo) {
        log.info("SysWareInServiceImpl removeStockxFlexWareIn start trackingNo ={} ", trackingNo);
        Assert.notNull(trackingNo, "trackingNo is not null");
        List<SysProdSearch> searches = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                .select(SysProdSearch::getProdId, SysProdSearch::getOneId)
                .eq(SysProdSearch::getInLogNo, trackingNo)
        );
        Assert.notNull(searches, "searches is not null");
        Assert.isTrue(searches.size() > 0, "searches size is 0");
        List<Integer> preId = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                .select(ShopPack::getPreId)
                .eq(ShopPack::getLogNo, trackingNo)
        ).stream().map(ShopPack::getPreId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(preId)) {
            log.info("SysWareInServiceImpl removeStockxFlexWareIn preId is null");
        }
        List<Integer> prodIds = searches.stream().map(SysProdSearch::getProdId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(prodIds)) {
            log.info("SysWareInServiceImpl removeStockxFlexWareIn prodIds is null");
        }
        List<String> oneIds = searches.stream().map(SysProdSearch::getOneId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(oneIds)) {
            log.info("SysWareInServiceImpl removeStockxFlexWareIn oneIds is null");
        }
        List<Integer> shopPackIds = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                .select(ShopPack::getId)
                .eq(ShopPack::getLogNo, trackingNo)
        ).stream().map(ShopPack::getId).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(shopPackIds)) {
            log.info("SysWareInServiceImpl removeStockxFlexWareIn shopPackIds is null");
        }
        iSysProdService.remove(Wrappers.<SysProd>lambdaQuery().in(!ObjectUtils.isEmpty(oneIds), SysProd::getOneId, oneIds));
        iShopPreService.remove(Wrappers.<ShopPre>lambdaQuery().in(!ObjectUtils.isEmpty(preId), ShopPre::getId, preId));
        iShopPackService.remove(Wrappers.<ShopPack>lambdaQuery().eq(ShopPack::getLogNo, trackingNo));
        iShopPackProdService.remove(Wrappers.<ShopPackProd>lambdaQuery().in(!ObjectUtils.isEmpty(shopPackIds), ShopPackProd::getPackId, shopPackIds));
        iSysWareInService.remove(Wrappers.<SysWareIn>lambdaQuery().eq(SysWareIn::getLogNoRelated, trackingNo));
        iSysWareInProdService.remove(Wrappers.<SysWareInProd>lambdaQuery().in(!ObjectUtils.isEmpty(prodIds), SysWareInProd::getProdId, prodIds));
        iSysProdSearchService.remove(Wrappers.<SysProdSearch>lambdaQuery().in(!ObjectUtils.isEmpty(prodIds), SysProdSearch::getProdId, prodIds));
        iSysWareShelvesProdService.remove(Wrappers.<SysWareShelvesProd>lambdaQuery().in(!ObjectUtils.isEmpty(prodIds), SysWareShelvesProd::getProdId, prodIds));
        log.info("SysWareInServiceImpl removeStockxFlexWareIn end");
    }

    /**
     * 根据 stockX flex 预报来生成对应的数据
     *
     * @param userId     用户 id
     * @param trackingNo stockX Flex package tracking Number
     * @param prods      包含的 商品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StockxFlexProd> stockxFlexWareIn(Integer userId, String trackingNo, List<StockxFlexProd> prods) {
        /*1. 生成 sys pre 和 sys pack 和 sys pack prod 数据
               2. 生成 sys ware in 和 sys ware in prod 的数据 且 入库的仓库为 20034 stockX Flex
               3. 按 StockxFlexProd 的 sku 和 size 来生成 对应的 sys prod 和 sys prod search 数据
               4. 将sys prod 的 one id 反绑定到 StockxFlexProd 上 并返回 */
        log.info("SysWareInServiceImpl stockxFlexWareIn status userID ={} trackingNo = {} prods={}", userId, trackingNo, JSON.toJSONString(prods));

        Assert.notNull(userId, "user Id is not null");
        Assert.notNull(trackingNo, "trackingNo Id is not null");
        Assert.notNull(prods, "prods Id is not null");
        Assert.isTrue(prods.size() > 0, "prods size must be greater than 0");
        Assert.isTrue(trackingNo.length() > 9, "trackingNo length must be greater than 9");


        // 入库的仓库为 20034 stockX Flex
        Integer wareId = 20034;
        Integer shelvesId = 26614;

        ShopUser shopUser = iShopUserService.getOne(Wrappers.<ShopUser>lambdaQuery()
                        .eq(ShopUser::getId, userId)
                , false);

        Assert.notNull(shopUser, "shopUser Id is not null");
        Assert.notNull(shopUser.getUid(), "user Uid Id is not null");
        Assert.notNull(shopUser.getRealname(), "user Realname Id is not null");

        // 预报批次
        ShopPre shopPre = new ShopPre()
                .setDelFlag(0)
                .setType(SysConstants.SHOP_PRE_TYPE_STOCKX_FLEX)
                .setShopId(userId)
                .setBatchNo(iSysCodePoolService.build(2, 1).get(0))
                .setWareId(wareId)
                .setInId(null)
                .setNote("STOCKX_FLEX")
                .setStatus(SysConstants.SHOP_PRE_STATUS_4)
                .setReason(null);

        iShopPreService.save(shopPre);

        // 预报包裹
        ShopPack shopPack = new ShopPack()
                .setDelFlag(0)
                .setType(SysConstants.SHOP_PRE_TYPE_STOCKX_FLEX)
                .setPreId(shopPre.getId())
                .setInId(null)
                .setShopId(userId)
                .setLogNo(trackingNo)
                .setLogNoSuffix(trackingNo.substring(trackingNo.length() - 9))
                .setNum(prods.size())
                .setWareId(wareId)
                .setGmtWare(new Date())
                .setNote("STOCKX_FLEX")
                .setStatus(SysConstants.SHOP_PRE_STATUS_4)
                .setReason(null)
                .setGmtIn(new Date());

        iShopPackService.save(shopPack);

        // 入库单
        SysWareIn sysWareIn = new SysWareIn()
                .setDelFlag(0)
                .setWareId(wareId)
                .setCreateById(userId)
                .setStaffId(null)
                .setCheckId(null)
                .setType(SysConstants.SHOP_PRE_TYPE_STOCKX_FLEX)
                .setBatchNo(iSysCodePoolService.build(24, 1).get(0))
                .setShopId(userId)
                .setSupply("STOCKX_FLEX")
                .setNum(prods.size())
                .setStatus(SysConstants.WARE_IN_STATUS_3)
                .setLogNoRelated(trackingNo);

        iSysWareInService.saveSysWareIn(sysWareIn, null);

        for (StockxFlexProd prod : prods) {
            String oneId = iSysCodePoolService.build(1, 1).get(0);

            // 组装 StockxFlexProd 的one ID
            prod.setOneId(oneId);
            prod.setGmtModify(DateTimeUtils.getNow());

            // 预报包裹商品
            ShopPackProd shopPackProd = new ShopPackProd()
                    .setDelFlag(0)
                    .setPackId(shopPack.getId())
                    .setShopId(userId)
                    .setImg(prod.getImg())
                    .setBrand(prod.getBrand())
                    .setRemarks(prod.getProductName())
                    .setSku(prod.getSku())
                    .setSpec(prod.getSize())
                    .setNum(1)
                    .setCostPrice(null)
                    .setSupply("STOCKX_FLEX")
                    .setReturnedReason(null);

            iShopPackProdService.save(shopPackProd);

            // 商品
            SysProd sysProd = new SysProd()
                    .setDelFlag(0)
                    .setShopId(userId)
                    .setOneId(oneId)
                    .setImg(prod.getImg())
                    .setPku(prod.getPku())
                    .setSku(prod.getSku())
                    .setSpec(prod.getSize())
                    .setBrand(prod.getBrand())
                    .setRemarks(prod.getProductName())
                    .setSupply("STOCKX_FLEX")
                    .setCostPrice(null)
                    .setStatus(SysConstants.PRODUCT_STATUS_2)
                    .setOddNo(null)
                    .setWareId(wareId)
                    .setGmtIn(new Date())
                    .setGmtOut(null);

            iSysProdService.save(sysProd);

            // 入库单商品
            SysWareInProd sysWareInProd = new SysWareInProd()
                    .setDelFlag(0)
                    .setWareId(wareId)
                    .setInId(sysWareIn.getId())
                    .setInType(SysConstants.SHOP_PRE_TYPE_STOCKX_FLEX)
                    .setPackId(shopPack.getId())
                    .setCheckId(null)
                    .setProdId(sysProd.getId())
                    .setGmtPay(new Date())
                    .setCheckResult(SysConstants.WARE_IN_CHECK_RESULT_1)
                    .setOneId(oneId)
                    .setImg(prod.getImg())
                    .setPku(prod.getPku())
                    .setSku(prod.getSku())
                    .setSpec(prod.getSize())
                    .setBrand(prod.getBrand())
                    .setRemarks(prod.getProductName())
                    .setCheckRemark("STOCKX_FLEX");

            iSysWareInProdService.save(sysWareInProd);

            // 货架商品
            SysWareShelvesProd sysWareShelvesProd = new SysWareShelvesProd()
                    .setDelFlag(0)
                    .setWareId(wareId)
                    .setShelvesId(shelvesId)
                    .setProdId(sysProd.getId())
                    .setUserId(userId)
                    .setUserName(shopUser.getNickname());

            iSysWareShelvesProdService.save(sysWareShelvesProd);

            // 商品筛选
            SysProdSearch sysProdSearch = new SysProdSearch()
                    .setDelFlag(0)
                    .setProdId(sysProd.getId())
                    .setDealId(null)
                    .setShopId(userId)
                    .setOneId(oneId)
                    .setSku(prod.getSku())
                    .setPku(prod.getPku())
                    .setSpec(BaseUtils.dealSizeStr(prod.getSize()))
                    .setBrand(prod.getBrand())
                    .setRemarks(prod.getProductName())
                    .setSupply("STOCKX_FLEX")
                    .setCostPrice(null)
                    .setStatus(SysConstants.PRODUCT_STATUS_2)
                    .setWareId(wareId)
                    .setShelvesId(null)
                    .setCheckResult(SysConstants.WARE_IN_CHECK_RESULT_1)
                    .setShopUid(shopUser.getUid())
                    .setShopName(shopUser.getRealname())
                    .setOddNo(null)
                    .setOddType(null)
                    .setInLogNo(trackingNo)
                    .setInBatchNo(sysWareIn.getBatchNo())
                    .setGmtIn(new Date())
                    .setThirdPlatId(null)
                    .setThirdPlatName(null)
                    .setPlatOrderNo(null)
                    .setOutNo(null)
                    .setGmtOut(null)
                    .setSearchType(1)
                    .setGmtPay(null)
                    .setTransferStatus(1)
                    .setOddType(6)
                    .setInLogNoRelated(trackingNo)
                    .setCheckRemark("STOCKX_FLEX");

            iSysProdSearchService.save(sysProdSearch);
        }

        return prods;
    }

    @Override
    public List<SysWareIn> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }


    /**
     * 查询15天未有入库订单的用户
     *
     * @return 是否满足规则
     */
    @ReadOnly
    @Override
    public boolean queryBlockedUser(ShopUser shopUser, Date dateTime, Integer offset) {
        LambdaQueryWrapper<SysWareIn> queryWrapper = new LambdaQueryWrapper<>();
        DateTime startTime = DateUtil.parse(REGISTRATION_TIME_START);
        DateTime offsetTime = DateUtil.offsetDay(dateTime, offset);
        queryWrapper
                .eq(SysWareIn::getShopId, shopUser.getId())
                .ge(SysWareIn::getGmtCreate, offsetTime)
                .ge(SysWareIn::getGmtCreate, startTime);
        // 查询匹配的订单数量
        int count = this.count(queryWrapper);
        return count == 0;
    }

    @ReadOnly
    @Override
    public Set<Integer> queryBlockedUserReturnIds(List<ShopUser> shopUsers, Date dateTime, Integer offset) {
        LambdaQueryWrapper<SysWareIn> queryWrapper = new LambdaQueryWrapper<>();
        List<Integer> shopIds = shopUsers.stream().map(ShopUser::getId).collect(Collectors.toList());
        DateTime startTime = DateUtil.parse(REGISTRATION_TIME_START);
        DateTime offsetTime = DateUtil.offsetDay(dateTime, offset);
        queryWrapper
                .in(CollUtil.isNotEmpty(shopIds), SysWareIn::getShopId, shopIds)
                .ge(SysWareIn::getGmtCreate, offsetTime)
                .ge(SysWareIn::getGmtCreate, startTime);
        // 查询匹配的订单数量
        List<SysWareIn> wareInList = this.list(queryWrapper);
        Set<Integer> excepted = wareInList.stream()
                .map(SysWareIn::getShopId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        return shopIds.stream()
                .filter(id -> !excepted.contains(id))
                .collect(Collectors.toSet());
    }

    @TrimParam
    @Override
    public List<String> selectWareBatchInToExportCsvData(SysWareInProdPageDto dto, String language) {
        return baseMapper.selectWareBatchInToExportCsvData(dto, language);
    }



    @TrimParam
    @Override
    public SysWareBatchCountVo selectWareBatchCount(SysWareInProdPageDto dto) {
        return baseMapper.selectWareBatchCount(dto);
    }

    @TrimParam
    @Override
    public List<SysWareInProdListVo> selectWareBatchList(SysWareInProdPageDto dto) {
        return baseMapper.selectWareBatchList(dto);
    }
}
