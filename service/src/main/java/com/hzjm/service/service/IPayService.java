package com.hzjm.service.service;

import com.hzjm.common.pay.PayVo;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;

public interface IPayService {

    PayVo getPayInfo(String remoteIpAddr, Integer id, Integer payType, Integer type, String attach);

    Integer getPayStatus(String outTradeNo);

    String wxPayCallback(HttpServletRequest request);

    String aliCallback(HttpServletRequest request);

    String unionCallback(HttpServletRequest request);

    void orderSuccess(String outTradeNo);

    void orderRefund(String outTradeNo, BigDecimal refundAmount);
}
