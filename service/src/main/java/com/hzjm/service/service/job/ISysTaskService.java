package com.hzjm.service.service.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.hzjm.service.entity.SysTask;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/26 15:25
 * @description: SysTaskService服务
 */
public interface ISysTaskService {
    /**
     * 查询任务列表
     *
     * @param queryWrapper 查询条件
     * @return 任务列表
     */
    List<SysTask> queryTaskList(QueryWrapper<SysTask> queryWrapper);

    /**
     * 清理任务
     *
     * @param tasks 任务列表
     */
    void cleanTasks(List<SysTask> tasks);

    /**
     * 设置任务为已完成
     *
     * @param taskIds 任务id列表
     */
    void setDoneTasks(List<Integer> taskIds);
}
