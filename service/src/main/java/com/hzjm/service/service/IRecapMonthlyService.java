package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.RecapMonthly;
import com.hzjm.service.model.DTO.TaskRecapReqDto;
import com.hzjm.service.model.VO.RecapMonthlyVo;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 14:27
 * @description: RecapMonthly服务类
 */
public interface IRecapMonthlyService extends IService<RecapMonthly> {

    /**
     * 生成商家月度统计
     *
     * @param recapReqDto 调用参数
     * @return 生成结果
     */
    Boolean createRecapMonthly(TaskRecapReqDto recapReqDto);

    /**
     * 根据条件查询商家月度统计
     *
     * @param recapReqDto 请求参数
     * @return list
     */
    List<RecapMonthlyVo> queryRecapMonthlyByCondition(TaskRecapReqDto recapReqDto);

    /**
     * 查询日期范围内，有订单的商家
     *
     * @param beginTime 开始
     * @param endTime   结束
     * @return 商家IDs
     */
    List<Long> queryUsedShopIds(Date beginTime, Date endTime);

    /**
     * 生成邮件内容
     *
     * @param recapMonthlyVo recapMonthlyVo
     * @return 邮件内容
     */
    String createEmailContent(RecapMonthlyVo recapMonthlyVo);

    /**
     * 根据条件查询商家年度统计
     *
     * @param recapReqDto 请求参数
     * @return list
     */
    List<RecapMonthly> queryRecapYearlyByCondition(TaskRecapReqDto recapReqDto);

    /**
     * 查询最佳月份-月度统计数据
     *
     * @param recapReqDto 请求参数
     * @return RecapMonthly
     */
    RecapMonthly queryBestMonthRecapMonthly(TaskRecapReqDto recapReqDto);

    /**
     * 初始化商家月度统计数据(2024前11个月的数据)
     *
     * @return 处理结果
     */
    Boolean initRecapMonthly(Integer year);
}
