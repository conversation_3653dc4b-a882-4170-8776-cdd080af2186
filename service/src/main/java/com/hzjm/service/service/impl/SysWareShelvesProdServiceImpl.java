package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysWareShelvesProdMapper;
import com.hzjm.service.model.DTO.SysWareShelvesDealDto;
import com.hzjm.service.model.DTO.SysWareShelvesProdPageDto;
import com.hzjm.service.model.VO.SysWareShelvesProdListVo;
import com.hzjm.service.model.VO.SysWareShelvesProdVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 货架商品 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Slf4j
@Service
public class SysWareShelvesProdServiceImpl extends ServiceImpl<SysWareShelvesProdMapper, SysWareShelvesProd> implements ISysWareShelvesProdService {

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Resource
    private ISysUserService iSysUserService;

    @Override
    public SysWareShelvesProd getByIdWithoutLogic(Integer id) {
        SysWareShelvesProd data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该货架商品"));
        }

        return data;
    }

    @Override
    public SysWareShelvesProdVo getDetail(Integer id) {
        SysWareShelvesProd data = getByIdWithoutLogic(id);

        SysWareShelvesProdVo vo = new SysWareShelvesProdVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareShelvesProd(SysWareShelvesProd dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysWareShelvesProdListVo> searchList(SysWareShelvesProdPageDto dto) {

        LambdaQueryWrapper<SysWareShelvesProd> qw = Wrappers.<SysWareShelvesProd>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareShelvesProd::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareShelvesProd::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareShelvesProd::getGmtCreate, endTime);

        IPage<SysWareShelvesProd> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareShelvesProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysWareShelvesProdListVo vo = new SysWareShelvesProdListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysWareShelvesProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWareShelvesProd> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareShelvesProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean batchDeal(SysWareShelvesDealDto dto) {
        Integer userId = JwtContentHolder.getUserId();
        String nickName = iSysUserService.getById(userId) != null
                ? iSysUserService.getById(userId).getNickname() : "";

        log.info("SysWareShelvesProdServiceImpl batchDeal start  dto ={} ,userid ={} ,nickName ={}", JSON.toJSONString(dto), userId, nickName);

        if (ObjectUtils.isEmpty(dto.getProdIdList())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意商品"));
        }

        // 出库中的商品不支持上下架操作
        if (iSysWareOutProdService.count(Wrappers.<SysWareOutProd>lambdaQuery()
                .isNull(SysWareOutProd::getCheckId)
                .in(SysWareOutProd::getProdId, dto.getProdIdList())) > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("存在商品正在出库中，此类商品暂不支持" + (dto.getType() == 1 ? "上架" : "下架")));
        }
        if (dto.getType() == 1) {
            if (ObjectUtils.isEmpty(dto.getShelvesId())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请选择待上的货架"));
            }

            SysWareShelves shelves = iSysWareShelvesService.getById(dto.getShelvesId());
            if (ObjectUtils.isEmpty(shelves)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("货架已废弃"));
            }

//            if (shelves.getMaxNum() < (count(Wrappers.<SysWareShelvesProd>lambdaQuery()
//                    .eq(SysWareShelvesProd::getShelvesId, dto.getShelvesId())
//                    .notIn(SysWareShelvesProd::getProdId, dto.getProdIdList()))
//                    + dto.getProdIdList().size())) {
//                throw new BaseException(LanguageConfigService.i18nForMsg("上架商品已超出货架最大数量"));
//            }
        }

        remove(Wrappers.<SysWareShelvesProd>lambdaQuery().in(SysWareShelvesProd::getProdId, dto.getProdIdList()));
        List<SysProdEvent> eventList = new ArrayList<>();
        List<Integer> prodIdList = dto.getProdIdList().stream().distinct().collect(Collectors.toList());

        switch (dto.getType()) {
            case 1:
                // 上架：直接覆盖货架，不再判断是否已在架上
//                if (count(Wrappers.<SysWareShelvesProd>lambdaQuery().in(SysWareShelvesProd::getProdId, dto.getProdIdList())) > 0) {
//                    throw new BaseException(LanguageConfigService.i18nForMsg("存在商品已在货架上"));
//                }

                List<SysWareShelvesProd> dataList = new ArrayList<>();
                dto.getProdIdList().stream().distinct().forEach(prodId -> {
                    SysWareShelvesProd data = new SysWareShelvesProd();
                    data.setShelvesId(dto.getShelvesId());
                    data.setProdId(prodId);
                    data.setWareId(JwtContentHolder.getWareId());
                    data.setUserName(nickName);
                    data.setUserId(userId);
//                    data.insert();
                    dataList.add(data);

                    // 商品事件：上架
                    SysProdEvent event = new SysProdEvent();
                    event.setProdId(prodId);
//                    event.setShopId(deal.getShopId());
                    event.setDescription(JwtContentHolder.getRoleType()+"操作,商品上架,"+JwtContentHolder.getUserId());
                    event.setType(SysProdEvent.TypeShelvesOn);
                    event.setRelationId(dto.getShelvesId());
//                    event.insert();
                    eventList.add(event);
                });
                insertList(dataList);

                // search同步更新
                iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                        .set(SysProdSearch::getDelFlag, 0)
                        .set(!ObjectUtils.isEmpty(dto.getShelvesId()), SysProdSearch::getShelvesId, dto.getShelvesId())
                        .in(SysProdSearch::getProdId, prodIdList)
                        .eq(SysProdSearch::getSearchType, 1));
                break;
            case 2:
                dto.getProdIdList().forEach(prodId -> {
                    // 商品事件：下架
                    SysProdEvent event = new SysProdEvent();
                    event.setProdId(prodId);
//                    event.setShopId(deal.getShopId());
                    event.setDescription(JwtContentHolder.getRoleType()+"操作,商品下架,"+JwtContentHolder.getUserId());
                    event.setType(SysProdEvent.TypeShelvesOff);
                    event.setRelationId(dto.getShelvesId());
//                    event.insert();
                    eventList.add(event);
                });
                // 下架
//                remove(Wrappers.<SysWareShelvesProd>lambdaQuery().in(SysWareShelvesProd::getProdId, dto.getProdIdList()));

                // search同步更新
                iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                        .setSql(" shelves_id = null ")
                        .in(SysProdSearch::getProdId, prodIdList)
                        .eq(SysProdSearch::getSearchType, 1));
                break;
        }

        if (!ObjectUtils.isEmpty(eventList)) {
            iSysProdEventService.insertList(eventList);
        }

        return true;
    }

    @Override
    public List<SysWareShelvesProd> qyeryByWareShelvesProdByProdId(Integer prodId) {
        if (ObjectUtils.isEmpty(prodId)) {
            return Collections.emptyList();
        }
        return baseMapper.qyeryByWareShelvesProdByProdId(prodId);
    }

    @Override
    public List<SysWareShelvesProd> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
