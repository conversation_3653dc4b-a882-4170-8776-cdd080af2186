package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysProdSwitchItem;
import com.hzjm.service.model.DTO.SysProdSwitchItemPageDto;
import com.hzjm.service.model.VO.SysProdSwitchItemCountVo;
import com.hzjm.service.model.VO.SysProdSwitchItemListVo;
import com.hzjm.service.model.VO.SysProdSwitchItemVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 转仓明细 服务类
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface ISysProdSwitchItemService extends IService<SysProdSwitchItem> {

    SysProdSwitchItem getByIdWithoutLogic(Integer id);

    SysProdSwitchItemVo getDetail(Integer id);

    Boolean saveSysProdSwitchItem(SysProdSwitchItem dto);

    Boolean insertList(List<SysProdSwitchItem> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysProdSwitchItemListVo> searchList(SysProdSwitchItemPageDto dto);

    List<SysProdSwitchItem> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdSwitchItem> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean release(List<Integer> prodIdList);

    SysProdSwitchItemCountVo getCount(SysProdSwitchItemPageDto dto);

    Boolean scanByOneId(String oneId, Integer status);
}
