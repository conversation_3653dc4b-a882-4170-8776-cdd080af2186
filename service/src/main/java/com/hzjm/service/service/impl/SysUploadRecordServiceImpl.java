package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysUploadRecord;
import com.hzjm.service.entity.SysUploadRecordDetail;
import com.hzjm.service.mapper.SysUploadRecordDetailMapper;
import com.hzjm.service.mapper.SysUploadRecordMapper;
import com.hzjm.service.model.DTO.SysUploadRecordPageDto;
import com.hzjm.service.model.VO.SysUploadRecordVo;
import com.hzjm.service.model.enums.SysUploadRecordUploadTypeEnum;
import com.hzjm.service.model.enums.SysUploadRecordUserTypeEnum;
import com.hzjm.service.service.ISysUploadRecordService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 导入记录流水表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
@Slf4j
@Service
public class SysUploadRecordServiceImpl extends ServiceImpl<SysUploadRecordMapper, SysUploadRecord> implements ISysUploadRecordService {


    @Resource
    private SysUploadRecordDetailMapper sysUploadRecordDetailMapper;

    @Override
    public SysUploadRecord getByIdWithoutLogic(Integer id) {
        SysUploadRecord data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该导入记录流水表"));
        }

        return data;
    }

    @Override
    public SysUploadRecordVo getDetail(Integer id) {
        SysUploadRecord data = getByIdWithoutLogic(id);

        SysUploadRecordVo vo = new SysUploadRecordVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public List<SysUploadRecordDetail> selectSysUploadRecordDetail(Integer id) {
        return sysUploadRecordDetailMapper.listWithoutLogic(Wrappers.<SysUploadRecordDetail>lambdaQuery()
                .eq(SysUploadRecordDetail::getUploadId, id)
                .eq(SysUploadRecordDetail::getDelFlag, 0)
                .orderByDesc(SysUploadRecordDetail::getId)
        );
    }

    @Override
    public Boolean saveSysUploadRecord(SysUploadRecord dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysUploadRecordVo> searchList(SysUploadRecordPageDto dto) {

        LambdaQueryWrapper<SysUploadRecord> qw = Wrappers.<SysUploadRecord>lambdaQuery();
        qw.eq(SysUploadRecord::getUserId, JwtContentHolder.getUserId());
        qw.eq(SysUploadRecord::getUserType, SysUploadRecordUserTypeEnum.SHOP);
        qw.eq(SysUploadRecord::getUploadType, SysUploadRecordUploadTypeEnum.SHOP_BATCH_DELIVERY);

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysUploadRecord::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysUploadRecord::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysUploadRecord::getGmtCreate, endTime);

        IPage<SysUploadRecord> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysUploadRecordVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysUploadRecordVo vo = new SysUploadRecordVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysUploadRecordVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysUploadRecord> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysUploadRecord> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysUploadRecord> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
