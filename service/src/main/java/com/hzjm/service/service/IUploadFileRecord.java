package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.UploadFileRecord;
import com.hzjm.service.model.enums.UploadFileType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/6 14:51
 * @description: UploadFileRecord接口
 */
public interface IUploadFileRecord extends IService<UploadFileRecord> {

    /**
     * 保存或更新
     *
     * @param fileUrls 文件地址
     * @param uid      用户id
     * @param type     文件类型
     */
    void saveFiles(List<String> fileUrls, Integer uid, UploadFileType type);

    /**
     * 获取用户上传文件
     *
     * @param uid  用户id
     * @param type 文件类型
     * @return list
     */
    List<String> getFileUrls(Integer uid, UploadFileType type);
}
