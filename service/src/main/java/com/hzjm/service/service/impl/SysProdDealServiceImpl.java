package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysProdDealMapper;
import com.hzjm.service.model.DTO.SysProdDealPageDto;
import com.hzjm.service.model.DTO.SysProdPageDto;
import com.hzjm.service.model.DTO.SysProdSaleDealDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品处理绑定关系 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Slf4j
@Service
public class SysProdDealServiceImpl extends ServiceImpl<SysProdDealMapper, SysProdDeal> implements ISysProdDealService {


    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Autowired
    private ISysSkuService iSysSkuService;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysProdTransferService iSysProdTransferService;

    @Autowired
    private ISysProdCashService iSysProdCashService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysThirdPlatService iSysThirdPlatService;

    @Autowired
    private IShopUserPlatService iShopUserPlatService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private ISysWareOutBatchService iSysWareOutBatchService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private TouchUtils touchUtils;

    @Override
    public SysProdDeal getByIdWithoutLogic(Integer id) {
        SysProdDeal data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商品处理绑定关系"));
        }

        return data;
    }

    @Override
    public SysProdDealVo getDetail(Integer id) {
        SysProdDeal data = getByIdWithoutLogic(id);

        SysProdDealVo vo = new SysProdDealVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdDeal(SysProdDeal dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdDealListVo> searchList(SysProdDealPageDto dto) {

        LambdaQueryWrapper<SysProdDeal> qw = Wrappers.<SysProdDeal>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdDeal::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdDeal::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdDeal::getGmtCreate, endTime);

        IPage<SysProdDeal> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdDealListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysProdDealListVo vo = new SysProdDealListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysProdDealListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdDeal> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdDeal> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdDeal> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    @ReadOnly
    public List<SysProdDealListVo> dealList(Integer relationId, Integer type, SysProdDealPageDto dto) {
        if (ObjectUtils.isEmpty(type)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("不可识别的数据类型"));
        }

        List<Integer> prodIdList = dto.getProdIdList();
        Map<Integer, SysProdDeal> dealMap = new HashMap<>();
        Map<String, SysSku> skuMap = new HashMap<>();
        Map<String, SysSku> pkuMap = new HashMap<>();
        List<SysProdDealListVo> prodVoList = new ArrayList<>();

        if (ObjectUtils.isEmpty(prodIdList)) {
            if ((ObjectUtils.isEmpty(type) || (ObjectUtils.isEmpty(relationId)) && type != SysProdEvent.TypeSale)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("查询对象不明"));
            }

            // 平台内转移只查询 状态，1-处理中，3-已完成 的数据
            LambdaQueryWrapper<SysProdDeal> dealQw = Wrappers.<SysProdDeal>lambdaQuery()
                    .in(SysProdDeal::getStatus, 1, 3);
            if (type == SysProdEvent.TypeSale) {
                List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                        .eq(SysWareOutProd::getOutId, relationId));
                List<Integer> dealIdList = BaseUtils.initList();
                dealIdList.addAll(outProdList.stream().map(SysWareOutProd::getDealId).collect(Collectors.toList()));
                dealQw.in(SysProdDeal::getId, dealIdList);
            } else {
                dealQw.eq(SysProdDeal::getType, type)
                        .eq(SysProdDeal::getRelationId, relationId);
            }

            List<SysProdDeal> dealList = list(dealQw);
            prodIdList = BaseUtils.initList();
            prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
            dealList.forEach(deal -> {
                dealMap.put(deal.getProdId(), deal);
            });
        }

        // 商品信息
        LambdaQueryWrapper<SysProd> qw1 = Wrappers.<SysProd>lambdaQuery()
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysProd::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysProd::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec())
                .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysProd::getSpec, dto.getSpecList())
                .like(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysProd::getSku, dto.getSkuList())
                .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProd::getOneId, dto.getOneId())
                .ge(!ObjectUtils.isEmpty(dto.getCostPriceMin()), SysProd::getCostPrice, dto.getCostPriceMin())
                .le(!ObjectUtils.isEmpty(dto.getCostPriceMax()), SysProd::getCostPrice, dto.getCostPriceMax())
                .in(SysProd::getId, prodIdList);
        if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
            StringBuffer sb = new StringBuffer();
            dto.getOneIdList().forEach(oneId -> {
                sb.append("(one_id like '%" + oneId + "%') or ");
            });
            qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
        }
        List<SysProd> prodList = iSysProdService.listWithoutLogic(qw1);
        Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
        List<Integer> shopIdList = BaseUtils.initList();
        shopIdList.addAll(prodList.stream().filter(a -> !ObjectUtils.isEmpty(a.getShopId())).map(SysProd::getShopId).collect(Collectors.toList()));
        List<String> pkuList = prodList.stream().filter(a -> {
            return !ObjectUtils.isEmpty(a.getPku());
        }).map(SysProd::getPku).distinct().collect(Collectors.toList());
        prodIdList = BaseUtils.getListSame(prodIdList, Arrays.stream(prodMap.keySet().toArray()).collect(Collectors.toList()));
        if (ObjectUtils.isEmpty(prodIdList)) {
            return prodVoList;
        }
        prodList.clear();

        List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery());
        Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
        shopList.clear();

        // 仓库
        List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
        Map<Integer, SysWareInProd> prodWareMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));
        List<Integer> wareIdList = BaseUtils.initList();
        wareIdList.addAll(inProdList.stream().map(SysWareInProd::getWareId).collect(Collectors.toList()));
        List<SysWare> wareList = iSysWareService.listWithoutLogic(Wrappers.<SysWare>lambdaQuery());
        Map<Integer, SysWare> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, a -> a));

        // 货架
        Map<Integer, String> shelvesMap = new HashMap<>();// 商品id -> 货架名
        List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                .in(SysWareShelvesProd::getProdId, prodIdList));
        if (!ObjectUtils.isEmpty(shelvesProdList)) {
            Map<Integer, List<SysWareShelvesProd>> shelvesProdMap = shelvesProdList.stream().collect(Collectors.groupingBy(SysWareShelvesProd::getShelvesId));
            List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                    .in(SysWareShelves::getId, shelvesProdList.stream().map(SysWareShelvesProd::getShelvesId).collect(Collectors.toList())));
            shelvesList.forEach(shelves -> {
                List<SysWareShelvesProd> prods = shelvesProdMap.get(shelves.getId());
                prods.forEach(prod -> {
                    shelvesMap.put(prod.getProdId(), shelves.getName());
                });
            });
        }

        if (type == SysProdEvent.TypeCash) {
            // 实际报价/寄售价
            if (ObjectUtils.isEmpty(dealMap)) {
                List<SysProdDeal> dealList = list(Wrappers.<SysProdDeal>lambdaQuery().orderByAsc(SysProdDeal::getGmtCreate)
                        .eq(SysProdDeal::getType, type)
                        .eq(!ObjectUtils.isEmpty(relationId), SysProdDeal::getRelationId, relationId)
                        .in(SysProdDeal::getStatus, 1, 3)
                        .in(SysProdDeal::getProdId, dto.getProdIdList()));
                dealList.forEach(deal -> {
                    dealMap.put(deal.getProdId(), deal);
                });
            }

            // sku
            if (!ObjectUtils.isEmpty(pkuList)) {
                List<SysSku> skuList = iSysSkuService.list(Wrappers.<SysSku>lambdaQuery()
                        .select(SysSku::getPrePrice, SysSku::getSku, SysSku::getPku, SysSku::getId)
                        .isNotNull(SysSku::getPrePrice)
                        .in(SysSku::getPku, pkuList));
//            skuMap.putAll(skuList.stream().collect(Collectors.toMap(SysSku::getSku, a -> a)));
                pkuMap.putAll(skuList.stream().filter(a -> {
                    return !ObjectUtils.isEmpty(a.getPku());
                }).collect(Collectors.toMap(SysSku::getPku, a -> a)));
            }
        }

        Map<Integer, SysThirdPlat> platMap = new HashMap<>();
        if (type == SysProdEvent.TypeSale) {
            List<SysThirdPlat> platList = iSysThirdPlatService.listWithoutLogic(Wrappers.<SysThirdPlat>lambdaQuery());
            platMap.putAll(platList.stream().collect(Collectors.toMap(SysThirdPlat::getId, a -> a)));
        }

//        List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery().in(SysWareOutBatchProd::getProdId, prodIdList));
//        Map<Integer, List<SysWareOutBatchProd>> batchProdMap = batchProdList.stream().collect(Collectors.groupingBy(SysWareOutBatchProd::getProdId));

        // 组装商品信息
        Date now = DateTimeUtils.getNow();
        prodIdList.remove(Integer.valueOf("-1"));
        prodIdList.remove(Integer.valueOf("0"));
        for (Integer prodId : prodIdList) {
            SysProdDealListVo prodVo = new SysProdDealListVo();
            prodVo.setProdId(prodId);
            prodVo.setType(type);
            prodVo.setRelationId(relationId);

            // 商品信息
            SysProd prod = prodMap.get(prodId);
            if (!ObjectUtils.isEmpty(prod)) {
                prodVo.setImg(prod.getImg());
                prodVo.setOneId(prod.getOneId());
                prodVo.setSku(prod.getSku());
                prodVo.setSpec(prod.getSpec());
                prodVo.setRemarks(prod.getRemarks());
                prodVo.setCostPrice(prod.getCostPrice());
                prodVo.setSupply(prod.getSupply());
                prodVo.setGmtIn(prod.getGmtIn());
            }

            // 预报价格
            prodVo.setPrePrice(Optional.ofNullable(prod.getPku()).map(pku -> pkuMap.get(pku)).map(sku -> sku.getPrePrice())
//                    .orElse(Optional.ofNullable(prod.getSku()).map(sku -> skuMap.get(sku)).map(sku -> sku.getPrePrice())
                    .orElse(SysConstants.zero));

            // 实际报价/寄售价
            Integer wareId = null;
            SysProdDeal deal = dealMap.get(prodId);
            if (!ObjectUtils.isEmpty(deal)) {
                wareId = deal.getWareId();
                prodVo.setCostPrice(deal.getCostPrice());
                prodVo.setSupply(deal.getSupply());
                prodVo.setQuotePrice(deal.getQuotePrice());
                prodVo.setSalePrice(deal.getSalePrice());
                prodVo.setSoldPrice(deal.getSoldPrice());
                prodVo.setPlatSoldPrice(deal.getPlatSoldPrice());
                prodVo.setSaleStatus(deal.getStatus());
                prodVo.setGmtSale(deal.getGmtModify());
                prodVo.setWareFee(deal.getWareFee());
                prodVo.setWareDays(deal.getWareDays());

                if (!ObjectUtils.isEmpty(deal.getSoldPrice()) && !ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
                    prodVo.setServiceFee(deal.getPlatSoldPrice().subtract(deal.getSoldPrice()));
                }

                ShopUser shop = shopMap.get(deal.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    prodVo.setShopId(shop.getId());
                    prodVo.setShopUid(shop.getUid());
                    prodVo.setShopName(shop.getRealname());
                }

                SysThirdPlat plat = platMap.get(deal.getThirdPlatId());
                if (!ObjectUtils.isEmpty(plat)) {
                    if (plat.getName() != null && "StockX Direct".equals(plat.getName())) {
                        prodVo.setThirdPlatName("SURGE");
                    } else {
                        prodVo.setThirdPlatName(plat.getName());
                    }

                    if (ObjectUtils.isEmpty(prodVo.getServiceFee())) {
                        ShopUserPlat shopPlat = iShopUserPlatService.getShopPlat(deal.getShopId(), deal.getThirdPlatId());
                        BigDecimal serviceFee = deal.getSalePrice().multiply(
                                        shopPlat.getServiceRate().add(shopPlat.getDrawRate())
                                ).divide(SysConstants.hundred, 2, RoundingMode.HALF_EVEN)
                                .add(plat.getOtherFee()).add(shop.getOcFee());
                        prodVo.setServiceFee(serviceFee);
                    }
                }
            } else {
                wareId = prod.getWareId();
            }

            SysWare ware = wareMap.get(wareId);
            if (!ObjectUtils.isEmpty(ware)) {
                prodVo.setWareName(ware.getName());

                SysWareInProd inProd = prodWareMap.get(prodId);
                if (!ObjectUtils.isEmpty(inProd)) {
                    prodVo.setCheckResult(inProd.getCheckResult());
                    if (ObjectUtils.isEmpty(prodVo.getWareDays())) {
                        // 在仓天数
                        Integer wareDays = DateTimeUtils.timeDiff(ObjectUtils.isEmpty(inProd.getGmtPay()) ? now : inProd.getGmtPay(), prodVo.getGmtIn(), 4);
                        prodVo.setWareDays(wareDays);

                        // 在仓费用
                        BigDecimal wareFee = wareDays - ware.getFreeDays() <= 0 ? SysConstants.zero : new BigDecimal(wareDays - ware.getFreeDays()).multiply(ware.getPrice());
                        prodVo.setWareFee(wareFee);
                    }
                }
            }

            // 货架号
            prodVo.setShelvesName(shelvesMap.get(prodId));
/*

            List<SysWareOutBatchProd> batchProds = batchProdMap.get(prodId);
            if (!ObjectUtils.isEmpty(batchProds)) {
                SysWareOutBatchProd batchProd = batchProds.get(batchProds.size() - 1);
                prodVo.setOutStatus(batchProd.getStatus());
                prodVo.setGmtScan(batchProd.getGmtScan());
                prodVo.setGmtPack(batchProd.getGmtPack());
            }
*/

            prodVoList.add(prodVo);
        }

        Map<Integer, List<SysWareOutBatchProd>> batchProdMap = new HashMap<>();
        List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .in(SysWareOutBatchProd::getProdId, prodVoList.stream().map(SysProdDealListVo::getProdId).collect(Collectors.toList())));
        if (!ObjectUtils.isEmpty(batchProdList)) {
            List<Integer> finishIdList = BaseUtils.initList();
            List<Integer> batchIdList = batchProdList.stream()
                    .filter(a -> {
                        return !ObjectUtils.isEmpty(a.getBatchId());
                    })
                    .map(SysWareOutBatchProd::getBatchId).distinct().collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(batchIdList)) {
                List<SysWareOutBatch> finishList = iSysWareOutBatchService.list(Wrappers.<SysWareOutBatch>lambdaQuery()
                        .in(SysWareOutBatch::getId, batchIdList)
                        .eq(SysWareOutBatch::getStatus, 2));
                finishIdList.addAll(finishList.stream().map(SysWareOutBatch::getId).collect(Collectors.toList()));
                finishList.clear();
            }

            batchProdMap.putAll(batchProdList.stream().collect(Collectors.groupingBy(SysWareOutBatchProd::getProdId)));
//            batchProdList.stream().filter(a -> {
//                return ObjectUtils.isEmpty(a.getBatchId()) || finishIdList.indexOf(a.getBatchId()) == -1;
//            }).collect(Collectors.groupingBy(SysWareOutBatchProd::getProdId));
            batchProdList.clear();
        }
        prodVoList.forEach(deal -> {
            List<SysWareOutBatchProd> batchProds = batchProdMap.get(deal.getProdId());
            if (!ObjectUtils.isEmpty(batchProds)) {
                SysWareOutBatchProd batchProd = batchProds.get(batchProds.size() - 1);
                deal.setOutStatus(batchProd.getStatus());
                deal.setGmtScan(batchProd.getGmtScan());
                deal.setGmtPack(batchProd.getGmtPack());
                deal.setGmtOut(batchProd.getGmtOut());
                deal.setBatchId(batchProd.getBatchId());
            } else {
                deal.setOutStatus(1); // 默认为待扫描状态
            }
        });

        // 默认排序：sku+尺码
        prodVoList = prodVoList.stream().sorted(Comparator.comparing(a -> {
            return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                    .map(spec -> BaseUtils.dealSizeStr(spec).toString())
                    .orElse("");
        })).collect(Collectors.toList());

        return prodVoList;
    }

    @Override
    public Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealGroup(List<Integer> relationIdList, List<Integer> typeList, List<Integer> prodIdList) {
        Map<Integer, Map<Integer, List<SysProdDealListVo>>> voMap = new HashMap<Integer, Map<Integer, List<SysProdDealListVo>>>() {{
            put(SysProdEvent.TypeSend, new HashMap<>());
            put(SysProdEvent.TypeTransport, new HashMap<>());
            put(SysProdEvent.TypeCash, new HashMap<>());
            put(SysProdEvent.TypeSale, new HashMap<>());
            put(SysProdEvent.TypeTransfer, new HashMap<>());
        }};

        if (ObjectUtils.isEmpty(relationIdList) || ObjectUtils.isEmpty(typeList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询对象不明"));
        }

        // 商品清单
        List<SysProdDeal> dealList = list(Wrappers.<SysProdDeal>lambdaQuery()
                .orderByDesc(SysProdDeal::getId)
                .in(SysProdDeal::getType, typeList)
                .in(SysProdDeal::getRelationId, relationIdList));

        if (ObjectUtils.isEmpty(prodIdList)) {
            prodIdList = BaseUtils.initList();
            prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
        }
        prodIdList = prodIdList.stream().distinct().collect(Collectors.toList());

        Map<Integer, SysProdDeal> dealMap = new HashMap<>();
        Map<Integer, List<Integer>> prodDealIdMap = new HashMap<>();
        dealList.forEach(deal -> {
            dealMap.put(deal.getId(), deal);
            List<Integer> dealIdList = prodDealIdMap.get(deal.getProdId());
            if (ObjectUtils.isEmpty(dealIdList)) {
                dealIdList = new ArrayList<>();
                prodDealIdMap.put(deal.getProdId(), dealIdList);
            }
            dealIdList.add(deal.getId());
        });

        // 商品信息
        List<SysProd> prodList = iSysProdService.listWithoutLogic(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
        Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));

        // 仓库
        List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
        Map<Integer, SysWareInProd> prodWareMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));
//        List<Integer> wareIdList = BaseUtils.initList();
//        wareIdList.addAll(inProdList.stream().map(SysWareInProd::getWareId).collect(Collectors.toList()));
        List<SysWare> wareList = iSysWareService.listWithoutLogic(Wrappers.<SysWare>lambdaQuery());
        Map<Integer, SysWare> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, a -> a));

        // 货架
        Map<Integer, String> shelvesMap = new HashMap<>();// 商品id -> 货架名
        // 货架：在架
        List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                .in(SysWareShelvesProd::getProdId, prodIdList));
        if (!ObjectUtils.isEmpty(shelvesProdList)) {
            Map<Integer, List<SysWareShelvesProd>> shelvesProdMap = shelvesProdList.stream().collect(Collectors.groupingBy(SysWareShelvesProd::getShelvesId));
            List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                    .in(SysWareShelves::getId, shelvesProdList.stream().map(SysWareShelvesProd::getShelvesId).collect(Collectors.toList())));
            shelvesList.forEach(shelves -> {
                List<SysWareShelvesProd> prods = shelvesProdMap.get(shelves.getId());
                prods.forEach(prod -> {
                    shelvesMap.put(prod.getProdId(), shelves.getName());
                });
            });
        }
        // 货架：出库
        List<SysWareOutProd> shelvesProdList2 = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                .isNotNull(SysWareOutProd::getShelvesId)
                .in(SysWareOutProd::getProdId, prodIdList));
        if (!ObjectUtils.isEmpty(shelvesProdList2)) {
            Map<Integer, List<SysWareOutProd>> shelvesProdMap = shelvesProdList2.stream().collect(Collectors.groupingBy(SysWareOutProd::getShelvesId));
            List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                    .in(SysWareShelves::getId, shelvesProdList2.stream().map(SysWareOutProd::getShelvesId).collect(Collectors.toList())));
            shelvesList.forEach(shelves -> {
                List<SysWareOutProd> prods = shelvesProdMap.get(shelves.getId());
                prods.forEach(prod -> {
                    shelvesMap.put(prod.getProdId(), shelves.getName());
                });
            });
        }

        // 预报价格
        Map<String, SysSku> pkuMap = new HashMap<>();
        if (typeList.indexOf(SysProdEvent.TypeCash) != -1) {
            List<String> pkuList = prodList.stream().filter(a -> {
                return !ObjectUtils.isEmpty(a.getPku());
            }).map(SysProd::getPku).distinct().collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(pkuList)) {
                List<SysSku> skuList = iSysSkuService.list(Wrappers.<SysSku>lambdaQuery()
                        .select(SysSku::getPrePrice, SysSku::getSku, SysSku::getPku, SysSku::getId)
                        .isNotNull(SysSku::getPrePrice)
                        .in(SysSku::getPku, pkuList));
                pkuMap.putAll(skuList.stream().collect(Collectors.toMap(SysSku::getPku, a -> a)));
            }
        }

        List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery());
        Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
        shopList.clear();

        // 组装商品信息
        Date now = DateTimeUtils.getNow();
        prodIdList.remove(Integer.valueOf("-1"));
        prodIdList.remove(Integer.valueOf("0"));
        for (Integer prodId : prodIdList) {
            Map<Integer, Integer> typeDealIdMap = new HashMap<>();
            List<Integer> dealIdList = prodDealIdMap.get(prodId);
            if (ObjectUtils.isEmpty(dealIdList)) {
                continue;
            }
            dealIdList.forEach(dealId -> {
                SysProdDeal deal = dealMap.get(dealId);
                Integer relationId = deal.getRelationId();

                Map<Integer, List<SysProdDealListVo>> typeMap = voMap.get(deal.getType());
                List<SysProdDealListVo> voList = typeMap.get(relationId);
                if (ObjectUtils.isEmpty(voList)) {
                    voList = new ArrayList<>();
                    typeMap.put(relationId, voList);
                }
                // 多次寄售导致展示了历史数据
                if (deal.getType() == SysProdEvent.TypeSale && !ObjectUtils.isEmpty(typeDealIdMap.get(deal.getType()))) {
                    return;
                }
                typeDealIdMap.put(deal.getType(), dealId);

                SysProdDealListVo prodVo = new SysProdDealListVo();
                prodVo.setId(dealId);
                prodVo.setProdId(prodId);
                prodVo.setRelationId(deal.getRelationId());
                prodVo.setType(deal.getType());

                prodVo.setCostPrice(deal.getCostPrice());
                prodVo.setSupply(deal.getSupply());
                prodVo.setGmtIn(deal.getGmtIn());

                // 商品信息
                SysProd prod = prodMap.get(prodId);
                if (!ObjectUtils.isEmpty(prod)) {
                    prodVo.setImg(prod.getImg());
                    prodVo.setOneId(prod.getOneId());
                    prodVo.setSku(prod.getSku());
                    prodVo.setSpec(prod.getSpec());
                    prodVo.setPku(prod.getPku());
                    prodVo.setRemarks(prod.getRemarks());
                }

                ShopUser shop = shopMap.get(deal.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    prodVo.setShopUid(shop.getUid());
                    prodVo.setShopName(shop.getRealname());
                }

                SysSku sku = pkuMap.get(prod.getPku());
                if (!ObjectUtils.isEmpty(sku)) {
                    prodVo.setPrePrice(sku.getPrePrice());
                } else {
                    prodVo.setPrePrice(SysConstants.zero);
                }
                prodVo.setSalePrice(deal.getSalePrice());

                SysWareInProd inProd = prodWareMap.get(prodId);
                if (!ObjectUtils.isEmpty(inProd)) {
                    prodVo.setCheckResult(inProd.getCheckResult());
                    SysWare ware = wareMap.get(deal.getWareId());
                    if (!ObjectUtils.isEmpty(ware)) {
                        prodVo.setWareName(ware.getName());

                        // 在仓天数
                        Integer wareDays = DateTimeUtils.timeDiff(ObjectUtils.isEmpty(inProd.getGmtPay()) ? now : inProd.getGmtPay(), prodVo.getGmtIn(), 4);
                        prodVo.setWareDays(wareDays);

                        // 在仓费用
                        BigDecimal wareFee = wareDays - ware.getFreeDays() <= 0 ? SysConstants.zero : new BigDecimal(wareDays - ware.getFreeDays()).multiply(ware.getPrice());
                        prodVo.setWareFee(wareFee);
                    }
                }

                if (!ObjectUtils.isEmpty(deal.getWareDays())) {
                    prodVo.setWareDays(deal.getWareDays());
                }
                if (!ObjectUtils.isEmpty(deal.getWareFee())) {
                    prodVo.setWareFee(deal.getWareFee());
                }

                // 货架号
                prodVo.setShelvesName(shelvesMap.get(prodId));

                voList.add(prodVo);
            });
        }

        voMap.keySet().forEach(type -> {
            Map<Integer, List<SysProdDealListVo>> typeMap = voMap.get(type);
            typeMap.keySet().forEach(relationId -> {
                typeMap.put(relationId, typeMap.get(relationId).stream().sorted(Comparator.comparing(a -> {
                    return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                            .map(spec -> BaseUtils.dealSizeStr(spec).toString())
                            .orElse("");
                })).collect(Collectors.toList()));
            });
        });

        return voMap;
    }

    @Override
    public IPage<SysProdListVo> saleList(SysProdDealPageDto dto) {
        int type = SysProdEvent.TypeSale;

        List<Integer> prodIdList = null;
        if (!ObjectUtils.isEmpty(dto.getIdList()) || !ObjectUtils.isEmpty(dto.getProdIdList())
                || !ObjectUtils.isEmpty(dto.getSkuList()) || !ObjectUtils.isEmpty(dto.getSku())
                || !ObjectUtils.isEmpty(dto.getSpecList()) || !ObjectUtils.isEmpty(dto.getSpec())
                || !ObjectUtils.isEmpty(dto.getOneIdList()) || !ObjectUtils.isEmpty(dto.getOneId())
                || !ObjectUtils.isEmpty(dto.getRemarks())) {

            LambdaQueryWrapper<SysProd> qw1 = Wrappers.<SysProd>lambdaQuery()
                    .select(SysProd::getId, SysProd::getImg, SysProd::getOneId, SysProd::getSku, SysProd::getSpec)
                    .in(!ObjectUtils.isEmpty(dto.getIdList()), SysProd::getId, dto.getIdList())
                    .in(!ObjectUtils.isEmpty(dto.getProdIdList()), SysProd::getId, dto.getProdIdList())
                    .eq(!ObjectUtils.isEmpty(dto.getRemarks()), SysProd::getRemarks, dto.getRemarks())
                    .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysProd::getSpec, dto.getSpecList())
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec())
                    .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysProd::getSku, dto.getSkuList())
                    .eq(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                    .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProd::getOneId, dto.getOneId())
                    .in(!ObjectUtils.isEmpty(dto.getOneIdList()), SysProd::getOneId, dto.getOneIdList());


            // 商品信息
            List<SysProd> prodList = iSysProdService.listWithoutLogic(qw1);
            prodIdList = BaseUtils.initList();
            prodIdList.addAll(prodList.stream().map(SysProd::getId).collect(Collectors.toList()));
            prodList.clear();
        }

        Integer shopId = dto.getShopId();
        if (ObjectUtils.isEmpty(shopId)) {
            shopId = JwtContentHolder.getShopId();
        }

        List<Integer> shopIdList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(dto.getShopUid()) || !ObjectUtils.isEmpty(dto.getShopName())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getShopName()), ShopUser::getRealname, dto.getShopName())
                    .like(!ObjectUtils.isEmpty(dto.getShopUid()), ShopUser::getUid, dto.getShopUid()));
            shopIdList = BaseUtils.initList();
            shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
        }

        LambdaQueryWrapper<SysProdDeal> qw = Wrappers.<SysProdDeal>lambdaQuery().orderByDesc(SysProdDeal::getGmtModify)
                .eq(SysProdDeal::getType, type)
                .eq(!ObjectUtils.isEmpty(shopId), SysProdDeal::getShopId, shopId)
                .in(!ObjectUtils.isEmpty(shopIdList), SysProdDeal::getShopId, shopIdList)
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysProdDeal::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysProdDeal::getWareId, dto.getWareId())
              //  .eq(!ObjectUtils.isEmpty(dto.getThirdPlatId()), SysProdDeal::getThirdPlatId, dto.getThirdPlatId())
                .like(!ObjectUtils.isEmpty(dto.getPlatOrderNo()), SysProdDeal::getPlatOrderNo, dto.getPlatOrderNo())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysProdDeal::getPlatProdStatus, dto.getStatus())
                .in(!ObjectUtils.isEmpty(prodIdList), SysProdDeal::getProdId, prodIdList)
                .in(SysProdDeal::getStatus, 1, 3);

        if (!ObjectUtils.isEmpty(dto.getThirdPlatId())) {
            if (dto.getThirdPlatId().equals(10000)) {
                qw.in(SysProdDeal::getThirdPlatId, 11001,11002,11003);
            }else{
                qw.eq(SysProdDeal::getThirdPlatId, dto.getThirdPlatId());
            }
        }

        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            qw.in(SysProdDeal::getShopId, shopIdPowerList);
        }

        // 出库情况
        if (!ObjectUtils.isEmpty(dto.getOddNo()) || !ObjectUtils.isEmpty(dto.getSaleStatus())) {
            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                    .select(SysWareOut::getId, SysWareOut::getOddNo, SysWareOut::getRelationId)
                    .eq(SysWareOut::getType, type)
                    .like(!ObjectUtils.isEmpty(dto.getOddNo()), SysWareOut::getOddNo, dto.getOddNo())
                    .apply(!ObjectUtils.isEmpty(shopId), " (shop_id is null or shop_id = '" + (ObjectUtils.isEmpty(shopId) ? "0" : shopId) + "') "));
            if (!ObjectUtils.isEmpty(dto.getOddNo())) {
                List<Integer> outIdList = BaseUtils.initList();
                outIdList.addAll(outList.stream().map(SysWareOut::getRelationId).collect(Collectors.toList()));
                qw.in(SysProdDeal::getSaleId, outIdList);
            }
            if (!ObjectUtils.isEmpty(dto.getSaleStatus())) {
                List<Integer> outIdList = BaseUtils.initList();
                outIdList.addAll(outList.stream().map(SysWareOut::getId).collect(Collectors.toList()));
                List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                        .in(SysWareOutProd::getOutId, outIdList)
                        .in(!ObjectUtils.isEmpty(prodIdList), SysWareOutProd::getProdId, prodIdList));
                List<Integer> dealIdList = BaseUtils.initList();
                dealIdList.addAll(outProdList.stream().map(SysWareOutProd::getDealId).collect(Collectors.toList()));

                // 寄售状态：4-已完成且有出库单，3-已完成但没出库单，2-未完成但有出库单，1-未完成且没出库单
                switch (dto.getSaleStatus()) {
                    case 1:
                        qw.and(a -> a.eq(SysProdDeal::getStatus, 1).notIn(SysProdDeal::getId, dealIdList));
                        break;
                    case 2:
                        qw.and(a -> a.eq(SysProdDeal::getStatus, 1).in(SysProdDeal::getId, dealIdList));
                        break;
                    case 3:
                        qw.and(a -> a.eq(SysProdDeal::getStatus, 3).notIn(SysProdDeal::getId, dealIdList));
                        break;
                    case 4:
                        qw.and(a -> a.eq(SysProdDeal::getStatus, 3).in(SysProdDeal::getId, dealIdList).isNull(SysProdDeal::getPlatSoldPrice));
                        break;
                    case 5:
                        qw.and(a -> a.eq(SysProdDeal::getStatus, 3).in(SysProdDeal::getId, dealIdList).isNotNull(SysProdDeal::getPlatSoldPrice));
                        break;
                }
            }
        }


        // 商品清单
        IPage<SysProdDeal> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            prodIdList = pageResult.getRecords().stream().map(SysProdDeal::getProdId).collect(Collectors.toList());

            // 商品信息
            List<SysProd> prodList = iSysProdService.listWithoutLogic(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
            Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
            prodList.clear();

            // 仓库
            List<SysWare> wareList = iSysWareService.listWithoutLogic(Wrappers.<SysWare>lambdaQuery());
            Map<Integer, SysWare> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, a -> a));
            wareList.clear();

            List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery()
                    .in(SysWareInProd::getProdId, prodIdList));
            Map<Integer, SysWareInProd> inProdMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));
            inProdList.clear();

            // 商家
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery());
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
            shopList.clear();

            // 平台
            List<Integer> platIdList = BaseUtils.initList();
            platIdList.addAll(pageResult.getRecords().stream().map(SysProdDeal::getThirdPlatId).collect(Collectors.toList()));
            List<SysThirdPlat> platList = iSysThirdPlatService.listWithoutLogic(Wrappers.<SysThirdPlat>lambdaQuery()
                    .in(SysThirdPlat::getId, platIdList));
            Map<Integer, SysThirdPlat> platMap = platList.stream().collect(Collectors.toMap(SysThirdPlat::getId, a -> a));

            // 货架
            Map<Integer, String> shelvesMap = new HashMap<>();// 商品id -> 货架名
            List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                    .in(SysWareShelvesProd::getProdId, prodIdList));
            if (!ObjectUtils.isEmpty(shelvesProdList)) {
                Map<Integer, List<SysWareShelvesProd>> shelvesProdMap = shelvesProdList.stream().collect(Collectors.groupingBy(SysWareShelvesProd::getShelvesId));
                List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                        .in(SysWareShelves::getId, shelvesProdList.stream().map(SysWareShelvesProd::getShelvesId).collect(Collectors.toList())));
                shelvesList.forEach(shelves -> {
                    List<SysWareShelvesProd> prods = shelvesProdMap.get(shelves.getId());
                    prods.forEach(prod -> {
                        shelvesMap.put(prod.getProdId(), shelves.getName());
                    });
                });
            }
            shelvesProdList.clear();

            // 出库情况
            List<Integer> dealIdList2 = BaseUtils.initList();
            dealIdList2.addAll(pageResult.getRecords().stream().map(SysProdDeal::getId).collect(Collectors.toList()));
            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getDealId, dealIdList2)
                    .in(SysWareOutProd::getProdId, prodIdList));
            Map<Integer, String> oddNoMap = new HashMap<>();
            Map<Integer, Date> outTimeMap = new HashMap<>();
            if (!ObjectUtils.isEmpty(outProdList)) {
                Map<Integer, Integer> prodOutMap = outProdList.stream().collect(Collectors.toMap(SysWareOutProd::getDealId, SysWareOutProd::getOutId));

                List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                        .select(SysWareOut::getId, SysWareOut::getOddNo, SysWareOut::getRelationId)
                        .in(SysWareOut::getId, outProdList.stream().map(SysWareOutProd::getOutId).collect(Collectors.toList())));

                Map<Integer, String> outMap = outList.stream().collect(Collectors.toMap(SysWareOut::getId, SysWareOut::getOddNo));
                outList.clear();
                prodOutMap.keySet().forEach(dealId -> {
                    String oddNo = Optional.ofNullable(prodOutMap.get(dealId)).map(outId -> outMap.get(outId)).orElse(null);
                    oddNoMap.put(dealId, oddNo);
                });

                List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                        .eq(SysWareOutBatchProd::getStatus, 4)
                        .in(SysWareOutBatchProd::getProdId, prodIdList));
                outTimeMap.putAll(batchProdList.stream().collect(Collectors.toMap(SysWareOutBatchProd::getProdId, SysWareOutBatchProd::getGmtOut)));
                batchProdList.clear();
            }

            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(pageResult.getRecords().stream()
                    .filter(a -> !ObjectUtils.isEmpty(a.getSaleId())).map(SysProdDeal::getSaleId).collect(Collectors.toList()));
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
            Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
            saleList.clear();

            Date now = DateTimeUtils.getNow();
            pageResult.getRecords().forEach(data -> {
                SysProdListVo vo = new SysProdListVo();

                vo.setId(data.getProdId());
                vo.setEventType(type);
                vo.setWareDays(data.getWareDays());
                vo.setGmtCreate(data.getGmtCreate());
                vo.setCostPrice(data.getCostPrice());
                vo.setSupply(data.getSupply());
                vo.setDealId(data.getId());
                vo.setPlatOrderNo(data.getPlatOrderNo());

                // 出库单号
                vo.setOddNo(oddNoMap.get(data.getId()));
                vo.setOutNo(oddNoMap.get(data.getId()));

                // 商品信息
                SysProd prod = prodMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    vo.setImg(prod.getImg());
                    vo.setOneId(prod.getOneId());
                    vo.setPku(prod.getPku());
                    vo.setSku(prod.getSku());
                    vo.setSpec(prod.getSpec());
                    vo.setBrand(prod.getBrand());
                    vo.setRemarks(prod.getRemarks());
                    if (!ObjectUtils.isEmpty(prod.getShopId()) && prod.getShopId() == data.getShopId().intValue()) {
                        if (ObjectUtils.isEmpty(vo.getCostPrice())) {
                            vo.setCostPrice(prod.getCostPrice());
                        }
                        if (ObjectUtils.isEmpty(vo.getSupply())) {
                            vo.setSupply(prod.getSupply());
                        }
                    }
                }

                // 仓库信息
                SysWare ware = wareMap.get(data.getWareId());
                if (!ObjectUtils.isEmpty(ware)) {
                    vo.setWareId(ware.getId());
                    vo.setWareName(ware.getName());
                }

                // 入库信息
                SysWareInProd inProd = inProdMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(inProd)) {
                    vo.setCheckResult(inProd.getCheckResult());
                    vo.setGmtIn(inProd.getGmtCreate());
                    if (ObjectUtils.isEmpty(vo.getWareDays())) {
                        // 在仓天数
                        Integer wareDays = DateTimeUtils.timeDiff(ObjectUtils.isEmpty(inProd.getGmtPay()) ? now : inProd.getGmtPay(), inProd.getGmtCreate(), 4);
                        vo.setWareDays(wareDays);
                    }
                }

                // 归属人
                ShopUser shop = shopMap.get(data.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    vo.setShopId(data.getShopId());
                    vo.setShopUid(shop.getUid());
                    vo.setShopName(shop.getRealname());
                }

                // 上架情况
                vo.setSaleDays(DateTimeUtils.timeDiff(data.getStatus() == 3 ? data.getGmtModify() : now, data.getGmtCreate(), 4));
                vo.setSalePrice(data.getSalePrice());
                vo.setPlatSoldPrice(data.getPlatSoldPrice());

                // 平台情况
                SysThirdPlat plat = platMap.get(data.getThirdPlatId());
                if (!ObjectUtils.isEmpty(plat)) {
                    vo.setThirdPlatId(plat.getId());
                    vo.setPlatName(plat.getName());

                    vo.setGmtOut(outTimeMap.get(data.getProdId()));
                    SysProdSale sale = saleMap.get(data.getSaleId());
                    if (!ObjectUtils.isEmpty(sale)) {
                        vo.setGmtOrder(sale.getGmtCreate());
                    }

                    if (!ObjectUtils.isEmpty(data.getSalePrice())) {
                        shop = shopMap.get(data.getShopId());
                        if (plat.getName().contains("ouch")) {
                            if (!ObjectUtils.isEmpty(data.getSoldPrice()) && !ObjectUtils.isEmpty(data.getPlatSoldPrice())) {
                                vo.setDecrFee(data.getSalePrice().subtract(data.getSoldPrice()));
                                vo.setServiceFee(data.getPlatSoldPrice().subtract(data.getSoldPrice()));
                                vo.setSoldPrice(data.getSoldPrice());
                            } else {
                                vo.setDecrFee(null);
                                vo.setServiceFee(shop.getOcFee());
                                vo.setSoldPrice(null);
                            }
                        } else {
                            ShopUserPlat shopPlat = iShopUserPlatService.getShopPlat(data.getShopId(), data.getThirdPlatId());
                            // 到手价 = 寄价格*(1-第三方手续费-第三方提现费)*100%-第三方其他费用-0C服务费
                            BigDecimal serviceFee = data.getSalePrice().multiply(
                                            shopPlat.getServiceRate().add(shopPlat.getDrawRate())
                                    ).divide(SysConstants.hundred, 2, RoundingMode.HALF_EVEN)
                                    .add(plat.getOtherFee()).add(shop.getOcFee());

                            vo.setDecrFee(serviceFee);
                            vo.setServiceFee(serviceFee);
                            vo.setSoldPrice(data.getSalePrice().subtract(vo.getDecrFee()));
                        }
                    }

                    if (!ObjectUtils.isEmpty(vo.getSoldPrice())) {
                        vo.setGmtSettle(data.getGmtModify());
                    }
                }

                // 货架号
                vo.setShelvesName(shelvesMap.get(data.getProdId()));

                // 寄售状态：5-已出库且已结算，4-已完成且有出库单，3-已完成但没出库单，2-未完成但有出库单，1-未完成且没出库单
                if (data.getStatus() == 3) {
                    if (ObjectUtils.isEmpty(vo.getOddNo())) {
                        vo.setSaleStatus(3);
                    } else {
                        if (ObjectUtils.isEmpty(vo.getPlatSoldPrice())) {
                            vo.setSaleStatus(4);
                        } else {
                            vo.setSaleStatus(5);
                        }
                    }
                } else {
                    if (!ObjectUtils.isEmpty(vo.getOddNo())) {
                        vo.setSaleStatus(2);
                    } else {
                        vo.setSaleStatus(1);
                    }
                }

                voList.add(vo);
            });
        }

        IPage<SysProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public List<SysProdGroupListVo> saleGroup(SysProdDealPageDto dto) {
        List<SysProdListVo> dataList = saleList(dto).getRecords();
        Map<String, List<SysProdListVo>> skuMap = dataList.stream().collect(Collectors.groupingBy(SysProdListVo::getSku));

        List<SysProdGroupListVo> groupList = new ArrayList<>();
        skuMap.keySet().forEach(sku -> {
            SysProdGroupListVo vo = new SysProdGroupListVo();

            List<SysProdListVo> prodList = skuMap.get(sku);
            SysProdListVo sample = prodList.get(0);
            BeanUtils.copyProperties(sample, vo);

            vo.setProdList(prodList.stream().sorted(Comparator.comparing(a -> {
                return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                        .map(spec -> BaseUtils.dealSizeStr(spec).toString())
                        .orElse("");
            })).collect(Collectors.toList()));

            int num = 0;
            int intactNum = 0;
            int brokenNum = 0;

            for (SysProdListVo prod : prodList) {
                num++;

                Integer checkResult = prod.getCheckResult();
                if (!ObjectUtils.isEmpty(checkResult)) {
                    if (checkResult == 1) {
                        intactNum++;
                    } else {
                        brokenNum++;
                    }
                }
            }
            vo.setIntactNum(intactNum);
            vo.setBrokenNum(brokenNum);
            vo.setNum(num);
            groupList.add(vo);
        });

        return groupList;
    }

    @Override
    public IPage<SysProdListVo> outList(SysProdPageDto dto) {
        dto.setStatusList(new ArrayList<>(Arrays.asList(6)));
        dto.setSearchType(null);
        IPage<SysProdListVo> voPage = iSysProdService.searchList(dto);

        voPage.getRecords().forEach(data -> {
            data.setGmtModify(data.getGmtOut());
        });

        return voPage;
    }

    @Override
    @Deprecated
    public List<SysProdGroupListVo> outGroup(SysProdPageDto dto) {
        List<SysProdListVo> dataList = outList(dto).getRecords();
        Map<String, List<SysProdListVo>> skuMap = dataList.stream().collect(Collectors.groupingBy(SysProdListVo::getSku));

        List<SysProdGroupListVo> groupList = new ArrayList<>();
        skuMap.keySet().forEach(sku -> {
            SysProdGroupListVo vo = new SysProdGroupListVo();

            List<SysProdListVo> prodList = skuMap.get(sku);
            SysProdListVo sample = prodList.get(0);
            BeanUtils.copyProperties(sample, vo);

            vo.setProdList(prodList);
            vo.setNum(prodList.size());
            groupList.add(vo);
        });

        return groupList;
    }

    @Override
    public Boolean saveWareInfo(Integer relationId, List<SysProdDealListVo> prodList) {
        return baseMapper.saveWareInfo(relationId, prodList);
    }

    @Override
    public Boolean dealSale(SysProdSaleDealDto dto) {
        int type = SysProdEvent.TypeSale;
        if (count(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(SysProdDeal::getStatus, 1)
                .eq(SysProdDeal::getType, type)
                .in(SysProdDeal::getProdId, dto.getProdIdList())) != dto.getProdIdList().size()) {
            throw new BaseException(LanguageConfigService.i18nForMsg("已勾选商品的寄售状态存在异常"));
        }

        List<Integer> prodIdList = dto.getProdIdList();
        List<SysProdEvent> eventList = new ArrayList<>();
        Date now = DateTimeUtils.getNow();

        List<SysProdDeal> dealList = list(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(SysProdDeal::getType, type)
                .eq(SysProdDeal::getStatus, 1)
                .in(SysProdDeal::getProdId, dto.getProdIdList()));
        switch (dto.getType()) {
            case 1:
                if (iSysWareOutProdService.count(Wrappers.<SysWareOutProd>lambdaQuery()
                        .in(SysWareOutProd::getDealId, dealList.stream().map(SysProdDeal::getId).collect(Collectors.toList()))) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("商品已生成出库单"));
                }
                List<SysProdDealListVo> wareInfoList = new ArrayList<>();

                // 更新入库商品信息
                iSysWareInProdService.update(Wrappers.<SysWareInProd>lambdaUpdate()
                        .set(SysWareInProd::getGmtPay, now) // 免支付出库
                        .in(SysWareInProd::getProdId, prodIdList));

                List<SysProd> prods = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                        .select(SysProd::getId, SysProd::getWareId, SysProd::getShopId)
                        .in(SysProd::getId, dto.getProdIdList()));
                Map<Integer, List<SysProd>> prodWareMap = prods.stream().collect(Collectors.groupingBy(SysProd::getWareId));
                Map<Integer, Integer> dealIdMap = dealList.stream().collect(Collectors.toMap(SysProdDeal::getProdId, SysProdDeal::getId));

                dealList.forEach(deal -> {
                    SysProdDealListVo wareInfo = new SysProdDealListVo();
                    wareInfo.setType(SysProdEvent.TypeSale);
                    wareInfo.setProdId(deal.getProdId());
                    wareInfo.setRelationId(0);
                    wareInfo.setWareDays(DateTimeUtils.timeDiff(now, deal.getGmtIn(), 4));
                    wareInfo.setWareFee(SysConstants.zero);
                    wareInfoList.add(wareInfo);
                });
                if (!ObjectUtils.isEmpty(wareInfoList)) {
                    saveWareInfo(0, wareInfoList);
                }

                // 生成出库单
                prodWareMap.keySet().forEach(wareId -> {
                    SysWareOut outDto = new SysWareOut();
                    BeanUtils.copyProperties(dto, outDto);

                    String oddNo = iSysCodePoolService.build(type, 1).get(0);
                    outDto.setOddNo(oddNo);
                    outDto.setRelationId(0);
                    outDto.setType(type);

                    Map<Integer, Integer> prodDealMap = new HashMap<>();
                    List<SysProd> prodList = prodWareMap.get(wareId);
                    prodList.forEach(prod -> {
                        prodDealMap.put(prod.getId(), dealIdMap.get(prod.getId()));
                    });
                    outDto.setProdDealMap(prodDealMap);

                    iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                            .in(SysProd::getId, prodList.stream().map(SysProd::getId).collect(Collectors.toList()))
                            .set(SysProd::getOddNo, oddNo));

                    SysProd prod = prodList.get(0);
                    outDto.setGmtCreate(now);
                    outDto.setWareId(wareId);
                    outDto.setShopId(prod.getShopId());

                    iSysWareOutService.saveSysWareOut(outDto);
                });
                break;
            case 2:
                throw new BaseException(LanguageConfigService.i18nForMsg("如需转自营，请与商家协商进行套现"));
            case 4:
                // 商品下架，流程结束
                iSysProdService.release(prodIdList, SysProdEvent.TypeSale);
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("操作不明"));
        }

        iSysProdEventService.insertList(eventList);
        return true;
    }

    @Override
    public Boolean saleOut(List<Integer> prodIdList, Integer outId) {
        if (ObjectUtils.isEmpty(prodIdList)) {
            return false;
        }

        List<SysProdDeal> dealList = list(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(SysProdDeal::getType, SysProdEvent.TypeSale)
                .eq(SysProdDeal::getStatus, 1)
                .in(SysProdDeal::getProdId, prodIdList));
        if (ObjectUtils.isEmpty(dealList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("寄售状态异常"));
        }
        List<SysThirdPlat> platList = iSysThirdPlatService.listWithoutLogic(Wrappers.<SysThirdPlat>lambdaQuery()
                .in(SysThirdPlat::getId, dealList.stream().map(SysProdDeal::getThirdPlatId).collect(Collectors.toList())));
        Map<Integer, SysThirdPlat> platMap = platList.stream().collect(Collectors.toMap(SysThirdPlat::getId, a -> a));

        List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                .in(SysProd::getId, prodIdList));
        Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
        prodList.clear();

        List<SysWare> wareList = iSysWareService.list();
        Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
        wareList.clear();

        List<ShopUser> shopList = iShopUserService.list();
        Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
        shopList.clear();

        // 流程结束：寄售 完成
        Date now = DateTimeUtils.getNow();
        List<SysProdEvent> eventList = new ArrayList<>();
        for (SysProdDeal deal : dealList) {
            deal.setGmtModify(now);
            deal.setGmtOut(now);
            deal.setStatus(3);

            SysProd prod = prodMap.get(deal.getProdId());
            if (!ObjectUtils.isEmpty(prod)) {
                deal.setSupply(prod.getSupply());
                deal.setCostPrice(prod.getCostPrice());
                deal.setGmtIn(prod.getGmtIn());
            }

            SysThirdPlat plat = platMap.get(deal.getThirdPlatId());
            if (!ObjectUtils.isEmpty(plat)) {
                BigDecimal soldPrice = null;
                ShopUser shop = shopMap.get(deal.getShopId());
                if (ObjectUtils.isEmpty(shop)) {
                    shop = new ShopUser();
                    shop.setId(0);
                    shop.setOcFee(new BigDecimal("5"));
                }
                if (plat.getId().equals(11001)  // stockX 平台
                        || plat.getId().equals(11002) // goat stv
                        || plat.getId().equals(11003) // goat instant ship
                        || plat.getId().equals(11004) // goat 瑕疵
                        || plat.getId().equals(11005) // kicks crew
                        || plat.getId().equals(11006) // ebay
                        || plat.getId().equals(11007) // Poizon
                        || plat.getId().equals(11008) // poizon dropship
                        || plat.getId().equals(20006) // touch
                        || plat.getId().equals(11010) // Tiktok
                        || plat.getId().equals(11011) // b2b
                        || plat.getId().equals(11012) // stockX Direct
                        || plat.getId().equals(11013) // shein
                ) {
                    soldPrice = shop.getOcFee().negate();
                } else if (plat.getId().equals(11009)) { // stockX flex 的出库不会扣费
                    soldPrice = BigDecimal.ZERO;
                } else {
                    // 到手价 = 寄价格*(1-第三方手续费-第三方提现费)*100%-第三方其他费用-0C服务费
                    ShopUserPlat shopPlat = iShopUserPlatService.getShopPlat(deal.getShopId(), deal.getThirdPlatId());
                    BigDecimal serviceFee = deal.getSalePrice().multiply(
                                    shopPlat.getServiceRate().add(shopPlat.getDrawRate())
                            ).divide(SysConstants.hundred, 2, RoundingMode.HALF_EVEN)
                            .add(plat.getOtherFee()).add(shop.getOcFee());
                    deal.setSoldPrice(deal.getSalePrice().subtract(serviceFee));
                    soldPrice = deal.getSoldPrice();
                }

                // 给商家发钱
                iSysMoneyService.change(5, shop.getId(), soldPrice);

                // 生成流水：平台服务费
                SysBill bill = new SysBill();
                bill.setStatus(2);
                bill.setUserId(shop.getId());
                bill.setUserType(5);
                bill.setPayType(4);
                bill.setRelationId(outId);
                bill.setTotalFee(soldPrice.abs());

                Map<String, String> attach = new HashMap<>();
                attach.put("platOrderNo", deal.getPlatOrderNo());

                if (soldPrice.compareTo(SysConstants.zero) > 0) {
                    attach.put("wareName", wareMap.get(deal.getWareId()));
                    if (!ObjectUtils.isEmpty(deal.getSalePrice())) {
                        attach.put("salePrice", deal.getSalePrice().toString());
                    }
                    if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
                        attach.put("platSoldPrice", deal.getPlatSoldPrice().toString());
                    }
                    if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice()) && !ObjectUtils.isEmpty(deal.getSoldPrice())) {
                        attach.put("serviceFee", deal.getPlatSoldPrice().subtract(deal.getSoldPrice()).toString());
                    }
                    if (!ObjectUtils.isEmpty(prod)) {
                        attach.put("remarks", prod.getRemarks());
                        attach.put("sku", prod.getSku());
                        attach.put("spec", prod.getSpec());
                        attach.put("oneId", prod.getOneId());
                    }

                    bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysProdEvent.TypeSale, 4));
                    bill.setIeType(1);
                    bill.setRelationType(SysProdEvent.TypeSale);
                } else {
                    bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatSale, 4));
                    bill.setIeType(-1);
                    bill.setRelationType(SysBill.TypePlatSale);
                }
                bill.setAttach(BaseUtils.mapToQuery(attach) + "&");
                iSysBillService.saveSysBill(bill);
            }

            // 商品事件：出库
            SysProdEvent event = new SysProdEvent();
            event.setProdId(deal.getProdId());
            event.setShopId(deal.getShopId());
            event.setType(SysProdEvent.TypeOut);
            event.setDescription("商品出库");
            event.setRelationId(deal.getId());
            if (event.getProdId() > 0)
                eventList.add(event);

            deal.updateById();
        }

        // 商品：归属人重置，所属仓库重置
        iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                .in(SysProd::getId, prodIdList)
                .setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', gmt_out = '" + DateTimeUtils.getNowByStr() + "', `status` = 6, ware_id = null, shop_id = null"));

        iSysProdEventService.insertList(eventList);

        return true;
    }

    @Override
    public Boolean dealOut(SysProdSaleDealDto dto) {
        if (ObjectUtils.isEmpty(dto.getProdIdList())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意商品"));
        }

        List<SysWareOutProd> outProds = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                .in(SysWareOutProd::getProdId, dto.getProdIdList()));
        if (!ObjectUtils.isEmpty(outProds)) {
            if (iSysWareOutService.count(Wrappers.<SysWareOut>lambdaQuery()
                    .in(SysWareOut::getStatus, 1, 2, 4, 5)
                    .in(SysWareOut::getId, outProds.stream().map(SysWareOutProd::getOutId).collect(Collectors.toList()))) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("存在商品已生成出库单"));
            }
        }

        List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                .in(SysProd::getId, dto.getProdIdList()));

        int type = SysProdEvent.TypePlatOut;
        Map<Integer, Map<Integer, SysWareOut>> outMap = new HashMap<>();

        prodList.forEach(prod -> {
            Integer wareId = prod.getWareId();
            Integer shopId = prod.getShopId();

            Map<Integer, SysWareOut> shopMap = outMap.get(wareId);
            SysWareOut out = shopMap.get(shopId);
            if (ObjectUtils.isEmpty(out)) {
                out = new SysWareOut();
                BeanUtils.copyProperties(dto, out);

                out.setType(type);
                out.setRelationId(0);
                out.setOddNo(iSysCodePoolService.build(type, 1).get(0));
                out.setWareId(wareId);
                out.setShopId(shopId);
                out.setProdIdList(new ArrayList<>());
            }
            out.getProdIdList().add(prod.getId());
        });

        outMap.keySet().forEach(wareId -> {
            Map<Integer, SysWareOut> shopMap = outMap.get(wareId);
            shopMap.keySet().forEach(shopId -> {
                iSysWareOutService.saveSysWareOut(shopMap.get(shopId));
            });
        });

        return true;
    }

    @Override
    public List<ShopEventListVo> eventList() {
        List<ShopEventListVo> voList = new ArrayList<>();
        Integer shopId = JwtContentHolder.getShopId();

        List<SysProdTransport> transportList = iSysProdTransportService.list(Wrappers.<SysProdTransport>lambdaQuery()
                .eq(SysProdTransport::getStatus, 3)
                .eq(SysProdTransport::getShopId, shopId));

        List<SysProdTransfer> transferList = iSysProdTransferService.list(Wrappers.<SysProdTransfer>lambdaQuery()
                .eq(SysProdTransfer::getStatus, 3)
                .eq(SysProdTransfer::getShopId, shopId));

        List<SysProdCash> cashList = iSysProdCashService.list(Wrappers.<SysProdCash>lambdaQuery()
                .eq(SysProdCash::getStatus, 3)
                .eq(SysProdCash::getShopId, shopId));

        List<Integer> idList = new ArrayList<>();
        idList.addAll(transportList.stream().map(SysProdTransport::getId).collect(Collectors.toList()));
        idList.addAll(transferList.stream().map(SysProdTransfer::getId).collect(Collectors.toList()));
        idList.addAll(cashList.stream().map(SysProdCash::getId).collect(Collectors.toList()));

        if (ObjectUtils.isEmpty(idList)) {
            return voList;
        }

        Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealMap = dealGroup(
                idList,
                new ArrayList<>(Arrays.asList(SysProdEvent.TypeSend, SysProdEvent.TypeTransport, SysProdEvent.TypeCash, SysProdEvent.TypeTransfer)),
                null);

        if (!ObjectUtils.isEmpty(transportList)) {
            transportList.forEach(data -> {
                ShopEventListVo vo = new ShopEventListVo();
                vo.setDescription("平台已通过审核");
                vo.setEventType(data.getType());
                vo.setOddNo(data.getOddNo());
                vo.setGmtCreate(data.getGmtModify());

                List<SysProdDealListVo> prodVoList = dealMap.get(data.getType()).get(data.getId());
                // 仓储费用
                BigDecimal wareTotalFee = SysConstants.zero;
                for (SysProdDealListVo prodVo : prodVoList) {
                    if (!ObjectUtils.isEmpty(prodVo)) {
                        wareTotalFee = wareTotalFee.add(prodVo.getWareFee());
                    }
                }
                if (!ObjectUtils.isEmpty(data.getFreeFee()) && !ObjectUtils.isEmpty(data.getDeliveryFee()) && !ObjectUtils.isEmpty(data.getPlatFee())) {
                    vo.setTotalFee(wareTotalFee.add(data.getDeliveryFee()).add(data.getPlatFee().multiply(new BigDecimal(prodVoList.size()))).subtract(data.getFreeFee()));
                }

                voList.add(vo);
            });
        }
        if (!ObjectUtils.isEmpty(transferList)) {
            Map<Integer, List<SysProdDealListVo>> dealTree = dealMap.get(SysProdEvent.TypeTransfer);
            transferList.forEach(data -> {
                ShopEventListVo vo = new ShopEventListVo();
                vo.setDescription("平台已通过审核");
                vo.setEventType(SysProdEvent.TypeTransfer);
                vo.setOddNo(data.getOddNo());
                vo.setGmtCreate(data.getGmtModify());

                List<SysProdDealListVo> prodVoList = dealTree.get(data.getId());
                // 仓储费用
                BigDecimal wareTotalFee = SysConstants.zero;
                for (SysProdDealListVo prodVo : prodVoList) {
                    if (!ObjectUtils.isEmpty(prodVo)) {
                        wareTotalFee = wareTotalFee.add(prodVo.getWareFee());
                    }
                }
                if (!ObjectUtils.isEmpty(data.getFreeFee()) && !ObjectUtils.isEmpty(data.getDeliveryFee()) && !ObjectUtils.isEmpty(data.getPlatFee())) {
                    vo.setTotalFee(wareTotalFee.add(data.getDeliveryFee()).add(data.getPlatFee().multiply(new BigDecimal(prodVoList.size()))).subtract(data.getFreeFee()));
                }
                voList.add(vo);
            });
        }
        if (!ObjectUtils.isEmpty(cashList)) {
            Map<Integer, List<SysProdDealListVo>> dealTree = dealMap.get(SysProdEvent.TypeCash);
            cashList.forEach(data -> {
                ShopEventListVo vo = new ShopEventListVo();
                vo.setDescription("平台已报价");
                vo.setEventType(SysProdEvent.TypeCash);
                vo.setOddNo(data.getOddNo());
                vo.setGmtCreate(data.getGmtModify());

                List<SysProdDealListVo> prodVoList = dealTree.get(data.getId());

                BigDecimal wareTotalFee = SysConstants.zero; // 仓储费用
                BigDecimal saleTotalFee = SysConstants.zero; // 实际报价/寄售价
                BigDecimal preTotalFee = SysConstants.zero; // 预计报价
                for (SysProdDealListVo prodVo : prodVoList) {
                    if (!ObjectUtils.isEmpty(prodVo)) {
                        wareTotalFee = wareTotalFee.add(prodVo.getWareFee());

                        if (!ObjectUtils.isEmpty(preTotalFee) && !ObjectUtils.isEmpty(prodVo.getPrePrice()))
                            preTotalFee = preTotalFee.add(prodVo.getPrePrice());
                        else
                            preTotalFee = null;
                        if (!ObjectUtils.isEmpty(saleTotalFee) && !ObjectUtils.isEmpty(prodVo.getSalePrice()))
                            saleTotalFee = saleTotalFee.add(prodVo.getSalePrice());
                        else
                            saleTotalFee = null;
                    }
                }

                if (!ObjectUtils.isEmpty(saleTotalFee))
                    vo.setTotalFee(saleTotalFee.subtract(wareTotalFee));
                voList.add(vo);
            });
        }

        return voList;
    }

    @Override
    public Boolean cancel(List<Integer> prodIdList, Integer type) {
        if (ObjectUtils.isEmpty(prodIdList)) {
            return false;
        }

        if (type == SysProdEvent.TypeSale) {
            // 下架后推送给touch
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .in(SysProd::getId, prodIdList)
                    .select(SysProd::getId, SysProd::getOneId, SysProd::getStatus));
            touchUtils.offProduct(prodList.stream().map(SysProd::getOneId).collect(Collectors.toList()));
        }

        List<SysProdDeal> dealList = list(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(SysProdDeal::getStatus, 1)
                .in(SysProdDeal::getProdId, prodIdList).eq(SysProdDeal::getType, type));
        if (ObjectUtils.isEmpty(dealList)) {
            return false;
        }

        // 商品事件：撤销操作
        List<SysProdEvent> eventList = new ArrayList<>();
        dealList.forEach(deal -> {
            SysProdEvent event = new SysProdEvent();
            event.setProdId(deal.getProdId());
            event.setShopId(deal.getShopId());
            event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, type)));
            event.setDescription("操作撤销");
            event.setRelationId(deal.getType() == SysProdEvent.TypeSale ? deal.getId() : deal.getRelationId());
            if (event.getProdId() > 0) eventList.add(event);
        });
        iSysProdEventService.insertList(eventList);

        update(Wrappers.<SysProdDeal>lambdaUpdate()
                .set(SysProdDeal::getStatus, 2)
                .in(SysProdDeal::getId, dealList.stream().map(SysProdDeal::getId).collect(Collectors.toList())));

        // 拒绝后释放商品
        iSysProdService.update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));
        // search同步更新
        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                .in(SysProdSearch::getProdId, prodIdList)
                .eq(SysProdSearch::getSearchType, 1));

        return true;
    }

    @Override
    public List<Integer> getProdIdList(int type, Integer relationId) {
        List<SysProdDeal> dealList = list(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(type != SysProdEvent.TypeSale, SysProdDeal::getRelationId, relationId)
                .eq(type == SysProdEvent.TypeSale, SysProdDeal::getProdId, relationId)
                .eq(SysProdDeal::getType, type));
        List<Integer> prodIdList = BaseUtils.initList();
        prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
        return prodIdList;
    }

}
