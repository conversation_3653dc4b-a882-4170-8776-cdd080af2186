package com.hzjm.service.service.export.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.DownloadFileRecord;
import com.hzjm.service.entity.SysTask;
import com.hzjm.service.entity.SysUser;
import com.hzjm.service.entity.SysWareOutProd;
import com.hzjm.service.model.DTO.SysInventoryExportDto;
import com.hzjm.service.model.DTO.SysProdPageDto;
import com.hzjm.service.model.VO.SysProdListVo;
import com.hzjm.service.model.enums.SourceType;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.model.enums.SysTaskType;
import com.hzjm.service.model.event.SysTaskEvent;
import com.hzjm.service.service.ISysProdService;
import com.hzjm.service.service.ISysUserService;
import com.hzjm.service.service.ISysWareOutProdService;
import com.hzjm.service.service.LanguageConfigService;
import com.hzjm.service.service.export.ISysWarehouseExportService;
import com.hzjm.service.service.job.IDownloadFileRecordService;
import com.hzjm.service.service.job.ISysTaskSaveService;
import com.hzjm.service.utils.TokenUtils;
import com.hzjm.service.utils.common.RedisUtils;
import com.hzjm.service.utils.converter.InterConvertedStr;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.*;

/**
 * <AUTHOR>
 * @date 2025/1/2 13:51
 * @description: 仓库管理-导出服务实现类
 */
@Slf4j
@Service
public class SysWarehouseExportServiceImpl implements ISysWarehouseExportService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ISysTaskSaveService iSysTaskSaveService;
    @Resource
    private IDownloadFileRecordService iDownloadFileRecordService;
    @Resource
    private ISysProdService iSysProdService;
    @Resource
    private ISysUserService iSysUserService;
    @Resource
    private ISysWareOutProdService iSysWareOutProdService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createExportSysInventoryTask(SysProdPageDto dto, String timeZone, String language) {
        log.info("创建-库存管理-导出库存-任务 dto:[{}],timeZone:[{}]", dto, timeZone);
        SysInventoryExportDto req = new SysInventoryExportDto();
        BeanUtil.copyProperties(dto, req);
        req.setToken(TokenUtils.getToken());
        req.setTimeZone(timeZone);
        req.setDefaultLanguage(language);
        //创建导出任务
        Integer userId = JwtContentHolder.getUserId();
        SysTask build = SysTask.builder()
                .uid(userId)
                .type(SysTaskType.DOWNLOAD_SYS_INVENTORY)
                .params(JSON.toJSONString(req))
                .status(SysTaskStatus.PENDING)
                .language(req.getLanguage())
                .build();
        Integer taskId = iSysTaskSaveService
                .createTask(build);
        if (taskId > 0) {
            log.info("创建-库存管理-导出库存-任务。成功，userId:{},taskId:{}", userId, taskId);
            String fileName = String.format(EXPORT_FILE_NAME_TEMPLATE,
                    LanguageConfigService.i18nForMsg("商品库存", req.getLanguage()),
                    DateTimeUtils.getFileSuffix(),
                    BaseUtils.getRandomStr(3));
            redisUtils.putForValueAndTimeOut(DOWNLOAD_FILE_TASK_ID.concat(String.valueOf(taskId)), fileName, TWENTY_FOUR_HOUR);
            iDownloadFileRecordService.create(DownloadFileRecord.builder()
                    .uid(userId)
                    .taskId(taskId)
                    .fileName(fileName)
                    .status(SysTaskStatus.PENDING)
                    .sourceType(SourceType.ADMIN)
                    .build());
            SysTaskEvent sysTaskEvent = new SysTaskEvent(this, build);
            applicationEventPublisher.publishEvent(sysTaskEvent);
            return true;
        }
        log.error("创建-库存管理-导出库存-任务。失败 userId:{}", userId);
        return false;
    }

    @Override
    public HashMap<String, String> exportSysInventory(SysInventoryExportDto dto, String fileName) {
        log.info("导出 库存管理-导出库存 导出开始 , dto: {}", dto);
        dto.setJwtContentHolder();
        HashMap<String, String> resultMap = new HashMap<>(2);
        // 当搜索状态为3-转运中时，同时增加搜索条件8-转仓中，9-代发中，10-转运中
        List<Integer> statusList2 = dto.getStatusList2();
        if (!ObjectUtils.isEmpty(dto.getStatusList2()) && statusList2.contains(3)) {
            statusList2.addAll(Arrays.asList(8, 9, 10));
            dto.setStatusList2(statusList2);
        }
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        dataList.add(LanguageConfigService.i18nForMsg(INVENTORY_EXPORT_EXCEL_HEADER_TEMPLATE, dto.getLanguage()));
        List<SysProdListVo> entityList = iSysProdService.searchList(dto).getRecords();
        // 人员姓名
        Map<Integer, String> userMap = new HashMap<>();
        // 商品出库信息
        Map<Integer, SysWareOutProd> outProdMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(entityList)) {
            List<SysUser> userList = iSysUserService.listAll();
            userMap.putAll(userList.stream().filter(a -> {
                return !ObjectUtils.isEmpty(a.getNickname());
            }).collect(Collectors.toMap(SysUser::getId, SysUser::getNickname)));
            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getProdId, entityList.stream().map(SysProdListVo::getId).collect(Collectors.toList())));
            outProdMap.putAll(outProdList.stream().collect(Collectors.toMap(SysWareOutProd::getProdId, a -> a)));
        }
        // 填充数据
        int i = 1;
        for (SysProdListVo data : entityList) {
            List<String> cowList = new ArrayList<>();
            cowList.add(BaseUtils.covertString(data.getRemarks()));
            cowList.add(BaseUtils.covertString(data.getSku()));
            cowList.add(BaseUtils.covertString(data.getSpec()));
            cowList.add(BaseUtils.covertString(data.getOneId()));
            cowList.add(BaseUtils.covertString(data.getShopName()));
            cowList.add(BaseUtils.covertString(data.getShopUid()));
            // 状态
            cowList.add(InterConvertedStr.getStatusCnString(data.getStatus()));
            cowList.set(cowList.size() - 1, LanguageConfigService.i18nForMsg(cowList.get(cowList.size() - 1), dto.getLanguage()));
            if (!ObjectUtils.isEmpty(data.getCheckResult())) {
                // 验货结果
                cowList.add(InterConvertedStr.getCheckResultCnString(data.getCheckResult()));
                cowList.set(cowList.size() - 1, LanguageConfigService.i18nForMsg(cowList.get(cowList.size() - 1), dto.getLanguage()));
            } else {
                cowList.add("");
            }
            if (!ObjectUtils.isEmpty(data.getType())) {
                // 预报类型
                cowList.add(InterConvertedStr.getTypeCnString(data.getType()));
            } else {
                cowList.add("");
            }
            cowList.add(BaseUtils.covertString(data.getWareDays()));
            cowList.add(BaseUtils.covertString(data.getWareName()));
            cowList.add(BaseUtils.covertString(data.getShelvesName()));
            cowList.add(BaseUtils.covertString(data.getLogNo()));
            cowList.add(BaseUtils.covertString(data.getInBatchNo()));
            cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtIn()));
            if (!ObjectUtils.isEmpty(data.getEventType())) {
                cowList.add(InterConvertedStr.getEventTypeCnString(data.getEventType()));
                cowList.set(cowList.size() - 1, LanguageConfigService.i18nForMsg(cowList.get(cowList.size() - 1), dto.getLanguage()));
            } else {
                cowList.add("");
            }
            cowList.add(BaseUtils.covertString(data.getPlatName()));
            cowList.add(BaseUtils.covertString(data.getOutNo()));
            cowList.add(BaseUtils.covertString(data.getPlatOrderNo()));
            cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtOut()));
            cowList.add(data.getPku());
            cowList.add(data.getInLogNoRelated());
            cowList.add(data.getCheckRemark());
            cowList.add(data.getCostPrice() != null ? data.getCostPrice().toString() : "");
            cowList.add(data.getSupply() != null ? data.getSupply() : "");
            dataList.add(cowList);
            i = i + 1;
        }
        log.info("导出 库存管理-导出库存 数据组装完成");
        try {
            String fileUrl = ExcelReader.exportExcel(dataList, fileName);
            resultMap.put("fileUrl", fileUrl);
        } catch (IOException e) {
            e.printStackTrace();
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }
        dataList.clear();
        log.info("导出 库存管理-导出库存 导出结束");
        return resultMap;
    }
}
