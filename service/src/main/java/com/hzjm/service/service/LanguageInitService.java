package com.hzjm.service.service;

import com.alibaba.fastjson.JSON;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.service.entity.Language;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysUser;
import com.hzjm.service.mapper.ApiConfigMapper;
import com.hzjm.service.mapper.SysUserMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 初始化语言
 */
@Service
@Slf4j
public class LanguageInitService {
    @Resource
    public ApiConfigMapper apiConfigMapper;
    @Resource
    public IShopUserService shopUserService;
    @Resource
    public RedisTemplate<String, String> redisTemplate;
    @Resource
    public SysUserMapper sysUserMapper;

    // Redis 中存储语言映射的 key
    private static final String LANGUAGE_MAP_REDIS_KEY = "knet:language:map";

    /**
     * 中英文翻译文本 - 从 Redis 获取
     */
    public Map<String, Language> getLanguageMap() {
        try {
            // 尝试从 Redis 获取语言映射
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(LANGUAGE_MAP_REDIS_KEY);
            if (!ObjectUtils.isEmpty(entries)) {
                Map<String, Language> result = new HashMap<>();
                for (Map.Entry<Object, Object> entry : entries.entrySet()) {
                    String key = entry.getKey().toString();
                    String value = entry.getValue().toString();
                    Language language = JSON.parseObject(value, Language.class);
                    result.put(key, language);
                }
                return result;
            }
        } catch (Exception e) {
            log.error("LanguageInitService getLanguageMap : {}", e.getMessage(), e);
        }

        // Redis 中没有数据，从数据库加载并缓存到 Redis
        return setLanguageMap();
    }

    /**
     * 中英文翻译文本 - 设置到 Redis
     */
    public Map<String, Language> setLanguageMap() {
        log.info("LanguageInitService setLanguageMap start !");

        // 从数据库查询语言配置
        List<Language> list = apiConfigMapper.selectLanguage();

        Map<String, Language> languageMap = list.stream()
                .collect(Collectors.toMap(Language::getMsg, language -> language, (existing, replacement) -> existing));

        // 将语言映射存储到 Redis Hash 结构中
        try {
            Map<String, String> hashMap = new HashMap<>();
            for (Map.Entry<String, Language> entry : languageMap.entrySet()) {
                hashMap.put(entry.getKey(), JSON.toJSONString(entry.getValue()));
            }
            redisTemplate.opsForHash().putAll(LANGUAGE_MAP_REDIS_KEY, hashMap);
            log.info("LanguageInitService setLanguageMap Hash，size =  {} ", languageMap.size());
        } catch (Exception e) {
            log.error("LanguageInitService setLanguageMap error {}", e.getMessage(), e);
        }

        return languageMap;
    }

    /**
     * 清空 Redis 中的语言映射缓存
     */
    public void clearLanguageMapCache() {
        try {
            redisTemplate.delete(LANGUAGE_MAP_REDIS_KEY);
            log.info("LanguageInitService clearLanguageMapCache end");
        } catch (Exception e) {
            log.error("LanguageInitService clearLanguageMapCache error = {}", e.getMessage(), e);
        }
    }

    /**
     * 修改传入用户的语言
     */
    public void updateUserLanguage(String id, String language) {
        log.info("LanguageInitService updateUserLanguage start id ={} ,language={}", id, language);
        if (ObjectUtils.isEmpty(id) || StringUtil.isBlank(language)) {
            return;
        }
        String key = this.getKey(id);

        Map<Object, Object> map = this.getRedisUser(id);
        map.put("language", language);
        this.setUserRedis(key, map);

        log.info("LanguageInitService updateUserLanguage end");
    }

    /**
     * 获取或者设置缓存中的用户语言，默认和异常情况下使用英文
     */
    public String getOrSetLanguage(Integer id, Integer type) {
        if (ObjectUtils.isEmpty(id)) {
            return SysConstants.LANGUAGE_EN_US;
        }
        Map<Object, Object> map = new HashMap<>();

        String redisId = "type" + type +":"+ id;
        String key = this.getKey(redisId);

        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {
            try {
                map = redisTemplate.opsForHash().entries(key);
            } catch (Exception e) {
                log.info("getOrSetLanguage error id ={},key={} e={},e.message={}", id, key, e, e.getMessage());
                return SysConstants.LANGUAGE_EN_US;
            }

            if (!ObjectUtils.isEmpty(map) && !ObjectUtils.isEmpty(map.get("language"))) {
                return map.get("language") != null ? map.get("language").toString() : "";
            }
        }

        // 缓存中不存在时，查询数据库重新设置缓存
        UserEntity userEntity = this.getUserEntity(id, type);

        if (ObjectUtils.isEmpty(userEntity) || ObjectUtils.isEmpty(userEntity.getLanguage())) { // 查询不到用户信息时返回空
            log.info("LanguageInitService getLanguage user is null id= {}", id);
            return SysConstants.LANGUAGE_EN_US;
        }

        // 设置Redis缓存
        map.put("language", userEntity.getLanguage());
        this.setUserRedis(key, map);

        log.info("LanguageInitService getLanguage end userEntity = {}", JSON.toJSONString(map));
        return userEntity.getLanguage() != null ? userEntity.getLanguage() : "";
    }

    /**
     * 获取缓存中的用户
     */
    public Map<Object, Object> getRedisUser(String id) {
        log.info("LanguageInitService getRedisUser id ={}", id);
        if (ObjectUtils.isEmpty(id)) {
            return new HashMap<>();
        }

        String key = this.getKey(id);

        if (Boolean.TRUE.equals(redisTemplate.hasKey(key))) {

            return redisTemplate.opsForHash().entries(key);
        }

        return new HashMap<>();
    }

    /**
     * 返回数据库的用户信息
     * id = userID
     * type = 用户类型
     */
    UserEntity getUserEntity(Integer id, Integer type) {
        if (ObjectUtils.isEmpty(id) || ObjectUtils.isEmpty(type)) {
            return null;
        }

        UserEntity userEntity = new UserEntity();
        switch (type) {
            case 1: // 管理端
            case 4: // 仓库端
                SysUser sysUser = sysUserMapper.selectByIdWithoutLogic(id);

                if (ObjectUtils.isEmpty(sysUser)) {
                    return null;
                }

                userEntity.setLanguage(sysUser.getLanguage());
                return userEntity;

            case 5: // 商家端
                ShopUser shopUser = shopUserService.getById(id);

                if (ObjectUtils.isEmpty(shopUser)) {
                    return null;
                }

                userEntity.setLanguage(shopUser.getLanguage());
                return userEntity;

            default:
                ShopUser shopUser2 = shopUserService.getById(id);

                if (ObjectUtils.isEmpty(shopUser2)) {
                    return null;
                }

                userEntity.setLanguage(shopUser2.getLanguage());
                return userEntity;

        }

    }

    public String getKey(String id) {
        if (ObjectUtils.isEmpty(id)) {
            return "";
        }
        return SysConstants.USER_HASH_KEY + id;
    }

    /**
     * 重置缓存
     */
    public void reLangUage() {
        log.info("LanguageInitService reLangUage init start ");

        try {
            // 使用模式匹配批量删除所有用户缓存
            Set<String> keys = redisTemplate.keys(SysConstants.USER_HASH_KEY + "*");
            if (!ObjectUtils.isEmpty(keys)) {
                redisTemplate.delete(keys);
                log.info("Batch deleted user cache, total {} items", keys.size());
            }
        } catch (Exception e) {
            log.error("Failed to batch delete user cache: {}", e.getMessage(), e);
        }

        // 清空翻译文本的缓存
        this.clearLanguageMapCache();

        // 重新设置翻译文本的缓存
        this.setLanguageMap();

        log.info("LanguageInitService reLangUage init end ");
    }

    /**
     * 修改redis数据
     */
    public void setUserRedis(String key, Map<Object, Object> map) {
        if (StringUtil.isBlank(key)) {
            return;
        }
        redisTemplate.opsForHash().putAll(key, map);
        // 设置Redis键的过期时间为60分钟
        redisTemplate.expire(key, 60, TimeUnit.MINUTES);
    }

    /**
     * 获取单个翻译条目
     */
    public Language getLanguageItem(String msg) {
        try {
            Object value = redisTemplate.opsForHash().get(LANGUAGE_MAP_REDIS_KEY, msg);
            if (!ObjectUtils.isEmpty(value)) {
                return JSON.parseObject(value.toString(), Language.class);
            }
        } catch (Exception e) {
            log.error("Failed to get translation item from Redis: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 批量获取翻译条目
     */
    public Map<String, Language> getLanguageItems(List<String> msgs) {
        if (ObjectUtils.isEmpty(msgs)) {
            return new HashMap<>();
        }

        try {
            List<Object> keys = new ArrayList<>(msgs);
            List<Object> values = redisTemplate.opsForHash().multiGet(LANGUAGE_MAP_REDIS_KEY, keys);
            Map<String, Language> result = new HashMap<>();
            for (int i = 0; i < msgs.size(); i++) {
                if (!ObjectUtils.isEmpty(values.get(i))) {
                    Language language = JSON.parseObject(values.get(i).toString(), Language.class);
                    result.put(msgs.get(i), language);
                }
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to batch get translation items from Redis: {}", e.getMessage(), e);
        }
        return new HashMap<>();
    }
}

/**
 * 用户信息 —— redis 缓存
 */
@Data
class UserEntity {
    // 语言设置
    public String language;

}
