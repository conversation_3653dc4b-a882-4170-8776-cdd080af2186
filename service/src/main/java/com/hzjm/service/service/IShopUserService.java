package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.model.DTO.ShopUserPageDto;
import com.hzjm.service.model.VO.ShopUserAllListVo;
import com.hzjm.service.model.VO.ShopUserListVo;
import com.hzjm.service.model.VO.ShopUserPlatListVo;
import com.hzjm.service.model.VO.ShopUserVo;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商家 服务类
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
public interface IShopUserService extends IService<ShopUser> {

    ShopUser getByIdWithoutLogic(Integer id);

    ShopUserVo getDetail(Integer id);

    Boolean saveShopUser(ShopUser dto);

    Boolean insertList(List<ShopUser> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopUserListVo> searchList(ShopUserPageDto dto);

    List<ShopUser> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    ShopUser getByUid(String uid);

    List<ShopUserPlatListVo> queryUserPlatFeeByShopId(Integer shopId);

    List<ShopUserAllListVo> listAll(ShopUserPageDto dto);

    // 返回订单号与订单平台的查询
    List<Map<String, String>> getOrderIdAndPlatform();

    /**
     * 获取可发送商家邮箱地址 map< 用户id,邮箱地址>
     *
     * @param shopIds 待发送商家ids
     * @return map<用户id, 邮箱地址>
     */
    Map<Integer, String> getShopEmails(List<Long> shopIds);

    /**
     * 查询某个注册时间节点之后的用户列表
     *
     * @return 账号列表
     */
    List<ShopUser> getActiveUsersRegisteredOverDays();

    @SuppressWarnings("unchecked")
    Set<Integer> shopListByUserId(Integer userId);

    /**
     * 增加注册时间规则
     * 注册时间超过xx天
     *
     * @param shopUser 用户
     * @param dateTime 系统被调用时间
     * @param gap      xx天
     * @return 是否符合规则
     */
    boolean registerCondition(ShopUser shopUser, Date dateTime, Integer gap);

    /**
     * 根据用户id查询商家信息(缓存)
     *
     * @param id 用户id
     * @return 商家信息
     */
    ShopUser getShopUserWithCache(String id);

    /**
     * 是否允许上架b2b
     *
     * @param shopUserId 商家id
     * @return 是否允许
     */
    boolean allowCreatedForB2b(Integer shopUserId);

    /**
     * 校验商家密码
     *
     * @param key 密钥
     * @return 是否允许
     */
    boolean checkKey(String key, String password);
}
