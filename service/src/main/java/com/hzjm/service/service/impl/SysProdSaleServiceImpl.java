package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.AwsS3Utils;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.FileUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysProdSaleMapper;
import com.hzjm.service.model.DTO.SysProdSalePageDto;
import com.hzjm.service.model.DTO.SysProdThirdSaleDealDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.model.touch.TouchProdStatusRequest;
import com.hzjm.service.model.touch.TouchSettleRequest;
import com.hzjm.service.service.*;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 三方寄售单 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-07
 */
@Slf4j
@Service
public class SysProdSaleServiceImpl extends ServiceImpl<SysProdSaleMapper, SysProdSale> implements ISysProdSaleService {

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysThirdPlatService iSysThirdPlatService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private IShopPreService iShopPreService;

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Autowired
    private ISysProdSalePoolService iSysProdSalePoolService;

    @Autowired
    private TouchUtils touchUtils;

    private final PlatformTransactionManager transactionManager;

    public SysProdSaleServiceImpl(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
    }

    @Override
    public SysProdSale getByIdWithoutLogic(Integer id) {
        SysProdSale data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该三方寄售单"));
        }

        return data;
    }

    @Override
    public SysProdSaleVo getDetail(Integer id) {
        SysProdSale data = getByIdWithoutLogic(id);

        SysProdSaleVo vo = new SysProdSaleVo();
        BeanUtils.copyProperties(data, vo);

        Map<Integer, SysWare> wareMap = new HashMap<>();
        Map<Integer, List<SysProdDeal>> dealMap = new HashMap<>();
        Map<Integer, SysProd> prodMap = new HashMap<>();
        Map<Integer, SysWareInProd> prodWareMap = new HashMap<>();
        Map<Integer, ShopUser> shopMap = new HashMap<>();
        Map<Integer, SysThirdPlat> platMap = new HashMap<>();
        // 仓库信息
        List<SysWare> wareList = iSysWareService.list();
        wareMap.putAll(wareList.stream().collect(Collectors.toMap(SysWare::getId, a -> a)));
        wareList.clear();

        List<SysProdDeal> list = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(SysProdDeal::getSaleId, data.getId()));
        if (!ObjectUtils.isEmpty(list)) {
            List<Integer> prodIdList = list.stream().map(SysProdDeal::getProdId).distinct().collect(Collectors.toList());
            dealMap.putAll(list.stream().collect(Collectors.groupingBy(SysProdDeal::getSaleId)));
            list.clear();

            // 商品信息
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
            prodMap.putAll(prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a)));
            prodList.clear();

            // 在仓信息
            List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
            prodWareMap.putAll(inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a)));
        }

        // 商家信息
        List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery());
        shopMap.putAll(shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a)));
        shopList.clear();

        // 寄售平台
        List<SysThirdPlat> platList = iSysThirdPlatService.list();
        platMap.putAll(platList.stream().collect(Collectors.toMap(SysThirdPlat::getId, a -> a)));

        Date now = DateTimeUtils.getNow();
        List<SysProdDeal> dealList = dealMap.get(data.getId());
        if (!ObjectUtils.isEmpty(dealList)) {
            List<SysProdSaleListVo.SaleProdListVo> itemList = new ArrayList<>();

            dealList.forEach(deal -> {
                SysProdSaleListVo.SaleProdListVo prodVo = new SysProdSaleListVo.SaleProdListVo();

                prodVo.setSalePrice(deal.getSalePrice());
                prodVo.setPlatPrice(deal.getPlatSoldPrice());
                prodVo.setPlatOrderNo(deal.getPlatOrderNo());

                ShopUser shop = shopMap.get(deal.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    prodVo.setShopUid(shop.getUid());
                    prodVo.setShopName(shop.getRealname());
                    prodVo.setServiceFee(shop.getOcFee());

                    if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
                        if (ObjectUtils.isEmpty(deal.getSoldPrice())) {
                            if (ObjectUtils.isEmpty(shop.getOcFee())) {
                                prodVo.setServiceFee(SysConstants.zero);
                            }
                        } else {
                            prodVo.setServiceFee(deal.getPlatSoldPrice().subtract(deal.getSoldPrice()));
                        }
                        prodVo.setSoldPrice(deal.getPlatSoldPrice().subtract(prodVo.getServiceFee()));
                    }
                }

                SysProd prod = prodMap.get(deal.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    prodVo.setShopId(prod.getShopId());
                    prodVo.setProdId(prod.getId());
                    prodVo.setSku(prod.getSku());
                    prodVo.setSpec(prod.getSpec());
                    prodVo.setRemarks(prod.getRemarks());
                    prodVo.setImg(prod.getImg());
                    prodVo.setOneId(prod.getOneId());
                }

                SysWare ware = wareMap.get(deal.getWareId());
                if (!ObjectUtils.isEmpty(ware)) {
                    prodVo.setWareName(ware.getName());
                }

                if (!ObjectUtils.isEmpty(prod)) {
                    SysWareInProd inProd = prodWareMap.get(prod.getId());
                    if (!ObjectUtils.isEmpty(inProd)) {
                        // 在仓天数
                        Integer wareDays = DateTimeUtils.timeDiff(ObjectUtils.isEmpty(inProd.getGmtPay()) ? now : inProd.getGmtPay(), inProd.getGmtCreate(), 4);
                        prodVo.setWareDays(wareDays);
                    }
                }

                itemList.add(prodVo);
            });

            vo.setProdList(itemList.stream().sorted(Comparator.comparing(a -> {
                return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                        .map(spec -> BaseUtils.dealSizeStr(spec).toString())
                        .orElse("");
            })).collect(Collectors.toList()));
        }

        return vo;
    }

    @Override
    public Boolean saveSysProdSale(SysProdSale dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getProdList())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("订单下未包含商品，不受理"));
            }

            if (count(Wrappers.<SysProdSale>lambdaQuery().ne(SysProdSale::getStatus, 2).eq(SysProdSale::getPlatOrderNo, dto.getPlatOrderNo())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("The order of [" + dto.getPlatOrderNo() + "] has been created at erp system"));
            }

            // 获取三方寄售数组的所有 oneId
            List<String> oneIdList = dto.getProdList().stream().map(ThirdPlatProdListVo::getOneId).collect(Collectors.toList());

            // 根据这个 oneID List 去找 处在寄卖中状态的 SysProd
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .eq(SysProd::getStatus, 2)
                    .in(SysProd::getOneId, oneIdList));

            // 按 oneId 分组
            Map<String, SysProd> oneMap = prodList.stream().collect(Collectors.toMap(SysProd::getOneId, a -> a));

            // 所有不是处于寄售状态的 oneId
            List<String> reSaleList = new ArrayList<>();
            for (String oneId : oneIdList) {
                if (!oneMap.containsKey(oneId)) {
                    reSaleList.add(oneId);
                }
            }

            // 不为空 去 修正其状态 ，再读取一次
            if (!ObjectUtils.isEmpty(reSaleList)) {
                List<String> failList = iSysProdService.fixProd(reSaleList);

                if (!ObjectUtils.isEmpty(failList)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("此 SysProd 不处于 寄售状态，无法生成Sys Prod Sale ：" + BaseUtils.listToStr(",", failList)));
                }

                prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                        .eq(SysProd::getStatus, 2)
                        .in(SysProd::getOneId, oneIdList));
                oneMap = prodList.stream().collect(Collectors.toMap(SysProd::getOneId, a -> a));
            }

            // 处理生成编号异常
            List<String> list = new ArrayList<>();
            try {
                list = iSysCodePoolService.build(SysProdEvent.TypeSale, 1);
            } catch (Exception e) {
                throw new BaseException(LanguageConfigService.i18nForMsg("网络繁忙，请重试"));
            }

            if (ObjectUtils.isEmpty(list) || list.size() == 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("网络繁忙，请重试"));
            }

            // 存储这个 Sys ProdSale
            dto.setShopId(prodList.get(0).getShopId());
            dto.setOddNo(list.get(0));
            dto.setGmtDeal(BaseUtils.getGmtDeal());
            rs = baseMapper.insert(dto) > 0;

            // 读取 SysProdDeal 列表， 读取 sysProd 的 id 数组 ，然后去搜索 SysProdDeal 的 prodId 字段 且 status 为 1的
            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .in(SysProdDeal::getProdId, prodList.stream().map(SysProd::getId).collect(Collectors.toList()))
                    .isNull(SysProdDeal::getSaleId) // 超卖会重复推送，因此修改接收一次
                    .eq(SysProdDeal::getStatus, 1)
                    .eq(SysProdDeal::getType, 6)
            );

            if (!ObjectUtils.isEmpty(dealList)) {
                // 将 prodID 字段取出做键（也就是 sys prod 的主键）
                Map<Integer, SysProdDeal> dealMap = dealList.stream().collect(Collectors.toMap(SysProdDeal::getProdId, a -> a));

                // 循环三方推进来的订单产品表
                for (ThirdPlatProdListVo item : dto.getProdList()) {
                    SysProd prod = oneMap.get(item.getOneId());
                    SysProdDeal deal = dealMap.get(prod.getId());

                    if (!ObjectUtils.isEmpty(deal)) {
                        if (iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                                .set(SysProdDeal::getPlatOrderNo, item.getOrderNo())
                                .set(SysProdDeal::getThirdPlatId, dto.getPlatId())
                                .set(SysProdDeal::getSaleId, dto.getId())
                                .set(SysProdDeal::getRelationId, dto.getId())
                                .set(SysProdDeal::getPlatProdStatus, 2)
                                .eq(SysProdDeal::getId, deal.getId())
                                // 防止超卖幻读，加个版本锁
                                .isNull(SysProdDeal::getPlatOrderNo))) {
                            // 同步search
                            iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                                    .set(SysProdSearch::getDealId, deal.getId())
                                    .set(SysProdSearch::getOddNo, dto.getOddNo())
                                    .set(SysProdSearch::getPlatOrderNo, item.getOrderNo())
                                    .set(SysProdSearch::getOddType, SysProdEvent.TypeSale)
                                    .eq(SysProdSearch::getSearchType, 1)
                                    .eq(SysProdSearch::getProdId, prod.getId()));
                        }
                    }
                }
            }

        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    /**
     * 寄卖审核列表查询
     * 替换该方法： com.hzjm.service.service.impl.SysProdSaleServiceImpl#searchList(com.hzjm.service.model.DTO.SysProdSalePageDto)
     */
    @Override
    @TrimParam
    @ReadOnly
    public IPage<SysProdSaleListVo> searchListNew(SysProdSalePageDto dto) {
        Assert.notNull(dto,"searchListNew SysProdSalePageDto is null ");
        Assert.notNull(dto.getTabType(),"searchListNew SysProdSalePageDto TabType is null ");
        Assert.notNull(dto.getSize(),"searchListNew SysProdSalePageDto Size is null ");
        Assert.notNull(dto.getCurrent(),"searchListNew SysProdSalePageDto Current is null ");
        return baseMapper.selectSaleList(new Page<>(dto.getCurrent(), dto.getSize()), dto);
    }

    @Override
    @TrimParam
    public List<SysProdSaleListVo> searchListAll(SysProdSalePageDto dto) {
        Assert.notNull(dto," searchListAll SysProdSalePageDto is null ");
        Assert.notNull(dto.getTabType(),"searchListAll SysProdSalePageDto TabType is null ");
        return baseMapper.selectSaleList(dto);
    }
    @Override
    @TrimParam
    @ReadOnly
    public SysProdSaleCountVo searchSaleListCountNew(SysProdSalePageDto dto) {
        return baseMapper.selectSaleListCount(dto);
    }

    @Override
    @Deprecated
    public IPage<SysProdSaleListVo> searchList(SysProdSalePageDto dto) {

        LambdaQueryWrapper<SysProdSale> qw = Wrappers.<SysProdSale>lambdaQuery();

        String platOrderNo = dto.getPlatOrderNo() == null ? null : dto.getPlatOrderNo().trim();
        List<String> platName = dto.getPlatName() == null ? null : dto.getPlatName();
        String oddNo = dto.getOddNo() == null ? null : dto.getOddNo().trim();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdSale::getGmtCreate)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysProdSale::getId, dto.getIdList())
                .like(!ObjectUtils.isEmpty(platOrderNo), SysProdSale::getPlatOrderNo, platOrderNo)
                .in(!ObjectUtils.isEmpty(platName), SysProdSale::getPlatName, platName)
                .like(!ObjectUtils.isEmpty(oddNo), SysProdSale::getOddNo, oddNo)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdSale::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdSale::getGmtCreate, endTime);

        if (!ObjectUtils.isEmpty(dto.getPlatOrderNoList())) {
            // dto.getPlatOrderNoList() 去除前后空格
            dto.setPlatOrderNoList(dto.getPlatOrderNoList().stream().map(String::trim).collect(Collectors.toList()));
            qw.in(SysProdSale::getPlatOrderNo, dto.getPlatOrderNoList());
        }

        Date now = DateTimeUtils.getNow();

        if (!ObjectUtils.isEmpty(dto.getTabType())) {
            // 1-今日待处理，2-待处理，3-待出库，4-已出库，5-已关闭
            switch (dto.getTabType()) {
                case 1:
                    // 今日待处理
                    qw.eq(SysProdSale::getStatus, 1).le(SysProdSale::getGmtDeal, now);
                    break;
                case 2:
                    // 待处理
                    qw.eq(SysProdSale::getStatus, 1).gt(SysProdSale::getGmtDeal, now);
                    break;
                case 3:
                    qw.in(SysProdSale::getStatus, 3, 4);
                    break;
                case 4:
                    qw.eq(SysProdSale::getStatus, 5);
                    break;
                case 5:
                    qw.eq(SysProdSale::getStatus, 2);
                    break;
            }
        }


        List<Integer> prodIdList = null;
        if (!ObjectUtils.isEmpty(dto.getOneId()) || !ObjectUtils.isEmpty(dto.getOneIdList())
                || !ObjectUtils.isEmpty(dto.getSku()) || !ObjectUtils.isEmpty(dto.getSkuList())
                || !ObjectUtils.isEmpty(dto.getSpecList()) || !ObjectUtils.isEmpty(dto.getSpec())
                || !ObjectUtils.isEmpty(dto.getWareId()) || !ObjectUtils.isEmpty(dto.getWareIdList())) {
            prodIdList = BaseUtils.initList();

            LambdaQueryWrapper<SysProdSearch> qw1 = Wrappers.<SysProdSearch>lambdaQuery()
                    .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysProdSearch::getWareId, dto.getWareIdList())
                    .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysProdSearch::getWareId, dto.getWareId())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), SysProdSearch::getSku, dto.getSku())
                    .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysProdSearch::getSku, dto.getSkuList())
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProdSearch::getSpec, dto.getSpec())
                    .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysProdSearch::getSpec, dto.getSpecSearchList())
                    .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProdSearch::getOneId, dto.getOneId());

            if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
                StringBuffer sb = new StringBuffer();
                dto.getOneIdList().forEach(oneId -> {
                    sb.append("(one_id like '%" + oneId + "%') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }
            // 数据限制
            List<Integer> saleProdId = new ArrayList<>();
            if (!ObjectUtils.isEmpty(dto)
                    && !ObjectUtils.isEmpty(dto.getTabType())
                    && dto.getTabType() == 1) { // 今日待处理
                saleProdId = baseMapper.selectProdIdPendingToday();
                qw1.in(!ObjectUtils.isEmpty(saleProdId), SysProdSearch::getProdId, saleProdId);
            } else if (dto.getTabType() == 2) {   // 待处理
                saleProdId = baseMapper.selectProdIdPending();
                qw1.in(!ObjectUtils.isEmpty(saleProdId), SysProdSearch::getProdId, saleProdId);
            }

            List<SysProdSearch> prodList = iSysProdSearchService.list(qw1);
            prodIdList.addAll(prodList.stream().map(SysProdSearch::getProdId).distinct().collect(Collectors.toList()));
            prodList.clear();
        }

        List<Integer> saleIdList = null;
        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(prodIdList) || !ObjectUtils.isEmpty(dto.getPlatId())
                || !ObjectUtils.isEmpty(dto.getShopUid()) || !ObjectUtils.isEmpty(shopIdPowerList)) {
            List<Integer> shopIdList = null;
            if (!ObjectUtils.isEmpty(dto.getShopUid())) {
                List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().like(ShopUser::getUid, dto.getShopUid()));
                shopIdList = BaseUtils.initList();
                shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
                shopList.clear();
            }

            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .select(SysProdDeal::getProdId, SysProdDeal::getSaleId)
                    .eq(!ObjectUtils.isEmpty(dto.getPlatId()), SysProdDeal::getThirdPlatId, dto.getPlatId())
                    .in(!ObjectUtils.isEmpty(prodIdList), SysProdDeal::getProdId, prodIdList)
                    .in(!ObjectUtils.isEmpty(shopIdList), SysProdDeal::getShopId, shopIdList)
                    .in(!ObjectUtils.isEmpty(shopIdPowerList), SysProdDeal::getShopId, shopIdPowerList)
                    .eq(SysProdDeal::getType, SysProdEvent.TypeSale));
            saleIdList = BaseUtils.initList();
            saleIdList.addAll(dealList.stream().map(SysProdDeal::getSaleId).distinct().collect(Collectors.toList()));
            dealList.clear();

            qw.in(SysProdSale::getId, saleIdList);
        }

        IPage<SysProdSale> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdSaleListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            Map<Integer, SysWare> wareMap = new HashMap<>();
            Map<Integer, List<SysProdDeal>> dealMap = new HashMap<>();
            Map<Integer, SysProd> prodMap = new HashMap<>();
            Map<Integer, SysWareInProd> prodWareMap = new HashMap<>();
            Map<Integer, ShopUser> shopMap = new HashMap<>();
            Map<Integer, SysThirdPlat> platMap = new HashMap<>();

            List<Integer> prodDetailsIdList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(dto.getExportXls()) && dto.getExportXls()) {
                prodDetailsIdList.addAll(pageResult.getRecords().stream()
                        .map(SysProdSale::getId).collect(Collectors.toList()));
            } else {
                prodDetailsIdList.addAll(pageResult.getRecords().stream()
                        .filter(a -> a.getPlatName().equals("goat"))
                        .map(SysProdSale::getId).collect(Collectors.toList()));
            }
            if (!ObjectUtils.isEmpty(prodDetailsIdList)) {
                List<SysProdDeal> list = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                        .in(SysProdDeal::getSaleId, prodDetailsIdList));
                prodIdList = BaseUtils.initList();
                prodIdList.addAll(list.stream().map(SysProdDeal::getProdId).distinct().collect(Collectors.toList()));
                dealMap.putAll(list.stream().collect(Collectors.groupingBy(SysProdDeal::getSaleId)));
                list.clear();

                if (!ObjectUtils.isEmpty(prodIdList)) {
                    // 商品信息
                    List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
                    prodMap.putAll(prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a)));
                    prodList.clear();

                    // 在仓信息
                    List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
                    prodWareMap.putAll(inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a)));
                    inProdList.clear();
                }

                // 商家信息
                List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery());
                shopMap.putAll(shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a)));
                shopList.clear();

                // 寄售平台
                List<SysThirdPlat> platList = iSysThirdPlatService.list();
                platMap.putAll(platList.stream().collect(Collectors.toMap(SysThirdPlat::getId, a -> a)));
                platList.clear();

                // 仓库信息
                List<SysWare> wareList = iSysWareService.list();
                wareMap.putAll(wareList.stream().collect(Collectors.toMap(SysWare::getId, a -> a)));
                wareList.clear();
            }

            pageResult.getRecords().forEach(data -> {
                SysProdSaleListVo vo = new SysProdSaleListVo();
                BeanUtils.copyProperties(data, vo);

                List<SysProdDeal> dealList = dealMap.get(data.getId());
                if (!ObjectUtils.isEmpty(dealList)) {
                    List<SysProdSaleListVo.SaleProdListVo> itemList = new ArrayList<>();

                    dealList.forEach(deal -> {
                        SysProdSaleListVo.SaleProdListVo prodVo = new SysProdSaleListVo.SaleProdListVo();
                        itemList.add(prodVo);

                        prodVo.setShopId(deal.getShopId());
                        prodVo.setProdId(deal.getId());
                        prodVo.setSku(deal.getSku());
                        prodVo.setSalePrice(deal.getSalePrice());
                        prodVo.setPlatPrice(deal.getPlatSoldPrice());

                        ShopUser shop = shopMap.get(deal.getShopId());
                        if (!ObjectUtils.isEmpty(shop)) {
                            prodVo.setShopUid(shop.getUid());
                            prodVo.setShopName(shop.getRealname());
                            prodVo.setServiceFee(shop.getOcFee());

                            if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
                                if (ObjectUtils.isEmpty(deal.getSoldPrice())) {
                                    if (!ObjectUtils.isEmpty(shop.getOcFee())) {
                                        prodVo.setServiceFee(shop.getOcFee());
                                    } else {
                                        prodVo.setServiceFee(SysConstants.zero);
                                    }
                                } else {
                                    prodVo.setServiceFee(deal.getPlatSoldPrice().subtract(deal.getSoldPrice()));
                                }
                                prodVo.setSoldPrice(deal.getPlatSoldPrice().subtract(prodVo.getServiceFee()));
                            }
                        }

                        SysWare ware = wareMap.get(deal.getWareId());
                        if (!ObjectUtils.isEmpty(ware)) {
                            prodVo.setWareName(ware.getName());

                            SysWareInProd inProd = prodWareMap.get(deal.getProdId());
                            if (!ObjectUtils.isEmpty(inProd)) {
                                // 在仓天数
                                Integer wareDays = DateTimeUtils.timeDiff(ObjectUtils.isEmpty(inProd.getGmtPay()) ? now : inProd.getGmtPay(), inProd.getGmtCreate(), 4);
                                prodVo.setWareDays(wareDays);
                            }
                        }

                        SysProd prod = prodMap.get(deal.getProdId());
                        if (!ObjectUtils.isEmpty(prod)) {
                            prodVo.setSpec(prod.getSpec());
                            prodVo.setRemarks(prod.getRemarks());
                            prodVo.setImg(prod.getImg());
                            prodVo.setOneId(prod.getOneId());
                        }
                    });
                    vo.setProdList(itemList.stream().sorted(Comparator.comparing(a -> {
                        return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                                .map(spec -> BaseUtils.dealSizeStr(spec).toString())
                                .orElse("");
                    })).collect(Collectors.toList()));
                }
                voList.add(vo);
            });
        }

        IPage<SysProdSaleListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdSale> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdSale> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean batchDeal(SysProdThirdSaleDealDto dto) {
        if (ObjectUtils.isEmpty(dto.getType())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("暂不支持此操作"));
        }

        if (ObjectUtils.isEmpty(dto.getIdList())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意订单"));
        }

        int type = SysProdEvent.TypeSale;
        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .eq(SysProdDeal::getType, type)
                .in(SysProdDeal::getSaleId, dto.getIdList()));
        if (dealList.stream().filter(a -> {
            return a.getStatus() != 1;
        }).collect(Collectors.toList()).size() != 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("已勾选的订单中，含有寄售状态异常的商品"));
        }

        List<Integer> prodIdList = BaseUtils.initList();
        prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
        Date now = DateTimeUtils.getNow();

        Integer status = null;
        switch (dto.getType()) {
            // 静默生成出库单
            case 1:
                if (iSysWareOutProdService.count(Wrappers.<SysWareOutProd>lambdaQuery()
                        .in(SysWareOutProd::getDealId, dealList.stream().map(SysProdDeal::getId).collect(Collectors.toList()))) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("商品已生成出库单"));
                }

                List<SysProdSale> saleList = list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, dto.getIdList()));
                Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
                saleList.clear();

                Map<Integer, List<SysProdDeal>> dealGroup = dealList.stream().collect(Collectors.groupingBy(SysProdDeal::getSaleId));
                dealGroup.keySet().forEach(saleId -> {
                    List<SysProdDeal> itemList = dealGroup.get(saleId);
                    Map<Integer, List<SysProdDeal>> prodWareMap = itemList.stream().collect(Collectors.groupingBy(SysProdDeal::getWareId));
                    SysProdSale sale = saleMap.get(saleId);

                    List<SysProdDealListVo> wareInfoList = new ArrayList<>();

                    List<Integer> wareIdList = prodWareMap.keySet().stream().collect(Collectors.toList());
                    int wareNum = wareIdList.size();
                    List<String> codeList = new ArrayList<>();
                    if (wareNum > 1) {
                        codeList.addAll(iSysCodePoolService.build(type, wareNum));
                    } else {
                        codeList.add(sale.getOddNo());
                    }

                    // 生成出库单
                    for (int i = 0; i < wareNum; i++) {
                        Integer wareId = wareIdList.get(i);

//                        Map<Integer, List<SysProdDeal>> shopDealMap = prodWareMap.get(wareId).stream().collect(Collectors.groupingBy(SysProdDeal::getShopId));
//                        shopDealMap.keySet().forEach(shopId -> {
                        //                        List<SysProdDeal> deals = shopDealMap.get(shopId);
                        List<SysProdDeal> deals = prodWareMap.get(wareId);

                        deals.forEach(deal -> {
                            SysProdDealListVo wareInfo = new SysProdDealListVo();
                            wareInfo.setType(SysProdEvent.TypeSale);
                            wareInfo.setProdId(deal.getProdId());
                            wareInfo.setRelationId(saleId);
                            wareInfo.setWareDays(DateTimeUtils.timeDiff(now, deal.getGmtIn(), 4));
                            wareInfo.setWareFee(SysConstants.zero);
                            wareInfoList.add(wareInfo);
                        });

                        SysWareOut outDto = new SysWareOut();

//                            String oddNo = iSysCodePoolService.build(type, 1).get(0);
                        String oddNo = codeList.get(0);
                        outDto.setOddNo(oddNo);
                        outDto.setLabelImg(sale.getLabelImg());
                        outDto.setRelationId(saleId);
                        outDto.setType(type);

                        Map<Integer, Integer> prodDealMap = new HashMap<>();
                        deals.forEach(deal -> {
                            prodDealMap.put(deal.getProdId(), deal.getId());
                        });
                        outDto.setProdDealMap(prodDealMap);

                        iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                                .in(SysProd::getId, deals.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()))
                                .set(SysProd::getOddNo, outDto.getOddNo()));

                        List<Integer> shopIdList = deals.stream().map(SysProdDeal::getShopId).distinct().collect(Collectors.toList());
                        if (!ObjectUtils.isEmpty(shopIdList) && shopIdList.size() == 1) {
                            outDto.setShopId(shopIdList.get(0));
                        }
                        outDto.setGmtCreate(now);
                        outDto.setWareId(wareId);
                        iSysWareOutService.saveSysWareOut(outDto);
                    }

                    if (!ObjectUtils.isEmpty(wareInfoList)) {
                        iSysProdDealService.saveWareInfo(saleId, wareInfoList);
                    }
                });


                // 更新入库商品信息
                iSysWareInProdService.update(Wrappers.<SysWareInProd>lambdaUpdate()
                        .set(SysWareInProd::getGmtPay, now) // 免支付出库
                        .in(SysWareInProd::getProdId, prodIdList));

                status = 3; // 待出库
                break;
            // 取消订单
            case 2:
                // 商品下架，流程结束
                iSysProdService.release(prodIdList, SysProdEvent.TypeSale);
                status = 2; // 已拒绝
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("暂不支持此操作"));
        }

        // 更新寄售单状态
        update(Wrappers.<SysProdSale>lambdaUpdate()
                .in(SysProdSale::getId, dto.getIdList())
                .set(SysProdSale::getCheckId, JwtContentHolder.getUserId())
                .set(SysProdSale::getStatus, status));

        return true;
    }

    @Transactional
    @Override
    public Boolean settle(TouchSettleRequest dto) {
        Integer prodId = null;
        if (!ObjectUtils.isEmpty(dto.getErpProductId())) {
            SysProd prod = iSysProdService.getOne(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getOneId, dto.getErpProductId()));
            if (ObjectUtils.isEmpty(prod)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("Invalid erpProductId, please recheck the product was pushed"));
            }
            prodId = prod.getId();
        }

        if (ObjectUtils.isEmpty(prodId)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Error request body，concat and recheck with developer of KnetGroup"));
        }

        boolean rs = iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                .set(!ObjectUtils.isEmpty(dto.getSalePrice()), SysProdDeal::getSalePrice, dto.getSalePrice())
                .set(SysProdDeal::getPlatSoldPrice, dto.getAmount())
                .isNull(SysProdDeal::getPlatSoldPrice)
                .eq(SysProdDeal::getProdId, prodId)
//                .eq(SysProdDeal::getSaleId, orderId)
                .eq(SysProdDeal::getType, SysProdEvent.TypeSale));
        if (rs) {
            // 结算并给商家打钱
            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .eq(SysProdDeal::getProdId, prodId)
                    .eq(SysProdDeal::getType, SysProdEvent.TypeSale)
                    .in(SysProdDeal::getStatus, 1, 3)
            );

            Integer shopId = dealList.get(0).getShopId();
            ShopUser shop = iShopUserService.getById(shopId);

            Map<Integer, Integer> prodOutIdMap = new HashMap<>();
            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getDealId, dealList.stream().map(SysProdDeal::getId).collect(Collectors.toList())));
            outProdList.forEach(outProd -> {
                prodOutIdMap.put(outProd.getProdId(), outProd.getOutId());
            });

            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .in(SysProd::getId, dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList())));
            Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
            prodList.clear();

            List<SysWare> wareList = iSysWareService.list();
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
            wareList.clear();

            List<SysProdSalePool> itemList = new ArrayList<>();

            // 不设置延迟打款时间默认是两天
            Integer delayDays = ObjectUtils.isEmpty(dto.getDelaySettleDays()) ? 2 : dto.getDelaySettleDays();
            Calendar c = Calendar.getInstance();
            c.setTime(DateTimeUtils.getNow());
            c.add(Calendar.DATE, delayDays); // 延迟两天打款
            Date gmtSettle = c.getTime();
            for (SysProdDeal deal : dealList) {
                deal.setPlatProdStatus(3);
                // 到手价 = 寄售平台到手价-0C服务费，先扣费服务费，再打款，因此此处不需要扣除OC服务费
                if (deal.getThirdPlatId() == null || deal.getThirdPlatId().equals(20006)) {
                    deal.setSoldPrice(deal.getPlatSoldPrice().subtract(shop.getOcFee()));
                } else {
                    deal.setSoldPrice(deal.getPlatSoldPrice());
                }

                deal.updateById();

                // 生成流水：寄售收入
                SysBill bill = new SysBill();
                bill.setStatus(1);
                bill.setUserId(shopId);
                bill.setUserType(5);
                bill.setIeType(1);
                bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysProdEvent.TypeSale, 4));
                bill.setPayType(4);
                bill.setTotalFee(deal.getPlatSoldPrice());
                bill.setRelationType(SysProdEvent.TypeSale);

                Map<String, String> attach = new HashMap<>();
                attach.put("platOrderNo", deal.getPlatOrderNo());
                attach.put("wareName", wareMap.get(deal.getWareId()));
                if (!ObjectUtils.isEmpty(deal.getSalePrice())) {
                    attach.put("salePrice", deal.getSalePrice().toString());
                }
                if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
                    attach.put("platSoldPrice", deal.getPlatSoldPrice().toString());
                }
                if (!ObjectUtils.isEmpty(deal.getPlatSoldPrice()) && !ObjectUtils.isEmpty(deal.getSoldPrice())) {
                    attach.put("serviceFee", deal.getPlatSoldPrice().subtract(deal.getSoldPrice()).toString());
                }
                SysProd prod = prodMap.get(deal.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    attach.put("remarks", prod.getRemarks());
                    attach.put("sku", prod.getSku());
                    attach.put("spec", prod.getSpec());
                    attach.put("oneId", prod.getOneId());
                }

                //记录订单的销售来源
                if (!ObjectUtils.isEmpty(dto.getSaleSource())) {
//                    attach.put("salePrice", dto.getSaleSource());
                    attach.put("saleSource", dto.getSaleSource());
                }

                bill.setAttach(BaseUtils.mapToQuery(attach) + "&");
                bill.setRelationId(prodOutIdMap.get(deal.getProdId()));
                iSysBillService.saveSysBill(bill);

                SysProdSalePool item = new SysProdSalePool();
                item.setSalePrice(dto.getSalePrice());
                item.setPlatSoldPrice(dto.getAmount());
                item.setDealId(deal.getId());
                item.setGmtSettle(gmtSettle);
                item.setBillId(bill.getId());
                itemList.add(item);
            }
            iSysProdSalePoolService.insertList(itemList);
        }

        return rs;
    }

    @Override
    public Boolean prodStatus(TouchProdStatusRequest dto) {
        Integer prodId = null;
        if (ObjectUtils.isEmpty(dto.getErpProductId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Required param [erpProductId]"));
        }
        SysProd prod = iSysProdService.getOne(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getOneId, dto.getErpProductId()));
        if (ObjectUtils.isEmpty(prod)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Invalid erpProductId, please recheck the product was pushed"));
        }
        prodId = prod.getId();

        switch (dto.getStatus()) {
            case 2:
                return iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                        .set(SysProdDeal::getPlatProdStatus, dto.getStatus())
                        .ne(SysProdDeal::getStatus, 2)
                        .eq(SysProdDeal::getPlatProdStatus, 1)
                        .eq(!ObjectUtils.isEmpty(prodId), SysProdDeal::getProdId, prodId)
                        .eq(SysProdDeal::getType, SysProdEvent.TypeSale));
            case 3:
                return iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                        .set(SysProdDeal::getPlatProdStatus, dto.getStatus())
                        .ne(SysProdDeal::getStatus, 2)
                        .eq(SysProdDeal::getPlatProdStatus, 2)
                        .eq(!ObjectUtils.isEmpty(prodId), SysProdDeal::getProdId, prodId)
                        .eq(SysProdDeal::getType, SysProdEvent.TypeSale));
            case 4:
                // 静默生成预报信息
                String logNo = dto.getTrackingNumber();
                if (ObjectUtils.isEmpty(logNo)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("Requried param [trackingNumber]"));
                }
                if (logNo.length() < 9) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("The tracking number must be longer than 8 length"));
                }
                String logNoSuffix = logNo.substring(logNo.length() - 9);
                ShopPack pack = iShopPackService.getOne(Wrappers.<ShopPack>lambdaQuery().ne(ShopPack::getStatus, 3)
                        .eq(ShopPack::getLogNoSuffix, logNoSuffix));
                if (!ObjectUtils.isEmpty(pack)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("The tracking number is already exist"));
                }
                SysProdDeal deal = iSysProdDealService.getOne(Wrappers.<SysProdDeal>lambdaUpdate()
                        .eq(SysProdDeal::getPlatProdStatus, 2)
                        .ne(SysProdDeal::getStatus, 2)
                        .eq(!ObjectUtils.isEmpty(prodId), SysProdDeal::getProdId, prodId)
                        .eq(SysProdDeal::getType, SysProdEvent.TypeSale).last("limit 1"));
                if (ObjectUtils.isEmpty(deal)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("The product sale status is not synchronized"));
                }
                Integer shopId = deal.getShopId();

                // 包裹未预报：静默生成预报批次以及相应包裹
                pack = new ShopPack();
                pack.setShopId(shopId);
                pack.setLogNo(logNo);
                pack.setType(1);
                pack.setStatus(1);

                ShopPre pre = new ShopPre();
                pre.setShopId(shopId);
                pre.setType(pack.getType());
                pre.setPackList(new ArrayList<>(Arrays.asList(pack)));
                iShopPreService.saveShopPre(pre);

                // touch推过来的已关闭订单需要扣除15刀

                // 商家扣除余额
                BigDecimal fee = new BigDecimal("15");
                iSysMoneyService.change(5, shopId, fee.negate());

                // 生成流水：平台退货扣款
                SysBill bill = new SysBill();
                bill.setStatus(2);
                bill.setUserId(shopId);
                bill.setUserType(5);
                bill.setIeType(-1);
                bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypeSaleCancel, 4));
                bill.setPayType(4);
                bill.setTotalFee(fee);
                bill.setRelationType(SysBill.TypeSaleCancel);
                bill.setAttach("platOrderNo=" + BaseUtils.covertString(deal.getPlatOrderNo()) + "&");
                bill.setRelationId(deal.getSaleId());
                iSysBillService.saveSysBill(bill);

                deal.setPlatProdStatus(4);
                deal.updateById();
                return true;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("Not support of command!"));
        }
    }

    @Override
    public List<String> platList() {
        return baseMapper.platList();
    }

    @Override
    public Boolean close(Integer id) {
        SysProdSale sale = getById(id);
        if (ObjectUtils.isEmpty(sale)) {
            return false;
        }

        // 还原商品寄卖信息
        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery().eq(SysProdDeal::getSaleId, sale.getId()));
        if (ObjectUtils.isEmpty(dealList)) {
            return false;
        }
        List<Integer> dealIdList = dealList.stream().map(SysProdDeal::getId).collect(Collectors.toList());

        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .setSql("odd_no = null, plat_order_no = null, out_no = null, third_plat_name= null")
                .in(SysProdSearch::getDealId, dealIdList)
                .eq(SysProdSearch::getSearchType, 1));
        iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                .setSql("plat_order_no = null, plat_sold_price = null, sold_price = null, relation_id = null, sale_id = null, plat_prod_status = 1")
                .in(SysProdDeal::getId, dealIdList));


        // 移除出库信息
        SysWareOut out = iSysWareOutService.getOne(Wrappers.<SysWareOut>lambdaQuery().eq(SysWareOut::getRelationId, sale.getId()));
        if (!ObjectUtils.isEmpty(out)) {
            iSysWareOutProdService.remove(Wrappers.<SysWareOutProd>lambdaQuery().eq(SysWareOutProd::getOutId, out.getId()));
            iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaQuery().eq(SysWareOutBatchProd::getOutId, out.getId()));
            out.deleteById();
        }

        // 移除寄售单
        sale.deleteById();

        return true;
    }

    @Override
    public List<SysProdSale> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }


    /**
     * 保存寄售单 - 批量上传label进行修改
     */
    @Override
    public void batchLabelSave(MultipartFile archiveFile, Integer tabType) {
        Assert.notNull(archiveFile, LanguageConfigService.i18nForMsg("请上传ZIP文件"));
        Assert.notNull(tabType, LanguageConfigService.i18nForMsg("请选择标签类型"));
        Assert.isTrue(tabType == 1 || tabType == 2,
                LanguageConfigService.i18nForMsg("标签类型只能是今日待处理或待处理"));

        // 拉取 ebay 寄卖审核今日待处理的订单
        SysProdSalePageDto dto = new SysProdSalePageDto();
        dto.setCurrent(1);
        dto.setSize(99999);
        dto.setTabType(tabType);
        dto.setPlatName(Collections.singletonList("ebay"));

        List<SysProdSaleListVo> sysProdSaleListVos = this.searchList(dto).getRecords();
        if (ObjectUtils.isEmpty(sysProdSaleListVos)) {
            log.info("batchLabelSave sysProdSaleListVoIPage is null");
            return;
        }

        // 手动获取事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            String labelUrl;
            // 读取文件,上传label到S3
            for (SysProdSaleListVo sysProdSaleListVo : sysProdSaleListVos) {
                if (ObjectUtils.isEmpty(sysProdSaleListVo.getId())) {
                    log.info("id为null，跳过");
                    continue;
                }
                if (ObjectUtils.isEmpty(sysProdSaleListVo.getPlatOrderNo())) {
                    log.info("三方寄售单为null，跳过");
                    continue;
                }
                if (!sysProdSaleListVo.getPlatOrderNo().endsWith(".pdf")) { // 确保 lableName 以.pdf结尾
                    sysProdSaleListVo.setPlatOrderNo(sysProdSaleListVo.getPlatOrderNo() + ".pdf");
                }

                InputStream lableFile = FileUtils.getPdfInputStreamFromZip(archiveFile, sysProdSaleListVo.getPlatOrderNo());
                if (ObjectUtils.isEmpty(lableFile)) {
                    log.info("zip 未包含该订单的label ={}", JSON.toJSONString(sysProdSaleListVo));
                    continue;
                }
                // 文件上传 S3
                labelUrl = AwsS3Utils.uploadFile(lableFile, sysProdSaleListVo.getPlatOrderNo());
                if (ObjectUtils.isEmpty(labelUrl)) {
                    log.info("label文件返回为null，上传失败");
                    continue;
                }
                // 调用保存
                SysProdSale entity = new SysProdSale();
                entity.setId(sysProdSaleListVo.getId());
                entity.setLabelImg(labelUrl);
                this.saveSysProdSale(entity);

            }

            transactionManager.commit(status);
        } catch (Exception e) {
            // 回滚事务
            e.printStackTrace();
            log.error("文件读取异常", e);
            transactionManager.rollback(status);
        }


    }
}
