package com.hzjm.service.service;

import com.hzjm.common.model.HttpPageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysUserPermission;
import com.hzjm.service.model.DTO.SysUserPermissionPageDto;
import com.hzjm.service.model.VO.SysUserPermissionListVo;
import com.hzjm.service.model.VO.SysUserPermissionVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 用户权限 服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
public interface ISysUserPermissionService extends IService<SysUserPermission> {

    SysUserPermission getByIdWithoutLogic(Integer id);

    SysUserPermissionVo getDetail(Integer id);

    Boolean saveSysUserPermission(SysUserPermission dto);

    Boolean insertList(List<SysUserPermission> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    HttpPageResult<SysUserPermissionListVo> searchList(SysUserPermissionPageDto dto);

    List<SysUserPermission> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysUserPermission> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean reset(Integer userId, List<Integer> permissionIdList);
}
