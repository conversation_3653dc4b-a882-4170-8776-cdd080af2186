package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import java.util.Map;

import com.hzjm.service.entity.SysPermission;
import com.hzjm.service.entity.SysWareUserPermission;
import com.hzjm.service.model.DTO.SysWareUserPermissionPageDto;
import com.hzjm.service.model.VO.SysWareUserPermissionListVo;
import com.hzjm.service.model.VO.SysWareUserPermissionVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 仓库人员权限 服务类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
public interface ISysWareUserPermissionService extends IService<SysWareUserPermission> {

    SysWareUserPermission getByIdWithoutLogic(Integer id);

    SysWareUserPermissionVo getDetail(Integer id);

    Boolean saveSysWareUserPermission(SysWareUserPermission dto);

    Boolean insertList(List<SysWareUserPermission> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysWareUserPermissionListVo> searchList(SysWareUserPermissionPageDto dto);

    List<SysWareUserPermission> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareUserPermission> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean reset(Integer id, Integer wareId, Integer userId, List<Integer> permissionIdList);

    Map<Integer, List<SysPermission>> group(Integer wareId);
}
