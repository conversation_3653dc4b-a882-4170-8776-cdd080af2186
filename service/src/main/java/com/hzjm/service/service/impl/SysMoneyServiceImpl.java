package com.hzjm.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysBill;
import com.hzjm.service.entity.SysMoney;
import com.hzjm.service.entity.SysMoneyMany;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.mapper.SysMoneyManyMapper;
import com.hzjm.service.mapper.SysMoneyMapper;
import com.hzjm.service.model.DTO.SysMoneyPageDto;
import com.hzjm.service.model.DTO.SysMoneySaveDto;
import com.hzjm.service.model.VO.SysMoneyListVo;
import com.hzjm.service.model.VO.SysMoneyVo;
import com.hzjm.service.service.ISysBillService;
import com.hzjm.service.service.ISysMoneyService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 用户钱包 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Slf4j
@Service
public class SysMoneyServiceImpl extends ServiceImpl<SysMoneyMapper, SysMoney> implements ISysMoneyService {

    @Resource
    public SysMoneyMapper sysMoneyMapper;
    @Resource
    public SysMoneyManyMapper sysMoneyManyMapper;

    @Resource
    public ISysBillService iSysBillService;

    @Override
    public SysMoney getByIdWithoutLogic(Integer id) {
        SysMoney data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该用户钱包"));
        }

        return data;
    }

    @Override
    public SysMoneyVo getDetail(Integer id) {
        SysMoney data = getByIdWithoutLogic(id);

        SysMoneyVo vo = new SysMoneyVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysMoney(SysMoney dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            // 余额不可编辑
            dto.setMoney(null);
            dto.setIntegral(null);
            // 归属人不可编辑
            dto.setUserId(null);
            dto.setType(null);
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSysMoneyMany(SysMoney dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();
        // 当钱包的支付方式被设置为默认支付时,同步给 sys_money
        if (!ObjectUtils.isEmpty(dto)
                && !ObjectUtils.isEmpty(dto.getMoneyFlag())
                && !ObjectUtils.isEmpty(JwtContentHolder.getShopId())
                && SysConstants.MONEY_FLAG_DEFAULT.equals(dto.getMoneyFlag().trim())) {

            SysMoney sysMoney = new SysMoney();
            sysMoney.setRoutingNumber(dto.getRoutingNumber());
            sysMoney.setCountry(dto.getCountry());

            // 根据当前登录人的角色类型和类型来修改
            LambdaQueryWrapper<SysMoney> lambdaQueryWrapper = Wrappers.<SysMoney>lambdaQuery()
                    .eq(SysMoney::getUserId, JwtContentHolder.getShopId())
                    .eq(SysMoney::getType, JwtContentHolder.getRoleType());

            if (!ObjectUtils.isEmpty(dto.getCardAccount())) { // 银行卡
                sysMoney.setCardAccount(dto.getCardAccount());
                sysMoney.setCardBank(dto.getCardBank());
                sysMoney.setCardBankSub(dto.getCardBankSub());
                sysMoney.setCardName(dto.getCardName());
                sysMoney.setAccountCurrency(dto.getAccountCurrency());
                sysMoney.setAccountHolderType(dto.getAccountHolderType());
                sysMoney.setAccountInfoSupply(dto.getAccountInfoSupply());

                sysMoneyMapper.update(sysMoney, lambdaQueryWrapper);

                // 之前存在的默认地址设置为空
                sysMoneyManyMapper.updateAddressFlagByShopIdCard(JwtContentHolder.getShopId());

            }

            if (!ObjectUtils.isEmpty(dto.getAliAccount())) { // 支付宝
                sysMoney.setAliAccount(dto.getAliAccount());
                sysMoney.setAliName(dto.getAliName());
                sysMoneyMapper.update(sysMoney, lambdaQueryWrapper);

                // 之前存在的默认地址设置为空
                sysMoneyManyMapper.updateAddressFlagByShopIdZfb(JwtContentHolder.getShopId());
            }

        }

        // 取消默认钱包,把 sys_money 的数据清空
        if (!ObjectUtils.isEmpty(dto)
                && !ObjectUtils.isEmpty(dto.getMoneyFlag())
                && !ObjectUtils.isEmpty(JwtContentHolder.getShopId())
                && SysConstants.MONEY_FLAG_DEFAULT_1.equals(dto.getMoneyFlag().trim())) {

            SysMoney sysMoney = new SysMoney();
            sysMoney.setRoutingNumber(dto.getRoutingNumber());

            // 根据当前登录人的角色类型和类型来修改
            LambdaQueryWrapper<SysMoney> lambdaQueryWrapper = Wrappers.<SysMoney>lambdaQuery()
                    .eq(SysMoney::getUserId, JwtContentHolder.getShopId())
                    .eq(SysMoney::getType, JwtContentHolder.getRoleType());

            if (!ObjectUtils.isEmpty(dto.getCardAccount())) { // 银行卡
                sysMoney.setCardAccount("");
                sysMoney.setCardBank("");
                sysMoney.setCardBankSub("");
                sysMoney.setCardName("");
                sysMoney.setAccountCurrency("");
                sysMoney.setAccountHolderType("");
                sysMoney.setAccountInfoSupply("");

                sysMoneyMapper.update(sysMoney, lambdaQueryWrapper);
            }

            if (!ObjectUtils.isEmpty(dto.getAliAccount())) { // 支付宝
                sysMoney.setAliAccount("");
                sysMoney.setAliName("");
                sysMoneyMapper.update(sysMoney, lambdaQueryWrapper);
            }

            // 取消默认钱包
            dto.setMoneyFlag("");

        }

        // 新增一个 sysMoneyMany 对象，把 dto 赋值给 sysMoneyMany 对象
        SysMoneyMany dtoMany = new SysMoneyMany();
        BeanUtils.copyProperties(dto, dtoMany);

        if (ObjectUtils.isEmpty(dtoMany.getId())) {
            rs = sysMoneyManyMapper.insert(dtoMany) > 0;
        } else if (isDelete) {
            // 通过 dtoMany 的 id 查询 sysMoneyMany 对象中的默认支付方式
            SysMoneyMany sysMoneyMany = sysMoneyManyMapper.selectByIdWithoutLogic(dtoMany.getId());
            if (ObjectUtils.isEmpty(sysMoneyMany)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未找到该钱包信息"));
            }
            if (!ObjectUtils.isEmpty(sysMoneyMany.getMoneyFlag())
                    && SysConstants.MONEY_FLAG_DEFAULT.equals(sysMoneyMany.getMoneyFlag().trim())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("默认支付方式不允许删除"));
            }
            rs = sysMoneyManyMapper.deleteById(dtoMany.getId()) > 0;
        } else {
            // 余额不可编辑
            dtoMany.setMoney(null);
            dtoMany.setIntegral(null);
            // 归属人不可编辑
            dtoMany.setUserId(null);
            dtoMany.setType(null);
            rs = sysMoneyManyMapper.updateById(dtoMany) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysMoneyListVo> searchList(SysMoneyPageDto dto) {

        LambdaQueryWrapper<SysMoney> qw = Wrappers.<SysMoney>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysMoney::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysMoney::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysMoney::getGmtCreate, endTime);

        IPage<SysMoney> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysMoneyListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysMoneyListVo vo = new SysMoneyListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysMoneyListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysMoney> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysMoney> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean change(int type, Integer userId, BigDecimal amount) {
        return baseMapper.change(type, userId, amount);
    }

    @Override
    public SysMoneyVo getMy() {
        SysMoneyVo vo = new SysMoneyVo();
        Integer roleType = JwtContentHolder.getRoleType();

        SysMoney money = getOne(Wrappers.<SysMoney>lambdaQuery()
                        .eq(roleType != 1, SysMoney::getUserId, JwtContentHolder.getUserId())
                        .eq(SysMoney::getType, roleType)
                        .eq(SysMoney::getDelFlag, 0)
                , false);
        BeanUtils.copyProperties(money, vo);

        return vo;
    }

    @Override
    public void checkCondition(SysMoneySaveDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            log.error("SysMoneyServiceImpl checkCondition dto is null");
            throw new BaseException(LanguageConfigService.i18nForMsg("dto is not null"));
        }
        if (ObjectUtil.isEmpty(dto.getId()) && ObjectUtil.isEmpty(dto.getDelFlag())) {
            QueryWrapper<SysMoneyMany> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("card_account", dto.getCardAccount());
            Integer userId = JwtContentHolder.getUserId();
            queryWrapper.eq("user_id", userId);
            if (sysMoneyManyMapper.selectCount(queryWrapper) > 0) {
                log.error("SysMoneyServiceImpl checkCondition bank card already exists,pls edit card info");
                throw new BaseException(LanguageConfigService.i18nForMsg("bank card already exists,pls edit card info"));
            }
        }
    }

    /**
     * 统计： 总余额 在途余额 可用余额 历史提款
     */
    @Override
    public SysMoneyVo getContBalance(Integer shopId) {
        log.info("SysMoneyServiceImpl getContBalance start shopid ={}", shopId);
        SysMoneyVo sysMoneyVo = new SysMoneyVo();
        // 可用余额
        SysMoney money = this.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getType, 5).eq(SysMoney::getUserId, shopId)
                , false);
        if (ObjectUtils.isEmpty(money)) {
            return null;
        }
        // 在途余额
        QueryWrapper<SysBill> iceQw = new QueryWrapper<>();
        iceQw.select("ifnull(sum(total_fee),0) total_fee");
        iceQw.in("relation_type", Arrays.asList(SysProdEvent.TypeSale, SysBill.TypeShopDrawLabelRefund));
        iceQw.eq("status", 1);
        iceQw.eq("user_id", shopId);
        iceQw.eq("user_type", 5);
        iceQw.eq("del_flag", 0);
        SysBill iceBill = iSysBillService.getOne(iceQw, false);
        if (ObjectUtils.isEmpty(iceBill)) {
            iceBill = new SysBill();
            iceBill.setTotalFee(SysConstants.zero);
        }
        // 历史提现
        QueryWrapper<SysBill> iceQw02 = new QueryWrapper<>();
        iceQw02.select("ifnull(sum(total_fee),0) total_fee");
        iceQw02.eq("relation_type", SysBill.TypeShopDraw);
        iceQw02.eq("user_id", shopId);
        iceQw02.eq("user_type", 5);
        iceQw02.eq("del_flag", 0);
        SysBill iceBill02 = iSysBillService.getOne(iceQw02, false);
        if (ObjectUtils.isEmpty(iceBill02)) {
            iceBill02 = new SysBill();
            iceBill02.setTotalFee(SysConstants.zero);
        }

        /*
         * 钱包可用余额+在途余额 = 总金额
         * 【注意】sys_money 表中的当前余额不包含在途资金
         */
        sysMoneyVo.setTotalBalance(money.getMoney().add(iceBill.getTotalFee()));
        sysMoneyVo.setPendingBalance(iceBill.getTotalFee());
        sysMoneyVo.setAvailableBalance(money.getMoney());
        sysMoneyVo.setHistoricWithdrawals(iceBill02.getTotalFee()); //历史提款
        return sysMoneyVo;
    }

    @Override
    public Map<String, List<SysMoneyVo>> getMyMap() {
        List<SysMoneyVo> zfbList = new ArrayList<>();
        List<SysMoneyVo> bankCardList = new ArrayList<>();

        Integer roleType = JwtContentHolder.getRoleType();

        List<SysMoneyMany> moneyList = sysMoneyManyMapper.listWithoutLogic(Wrappers.<SysMoney>lambdaQuery()
                .eq(roleType != 1, SysMoney::getUserId, JwtContentHolder.getUserId())
                .eq(SysMoney::getType, roleType)
                .eq(SysMoney::getDelFlag, 0)
        );

        for (SysMoneyMany money : moneyList) {
            if (!StringUtil.isBlank(money.getAliAccount())) {
                SysMoneyVo vo = new SysMoneyVo();
                BeanUtils.copyProperties(money, vo);
                zfbList.add(vo);
            }

            if (!StringUtil.isBlank(money.getCardAccount())) {
                SysMoneyVo vo = new SysMoneyVo();
                BeanUtils.copyProperties(money, vo);
                bankCardList.add(vo);
            }

        }

        Map<String, List<SysMoneyVo>> map = new HashMap<>();
        map.put("zfb", zfbList);
        map.put("bankCards", bankCardList);

        return map;
    }

    @Override
    public List<SysMoney> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
