package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpPageResult;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysUserPermissionPageDto;
import com.hzjm.service.model.VO.SysUserPermissionListVo;
import com.hzjm.service.model.VO.SysUserPermissionVo;
import com.hzjm.service.entity.SysUserPermission;
import com.hzjm.service.mapper.SysUserPermissionMapper;
import com.hzjm.service.service.ISysUserPermissionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 用户权限 服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Slf4j
@Service
public class SysUserPermissionServiceImpl extends ServiceImpl<SysUserPermissionMapper, SysUserPermission> implements ISysUserPermissionService {

    @Override
    public SysUserPermission getByIdWithoutLogic(Integer id) {
        SysUserPermission data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public SysUserPermissionVo getDetail(Integer id) {
        SysUserPermission data = getByIdWithoutLogic(id);

        SysUserPermissionVo vo = new SysUserPermissionVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysUserPermission(SysUserPermission dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public Boolean insertList(List<SysUserPermission> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {
            data.setDelFlag(0);
        });

        return baseMapper.insertList(dataList) > 0;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public HttpPageResult<SysUserPermissionListVo> searchList(SysUserPermissionPageDto dto) {
        Date endTime = dto.dealEndTime();

        LambdaQueryWrapper<SysUserPermission> qw = Wrappers.<SysUserPermission>lambdaQuery()
            .in(!ObjectUtils.isEmpty(dto.getIdList()), SysUserPermission::getId, dto.getIdList());

        HttpPageResult<SysUserPermission> pageResult = new HttpPageResult();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            IPage<SysUserPermission> iPage = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
            BeanUtils.copyProperties(iPage, pageResult);
        } else {
            pageResult.setRecords(list(qw));
        }

        List<SysUserPermissionListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysUserPermissionListVo vo = new SysUserPermissionListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        HttpPageResult voResult = new HttpPageResult();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public IPage<SysUserPermission> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysUserPermission> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    public Boolean reset(Integer userId, List<Integer> permissionIdList) {
        remove(Wrappers.<SysUserPermission>lambdaQuery().eq(SysUserPermission::getUserId, userId));

        if(!ObjectUtils.isEmpty(permissionIdList)) {
            List<SysUserPermission> dataList = new ArrayList<>();
            permissionIdList.forEach(permissionId -> {
                SysUserPermission userPermission = new SysUserPermission();
                userPermission.setUserId(userId);
                userPermission.setPermissionId(permissionId);

                dataList.add(userPermission);
            });
            saveBatch(dataList);
        }

        return true;
    }
}
