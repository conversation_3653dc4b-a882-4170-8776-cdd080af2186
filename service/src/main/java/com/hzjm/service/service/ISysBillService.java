package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.service.entity.SysBill;
import com.hzjm.service.entity.WithdrawRate;
import com.hzjm.service.model.DTO.RepairProcessDto;
import com.hzjm.service.model.DTO.ShopBillPageDto;
import com.hzjm.service.model.DTO.SysBillPageDto;
import com.hzjm.service.model.DTO.SysDeductionSaveDto;
import com.hzjm.service.model.VO.*;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

/**
 * 平台交易流水 服务类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
public interface ISysBillService extends IService<SysBill> {

    SysBill getByIdWithoutLogic(Integer id);

    SysBillVo getDetail(Integer id);

    Boolean saveSysBill(SysBill dto);

    Boolean insertList(List<SysBill> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysBillListVo> searchList(SysBillPageDto dto);

    List<SysBill> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysBill> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    SysBill getByOutTradeNo(String outTradeNo);

    SysBillTableVo shopTable(Integer userId, Integer roleType);

    IPage<ShopBillListVo> shopList(ShopBillPageDto dto);

    Boolean deduction(SysDeductionSaveDto dto);

    SysBillCountVo getCount(SysBillPageDto dto);

    String getSysBillSource(String platformOrderId);

    void setOrderNoList();

    WithdrawRate withdrawRate(BigDecimal money);


    void checkBalance(Integer userId, Integer userType, BigDecimal money);

    /**
     * 钱包管理 - 支出与退回时步钱包金额
     */
    @AcquireTaskLock(exceptionMessage = "有未完成任务，请稍后再试！")
    void syncWallet(SysBill dto, Integer userId, Integer roleType);

    @Transactional(rollbackFor = Exception.class)
    boolean processTransactionRecords(List<SysBillImportVo> sysBillImportVoList);

    @Transactional(rollbackFor = Exception.class)
    boolean processUpsRecords(List<SysBillImportVo> sysBillImportVoList);

    @Transactional(rollbackFor = Exception.class)
    void processRepairRecords(List<RepairProcessDto> repairProcessDtoList);
}
