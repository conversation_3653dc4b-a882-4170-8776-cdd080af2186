package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.StockxFlexProd;
import com.hzjm.service.entity.SysWareIn;
import com.hzjm.service.model.DTO.SysWareInPageDto;
import com.hzjm.service.model.DTO.SysWareInProdPageDto;
import com.hzjm.service.model.VO.*;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 入库单 服务类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface ISysWareInService extends IService<SysWareIn> {

    SysWareIn getByIdWithoutLogic(Integer id);

    SysWareInVo getDetail(Integer id);

    Boolean saveSysWareIn(SysWareIn dto, String logNo);

    Boolean insertList(List<SysWareIn> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWareInListVo> searchList(SysWareInPageDto dto);

    List<SysWareIn> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareIn> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    ShopPackScanVo scanByLogNo(String logNo,Boolean isReturn);

    Boolean bindShop(Integer inId, Integer shopId);

    Boolean bindPre(Integer inId, String preBatchNo);

    void removeStockxFlexWareIn(String trackingNo);

    /**
     * 根据 stockX flex 预报来生成对应的数据
     *
     * @param userId     用户 id
     * @param trackingNo stockX Flex package tracking Number
     * @param prods      包含的 商品
     * @return
     */
    List<StockxFlexProd> stockxFlexWareIn(Integer userId, String trackingNo, List<StockxFlexProd> prods);

    /**
     * 查询用户订单是否存在
     *
     * @param shopUser 用户
     * @param dateTime 时间
     * @param offset   偏移量 整数 为当天 -1 为昨天
     * @return 查询订单是否存在
     */
    boolean queryBlockedUser(ShopUser shopUser, Date dateTime, Integer offset);

    /**
     * 查询用户订单是否存在-返回不存在的商家
     *
     * @param shopUsers 用户s
     * @param dateTime  时间
     * @param offset    偏移量 整数 为当天 -1 为昨天
     * @return 返回不存在订单的商家
     */
    Set<Integer> queryBlockedUserReturnIds(List<ShopUser> shopUsers, Date dateTime, Integer offset);

    @TrimParam
    List<String> selectWareBatchInToExportCsvData(SysWareInProdPageDto dto, String language);

    @TrimParam
    SysWareBatchCountVo selectWareBatchCount(SysWareInProdPageDto dto);

    @TrimParam
    List<SysWareInProdListVo> selectWareBatchList(SysWareInProdPageDto dto);
}
