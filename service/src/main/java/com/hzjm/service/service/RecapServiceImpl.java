package com.hzjm.service.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hzjm.service.model.DTO.TaskRecapReqDto;
import com.hzjm.service.model.VO.RecapMonthlyVo;
import com.hzjm.service.model.VO.RecapYearlyVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import static com.hzjm.service.constants.ServiceConstants.MONTHLY;
import static com.hzjm.service.constants.ServiceConstants.YEARLY;

/**
 * <AUTHOR>
 * @date 2024/12/13 17:09
 * @description:
 */
@Slf4j
@Service
public class RecapServiceImpl implements IRecapService {
    @Resource
    SendEmailService sendEmailService;
    @Resource
    IShopUserService iShopUserService;
    @Resource
    IRecapMonthlyService iRecapMonthlyService;
    @Resource
    private IRecapYearlyService iRecapYearlyService;

    @Async
    @Override
    public void sendEmail(TaskRecapReqDto recapReqDto) {
        if (StrUtil.isEmpty(recapReqDto.getType())) {
            log.error("邮件发送类型不能为空");
            return;
        }
        if (MONTHLY.equals(recapReqDto.getType())) {
            sendMonthlyEmail(recapReqDto);
            return;
        }
        if (YEARLY.equals(recapReqDto.getType())) {
            sendYearlyEmail(recapReqDto);
        }
    }

    private void sendMonthlyEmail(TaskRecapReqDto recapReqDto) {
        log.info("发送商家月度统计数据邮件，recapReqDto：{}", recapReqDto);
        SimpleDateFormat sdf = new SimpleDateFormat("MMMM", Locale.US);
        String now = DateUtil.today();
        Map<String, String> map = new HashMap<>(2);
        List<RecapMonthlyVo> recapMonthlyVos = iRecapMonthlyService.queryRecapMonthlyByCondition(recapReqDto);
        log.info("月度统计商家数量：{}", recapMonthlyVos.size());
        Map<Integer, String> emailMaps = iShopUserService.getShopEmails(recapReqDto.getShopIds());
        log.info("待发邮件商家数量：{}", emailMaps.size());
        if (CollUtil.isEmpty(recapMonthlyVos)) {
            log.error("发送商家月度统计数据邮件，recapMonthlyVos为空 月度统计数据不存在 recapReqDto：{}", recapReqDto);
            return;
        }
        for (RecapMonthlyVo recapMonthlyVo : recapMonthlyVos) {
            String content = iRecapMonthlyService.createEmailContent(recapMonthlyVo);
            String email = emailMaps.get(Integer.valueOf(recapMonthlyVo.getShopUid()));
//            String email = "<EMAIL>";
            if (StrUtil.isBlank(email)) {
                log.error("发送商家月度统计数据邮件 商家:shopUid：{} 邮箱为空", recapMonthlyVo.getShopUid());
                continue;
            }
            map.put("email", email);
            map.put("content", content);
            map.put("month", sdf.format(recapReqDto.getStartDate()));
            map.put("businessId", now.concat(recapMonthlyVo.getShopUid()));
            sendEmailService.sendRecapMonthly(map);
        }
    }

    /**
     * 发送年度统计邮件
     *
     * @param recapReqDto 请求参数 shopIds, year，type
     */
    private void sendYearlyEmail(TaskRecapReqDto recapReqDto) {
        log.info("发送商家年度统计数据邮件，recapReqDto：{}", recapReqDto);
        String now = DateUtil.today();
        Map<String, String> map = new HashMap<>(2);
        List<RecapYearlyVo> recapYearlyVos = iRecapYearlyService.queryRecapYearlyByCondition(recapReqDto);
        Map<Integer, String> emailMaps = iShopUserService.getShopEmails(recapReqDto.getShopIds());
        if (CollUtil.isEmpty(recapYearlyVos)) {
            log.error("发送商家年度统计数据邮件，recapYearlyVos为空 年度统计数据不存在 recapReqDto：{}", recapReqDto);
            return;
        }
        for (RecapYearlyVo recapYearlyVo : recapYearlyVos) {
            String content = iRecapYearlyService.createEmailContent(recapYearlyVo);
            String email = emailMaps.get(Integer.valueOf(recapYearlyVo.getShopUid()));
//            String email = "<EMAIL>";
            if (StrUtil.isBlank(email)) {
                log.error("发送商家年度统计数据邮件 商家:shopUid：{} 邮箱为空", recapYearlyVo.getShopUid());
                continue;
            }
            map.put("email", email);
            map.put("content", content);
            map.put("businessId", now.concat(recapYearlyVo.getShopUid()));
            sendEmailService.sendRecapYearly(map);
        }
    }
}
