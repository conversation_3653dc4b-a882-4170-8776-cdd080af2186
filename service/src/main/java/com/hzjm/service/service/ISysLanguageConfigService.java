package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysLanguageConfig;
import com.hzjm.service.model.DTO.SysLanguageConfigPageDto;
import com.hzjm.service.model.VO.SysLanguageConfigListVo;
import com.hzjm.service.model.VO.SysLanguageConfigVo;

import java.util.List;

/**
 * 多语言配置表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-17
 */
public interface ISysLanguageConfigService extends IService<SysLanguageConfig> {

    SysLanguageConfig getByIdWithoutLogic(Integer id);

    SysLanguageConfigVo getDetail(Integer id);

    Boolean saveSysLanguageConfig(SysLanguageConfig dto);

    Boolean insertList(List<SysLanguageConfig> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysLanguageConfigListVo> searchList(SysLanguageConfigPageDto dto);

    List<SysLanguageConfig> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysLanguageConfig> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
