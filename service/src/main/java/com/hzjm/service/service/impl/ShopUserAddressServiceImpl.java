package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ShopUserAddress;
import com.hzjm.service.mapper.ShopUserAddressMapper;
import com.hzjm.service.model.DTO.ShopUserAddressPageDto;
import com.hzjm.service.model.VO.ShopUserAddressListVo;
import com.hzjm.service.model.VO.ShopUserAddressVo;
import com.hzjm.service.service.IShopUserAddressService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商家地址 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
@Slf4j
@Service
public class ShopUserAddressServiceImpl extends ServiceImpl<ShopUserAddressMapper, ShopUserAddress> implements IShopUserAddressService {

    @Resource
    private ShopUserAddressMapper shopUserAddressMapper;

    @Override
    public ShopUserAddress getByIdWithoutLogic(Integer id) {
        ShopUserAddress data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商家地址"));
        }

        return data;
    }

    @Override
    public ShopUserAddressVo getDetail(Integer id) {
        ShopUserAddress data = getByIdWithoutLogic(id);

        ShopUserAddressVo vo = new ShopUserAddressVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveShopUserAddress(ShopUserAddress dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (!isDelete) {
            StringBuffer sb = new StringBuffer();
            if (!ObjectUtils.isEmpty(dto.getProvince())) {
                sb.append(dto.getProvince());
            }
            if (!ObjectUtils.isEmpty(dto.getCity())) {
                sb.append(dto.getCity());
            }
            if (!ObjectUtils.isEmpty(dto.getDistrict())) {
                sb.append(dto.getDistrict());
            }
            if (!ObjectUtils.isEmpty(dto.getDetail())) {
                sb.append(dto.getDetail());
            }
            if (!ObjectUtils.isEmpty(sb.toString())) {
                dto.setShowAddress(sb.toString());
            }
        }

        // 当地址被设置为默认地址时,清空该商家的其他默认地址
        if (!ObjectUtils.isEmpty(dto)
                && !ObjectUtils.isEmpty(dto.getAddressFlag())
                && !ObjectUtils.isEmpty(JwtContentHolder.getShopId())
                && SysConstants.ADDRESS_FLAG_DEFAULT.equals(dto.getAddressFlag().trim())){
            shopUserAddressMapper.updateAddressFlagByShopId(JwtContentHolder.getShopId());
        }

        if (ObjectUtils.isEmpty(dto.getId())) {
            dto.setShopId(JwtContentHolder.getShopId());
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<ShopUserAddressListVo> searchList(ShopUserAddressPageDto dto) {

        LambdaQueryWrapper<ShopUserAddress> qw = Wrappers.<ShopUserAddress>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopUserAddress::getGmtCreate)
                .like(!ObjectUtils.isEmpty(dto.getName()), ShopUserAddress::getName, dto.getName())
                .eq(!ObjectUtils.isEmpty(dto.getType()), ShopUserAddress::getType, dto.getType())
                .eq(!ObjectUtils.isEmpty(dto.getShopId()), ShopUserAddress::getShopId, dto.getShopId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUserAddress::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUserAddress::getGmtCreate, endTime);

        if (!ObjectUtils.isEmpty(dto.getPhone())) {
            qw.apply("concat(pre_phone,phone) like '%" + dto.getPhone().replace(" ", "") + "%' ");
        }

        IPage<ShopUserAddress> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopUserAddressListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopUserAddressListVo vo = new ShopUserAddressListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<ShopUserAddressListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopUserAddress> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopUserAddress> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopUserAddress> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 查询 shop_user_address
     * select * from shop_user_address t1 inner join sys_prod_transport t2 on t1.detail = t2.receive_address  where t1.id_card_number is not null
     * @return 返回 map，key 是 地址， value 是 身份证号码
     */
    @Override
    public Map<String,String> queryShopUserAddress() {
        return this.list(new QueryWrapper<ShopUserAddress>()
                .select("DISTINCT id_card_number, show_address")
                .isNotNull("id_card_number")
                .isNotNull("show_address")
        ).stream().collect(Collectors.toMap(
                address -> address.getShowAddress().trim(),
                address -> address.getIdCardNumber().trim(),
                (existing, replacement) -> existing
        ));
    }

    /**
     * 根据收件人姓名查询身份证号
     * @param recipientName 收件人姓名
     * @return 身份证号，如果未找到则返回null
     */
    @Override
    public String queryIdCardByRecipientName(String recipientName) {
        // 输入验证
        if (ObjectUtils.isEmpty(recipientName)) {
            log.warn("收件人姓名为空，无法查询身份证号");
            return null;
        }

        // 构建查询条件
        QueryWrapper<ShopUserAddress> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id_card_number");
        queryWrapper.isNotNull("id_card_number");
        queryWrapper.eq("name", recipientName.trim());
        queryWrapper.eq("del_flag", 0);
        queryWrapper.last("limit 1");

        // 执行查询
        ShopUserAddress shopUserAddress = shopUserAddressMapper.selectOne(queryWrapper);

        if (shopUserAddress != null) {
            log.debug("根据收件人姓名 [{}] 查询到身份证号", recipientName);
            return shopUserAddress.getIdCardNumber();
        } else {
            log.debug("根据收件人姓名 [{}] 未查询到身份证号", recipientName);
            return null;
        }
    }

}
