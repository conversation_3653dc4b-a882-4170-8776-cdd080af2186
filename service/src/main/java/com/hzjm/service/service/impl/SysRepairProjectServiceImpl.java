package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysRepairProject;
import com.hzjm.service.mapper.SysRepairProjectMapper;
import com.hzjm.service.model.DTO.SysRepairProjectPageDto;
import com.hzjm.service.model.VO.SysRepairProjectListVo;
import com.hzjm.service.model.VO.SysRepairProjectVo;
import com.hzjm.service.service.ISysRepairProjectService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 维修项目表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@Slf4j
@Service
public class SysRepairProjectServiceImpl extends ServiceImpl<SysRepairProjectMapper, SysRepairProject> implements ISysRepairProjectService {

    @Override
    public SysRepairProject getByIdWithoutLogic(Integer id) {
        SysRepairProject data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该维修项目表");
        }

        return data;
    }

    @Override
    public SysRepairProjectVo getDetail(Integer id) {
        SysRepairProject data = getByIdWithoutLogic(id);

        SysRepairProjectVo vo = new SysRepairProjectVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysRepairProject(SysRepairProject dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        // 设置创建人
        dto.setCreatorId(JwtContentHolder.getUserId());

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysRepairProjectListVo> searchList(SysRepairProjectPageDto dto) {

        LambdaQueryWrapper<SysRepairProject> qw = Wrappers.<SysRepairProject>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysRepairProject::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysRepairProject::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysRepairProject::getGmtCreate, endTime);

        IPage<SysRepairProject> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysRepairProjectListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysRepairProjectListVo vo = new SysRepairProjectListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysRepairProjectListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysRepairProject> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysRepairProject> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysRepairProject> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
