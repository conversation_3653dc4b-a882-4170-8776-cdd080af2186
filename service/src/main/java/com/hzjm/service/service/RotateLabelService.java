package com.hzjm.service.service;

import com.hzjm.common.utils.AwsS3Utils;
import com.hzjm.common.utils.ImageRotationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RotateLabelService {

    /**
     * 旋转图片并上传到S3
     * @param imageUrl 图片的URL
     * @return S3存储的图片URL
     */
    public String rotateLabelOrientation(String imageUrl) {
        try {
            // 下载图片并旋转90度
            byte[] rotatedImageBytes = ImageRotationUtils.downloadAndRotateImage(imageUrl);

            // 生成文件名
            String fileName = "rotated_" + System.currentTimeMillis() + ".png";

            // 上传到S3
            String s3Url = AwsS3Utils.uploadFile(rotatedImageBytes, fileName);

            log.info("图片旋转和上传完成，S3 URL: {}", s3Url);
            return s3Url;

        } catch (Exception e) {
            log.error("图片旋转和上传失败，URL: {}", imageUrl, e);
            throw new RuntimeException("图片旋转和上传失败: " + e.getMessage(), e);
        }
    }


}
