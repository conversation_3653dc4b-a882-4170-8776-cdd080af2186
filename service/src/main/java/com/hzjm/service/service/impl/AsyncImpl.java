package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.GMailUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.*;
import com.hzjm.service.model.touch.TouchProdAddRequest;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@Async("KNetGroup")
public class AsyncImpl {

    @Autowired
    private TouchUtils touchUtils;

    @Autowired
    private SysProdMapper sysProdMapper;

    @Autowired
    private SysProdDealMapper sysProdDealMapper;

    @Autowired
    private SysProdSaleValidMapper sysProdSaleValidMapper;

    @Autowired
    private SysProdSearchMapper sysProdSearchMapper;

    @Autowired
    private SysEmailHistoryMapper sysEmailHistoryMapper;

    @Value("${gmail.usernamefrom}")
    private String usernamefrom;

    @Value("${gmail.username}")
    private String mailUsername;

    @Value("${gmail.password}")
    private String mailPassword;

    @Value("${gmail.from}")
    private String mailFrom;

    public void createProduct(List<TouchProdAddRequest> request) {
        int size = request.size();
        // 数据分块
        for (int i = 0; i < size; i = i + 50) {
            int index = i + 50;

            List<TouchProdAddRequest> body = request.subList(i, index > size ? size : index);
            JSONObject createRs = null;
            try {
                createRs = touchUtils.createProduct(body);
            } catch (BaseException e) {
                // 释放商品
                createRs = new JSONObject();
                List<String> oneIdList = body.stream().map(TouchProdAddRequest::getErpProductId).collect(Collectors.toList());
                createRs.put("erpProductIdList", oneIdList);

                Date now = DateTimeUtils.getNow();
                List<SysProdSaleValid> validList = new ArrayList<>();
                oneIdList.forEach(oneId -> {
                    SysProdSaleValid valid = new SysProdSaleValid();
                    valid.setOneId(oneId);
                    valid.setGmtCreate(now);
                    valid.setValidNum(0);
                    validList.add(valid);
                });

                sysProdSaleValidMapper.insertList(validList);
            }
            if (createRs.containsKey("erpProductIdList")) {
                List<String> oneIdList = createRs.getJSONArray("erpProductIdList").toJavaList(String.class);
                if (!ObjectUtils.isEmpty(oneIdList)) {
                    List<SysProd> prodList = sysProdMapper.selectList(Wrappers.<SysProd>lambdaQuery().in(SysProd::getOneId, oneIdList));
                    List<Integer> prodIdList = prodList.stream().map(SysProd::getId).collect(Collectors.toList());

                    sysProdDealMapper.update(null, Wrappers.<SysProdDeal>lambdaUpdate()
                            .set(SysProdDeal::getStatus, 2)
                            .eq(SysProdDeal::getStatus, 1)
                            .in(SysProdDeal::getProdId, prodIdList).eq(SysProdDeal::getType, SysProdEvent.TypeSale));

                    // 拒绝后释放商品
                    sysProdMapper.update(null, Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

                    // search同步更新
                    sysProdSearchMapper.update(null, Wrappers.<SysProdSearch>lambdaUpdate()
                            .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                            .in(SysProdSearch::getProdId, prodIdList)
                            .eq(SysProdSearch::getSearchType, 1));
                }
            }
        }
    }

    public void sendMail(String email, String content, String title) {
        if (ObjectUtils.isEmpty(email)) {
            return;
        }

        JSONObject body = new JSONObject();

        body.put("username", mailUsername); // gmail邮箱地址
        body.put("usernamefrom", usernamefrom); // 发送邮箱
        body.put("password", mailPassword); // 应用专用密钥，参考 https://www.waidi.vip/437.html
        body.put("email", email); // 目标邮箱
        body.put("from", mailFrom);
        body.put("bccEmail", "");// 秘密抄送人邮箱

        body.put("title", title); // 邮件标题
        body.put("content", content); // 邮件内容

        try {
            GMailUtils.sendGmail(body);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("发送失败： " + body.toJSONString());
        }
    }

    public void sendMailAndHis(String email, String bccEmail, String content, String title, SysEmailHistory sysEmailHistory) {
        if (ObjectUtils.isEmpty(sysEmailHistory)
                || ObjectUtils.isEmpty(sysEmailHistory.getBusinessId())
                || ObjectUtils.isEmpty(sysEmailHistory.getBusinessType())
        ) {
            return;
        }

        if (ObjectUtils.isEmpty(email)) {
            return;
        }

        //校验邮件是否可以发送
        Integer emailHistory = sysEmailHistoryMapper.selectCount(Wrappers.<SysEmailHistory>lambdaQuery()
                .eq(SysEmailHistory::getBusinessId, sysEmailHistory.getBusinessId())
                .eq(SysEmailHistory::getBusinessType, sysEmailHistory.getBusinessType())
                .eq(SysEmailHistory::getSendStatus, Boolean.TRUE)
        );

        // id + type  没有存在可以发送成功的则通过
        if (emailHistory > 0) {
            return;
        }

        JSONObject body = new JSONObject();

        body.put("username", mailUsername); // gmail邮箱地址
        body.put("usernamefrom", usernamefrom); // 发送邮箱
        body.put("password", mailPassword); // 应用专用密钥，参考 https://www.waidi.vip/437.html
        body.put("email", email); // 目标邮箱
        body.put("from", mailFrom);
        body.put("bccEmail", bccEmail);// 秘密抄送人邮箱

        body.put("title", title); // 邮件标题
        body.put("content", content); // 邮件内容

        String failureReason = "";
        try {
            GMailUtils.sendGmail(body);
        } catch (Exception e) {
            e.printStackTrace();
            failureReason = e + " ;e.message:" + e.getMessage();
            log.error("AsyncImpl.sendMailAndHis error={} ", e.getMessage());
            log.error("AsyncImpl.sendMailAndHis BaseUtils.getCallStack = {} ", BaseUtils.getCallStack());
            log.error("AsyncImpl.sendMailAndHis failureReason = {} ", failureReason);
        }

        // 记录历史
        sysEmailHistory.setToEmail(email);
        sysEmailHistory.setEmailSubject(title);
        sysEmailHistory.setEmailContent(content);
        sysEmailHistory.setSendStatus("".equals(failureReason));
        sysEmailHistory.setFailureReason(failureReason);
        this.saveEmailHistory(sysEmailHistory);
    }

    /**
     * 邮件发送后记录
     */
    void saveEmailHistory(SysEmailHistory sysEmailHistory) {

        if (ObjectUtils.isEmpty(sysEmailHistory.getId())) {
            sysEmailHistory.setSendTime(new Date());
            sysEmailHistoryMapper.insert(sysEmailHistory);
        }
    }


}
