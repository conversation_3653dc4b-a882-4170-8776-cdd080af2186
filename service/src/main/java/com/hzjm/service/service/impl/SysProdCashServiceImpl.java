package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysProdCashMapper;
import com.hzjm.service.model.DTO.SysProdCashPageDto;
import com.hzjm.service.model.DTO.SysProdDealNotePageDto;
import com.hzjm.service.model.DTO.SysProdDealPageDto;
import com.hzjm.service.model.VO.SysProdCashListVo;
import com.hzjm.service.model.VO.SysProdCashVo;
import com.hzjm.service.model.VO.SysProdDealListVo;
import com.hzjm.service.model.VO.export.SysCashOutExportVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.MAX_EXPORT_COUNT;
import static com.hzjm.service.constants.ServiceConstants.SYS_PROD_CASH_OUT_EXCEL_HEADER_TEMPLATE;

/**
 * 套现 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Slf4j
@Service
public class SysProdCashServiceImpl extends ServiceImpl<SysProdCashMapper, SysProdCash> implements ISysProdCashService {

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdDealNoteService iSysProdDealNoteService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysAuditService iSysAuditService;

    @Autowired
    private IShopUserService iShopUserService;

    @Override
    public SysProdCash getByIdWithoutLogic(Integer id) {
        SysProdCash data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该套现"));
        }

        return data;
    }

    @Override
    public SysProdCashVo getDetail(Integer id, String oddNo) {
        SysProdCash data;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<SysProdCash>lambdaQuery().eq(SysProdCash::getOddNo, oddNo));
            if (ObjectUtils.isEmpty(data)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未查询到该单号的信息"));
            }
        }

        SysProdCashVo vo = new SysProdCashVo();
        BeanUtils.copyProperties(data, vo);

        if (!ObjectUtils.isEmpty(data.getGmtPayValid())) {
            vo.setRestMill(data.getGmtPayValid().getTime() - DateTimeUtils.getNow().getTime());
        }

        vo.setType(SysProdEvent.TypeCash);

        List<SysProdDealListVo> prodVoList = iSysProdDealService.dealList(data.getId(), SysProdEvent.TypeCash, new SysProdDealPageDto());
        vo.setProdList(prodVoList);

        BigDecimal wareTotalFee = SysConstants.zero; // 仓储费用
        BigDecimal saleTotalFee = SysConstants.zero; // 实际报价/寄售价
        BigDecimal preTotalFee = SysConstants.zero; // 预计报价
        for (SysProdDealListVo prodVo : prodVoList) {
            if (!ObjectUtils.isEmpty(prodVo)) {
                wareTotalFee = wareTotalFee.add(prodVo.getWareFee());

                if (!ObjectUtils.isEmpty(preTotalFee) && !ObjectUtils.isEmpty(prodVo.getPrePrice()))
                    preTotalFee = preTotalFee.add(prodVo.getPrePrice());
                else
                    preTotalFee = null;
                if (!ObjectUtils.isEmpty(saleTotalFee) && !ObjectUtils.isEmpty(prodVo.getSalePrice()))
                    saleTotalFee = saleTotalFee.add(prodVo.getSalePrice());
                else
                    saleTotalFee = null;
            }
        }

        if (!ObjectUtils.isEmpty(data.getWareFee())) {
            wareTotalFee = data.getWareFee();
        }
        vo.setWareFee(wareTotalFee);
        if (!ObjectUtils.isEmpty(saleTotalFee)) {
            vo.setTotalFee(saleTotalFee.subtract(wareTotalFee.subtract(data.getFreeFee())));
        }
        vo.setSaleTotalFee(saleTotalFee);
        vo.setPreTotalFee(preTotalFee);

        // 留言信息
        SysProdDealNotePageDto noteDto = new SysProdDealNotePageDto();
        noteDto.setType(SysProdEvent.TypeCash);
        noteDto.setRelationId(data.getId());
        vo.setNoteList(iSysProdDealNoteService.searchList(noteDto).getRecords());

        return vo;
    }

    @Override
    public Boolean saveSysProdCash(SysProdCash dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            dto.setGmtDeal(BaseUtils.getGmtDeal());
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            // 仓储费用/实际到手价不可编辑
            dto.setTotalFee(null);
            dto.setWareFee(null);

            SysProdCash data = getById(dto.getId());
            if (!ObjectUtils.isEmpty(dto.getStatus()) && dto.getStatus() != data.getStatus().intValue()) {
                List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                        .eq(SysProdDeal::getStatus, 1)
                        .eq(SysProdDeal::getType, SysProdEvent.TypeCash).eq(SysProdDeal::getRelationId, data.getId()));
                List<Integer> prodIdList = BaseUtils.initList();
                prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));

                List<SysProdEvent> eventList = new ArrayList<>();
                switch (dto.getStatus()) {
                    // 审核拒绝
                    case 2:
                        if (data.getStatus() != 1) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("状态同步失败"));
                        }

                        // 商品事件：审核拒绝
                        prodIdList.forEach(prodId -> {
                            SysProdEvent event = new SysProdEvent();
                            event.setProdId(prodId);
                            event.setShopId(data.getShopId());
                            event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, SysProdEvent.TypeCash)));
                            event.setDescription("套现审核被拒：" + (ObjectUtils.isEmpty(dto.getReason()) ? "" : dto.getReason()));
                            event.setRelationId(data.getId());
                            if (event.getProdId() > 0)
                                eventList.add(event);
                        });

                        // 更新平台审核状态：审核拒绝
                        iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                                .set(SysAudit::getStatus, 2)
                                .set(SysAudit::getOperatorId, JwtContentHolder.getUserId())
                                .eq(SysAudit::getOddNo, data.getOddNo()));

                        // 拒绝后释放商品
                        iSysProdService.update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

                        // 流程结束：审核被拒
                        iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                                .eq(SysProdDeal::getRelationId, data.getId())
                                .eq(SysProdDeal::getType, SysProdEvent.TypeCash)
                                .set(SysProdDeal::getGmtModify, DateTimeUtils.getNow())
                                .set(SysProdDeal::getStatus, 2));

                        // search同步更新
                        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                                .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                                .in(SysProdSearch::getProdId, prodIdList)
                                .eq(SysProdSearch::getSearchType, 1));
                        break;
                    // 审核通过
                    case 3:
                        if (data.getStatus() != 1) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("状态同步失败"));
                        }
                        if (ObjectUtils.isEmpty(dto.getProdIdList())) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("请勾选要审批的鞋子"));
                        }
                        prodIdList.removeAll(dto.getProdIdList());

                        // 撤回部分商品
                        iSysProdDealService.cancel(prodIdList, SysProdEvent.TypeCash);

                        prodIdList = dto.getProdIdList();

                        if (dealList.stream().anyMatch(a -> ObjectUtils.isEmpty(a.getSalePrice()) && dto.getProdIdList().contains(a.getProdId()))) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("存在未报价商品"));
                        }

                        // 支付过期时间：1天
                        Calendar c = Calendar.getInstance();
                        c.setTime(DateTimeUtils.getNow());
                        c.add(Calendar.DATE, 1);
                        dto.setGmtPayValid(c.getTime());

                        // 商品事件：审核通过
                        prodIdList.forEach(prodId -> {
                            SysProdEvent event = new SysProdEvent();
                            event.setProdId(prodId);
                            event.setShopId(data.getShopId());
                            event.setDescription("套现审核通过");
                            event.setRelationId(data.getId());
                            if (event.getProdId() > 0)
                                eventList.add(event);
                        });

                        // 更新平台审核状态：审核通过
                        iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                                .set(SysAudit::getStatus, 3)
                                .set(SysAudit::getOperatorId, JwtContentHolder.getUserId())
                                .eq(SysAudit::getOddNo, data.getOddNo()));
                        break;
                    default:
                        throw new BaseException(LanguageConfigService.i18nForMsg("不支持的操作"));
                }

                iSysProdEventService.insertList(eventList);
            }

            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdCashListVo> searchList(SysProdCashPageDto dto) {

        LambdaQueryWrapper<SysProdCash> qw = Wrappers.<SysProdCash>lambdaQuery();

        Date endTime = dto.dealEndTime();
        Date outEndTime = dto.dealOutEndTime();
        qw.orderByDesc(SysProdCash::getGmtCreate)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysProdCash::getId, dto.getIdList())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysProdCash::getStatus, dto.getStatus())
                .like(!ObjectUtils.isEmpty(dto.getOddNo()), SysProdCash::getOddNo, dto.getOddNo())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdCash::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdCash::getGmtCreate, endTime)
                .ge(!ObjectUtils.isEmpty(dto.getOutBeginTime()), SysProdCash::getGmtModify, dto.getOutBeginTime())
                .lt(!ObjectUtils.isEmpty(outEndTime), SysProdCash::getGmtModify, outEndTime);

        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            qw.in(SysProdCash::getShopId, shopIdPowerList);
        }

        if (!ObjectUtils.isEmpty(dto.getDealTime())) {
            switch (dto.getDealTime()) {
                // 待处理
                case 1:
                    qw.in(SysProdCash::getStatus, SysProdCash.dealingStatus)
                            .gt(dto.getDealTime() == 1, SysProdCash::getGmtDeal, DateTimeUtils.getNow());
                    break;
                // 今日待处理
                case 2:
                    qw.in(SysProdCash::getStatus, SysProdCash.dealingStatus)
                            .le(dto.getDealTime() == 2, SysProdCash::getGmtDeal, DateTimeUtils.getNow());
                    break;
                case 3:
                    qw.in(SysProdCash::getStatus, SysProdCash.outingStatus);
                    break;
                case 4:
                    qw.in(SysProdCash::getStatus, SysProdCash.finishStatus);
                    break;
            }
        }

        Map<Integer, ShopUser> shopMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(dto.getShopUid())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .select(ShopUser::getId, ShopUser::getUid, ShopUser::getRealname)
                    .like(!ObjectUtils.isEmpty(dto.getShopUid()), ShopUser::getUid, dto.getShopUid()));
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            qw.in(SysProdCash::getShopId, shopIdList);

            shopMap.putAll(shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a)));
        }

        if (JwtContentHolder.getRoleType() == 1) {
            qw.ne(SysProdCash::getStatus, 5); // 超管端不展示已取消的记录
        }

        IPage<SysProdCash> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdCashListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            Map<Integer, List<SysProdDealListVo>> dealMap = new HashMap<>();
            if (ObjectUtils.isEmpty(shopMap)) {
                List<Integer> shopIdList = BaseUtils.initList();
                shopIdList.addAll(pageResult.getRecords().stream().map(SysProdCash::getShopId).collect(Collectors.toList()));
                List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                        .select(ShopUser::getId, ShopUser::getUid, ShopUser::getRealname)
                        .in(ShopUser::getId, shopIdList));
                shopMap.putAll(shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a)));
            }

            Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealGroup = iSysProdDealService.dealGroup(pageResult.getRecords().stream().map(SysProdCash::getId).collect(Collectors.toList()),
                    new ArrayList<>(Arrays.asList(SysProdEvent.TypeCash)), null);
            dealMap.putAll(dealGroup.get(SysProdEvent.TypeCash));

            pageResult.getRecords().forEach(data -> {
                SysProdCashListVo vo = new SysProdCashListVo();
                BeanUtils.copyProperties(data, vo);

                ShopUser shop = shopMap.get(data.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    vo.setShopUid(shop.getUid());
                    vo.setShopName(shop.getRealname());
                }

                List<SysProdDealListVo> dealList = dealMap.get(data.getId());
                if (!ObjectUtils.isEmpty(dealList)) {
                    BigDecimal prePrice = SysConstants.zero;
                    BigDecimal salePrice = SysConstants.zero;
                    for (SysProdDealListVo deal : dealList) {
                        if (!ObjectUtils.isEmpty(prePrice) && !ObjectUtils.isEmpty(deal.getPrePrice())) {
                            prePrice = prePrice.add(deal.getPrePrice());
                        }

                        if (!ObjectUtils.isEmpty(salePrice) && !ObjectUtils.isEmpty(deal.getSalePrice())) {
                            salePrice = salePrice.add(deal.getSalePrice());
                        }
                    }
                    vo.setPrePrice(prePrice);
                    vo.setSalePrice(salePrice);
                    vo.setNum(dealList.size());
                } else {
                    vo.setNum(0);
                }
                vo.setProdList(dealList);

                voList.add(vo);
            });
        }

        IPage<SysProdCashListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdCash> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdCash> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdCash> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    public String exportCashOutList(SysProdCashPageDto dto, String language) {
        dto.setSize(MAX_EXPORT_COUNT);
        log.info("开始调用 查询套现审核列表 接口 ");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IPage<SysProdCashListVo> searchList = searchList(dto);
        List<SysProdCashListVo> records = searchList.getRecords();
        stopWatch.stop();
        log.info("查询套现审核列表 耗时：{} ms", stopWatch.getTotalTimeMillis());
        List<SysCashOutExportVo> dataList = records.stream()
                .flatMap(sysProdCashListVo -> sysProdCashListVo.mapToSysCashOutExportVoList().stream())
                .collect(Collectors.toList());
        String fileName = LanguageConfigService.i18nForMsg("套现审核") +
                "(" + DateTimeUtils.getFileSuffix() + BaseUtils.getRandomStr(3) + ").xlsx";
        return ExcelReader.generateExcelFileEasyExcel(dataList, fileName, LanguageConfigService.generateExcelHeaders(SYS_PROD_CASH_OUT_EXCEL_HEADER_TEMPLATE, language));
    }
}
