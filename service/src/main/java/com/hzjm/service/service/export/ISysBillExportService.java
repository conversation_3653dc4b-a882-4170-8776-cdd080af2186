package com.hzjm.service.service.export;

import com.hzjm.service.model.DTO.SysBillExportPageReq;
import com.hzjm.service.model.DTO.SysBillPageDto;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/12/26 16:31
 * @description: ISysBill导出实现(管理端口 ， 导出商家钱包)
 */
public interface ISysBillExportService {

    /**
     * 导出商家钱包（重写版本-迁移旧方法）
     *
     * @param req      原始请求
     * @param fileName 文件名
     * @return 导出文件地址
     */
    HashMap<String, String> exportShopWallet(SysBillExportPageReq req, String fileName);

    /**
     * 创建导出商家钱包（异步）任务
     *
     * @param dto      导出请求
     * @param timeZone 时区
     * @param language 用户语言
     * @return 是否成功
     */
    Boolean createExportShopWalletTask(SysBillPageDto dto, String timeZone, String language);
}
