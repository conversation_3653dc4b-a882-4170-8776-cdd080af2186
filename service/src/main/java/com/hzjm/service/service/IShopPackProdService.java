package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.ShopPackProd;
import com.hzjm.service.model.DTO.ShopPackProdPageDto;
import com.hzjm.service.model.VO.ShopPackProdListVo;
import com.hzjm.service.model.VO.ShopPackProdVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 包裹货品 服务类
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface IShopPackProdService extends IService<ShopPackProd> {

    ShopPackProd getByIdWithoutLogic(Integer id);

    ShopPackProdVo getDetail(Integer id);

    Boolean saveShopPackProd(ShopPackProd dto);

    Boolean insertList(List<ShopPackProd> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<ShopPackProdListVo> searchList(ShopPackProdPageDto dto);

    List<ShopPackProd> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopPackProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
