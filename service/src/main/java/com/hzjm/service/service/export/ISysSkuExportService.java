package com.hzjm.service.service.export;

import com.hzjm.service.model.DTO.SysSkuPageDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/17 14:55
 * @description: sku导出服务
 */
public interface ISysSkuExportService {
    /**
     * 导出sku
     *
     * @param req      原始请求
     * @param fileName 文件名
     * @param language 语言
     * @return 导出文件地址
     */
    HashMap<String, String> exportSysSku(SysSkuPageDto req, String fileName, String language);

    /**
     * 导入sku
     *
     * @param file     文件
     * @param language 语言
     * @return 是否成功
     */
    Boolean importSku(MultipartFile file, String language);
}
