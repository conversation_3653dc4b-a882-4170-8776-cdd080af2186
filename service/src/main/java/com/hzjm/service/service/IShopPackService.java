package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopPack;
import com.hzjm.service.model.DTO.ShopPackPageDto;
import com.hzjm.service.model.VO.ShopPackListVo;
import com.hzjm.service.model.VO.ShopPackVo;

import java.util.List;

/**
 * 预报包裹 服务类
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
public interface IShopPackService extends IService<ShopPack> {

    ShopPack getByIdWithoutLogic(Integer id);

    ShopPackVo getDetail(Integer id, String logNo);

    // 传入物流单号，如果是未认领的返回ID，否则返回null
    Integer getIdForLogNo(ShopPack dto);

    ShopPack getShopPackForLogNo(ShopPack dto);

    Boolean saveShopPack(ShopPack dto);

    Boolean insertList(List<ShopPack> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopPackListVo> searchList(ShopPackPageDto dto);

    List<ShopPack> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopPack> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean fixLogNo(Integer id);
}
