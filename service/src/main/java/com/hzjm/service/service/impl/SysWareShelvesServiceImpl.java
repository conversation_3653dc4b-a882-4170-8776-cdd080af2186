package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysWareShelves;
import com.hzjm.service.entity.SysWareShelvesProd;
import com.hzjm.service.mapper.SysWareShelvesMapper;
import com.hzjm.service.model.DTO.SysWareShelvesBatchSaveDto;
import com.hzjm.service.model.DTO.SysWareShelvesPageDto;
import com.hzjm.service.model.VO.SysWareShelvesListVo;
import com.hzjm.service.model.VO.SysWareShelvesVo;
import com.hzjm.service.service.ISysWareShelvesProdService;
import com.hzjm.service.service.ISysWareShelvesService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库货架 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Slf4j
@Service
public class SysWareShelvesServiceImpl extends ServiceImpl<SysWareShelvesMapper, SysWareShelves> implements ISysWareShelvesService {

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Override
    public SysWareShelves getByIdWithoutLogic(Integer id) {
        SysWareShelves data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该仓库货架"));
        }

        return data;
    }

    @Override
    public SysWareShelvesVo getDetail(Integer id) {
        SysWareShelves data = getByIdWithoutLogic(id);

        SysWareShelvesVo vo = new SysWareShelvesVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareShelves(SysWareShelves dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getName())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请填写货架名"));
            }

            if (count(Wrappers.<SysWareShelves>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareShelves::getWareId, dto.getWareId())
                    .isNotNull(SysWareShelves::getWareId)
                    .eq(SysWareShelves::getName, dto.getName())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("该名称已被使用"));
            }

            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            // 货架中无商品方可删除
            if (iSysWareShelvesProdService.count(Wrappers.<SysWareShelvesProd>lambdaQuery()
                    .eq(SysWareShelvesProd::getShelvesId, dto.getId())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("货架中仍有商品，无法删除"));
            }

            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysWareShelvesListVo> searchList(SysWareShelvesPageDto dto) {

        LambdaQueryWrapper<SysWareShelves> qw = Wrappers.<SysWareShelves>lambdaQuery();
        // 支持仓位名称模糊查询
        if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getNameLike())){
            qw.like(SysWareShelves::getName,dto.getNameLike());
        }

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareShelves::getGmtCreate)
                .isNotNull(SysWareShelves::getWareId)
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysWareShelves::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareShelves::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getNameEq()), SysWareShelves::getName, dto.getNameEq())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareShelves::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareShelves::getGmtCreate, endTime);

        IPage<SysWareShelves> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareShelvesListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> shelvesIdList = BaseUtils.initList();
            shelvesIdList.addAll(pageResult.getRecords().stream().map(SysWareShelves::getId).collect(Collectors.toList()));
            List<SysWareShelvesProd> prodList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                    .select(SysWareShelvesProd::getShelvesId, SysWareShelvesProd::getProdId)
                    .in(SysWareShelvesProd::getShelvesId, shelvesIdList));
            Map<Integer, List<SysWareShelvesProd>> prodMap = prodList.stream().collect(Collectors.groupingBy(SysWareShelvesProd::getShelvesId));

            for (SysWareShelves data : pageResult.getRecords()) {
                SysWareShelvesListVo vo = new SysWareShelvesListVo();
                BeanUtils.copyProperties(data, vo);

                List<SysWareShelvesProd> prods = prodMap.get(data.getId());
                if (ObjectUtils.isEmpty(prods)) {
                    vo.setProdNum(0);
                } else {
                    vo.setProdNum(prods.size());
                }

                voList.add(vo);
            }
        }

        if(JwtContentHolder.getRoleType() == 4) {
            voList = voList.stream().sorted(Comparator.comparing(a -> {return (a.getMaxNum() - a.getProdNum()) * -1;})).collect(Collectors.toList());
//            voList.sort(new Comparator<SysWareShelvesListVo>() {
//                @Override
//                public int compare(SysWareShelvesListVo o1, SysWareShelvesListVo o2) {
//                    return (o1.getMaxNum()-o1.getProdNum()) < (o2.getMaxNum()-o2.getProdNum()) ? 1 : -1;
//                }
//            });
        }

        IPage<SysWareShelvesListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWareShelves> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareShelves> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysWareShelvesListVo> saveList(SysWareShelvesBatchSaveDto dto) {
        List<SysWareShelvesBatchSaveDto.ShelvesSaveDto> shelvesList = dto.getShelvesList();
        /*
        if (ObjectUtils.isEmpty(dto.getWareId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("货架所在仓库不明"));
        }*/
        if (ObjectUtils.isEmpty(shelvesList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("至少要有一个货架"));
        }
        if (shelvesList.stream().filter(a -> {
            return ObjectUtils.isEmpty(a.getName());
        }).collect(Collectors.toList()).size() > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("请填写货架名"));
        }

        List<String> nameList = shelvesList.stream().map(SysWareShelvesBatchSaveDto.ShelvesSaveDto::getName).distinct().collect(Collectors.toList());
        if (nameList.size() != shelvesList.size()) {
            throw new BaseException(LanguageConfigService.i18nForMsg("货架名不可重复"));
        }

        if (count(Wrappers.<SysWareShelves>lambdaQuery()
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareShelves::getWareId, dto.getWareId())
                .isNotNull(SysWareShelves::getWareId)
                .in(SysWareShelves::getName, nameList)) > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("存在货架名已被使用"));
        }

        List<SysWareShelves> dataList = new ArrayList<>();
        shelvesList.forEach(item -> {
            SysWareShelves data = new SysWareShelves();
            data.setWareId(dto.getWareId());
            data.setName(item.getName());
            data.setMaxNum(item.getMaxNum());
            dataList.add(data);
        });

        saveBatch(dataList);

        List<SysWareShelvesListVo> voList = new ArrayList<>();
        dataList.forEach(data -> {
            SysWareShelvesListVo vo = new SysWareShelvesListVo();
            BeanUtils.copyProperties(data, vo);

            voList.add(vo);
        });
        return voList;
    }

    @Override
    public List<SysWareShelves> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
