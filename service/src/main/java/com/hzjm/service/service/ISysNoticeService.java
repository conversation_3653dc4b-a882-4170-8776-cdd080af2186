package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysNotice;
import com.hzjm.service.model.DTO.SysNoticePageDto;
import com.hzjm.service.model.VO.SysNoticeListVo;
import com.hzjm.service.model.VO.SysNoticeVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 公告 服务类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface ISysNoticeService extends IService<SysNotice> {

    SysNotice getByIdWithoutLogic(Integer id);

    SysNoticeVo getDetail(Integer id);

    Boolean saveSysNotice(SysNotice dto);

    Boolean insertList(List<SysNotice> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysNoticeListVo> searchList(SysNoticePageDto dto);

    List<SysNotice> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysNotice> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
