package com.hzjm.service.service;

import java.util.Map;

public interface SendEmailService {

    /**
     * 上架商品通知商家
     */
    void oncomingShipmentSendEmail(Map<String, String> map);

    void dropOffBatchSendEmail(Map<String, String> map);

    void incomingShipmentSendEmail(Map<String, String> map);

    void dropOffBatchReceivedSendEmail(Map<String, String> map);

    void productSoldSendEmail(Map<String, Object> map);

    void fundAreAvailableSendEmail(Map<String, String> map);

    /**
     * 发送商家月度汇总邮件
     *
     * @param map 请求参数
     */
    void sendRecapMonthly(Map<String, String> map);

    /**
     * 发送商家年度汇总邮件
     *
     * @param map 请求参数
     */
    void sendRecapYearly(Map<String, String> map);

    /**
     * 发送商家将要被封禁（停用）邮件
     *
     * @param map 请求参数
     */
    void sendRulerNotedEmail(Map<String, String> map);
}
