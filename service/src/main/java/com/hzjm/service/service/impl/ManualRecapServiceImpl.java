package com.hzjm.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.service.model.DTO.ManualTaskRecapReqDto;
import com.hzjm.service.model.DTO.TaskRecapReqDto;
import com.hzjm.service.service.IManualRecapService;
import com.hzjm.service.service.IRecapMonthlyService;
import com.hzjm.service.service.IRecapYearlyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/30 14:04
 * @description:
 */
@Slf4j
@Service
public class ManualRecapServiceImpl implements IManualRecapService {
    @Resource
    private IRecapMonthlyService iRecapMonthlyService;
    @Resource
    private IRecapYearlyService iRecapYearlyService;

    @Override
    @AcquireTaskLock(name = "generateRecapMonthly", timeout = 3600)
    public void generateRecapMonthly(ManualTaskRecapReqDto recapReqDto) {
        if (BeanUtil.isEmpty(recapReqDto.getStartDate())) {
            log.error("调用参数为空");
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("任务: generateRecapMonthlyData 开始执行, 执行时间: {}", stopWatch.getTotalTimeMillis());
        DateTime beginTime = DateUtil.beginOfMonth(recapReqDto.getStartDate());
        DateTime endTime = DateUtil.endOfMonth(recapReqDto.getStartDate());
        log.info("generateRecapMonthlyData 开始时间：{}，结束时间：{} ", beginTime, endTime);
        List<Long> shopIds = iRecapMonthlyService.queryUsedShopIds(beginTime, endTime);
        TaskRecapReqDto taskRecapReqDto = TaskRecapReqDto.builder()
                .shopIds(shopIds)
                .startDate(beginTime)
                .endDate(endTime).build();
        iRecapMonthlyService.createRecapMonthly(taskRecapReqDto);
        stopWatch.stop();
        log.info("任务: generateRecapMonthlyData 执行结束, 耗时: {} 毫秒", stopWatch.getTotalTimeMillis());
    }

    @Override
    @AcquireTaskLock(name = "generateRecapYearly", timeout = 3600)
    public void generateRecapYearly(ManualTaskRecapReqDto recapReqDto) {
        if (BeanUtil.isEmpty(recapReqDto.getYear())) {
            log.error("调用参数为空");
            return;
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("任务: generateRecapYearlyData 开始执行, 执行时间: {}", stopWatch.getTotalTimeMillis());
        //2. 获取去年的第一天和最后一天
        DateTime beginTime = DateUtil.beginOfYear(recapReqDto.getYear());
        int year = DateUtil.year(beginTime);
        DateTime endTime = DateUtil.endOfYear(recapReqDto.getYear());
        log.info("generateRecapYearly 开始时间：{}，结束时间：{} , 年份： {}", beginTime, endTime, year);
        List<Long> shopIds = iRecapMonthlyService.queryUsedShopIds(beginTime, endTime);
        shopIds.forEach(shopId ->
                iRecapYearlyService.createRecapYearly(TaskRecapReqDto.builder()
                        .shopUid(shopId)
                        .year(year)
                        .build()));
        stopWatch.stop();
        log.info("任务: generateRecapYearlyData 执行结束, 耗时: {} 毫秒", stopWatch.getTotalTimeMillis());
    }
}
