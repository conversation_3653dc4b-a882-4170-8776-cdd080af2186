package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.LabelCenterStatus;
import com.hzjm.service.entity.ShopLabelCenter;
import com.hzjm.service.model.DTO.ShopLabelCenterPageDto;
import com.hzjm.service.model.VO.ShopLabelCenterListVo;
import com.hzjm.service.model.VO.ShopLabelCenterVo;

import java.util.List;

/**
 * label 中心 服务类
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface IShopLabelCenterService extends IService<ShopLabelCenter> {

    ShopLabelCenter getByIdWithoutLogic(Integer id);

    ShopLabelCenterVo getDetail(Integer id);

    List<ShopLabelCenterVo> getDetailList(ShopLabelCenterPageDto dto);

    Boolean saveShopLabelCenter(ShopLabelCenter dto);

    Boolean insertList(List<ShopLabelCenter> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopLabelCenterListVo> searchList(ShopLabelCenterPageDto dto);

    List<ShopLabelCenter> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopLabelCenter> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    String exportExcel(ShopLabelCenterPageDto dto);

    Boolean updateStatus(List<String> trackingNos, LabelCenterStatus status);

    List<ShopLabelCenter> listNotFinalStatus();

    ShopLabelCenter selectLabel(String trackingNo);
}
