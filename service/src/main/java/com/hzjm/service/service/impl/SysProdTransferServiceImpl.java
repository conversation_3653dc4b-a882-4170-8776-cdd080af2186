package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysProdTransferMapper;
import com.hzjm.service.model.DTO.SysProdDealNotePageDto;
import com.hzjm.service.model.DTO.SysProdDealPageDto;
import com.hzjm.service.model.DTO.SysProdTransferPageDto;
import com.hzjm.service.model.VO.PlatDefaultPriceVo;
import com.hzjm.service.model.VO.SysProdDealListVo;
import com.hzjm.service.model.VO.SysProdTransferListVo;
import com.hzjm.service.model.VO.SysProdTransferVo;
import com.hzjm.service.model.VO.export.SysProdTransferExportVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.MAX_EXPORT_COUNT;
import static com.hzjm.service.constants.ServiceConstants.SYS_PROD_TRANSFER_EXCEL_HEADER_TEMPLATE;

/**
 * 平台内转移 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Slf4j
@Service
public class SysProdTransferServiceImpl extends ServiceImpl<SysProdTransferMapper, SysProdTransfer> implements ISysProdTransferService {
    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdDealNoteService iSysProdDealNoteService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysAuditService iSysAuditService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysParamSetService iSysParamSetService;

    @Override
    public SysProdTransfer getByIdWithoutLogic(Integer id) {
        SysProdTransfer data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该平台内转移"));
        }

        return data;
    }

    @Override
    public SysProdTransferVo getDetail(Integer id, String oddNo) {
        SysProdTransfer data;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<SysProdTransfer>lambdaQuery().eq(SysProdTransfer::getOddNo, oddNo));
            if (ObjectUtils.isEmpty(data)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未查询到该单号的信息"));
            }
        }

        SysProdTransferVo vo = new SysProdTransferVo();
        BeanUtils.copyProperties(data, vo);

        vo.setType(SysProdEvent.TypeTransfer);

        if (!ObjectUtils.isEmpty(data.getGmtPayValid())) {
            vo.setRestMill(data.getGmtPayValid().getTime() - DateTimeUtils.getNow().getTime());
        }

        ShopUser oldShop = iShopUserService.getById(data.getShopId());
        vo.setOldShopUid(oldShop.getUid());
        vo.setOldShopName(oldShop.getRealname());

        ShopUser newShop = iShopUserService.getById(data.getNewShopId());
        vo.setNewShopUid(newShop.getUid());
        vo.setNewShopName(newShop.getRealname());

        List<SysProdDealListVo> prodVoList = iSysProdDealService.dealList(data.getId(), SysProdEvent.TypeTransfer, new SysProdDealPageDto());
        vo.setProdList(prodVoList);

        // 仓储费用
        BigDecimal wareTotalFee = SysConstants.zero;
        for (SysProdDealListVo prodVo : prodVoList) {
            if (!ObjectUtils.isEmpty(prodVo)) {
                wareTotalFee = wareTotalFee.add(prodVo.getWareFee());
            }
        }

        vo.setWareFee(wareTotalFee);
        if (!ObjectUtils.isEmpty(data.getFreeFee()) && !ObjectUtils.isEmpty(data.getDeliveryFee()) && !ObjectUtils.isEmpty(data.getPlatFee())) {
            vo.setTotalFee(vo.getWareFee().add(vo.getDeliveryFee()).add(vo.getPlatFee().multiply(new BigDecimal(prodVoList.size()))).subtract(vo.getFreeFee()));
        }

        // 留言信息
        SysProdDealNotePageDto noteDto = new SysProdDealNotePageDto();
        noteDto.setType(SysProdEvent.TypeTransfer);
        noteDto.setRelationId(data.getId());
        vo.setNoteList(iSysProdDealNoteService.searchList(noteDto).getRecords());

        return vo;
    }


    @Override
    public Boolean saveSysProdTransfer(SysProdTransfer dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getNewShopId())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("新归属人不明"));
            }

            PlatDefaultPriceVo defaultPrice = iSysParamSetService.defaultPrice(SysProdEvent.TypeTransfer);
            if (ObjectUtils.isEmpty(dto.getPlatFee())) {
                dto.setPlatFee(defaultPrice.getPlatFee());
            }
            if (ObjectUtils.isEmpty(dto.getDeliveryFee())) {
                dto.setPlatFee(defaultPrice.getDeliveryFee());
            }
            if (ObjectUtils.isEmpty(dto.getFreeFee())) {
                dto.setPlatFee(defaultPrice.getFreeFee());
            }

            dto.setGmtDeal(BaseUtils.getGmtDeal());

            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            // 仓储费用/费用合计不可编辑
            dto.setTotalFee(null);
            dto.setWareFee(null);

            // 运费/服务费/平台优惠：非超管不可编辑
            if (JwtContentHolder.getRoleType() != 1) {
                dto.setDeliveryFee(null);
                dto.setFreeFee(null);
                dto.setPlatFee(null);
            }

            SysProdTransfer data = getById(dto.getId());
            if (!ObjectUtils.isEmpty(dto.getStatus()) && dto.getStatus() != data.getStatus().intValue()) {
                List<Integer> prodIdList = iSysProdDealService.getProdIdList(SysProdEvent.TypeTransfer, data.getId());
                List<SysProdEvent> eventList = new ArrayList<>();

                switch (dto.getStatus()) {
                    // 审核拒绝
                    case 2:
                        if (data.getStatus() != 1) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("状态同步失败"));
                        }

                        // 商品事件：审核拒绝
                        prodIdList.forEach(prodId -> {
                            SysProdEvent event = new SysProdEvent();
                            event.setProdId(prodId);
                            event.setShopId(data.getShopId());
                            event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, SysProdEvent.TypeTransfer)));
                            event.setDescription("平台内转移审核被拒：" + (ObjectUtils.isEmpty(dto.getReason()) ? "" : dto.getReason()));
                            event.setRelationId(data.getId());
                            if (event.getProdId() > 0)
                                eventList.add(event);
                        });

                        // 拒绝后释放商品
                        iSysProdService.update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

                        // 流程结束：审核被拒
                        iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                                .eq(SysProdDeal::getRelationId, data.getId())
                                .eq(SysProdDeal::getType, SysProdEvent.TypeTransfer)
                                .set(SysProdDeal::getGmtModify, DateTimeUtils.getNow())
                                .set(SysProdDeal::getStatus, 2));

                        // 更新平台审核状态：审核拒绝
                        iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                                .set(SysAudit::getStatus, 2)
                                .set(SysAudit::getOperatorId, JwtContentHolder.getUserId())
                                .eq(SysAudit::getOddNo, data.getOddNo()));

                        // search同步更新
                        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                                .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                                .in(SysProdSearch::getProdId, prodIdList)
                                .eq(SysProdSearch::getSearchType, 1));
                        break;
                    // 审核通过
                    case 3:
                        if (data.getStatus() != 1) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("状态同步失败"));
                        }
                        // 支付过期时间：1天
                        Calendar c = Calendar.getInstance();
                        c.setTime(DateTimeUtils.getNow());
                        c.add(Calendar.DATE, 1);
                        dto.setGmtPayValid(c.getTime());

                        // 商品事件：审核通过
                        prodIdList.forEach(prodId -> {
                            SysProdEvent event = new SysProdEvent();
                            event.setProdId(prodId);
                            event.setShopId(data.getShopId());
                            event.setDescription("平台内转移审核通过");
                            event.setRelationId(data.getId());
                            if (event.getProdId() > 0)
                                eventList.add(event);
                        });

                        // 更新平台审核状态：审核通过
                        iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                                .set(SysAudit::getStatus, 3)
                                .set(SysAudit::getOperatorId, JwtContentHolder.getUserId())
                                .eq(SysAudit::getOddNo, data.getOddNo()));
                        break;
                    default:
                        throw new BaseException(LanguageConfigService.i18nForMsg("不支持的操作"));
                }

                iSysProdEventService.insertList(eventList);
            }
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdTransferListVo> searchList(SysProdTransferPageDto dto) {

        LambdaQueryWrapper<SysProdTransfer> qw = Wrappers.<SysProdTransfer>lambdaQuery();

        Date endTime = dto.dealEndTime();
        Date outEndTime = dto.dealOutEndTime();
        qw.orderByDesc(SysProdTransfer::getGmtCreate)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysProdTransfer::getId, dto.getIdList())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysProdTransfer::getStatus, dto.getStatus())
                .like(!ObjectUtils.isEmpty(dto.getOddNo()), SysProdTransfer::getOddNo, dto.getOddNo())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdTransfer::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdTransfer::getGmtCreate, endTime)
                .ge(!ObjectUtils.isEmpty(dto.getOutBeginTime()), SysProdTransfer::getGmtModify, dto.getOutBeginTime())
                .lt(!ObjectUtils.isEmpty(outEndTime), SysProdTransfer::getGmtModify, outEndTime);

        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            qw.in(SysProdTransfer::getShopId, shopIdPowerList);
        }

        if (!ObjectUtils.isEmpty(dto.getDealTime())) {
            switch (dto.getDealTime()) {
                // 待处理
                case 1:
                    qw.in(SysProdTransfer::getStatus, SysProdTransfer.dealingStatus)
                            .gt(dto.getDealTime() == 1, SysProdTransfer::getGmtDeal, DateTimeUtils.getNow());
                    break;
                // 今日待处理
                case 2:
                    qw.in(SysProdTransfer::getStatus, SysProdTransfer.dealingStatus)
                            .le(dto.getDealTime() == 2, SysProdTransfer::getGmtDeal, DateTimeUtils.getNow());
                    break;
                case 3:
                    qw.in(SysProdTransfer::getStatus, SysProdTransfer.outingStatus);
                    break;
                case 4:
                    qw.in(SysProdTransfer::getStatus, SysProdTransfer.finishStatus);
                    break;
            }
        }

        if (!ObjectUtils.isEmpty(dto.getUid())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getUid()), ShopUser::getUid, dto.getUid()));
            List<Integer> shopIdList = BaseUtils.initList();

            if (!ObjectUtils.isEmpty(shopList)) {
                shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            }
            qw.in(SysProdTransfer::getShopId, shopIdList);
        }

        if (!ObjectUtils.isEmpty(dto.getNewShopUid())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getNewShopUid()), ShopUser::getUid, dto.getNewShopUid()));
            List<Integer> shopIdList = BaseUtils.initList();

            if (!ObjectUtils.isEmpty(shopList)) {
                shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            }
            qw.in(SysProdTransfer::getNewShopId, shopIdList);
        }

        if (!ObjectUtils.isEmpty(dto.getOldShopUid())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getOldShopUid()), ShopUser::getUid, dto.getOldShopUid()));
            List<Integer> shopIdList = BaseUtils.initList();

            if (!ObjectUtils.isEmpty(shopList)) {
                shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            }
            qw.in(SysProdTransfer::getShopId, shopIdList);
        }

        if (JwtContentHolder.getRoleType() == 1) {
            qw.ne(SysProdTransfer::getStatus, 5); // 超管端不展示已取消的记录
        }

        IPage<SysProdTransfer> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdTransferListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            int type = SysProdEvent.TypeTransfer;

            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().map(SysProdTransfer::getShopId).collect(Collectors.toList()));
            shopIdList.addAll(pageResult.getRecords().stream().map(SysProdTransfer::getNewShopId).collect(Collectors.toList()));
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .in(ShopUser::getId, shopIdList.stream().distinct().collect(Collectors.toList())));
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
            shopList.clear();

            Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealGroup = iSysProdDealService.dealGroup(
                    pageResult.getRecords().stream().map(SysProdTransfer::getId).collect(Collectors.toList()),
                    new ArrayList<>(Arrays.asList(type)), null);

            pageResult.getRecords().forEach(data -> {
                SysProdTransferListVo vo = new SysProdTransferListVo();
                BeanUtils.copyProperties(data, vo);

                ShopUser oldShop = shopMap.get(data.getShopId());
                if (!ObjectUtils.isEmpty(oldShop)) {
                    vo.setOldShopUid(oldShop.getUid());
                }

                ShopUser newShop = shopMap.get(data.getNewShopId());
                if (!ObjectUtils.isEmpty(newShop)) {
                    vo.setNewShopUid(newShop.getUid());
                }

                BigDecimal wareFee = SysConstants.zero;
                List<SysProdDealListVo> dealList = dealGroup.get(type).get(data.getId());
                for (SysProdDealListVo deal : dealList) {
                    wareFee = wareFee.add(deal.getWareFee());
                }
                vo.setWareFee(wareFee);
                vo.setProdList(dealList);
                vo.setProdNum(dealList.size());
                voList.add(vo);
            });
            dealGroup.clear();
            shopMap.clear();
        }

        IPage<SysProdTransferListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdTransfer> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdTransfer> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdTransfer> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }


    @Override
    public String exportProdTransferList(SysProdTransferPageDto dto, String language) {
        dto.setSize(MAX_EXPORT_COUNT);
        log.info("开始调用 平台内转移列表 接口 ");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IPage<SysProdTransferListVo> searchList = searchList(dto);
        List<SysProdTransferListVo> records = searchList.getRecords();
        stopWatch.stop();
        log.info("查询 平台内转移列表 耗时：{} ms", stopWatch.getTotalTimeMillis());
        List<SysProdTransferExportVo> dataList = records.stream()
                .flatMap(sysProdTransferListVo -> sysProdTransferListVo.mapToSysCashOutExportVoList().stream())
                .collect(Collectors.toList());
        String fileName = LanguageConfigService.i18nForMsg("平台内转移") +
                "(" + DateTimeUtils.getFileSuffix() + BaseUtils.getRandomStr(3) + ").xlsx";
        return ExcelReader.generateExcelFileEasyExcel(dataList, fileName, LanguageConfigService.generateExcelHeaders(SYS_PROD_TRANSFER_EXCEL_HEADER_TEMPLATE, language));
    }
}
