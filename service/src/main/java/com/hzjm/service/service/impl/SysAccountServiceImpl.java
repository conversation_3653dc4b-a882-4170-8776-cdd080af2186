package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.LoginUser;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.JwtUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysAccountMapper;
import com.hzjm.service.model.DTO.LoginDto;
import com.hzjm.service.service.*;
import com.hzjm.service.utils.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 平台账号 服务实现类
 *
 * <AUTHOR>
 * @since 2020-09-30
 */
@Slf4j
@Service
public class SysAccountServiceImpl extends ServiceImpl<SysAccountMapper, SysAccount> implements ISysAccountService {

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private IAppUserService iAppUserService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysUserPermissionService iSysUserPermissionService;

    @Autowired
    private ISysUserShopService iSysUserShopService;

    @Autowired
    private RedisUtils redisUtils;

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${knet.allow-login-as-user}")
    private Boolean allowLoginAsUser;

    @Override
    public SysAccount getByIdWithoutLogic(Integer id) {
        return baseMapper.selectByIdWithoutLogic(id);
    }

    @Override
    public Boolean saveSysAccount(SysAccount dto) {
        Boolean rs = false;
        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (!ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag()) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public LoginUser login(LoginDto dto) {
        if (ObjectUtils.isEmpty(dto.getUserType())) {
            throw new BaseException("身份不明");
        }
        if (ObjectUtils.isEmpty(dto.getLoginType())) {
            throw new BaseException("无效的登录方式，请重新选择");
        }

        SysAccount account = baseMapper.selectOne(Wrappers.<SysAccount>lambdaQuery()
                .eq(SysAccount::getAccountType, dto.getLoginType())
                .eq(SysAccount::getAccount, dto.getAccount())
                .eq(SysAccount::getUserType, dto.getUserType() == 4 ? 1 : dto.getUserType())
                .eq(SysAccount::getDelFlag, 0)
        );
        if (dto.getUserType() != 2 && ObjectUtils.isEmpty(account)) {
            throw new BaseException("该账号未注册");
        }

        // 入参校验
        checkLoginType(dto, account);

        // 用户校验
        LoginUser user = new LoginUser();
        try {
            account = checkLoginUser(dto, account, user);
        } catch (BaseException e) {
            if (e.getCode() == 10001) {
                // 返回null，视为微信未绑定账号主体
                return null;
            } else {
                throw e;
            }
        }

        // 生成token
        user.setId(account.getUserId());
        user.setUserType(dto.getUserType());
        user.setToken(JwtUtils.createJWT(JSONObject.toJSONString(user)));

        // 获取用户权限
        if (dto.getUserType() == 1) {
            List<SysUserPermission> permList = iSysUserPermissionService.list(Wrappers.<SysUserPermission>lambdaQuery().eq(SysUserPermission::getUserId, user.getId()));
            List<Integer> userPermList =  permList.stream().map(SysUserPermission::getPermissionId).collect(Collectors.toList());
            // 这三个账号登录固定拥有101 【BI】 权限
            if (!userPermList.contains(101) && (
                        "knetfufu".equalsIgnoreCase(dto.getAccount())
                    || "Daejin".equalsIgnoreCase(dto.getAccount())
                    || "admin".equalsIgnoreCase(dto.getAccount())
            )){
                userPermList.add(101);
            }

            user.setRoles(userPermList);
        }

        String key = "";
        switch (dto.getUserType()) {
            case 1:
                key = SysConstants.manageToken;
                break;
            case 2:
                key = SysConstants.userToken;
                break;
            case 4:
                key = SysConstants.wareToken;
                break;
            case 5:
                key = SysConstants.shopToken;
                break;
        }
        redisUtils.putForValue(key + user.getId(), user.getToken());

        // 单点/禁用踢出：视需求开启
//        redisUtils.putForValue(applicationName + "_token_" + user.getId(), user.getToken());

        return user;
    }

    @Override
    public LoginUser loginAsUser(LoginDto dto)
    {
        if (!allowLoginAsUser) {
            throw new BaseException("禁止操作！");
        }

        SysAccount account = baseMapper.selectOne(Wrappers.<SysAccount>lambdaQuery()
                .eq(SysAccount::getAccountType, dto.getLoginType())
                .eq(SysAccount::getAccount, dto.getAccount())
                .eq(SysAccount::getUserType, dto.getUserType() == 4 ? 1 : dto.getUserType())
                .eq(SysAccount::getDelFlag, 0)
        );
        if (dto.getUserType() != 2 && ObjectUtils.isEmpty(account)) {
            throw new BaseException("该账号未注册");
        }

        // 用户校验
        LoginUser user = new LoginUser();
        try {
            account = checkLoginUser(dto, account, user);
        } catch (BaseException e) {
            if (e.getCode() == 10001) {
                return null;
            } else {
                throw e;
            }
        }

        // 生成token
        user.setId(account.getUserId());
        user.setUserType(dto.getUserType());
        user.setToken(JwtUtils.createJWT(JSONObject.toJSONString(user)));

        // 获取用户权限
        if (dto.getUserType() == 1) {
            List<SysUserPermission> permList = iSysUserPermissionService.list(Wrappers.<SysUserPermission>lambdaQuery().eq(SysUserPermission::getUserId, user.getId()));
            List<Integer> userPermList =  permList.stream().map(SysUserPermission::getPermissionId).collect(Collectors.toList());
            // 这三个账号登录固定拥有101 【BI】 权限
            if (!userPermList.contains(101) && (
                    "knetfufu".equalsIgnoreCase(dto.getAccount())
                            || "Daejin".equalsIgnoreCase(dto.getAccount())
                            || "admin".equalsIgnoreCase(dto.getAccount())
            )){
                userPermList.add(101);
            }

            user.setRoles(userPermList);
        }

        String key = "";
        switch (dto.getUserType()) {
            case 1:
                key = SysConstants.manageToken;
                break;
            case 2:
                key = SysConstants.userToken;
                break;
            case 4:
                key = SysConstants.wareToken;
                break;
            case 5:
                key = SysConstants.shopToken;
                break;
        }
        redisUtils.putForValue(key + user.getId(), user.getToken());

        return user;
    }

    /**
     * 入参校验
     *
     * @param dto     入参
     * @param account 登录账号
     */
    private void checkLoginType(LoginDto dto, SysAccount account) {
        switch (dto.getLoginType()) {
            // 通过账号获取
            case 1:
                // 1-后管，2-用户，4-仓库，5-商家
                if(dto.getUserType() == 1 || dto.getUserType() == 4 || dto.getUserType() == 5) {
                    // 验证图片码
                    redisUtils.checkCaptcha(dto.getCode());
                }

                // 校验密码
                if (ObjectUtils.isEmpty(dto.getPassword())) {
                    throw new BaseException("请输入密码");
                }

                String key = applicationName;
                if(dto.getUserType() == 1 || dto.getUserType() == 4) {
                    key = "ocean-flow-manage"; // 仓库跟平台共用一套key
                }
                if (!BaseUtils.md5(key, dto.getPassword()).equals(account.getPwd())) {
                    throw new BaseException("Incorrect password, please try again.");
                }
                break;
            // 通过手机号获取
            case 2:
                redisUtils.checkCode(dto.getAccount(), dto.getCode());
                break;
            // 通过三方标识获取
            default:
                break;
        }
    }

    /**
     * 用户校验
     *
     * @param dto       入参
     * @param account   登录账号
     * @param loginUser 返回的用户信息
     */
    private SysAccount checkLoginUser(LoginDto dto, SysAccount account, LoginUser loginUser) {
        // 根据用户类型判断用户状态
        if (dto.getUserType() == 1) {
            // 后管
            SysUser user = iSysUserService.getById(account.getUserId());
            if (ObjectUtils.isEmpty(user)) {
                throw new BaseException("该用户已被注销");
            }
            if (user.getStatus() == 2) {
                throw new BaseException(403, "该用户已被禁用");
            }
            if(user.getAdminPower() != 1) {
                throw new BaseException("该用户没有管理权限");
            }
            loginUser.setNickname(user.getNickname());
            loginUser.setAuditPower(user.getAuditPower());
            loginUser.setProdPower(user.getProdPower());
            iSysUserService.update(Wrappers.<SysUser>lambdaUpdate().eq(SysUser::getId, user.getId()).set(SysUser::getGmtLastLogin, DateTimeUtils.getNow()));

            // 获取商家权限
            if(user.getId() != 1) {
                // 超管拥有所有商家权限
                List<SysUserShop> userShopList = iSysUserShopService.list(Wrappers.<SysUserShop>lambdaQuery().eq(SysUserShop::getUserId, user.getId()));
                List<Integer> shopIdList = BaseUtils.initList();
                shopIdList.addAll(userShopList.stream().map(SysUserShop::getShopId).collect(Collectors.toList()));
                if(user.getShopPower() != 1) {
                    loginUser.setShopIdList(shopIdList);
                }
            }
        } else if (dto.getUserType() == 4) {
            // 仓库
            SysUser user = iSysUserService.getById(account.getUserId());
            if (ObjectUtils.isEmpty(user)) {
                throw new BaseException("该用户已被注销");
            }
            if (user.getStatus() == 2) {
                throw new BaseException(403,"该用户已被禁用");
            }
            if(user.getWarePower() != 1) {
                throw new BaseException("该用户没有仓库管理权限");
            }
            loginUser.setNickname(user.getNickname());
            iSysUserService.update(Wrappers.<SysUser>lambdaUpdate().eq(SysUser::getId, user.getId()).set(SysUser::getGmtLastLogin, DateTimeUtils.getNow()));
        } else if (dto.getUserType() == 5) {
            // 商家登录
            ShopUser user = iShopUserService.getById(account.getUserId());
            if (ObjectUtils.isEmpty(user)) {
                throw new BaseException("该用户已被注销");
            }
            if (user.getStatus() == 2) {
                throw new BaseException(403,"该用户已被禁用");
            }
            iShopUserService.update(Wrappers.<ShopUser>lambdaUpdate().eq(ShopUser::getId, user.getId()).set(ShopUser::getGmtLastLogin, DateTimeUtils.getNow()));
            loginUser.setNickname(user.getRealname());
            loginUser.setUid(user.getUid());
            loginUser.setHeadImg(user.getHeadImg());
        } else {
            AppUser user = null;
            if (ObjectUtils.isEmpty(account)) {
                // 注册
                account = new SysAccount();
                Boolean newUser = false;

                switch (dto.getLoginType()) {
                    case 2:
                        user = new AppUser();
                        user.setNickname("用户" + dto.getPhone().substring(dto.getPhone().length() - 4));
                        user.setHeadImg(SysConstants.defaultHead);
                        user.setPhone(dto.getAccount());
                        account.setAccountType(2);
                        break;
                    default:
                        // 判断账号主体是否已存在[默认系统账号主体为手机号]
                        if (ObjectUtils.isEmpty(dto.getPhone())) {
                            throw new BaseException(10001, "该微信未绑定手机号");
                        } else if (dto.getLoginType() == 3) {
                            redisUtils.checkCode(dto.getPhone(), dto.getCode());
                        }

                        user = iAppUserService.getOne(Wrappers.<AppUser>lambdaQuery().eq(AppUser::getPhone, dto.getPhone()));
                        if (ObjectUtils.isEmpty(user)) {
                            user = new AppUser();
                            if (dto.getLoginType() == 1) {
                                user.setNickname("用户" + dto.getPhone().substring(dto.getPhone().length() - 4));
                                user.setHeadImg(SysConstants.defaultHead);
                                user.setPhone(dto.getAccount());

                                account.setPwd(BaseUtils.md5(applicationName, dto.getPassword()));
                            } else {
                                user.setNickname(dto.getNickname());
                                user.setHeadImg(dto.getHeadImg());
                                user.setPhone(dto.getPhone());
                            }

                            newUser = true;
                        }

                        account.setAccountType(dto.getLoginType());
                        break;
                }
                iAppUserService.saveAppUser(user);

                account.setUserId(user.getId());
                account.setUserType(2);
                account.setAccount(dto.getAccount());
                saveSysAccount(account);

                // 非手机号登录且绑定了手机号：自动生成手机号账号
                if (dto.getLoginType() != 2 && !ObjectUtils.isEmpty(dto.getPhone()) && newUser) {
                    SysAccount phoneAccount = new SysAccount();
                    phoneAccount.setUserId(user.getId());
                    phoneAccount.setUserType(2);
                    phoneAccount.setAccount(dto.getPhone());
                    phoneAccount.setAccountType(2);
                    saveSysAccount(phoneAccount);
                }
            } else {
                // 用户
                user = iAppUserService.getById(account.getUserId());
                if (ObjectUtils.isEmpty(user)) {
                    log.error("账号[" + account.getUserId() + "]已注销，请及时处理异常数据");
                    throw new BaseException("该用户已被注销");
                }
                if (user.getStatus() == 2) {
                    throw new BaseException(403,"该用户已被禁用");
                }
            }
            loginUser.setNickname(user.getNickname());
        }

        return account;
    }

}
