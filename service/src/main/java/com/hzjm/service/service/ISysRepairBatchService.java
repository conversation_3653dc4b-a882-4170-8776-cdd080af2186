package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysRepairBatch;
import com.hzjm.service.model.DTO.SysRepairBatchPageDto;
import com.hzjm.service.model.VO.SysRepairBatchCountVo;
import com.hzjm.service.model.VO.SysRepairBatchListVo;
import com.hzjm.service.model.VO.SysRepairBatchVo;

import java.util.List;

/**
 * 维修批次表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
public interface ISysRepairBatchService extends IService<SysRepairBatch> {

    SysRepairBatch getByIdWithoutLogic(Integer id);

    SysRepairBatchVo getDetail(Integer id, Integer shopId);

    SysRepairBatchVo getDetail(String batchNo,Integer shopId);

    Boolean saveSysRepairBatch(SysRepairBatch dto);

    Boolean insertList(List<SysRepairBatch> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysRepairBatchListVo> searchList(SysRepairBatchPageDto dto);

    List<SysRepairBatch> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysRepairBatch> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    SysRepairBatchCountVo searchListCount(SysRepairBatchPageDto dto);

}
