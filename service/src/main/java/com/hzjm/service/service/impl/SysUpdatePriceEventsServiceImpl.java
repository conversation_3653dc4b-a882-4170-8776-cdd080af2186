package com.hzjm.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.SysUpdatePriceEvents;
import com.hzjm.service.mapper.SysUpdatePriceEventsMapper;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.service.ISysUpdatePriceEventsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/18 16:42
 * @description:
 */
@Slf4j
@Service
public class SysUpdatePriceEventsServiceImpl extends
        ServiceImpl<SysUpdatePriceEventsMapper, SysUpdatePriceEvents> implements ISysUpdatePriceEventsService {
    @Override
    public List<SysUpdatePriceEvents> findNeedToUpdatePriceEvents(Integer minutes, Integer total) {
        return baseMapper.findNeedToUpdatePriceEvents(minutes, total);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void successUpdatePriceTasks(List<SysUpdatePriceEvents> priceEvents) {
        // 5.更新价格事件表状态为成功
        LambdaUpdateWrapper<SysUpdatePriceEvents> successWrapper = Wrappers.lambdaUpdate();
        successWrapper
                .in(SysUpdatePriceEvents::getId, priceEvents.stream().map(SysUpdatePriceEvents::getId).toArray())
                .set(SysUpdatePriceEvents::getStatus, SysTaskStatus.SUCCESS);
        this.update(successWrapper);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void failUpdatePriceTasks(List<SysUpdatePriceEvents> priceEvents) {
        LambdaUpdateWrapper<SysUpdatePriceEvents> failWrapper = Wrappers.lambdaUpdate();
        failWrapper
                .in(SysUpdatePriceEvents::getId, priceEvents.stream().map(SysUpdatePriceEvents::getId).toArray())
                .set(SysUpdatePriceEvents::getStatus, SysTaskStatus.FAILURE);
        this.update(failWrapper);
    }

    @Override
    public void cleanUpdatePriceTasks(List<SysUpdatePriceEvents> priceEvents) {
        if (CollUtil.isEmpty(priceEvents)) {
            return;
        }
        List<Long> deleteIds = priceEvents.stream().map(SysUpdatePriceEvents::getId).collect(Collectors.toList());
        log.info("删除价格事件表数据，清理条数: {}", deleteIds.size());
        baseMapper.deleteBatchIds(deleteIds);
    }
}
