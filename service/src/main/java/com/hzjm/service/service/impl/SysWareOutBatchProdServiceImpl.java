package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysWareOutBatchProdMapper;
import com.hzjm.service.model.DTO.SysWareOutBatchProdPageDto;
import com.hzjm.service.model.VO.SysWareBatchCountVo;
import com.hzjm.service.model.VO.SysWareOutBatchListVo;
import com.hzjm.service.model.VO.SysWareOutBatchProdListVo;
import com.hzjm.service.model.VO.SysWareOutBatchProdVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 出库批次商品 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
@Slf4j
@Service
public class  SysWareOutBatchProdServiceImpl extends ServiceImpl<SysWareOutBatchProdMapper, SysWareOutBatchProd> implements ISysWareOutBatchProdService {

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutBatchService iSysWareOutBatchService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Override
    public SysWareOutBatchProd getByIdWithoutLogic(Integer id) {
        SysWareOutBatchProd data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该出库批次商品"));
        }

        return data;
    }

    @Override
    public SysWareOutBatchProdVo getDetail(Integer id) {
        SysWareOutBatchProd data = getByIdWithoutLogic(id);

        SysWareOutBatchProdVo vo = new SysWareOutBatchProdVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareOutBatchProd(SysWareOutBatchProd dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysWareOutBatchProdListVo> searchList(SysWareOutBatchProdPageDto dto) {
        Map<Integer, Integer> prodShopMap = new HashMap<>();
        LambdaQueryWrapper<SysWareOutBatchProd> qw = buildQw(dto, prodShopMap);

        IPage<SysWareOutBatchProd> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareOutBatchProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> outIdList = pageResult.getRecords().stream().map(SysWareOutBatchProd::getOutId).collect(Collectors.toList());
            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery().in(SysWareOut::getId, outIdList));
            Map<Integer, SysWareOut> outMap = outList.stream().collect(Collectors.toMap(SysWareOut::getId, a -> a));
            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(outList.stream().filter(a -> a.getType() == SysProdEvent.TypeSale).map(SysWareOut::getRelationId).collect(Collectors.toList()));

            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery());
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));

            List<SysWare> wareList = iSysWareService.list(Wrappers.<SysWare>lambdaQuery()
                    .in(SysWare::getId, outList.stream().map(SysWareOut::getWareId).collect(Collectors.toList())));
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
            wareList.clear();
            outList.clear();
            shopList.clear();

            List<Integer> prodIdList = pageResult.getRecords().stream().map(SysWareOutBatchProd::getProdId).collect(Collectors.toList());
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
            Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
            prodList.clear();

            List<Integer> batchIdList = pageResult.getRecords().stream().map(SysWareOutBatchProd::getBatchId).collect(Collectors.toList());
            List<SysWareOutBatch> batchList = iSysWareOutBatchService.list(Wrappers.<SysWareOutBatch>lambdaQuery().in(SysWareOutBatch::getId, batchIdList));
            Map<Integer, SysWareOutBatch> batchMap = batchList.stream().collect(Collectors.toMap(SysWareOutBatch::getId, a -> a));
            batchList.clear();

            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getId, pageResult.getRecords().stream().map(SysWareOutBatchProd::getOutProdId).collect(Collectors.toList())));

            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(outProdList.stream().filter(a -> {
                return !ObjectUtils.isEmpty(a.getCheckId());
            }).map(SysWareOutProd::getCheckId).collect(Collectors.toList()));
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery()
                    .isNotNull(SysUser::getNickname)
                    .in(SysUser::getId, userIdList));
            Map<Integer, String> userMap = userList.stream().filter(a -> !ObjectUtils.isEmpty(a.getNickname())).collect(Collectors.toMap(SysUser::getId, SysUser::getNickname));
            userList.clear();

            Map<Integer, Integer> shelvesIdMap = new HashMap<>();
            Map<Integer, String> shelvesMap = new HashMap<>();
            List<SysWareOutProd> outProdShelveList = outProdList.stream().filter(a -> !ObjectUtils.isEmpty(a.getShelvesId())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(outProdShelveList)) {
                shelvesIdMap.putAll(outProdShelveList.stream().collect(Collectors.toMap(SysWareOutProd::getProdId, SysWareOutProd::getShelvesId)));

                List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                        .in(SysWareShelves::getId, outProdShelveList.stream().map(SysWareOutProd::getShelvesId).collect(Collectors.toList())));
                shelvesMap.putAll(shelvesList.stream().collect(Collectors.toMap(SysWareShelves::getId, SysWareShelves::getName)));
            }
            outProdShelveList.clear();
            outProdList.clear();

            List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
            Map<Integer, SysWareInProd> inProdMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));
            inProdList.clear();

            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
            Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
            saleList.clear();

            if (ObjectUtils.isEmpty(prodShopMap)) {
                List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                        .select(SysProdDeal::getProdId, SysProdDeal::getShopId, SysProdDeal::getType)
                        .in(SysProdDeal::getType, SysProdEvent.TypeSale, SysProdEvent.TypeTransport, SysProdEvent.TypeSend)
                        .in(!ObjectUtils.isEmpty(prodIdList), SysProdDeal::getProdId, prodIdList)
                        .in(SysProdDeal::getStatus, 1, 3));
//                prodShopMap.putAll(dealList.stream().collect(Collectors.toMap(SysProdDeal::getProdId, SysProdDeal::getShopId)));
                prodShopMap.putAll(dealList.stream().collect(Collectors.toMap(
                        SysProdDeal::getProdId,
                        SysProdDeal::getShopId,
                        (existingValue, newValue) -> newValue
                )));

                dealList.clear();
            }

            pageResult.getRecords().forEach(data -> {
                SysWareOutBatchProdListVo vo = new SysWareOutBatchProdListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setGmtOut(data.getGmtOut());
                SysWareOutBatch batch = batchMap.get(data.getBatchId());
                if (!ObjectUtils.isEmpty(batch)) {
                    vo.setOutBatchNo(batch.getBatchNo());
                    vo.setChecker(userMap.get(batch.getCheckerId()));
                }

                // 归属者
                ShopUser shop = Optional.ofNullable(prodShopMap.get(data.getProdId())).map(shopId -> shopMap.get(shopId)).orElse(null);
                if (!ObjectUtils.isEmpty(shop)) {
                    vo.setShopUid(shop.getUid());
                    vo.setShopName(shop.getRealname());
                }

                SysWareOut out = outMap.get(data.getOutId());
                if (!ObjectUtils.isEmpty(out)) {
                    vo.setOutOddNo(out.getOddNo());
                    vo.setOutType(out.getType());
                    vo.setLabelImg(out.getLabelImg());

                    // 仓库
                    vo.setWareName(wareMap.get(out.getWareId()));

                    // 寄售信息
                    if (out.getType() == SysProdEvent.TypeSale) {
                        SysProdSale sale = saleMap.get(out.getRelationId());
                        if (!ObjectUtils.isEmpty(sale)) {
                            vo.setPlatName(sale.getPlatName());
                            vo.setPlatOrderNo(sale.getPlatOrderNo());
                            vo.setLogNo(sale.getLogNo());
                        }
                    }

                }


                SysProd prod = prodMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    vo.setSpec(prod.getSpec());
                    vo.setSku(prod.getSku());
                    vo.setRemarks(prod.getRemarks());
                    vo.setImg(prod.getImg());
                    vo.setOneId(prod.getOneId());
                    vo.setGmtIn(prod.getGmtIn());
                }

                SysWareInProd inProd = inProdMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(inProd)) {
                    vo.setCheckResult(inProd.getCheckResult());
                }

                // 货架信息
                String shelvesName = shelvesMap.get(shelvesIdMap.get(data.getProdId()));
                if (ObjectUtils.isEmpty(shelvesName)) {
                    shelvesName = "";
                }
                vo.setShelvesName(shelvesName);

                voList.add(vo);
            });
        }

        IPage<SysWareOutBatchProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    private LambdaQueryWrapper<SysWareOutBatchProd> buildQw(SysWareOutBatchProdPageDto dto, Map<Integer, Integer> prodShopMap) {

        LambdaQueryWrapper<SysWareOutBatchProd> qw = Wrappers.<SysWareOutBatchProd>lambdaQuery();

        Date endTime = dto.dealEndTime();
        Date endInTime = dto.dealEndInTime();
        qw.eq(SysWareOutBatchProd::getDelFlag, 0)
                .eq(!ObjectUtils.isEmpty(dto.getBatchId()), SysWareOutBatchProd::getBatchId, dto.getBatchId())
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysWareOutBatchProd::getBatchId, dto.getIdList())
   /*// 出库时间筛选
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareOutBatchProd::getGmtOut, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareOutBatchProd::getGmtOut, endTime)*/;
        if (ObjectUtils.isEmpty(dto.getSortField())) {
            qw.orderByDesc(SysWareOutBatchProd::getGmtCreate);
        } else {
            if (ObjectUtils.isEmpty(dto.getSortOrder())) {
                dto.setSortOrder("descend");
            }
            if (!ObjectUtils.isEmpty(dto.getSortField())) {
                switch (dto.getSortField()) {
                    case "gmtOut":
                        qw.orderBy(true, dto.getSortOrder().equals("ascend"), SysWareOutBatchProd::getGmtOut);
                        break;
                }
            }
        }

        if (JwtContentHolder.getRoleType() == 1) {
            qw.isNotNull(SysWareOutBatchProd::getGmtOut); // 已出库的
        }

        if (!ObjectUtils.isEmpty(dto.getChecker())) {
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery().like(SysUser::getNickname, dto.getChecker()));
            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(userList.stream().map(SysUser::getId).collect(Collectors.toList()));

            List<SysWareOutBatch> batchList = iSysWareOutBatchService.list(Wrappers.<SysWareOutBatch>lambdaQuery()
                    .in(SysWareOutBatch::getCheckerId, userIdList));
            qw.in(SysWareOutBatchProd::getBatchId, batchList.stream().map(SysWareOutBatch::getId).collect(Collectors.toList()));
            batchList.clear();
        }

        if (!ObjectUtils.isEmpty(dto.getOutBatchNo()) || !ObjectUtils.isEmpty(dto.getIdList())) {
            List<SysWareOutBatch> batchList = iSysWareOutBatchService.list(Wrappers.<SysWareOutBatch>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getOutBatchNo()), SysWareOutBatch::getBatchNo, dto.getOutBatchNo()));
            List<Integer> batchIdList = BaseUtils.initList();
            batchIdList.addAll(batchList.stream().map(SysWareOutBatch::getId).collect(Collectors.toList()));
            qw.in(SysWareOutBatchProd::getBatchId, batchIdList);
        }

        List<Integer> shopIdList = null;
        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(dto.getShopUid()) || !ObjectUtils.isEmpty(shopIdPowerList)) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .in(!ObjectUtils.isEmpty(shopIdPowerList), ShopUser::getId, shopIdPowerList)
                    .like(!ObjectUtils.isEmpty(dto.getShopUid()), ShopUser::getUid, dto.getShopUid()));
            shopIdList = BaseUtils.initList();
            shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            shopList.clear();
        }

        List<Integer> prodIdList = null;
        if (!ObjectUtils.isEmpty(dto.getSku()) || !ObjectUtils.isEmpty(dto.getSpec())
                || !ObjectUtils.isEmpty(dto.getSkuList()) || !ObjectUtils.isEmpty(dto.getSpecList())
                || !ObjectUtils.isEmpty(dto.getBeginInTime()) || !ObjectUtils.isEmpty(endInTime)
                || !ObjectUtils.isEmpty(dto.getOneId()) || !ObjectUtils.isEmpty(dto.getOneIdList())
                || !ObjectUtils.isEmpty(dto.getBrand()) || !ObjectUtils.isEmpty(dto.getRemarks())) {
            LambdaQueryWrapper<SysProd> qw1 = Wrappers.<SysProd>lambdaQuery()
                    // 入库时间筛选
                    .ge(!ObjectUtils.isEmpty(dto.getBeginInTime()), SysProd::getGmtIn, dto.getBeginInTime())
                    .lt(!ObjectUtils.isEmpty(endInTime), SysProd::getGmtIn, endInTime)
                    .like(!ObjectUtils.isEmpty(dto.getRemarks()), SysProd::getRemarks, dto.getRemarks())
                    .like(!ObjectUtils.isEmpty(dto.getBrand()), SysProd::getBrand, dto.getBrand())
                    .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProd::getOneId, dto.getOneId())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                    .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysProd::getSku, dto.getSkuList())
                    .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysProd::getSpec, dto.getSpecList())
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec());

            if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
                StringBuffer sb = new StringBuffer();
                dto.getOneIdList().forEach(oneId -> {
                    sb.append("(one_id like '%" + oneId + "%') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            List<SysProd> prodList = iSysProdService.list(qw1);
            prodIdList = BaseUtils.initList();
            prodIdList.addAll(prodList.stream().map(SysProd::getId).collect(Collectors.toList()));
            prodList.clear();

//            qw.in(SysWareOutBatchProd::getProdId, prodIdList);
        }

        List<Integer> saleIdList = null;
        if (!ObjectUtils.isEmpty(dto.getPlatName())) {
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery()
                    .select(SysProdSale::getId)
                    .like(SysProdSale::getPlatName, dto.getPlatName()));
            saleIdList = BaseUtils.initList();
            saleIdList.addAll(saleList.stream().map(SysProdSale::getId).collect(Collectors.toList()));
            saleList.clear();
        }

        if (!ObjectUtils.isEmpty(prodIdList) || !ObjectUtils.isEmpty(dto.getBeginTime()) || !ObjectUtils.isEmpty(endTime)) {
            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .select(SysProdDeal::getProdId, SysProdDeal::getShopId, SysProdDeal::getType)
                    .in(SysProdDeal::getType, SysProdEvent.TypeSale, SysProdEvent.TypeTransport, SysProdEvent.TypeSend)
                    .in(!ObjectUtils.isEmpty(prodIdList), SysProdDeal::getProdId, prodIdList)
                    .in(!ObjectUtils.isEmpty(shopIdList), SysProdDeal::getShopId, shopIdList)
                    .in(!ObjectUtils.isEmpty(saleIdList), SysProdDeal::getSaleId, saleIdList)
                    // 出库时间筛选
                    .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdDeal::getGmtOut, dto.getBeginTime())
                    .lt(!ObjectUtils.isEmpty(endTime), SysProdDeal::getGmtOut, endTime)
                    .in(SysProdDeal::getStatus, 1, 3));
            prodIdList = BaseUtils.initList();
            prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
            qw.in(SysWareOutBatchProd::getProdId, prodIdList);
//            prodShopMap.putAll(dealList.stream().collect(Collectors.toMap(SysProdDeal::getProdId, SysProdDeal::getShopId)));
            prodShopMap.putAll(dealList.stream().collect(Collectors.toMap(
                    SysProdDeal::getProdId,
                    SysProdDeal::getShopId,
                    (existingValue, newValue) -> newValue
            )));
            dealList.clear();
        }

        if (!ObjectUtils.isEmpty(dto.getOutType())
                || !ObjectUtils.isEmpty(dto.getWareId()) || !ObjectUtils.isEmpty(dto.getWareIdList())
                || !ObjectUtils.isEmpty(dto.getOddNo())) {
            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getOddNo()), SysWareOut::getOddNo, dto.getOddNo())
                    .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareOut::getWareId, dto.getWareId())
                    .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysWareOut::getWareId, dto.getWareIdList())
                    .eq(!ObjectUtils.isEmpty(dto.getOutType()), SysWareOut::getType, dto.getOutType()));
            List<Integer> outIdList = BaseUtils.initList();
            outIdList.addAll(outList.stream().map(SysWareOut::getId).collect(Collectors.toList()));
            outList.clear();

            qw.in(SysWareOutBatchProd::getOutId, outIdList);
        }
        return qw;

    }

    @Override
    public Boolean insertList(List<SysWareOutBatchProd> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareOutBatchProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    @ReadOnly
    @Deprecated
    public List<SysWareOutBatchListVo> searchGroup(SysWareOutBatchProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone)); // 默认东海岸的今天
        }

        List<SysWareOutBatchProdListVo> dataList = searchList(dto).getRecords();

        List<SysWareOutBatchListVo> groupList = new ArrayList<>();

        if (!ObjectUtils.isEmpty(dataList)) {
            // 使用过滤器忽略 batchId 为 null 的数据
            Map<Integer, List<SysWareOutBatchProdListVo>> keyMap = dataList.stream()
                    .filter(vo -> vo.getBatchId() != null) // 过滤掉 batchId 为 null 的数据
                    .collect(Collectors.groupingBy(SysWareOutBatchProdListVo::getBatchId));

            keyMap.keySet().forEach(key -> {
                SysWareOutBatchListVo vo = new SysWareOutBatchListVo();

                List<SysWareOutBatchProdListVo> prodList = keyMap.get(key);
                vo.setProdList(prodList);

                SysWareOutBatchProdListVo sample = prodList.get(0);
                vo.setId(sample.getBatchId());
                vo.setBatchNo(sample.getOutBatchNo());
                vo.setGmtCreate(sample.getGmtCreate());
                vo.setWareName(sample.getWareName());
                vo.setChecker(sample.getChecker());
                vo.setPlatName(sample.getPlatName());
                vo.setOutType(sample.getOutType());
                vo.setGmtOut(sample.getGmtOut());
                groupList.add(vo);
            });
        }

        return groupList.stream().sorted(Comparator.comparing(SysWareOutBatchListVo::getGmtOut).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<SysWareOutBatchProd> statistic(QueryWrapper qw) {
        return baseMapper.statistic(qw);
    }

    @Override
    @ReadOnly
    @Deprecated
    public SysWareBatchCountVo groupCount(SysWareOutBatchProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone)); // 默认东海岸的今天
        }

        LambdaQueryWrapper<SysWareOutBatchProd> qw = buildQw(dto, new HashMap<>());
        qw.isNotNull(SysWareOutBatchProd::getBatchId);

        return baseMapper.groupCount(qw);
    }

    @Override
    public List<SysWareOutBatchProd> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @ReadOnly
    @Override
    public IPage<SysWareOutBatchListVo> searchGroupNew(SysWareOutBatchProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone)); // 默认东海岸的今天
        }

        List<SysWareOutBatchProdListVo> list = this.searchListNew(dto);

        if (ObjectUtils.isEmpty(list)){
            return new Page<>(dto.getCurrent(),dto.getSize());
        }

        // 按照批次号分组
        Map<String, List<SysWareOutBatchProdListVo>> sysWareOutBatchProdListVoGroupBy = list.stream()
                .filter(vo -> !ObjectUtils.isEmpty(vo) && !ObjectUtils.isEmpty(vo.getOutBatchNo()))
                .collect(Collectors.groupingBy(SysWareOutBatchProdListVo::getOutBatchNo, Collectors.toList()));

        List<SysWareOutBatchListVo> groupList = new ArrayList<>();

        for (String batchNo : sysWareOutBatchProdListVoGroupBy.keySet()) {
            if (ObjectUtils.isEmpty(batchNo)) {
                continue;
            }

            List<SysWareOutBatchProdListVo> itemList = sysWareOutBatchProdListVoGroupBy.get(batchNo);
            // 按照gmtOut时间倒序排列
            itemList.sort(Comparator.comparing(SysWareOutBatchProdListVo::getGmtOut).reversed());

            SysWareOutBatchProdListVo sysWareOutBatchProdListVo  = new SysWareOutBatchProdListVo();
            if (!ObjectUtils.isEmpty(itemList)) {
                sysWareOutBatchProdListVo = itemList.get(0);
            }

            SysWareOutBatchListVo vo = new SysWareOutBatchListVo();
            BeanUtils.copyProperties(sysWareOutBatchProdListVo, vo);
            vo.setBatchNo(batchNo);
            vo.setOutType(sysWareOutBatchProdListVo.getOutType());
            vo.setProdList(itemList);
            groupList.add(vo);
        }

        // 按照出库时间倒序排列groupList
        groupList.sort(Comparator.comparing(SysWareOutBatchListVo::getGmtOut).reversed());

        IPage<SysWareOutBatchListVo> voResult = new Page<>(dto.getCurrent(),dto.getSize());
        voResult.setRecords(groupList);
        voResult.setTotal(baseMapper.selectWareBatchOutCount(dto).getNum());
        return voResult;
    }

    @ReadOnly
    @Override
    @TrimParam
    public List<SysWareOutBatchProdListVo> searchListNew(SysWareOutBatchProdPageDto dto) {
        Assert.notNull(dto,"system error : dto is null");
        Assert.notNull(dto.getSize(),"system error : dto size is null");
        Assert.notNull(dto.getCurrent(),"system error : dto current is null");

        // 商家权限控制
        if (JwtContentHolder.getRoleType() == 5) {
            dto.setShopId(JwtContentHolder.getUserId());
        }

        return baseMapper.selectWareBatchOutList(dto);
    }

    @ReadOnly
    @Override
//    @Cacheable(value = "ware_out_batch_count", key = "#root.target.generateWareOutBatchCacheKey(#dto, #timezone)", unless = "#result == null")
    public SysWareBatchCountVo groupCountNew(SysWareOutBatchProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone));
        }
        return baseMapper.selectWareBatchOutCount(dto);
    }

    @ReadOnly
    @Override
    public List<String> selectWareBatchOutToExportCsvData(SysWareOutBatchProdPageDto dto, String language) {
        return baseMapper.selectWareBatchOutToExportCsvData(dto, language);
    }

    /**
     * 生成出库批次统计缓存键
     */
    public String generateWareOutBatchCacheKey(SysWareOutBatchProdPageDto dto, String timezone) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("ware_out_batch_count:");
        keyBuilder.append("timezone:").append(timezone != null ? timezone : "").append(":");
        keyBuilder.append("beginTime:").append(dto.getBeginTime() != null ? dto.getBeginTime().getTime() : "").append(":");
        keyBuilder.append("endTime:").append(dto.getEndTime() != null ? dto.getEndTime().getTime() : "").append(":");
        keyBuilder.append("beginInTime:").append(dto.getBeginInTime() != null ? dto.getBeginInTime().getTime() : "").append(":");
        keyBuilder.append("endInTime:").append(dto.getEndInTime() != null ? dto.getEndInTime().getTime() : "").append(":");
        keyBuilder.append("wareId:").append(dto.getWareId() != null ? dto.getWareId() : "").append(":");
        keyBuilder.append("wareIdList:").append(dto.getWareIdList() != null ? dto.getWareIdList().toString() : "").append(":");
        keyBuilder.append("shopId:").append(dto.getShopId() != null ? dto.getShopId() : "").append(":");
        keyBuilder.append("shopUid:").append(dto.getShopUid() != null ? dto.getShopUid() : "").append(":");
        keyBuilder.append("outType:").append(dto.getOutType() != null ? dto.getOutType() : "").append(":");
        keyBuilder.append("batchId:").append(dto.getBatchId() != null ? dto.getBatchId() : "").append(":");
        keyBuilder.append("outBatchNo:").append(dto.getOutBatchNo() != null ? dto.getOutBatchNo() : "").append(":");
        keyBuilder.append("oddNo:").append(dto.getOddNo() != null ? dto.getOddNo() : "").append(":");
        keyBuilder.append("platName:").append(dto.getPlatName() != null ? dto.getPlatName() : "").append(":");
        keyBuilder.append("checker:").append(dto.getChecker() != null ? dto.getChecker() : "").append(":");
        keyBuilder.append("spec:").append(dto.getSpec() != null ? dto.getSpec() : "").append(":");
        keyBuilder.append("specList:").append(dto.getSpecList() != null ? dto.getSpecList().toString() : "").append(":");
        keyBuilder.append("sku:").append(dto.getSku() != null ? dto.getSku() : "").append(":");
        keyBuilder.append("skuList:").append(dto.getSkuList() != null ? dto.getSkuList().toString() : "").append(":");
        keyBuilder.append("oneId:").append(dto.getOneId() != null ? dto.getOneId() : "").append(":");
        keyBuilder.append("oneIdList:").append(dto.getOneIdList() != null ? dto.getOneIdList().toString() : "").append(":");
        keyBuilder.append("brand:").append(dto.getBrand() != null ? dto.getBrand() : "").append(":");
        keyBuilder.append("remarks:").append(dto.getRemarks() != null ? dto.getRemarks() : "");
        return keyBuilder.toString();
    }

}
