package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysWareUser;
import com.hzjm.service.model.DTO.SysWareUserPageDto;
import com.hzjm.service.model.VO.SysWareUserListVo;
import com.hzjm.service.model.VO.SysWareUserVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 仓库人员 服务类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
public interface ISysWareUserService extends IService<SysWareUser> {

    SysWareUser getByIdWithoutLogic(Integer id);

    SysWareUserVo getDetail(Integer id);

    Boolean saveSysWareUser(SysWareUser dto);

    Boolean insertList(List<SysWareUser> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysWareUserListVo> searchList(SysWareUserPageDto dto);

    List<SysWareUser> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
