package com.hzjm.service.service;

import java.util.List;
import com.hzjm.service.entity.SysParamSet;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.model.VO.PlatDefaultPriceVo;

/**
 * 系统参数设置 服务类
 *
 * <AUTHOR>
 * @since 2021-05-24
 */
public interface ISysParamSetService extends IService<SysParamSet> {

    SysParamSet getByIdWithoutLogic(Integer id);

    SysParamSet getDetail(Integer id);

    Boolean saveSysParamSet(SysParamSet dto);

    Boolean insertList(List<SysParamSet> dataList);

    String getValue(String keyName);

    PlatDefaultPriceVo defaultPrice(Integer type);

}
