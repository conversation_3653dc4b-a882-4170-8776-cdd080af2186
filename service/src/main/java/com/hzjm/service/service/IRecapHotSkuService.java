package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.RecapHotSku;
import com.hzjm.service.model.VO.PlatformOrderVo;

import java.time.Year;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/17 18:03
 * @description: 商家年度统计报表-热门商品
 */
public interface IRecapHotSkuService extends IService<RecapHotSku> {
    /**
     * 批量保存热门商品
     *
     * @param platformOrderVoList 热门商品列表
     * @param year                年月份
     * @return 是否保存成功
     */
    boolean batchSaveSortSku(List<PlatformOrderVo> platformOrderVoList, Year year);
}
