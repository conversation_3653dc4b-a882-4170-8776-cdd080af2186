package com.hzjm.service.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.service.entity.RecapMonthly;
import com.hzjm.service.mapper.RecapMonthlyMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/17 14:30
 * @description: RecapMonthly服务保存实现类
 */
@Slf4j
@Service
public class RecapMonthlySaveServiceImpl extends ServiceImpl<RecapMonthlyMapper, RecapMonthly> implements IRecapMonthlySaveService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    @AcquireTaskLock(name = "saveRecapMonthly", timeout = 30)
    public Boolean saveBatchCustomList(List<RecapMonthly> recapMonthlyList) {
        log.info("插入 商家月度统计数据 条数：{}", recapMonthlyList.size());
        if (CollUtil.isEmpty(recapMonthlyList)) {
            log.error("商家月度统计数据为空");
            return true;
        }
        //shop_id +开始与结束日期 唯一约束
        boolean flag = saveBatch(recapMonthlyList);
        log.info("生成商家月度统计成功,总插入条数: {}", recapMonthlyList.size());
        return flag;
    }
}
