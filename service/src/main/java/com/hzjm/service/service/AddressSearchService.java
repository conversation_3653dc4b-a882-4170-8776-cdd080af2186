package com.hzjm.service.service;

import com.google.maps.FindPlaceFromTextRequest;
import com.google.maps.PlaceAutocompleteRequest;
import com.google.maps.PlacesApi;
import com.google.maps.errors.ApiException;
import com.google.maps.model.*;
import com.hzjm.common.annotation.TrimParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

@Service
@Slf4j
public class AddressSearchService {


    @Resource
    private com.google.maps.GeoApiContext geoApiContext;

    /**
     * 根据传入的地址文本进行精确查找
     *
     * @param queryAddress 地址文本，例如 "15073 Gran, Fontana, CA"
     * @return 候选地址列表（格式化地址字符串）
     */
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:google:maps:findPlace:' + #queryAddress")
    @TrimParam
    public Set<String> findPlace(String queryAddress) {
        log.info("AddressSearchService findPlace start queryAddress = {}", queryAddress);
        Set<String> resultList = new HashSet<>();
        if (ObjectUtils.isEmpty(queryAddress)) {
            return resultList;
        }
        try {
            // 调用 Find Place API，进行地址精确查找
            FindPlaceFromText findPlaceFromText = PlacesApi.findPlaceFromText(geoApiContext, queryAddress, FindPlaceFromTextRequest.InputType.TEXT_QUERY)
                    .language("en")
                    .await();
            log.info("AddressSearchService findPlace response !");
            log.info(findPlaceFromText.toString());
            if (findPlaceFromText.candidates != null) {
                for (PlacesSearchResult result : findPlaceFromText.candidates) {
                    resultList.add(result.formattedAddress);
                }
            }
        } catch (ApiException | InterruptedException | IOException e) {
            // 建议使用日志记录错误信息，方便后续调试
            e.printStackTrace();
            log.error("AddressSearchService findPlace error! ");
            log.error(String.valueOf(e));
            log.error(e.getMessage());
        }
        log.info("AddressSearchService findPlace end ");
        return resultList;
    }


    /**
     * 根据传入的地址文本进行模糊搜索
     *
     * @param queryAddress 地址文本，例如 "15073 Gran, Fontana, CA"
     * @return 候选地址列表（格式化地址字符串）
     */
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:google:maps:'+#queryAddress")
    @TrimParam
    public Set<String> searchAddress(String queryAddress) {
        log.info("AddressSearchService searchAddress start queryAddress = {}", queryAddress);
        Set<String> resultList = new HashSet<>();
        if (ObjectUtils.isEmpty(queryAddress)) {
            return resultList;
        }
        try {
            // 调用 Text Search API，进行地址模糊查询
            PlacesSearchResponse response = PlacesApi.textSearchQuery(geoApiContext, queryAddress)
                    .region("us")
                    .language("en")
                    .await();
            log.info("AddressSearchService searchAddress  response !");
            log.info(response.toString());
            if (response.results != null) {
                for (PlacesSearchResult result : response.results) {
                    resultList.add(result.formattedAddress);
                }
            }
        } catch (ApiException | InterruptedException | IOException e) {
            // 建议使用日志记录错误信息，方便后续调试
            e.printStackTrace();
            log.error("AddressSearchService searchAddress error! ");
            log.error(String.valueOf(e));
            log.error(e.getMessage());
        }
        log.info("AddressSearchService searchAddress end ");
        return resultList;
    }

    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:google:maps:autocomplete:' + #queryAddress")
    @TrimParam
    public Set<String> autocompleteAddress(String queryAddress) {
        log.info("AddressSearchService autocompleteAddress start queryAddress = {}", queryAddress);
        Set<String> resultList = new HashSet<>();
        if (queryAddress == null || queryAddress.isEmpty()) {
            return resultList;
        }
        try {
            // 创建会话令牌
            PlaceAutocompleteRequest.SessionToken sessionToken = new PlaceAutocompleteRequest.SessionToken();

            // 调用 Place Autocomplete API，进行地址自动补全查询
            AutocompletePrediction[] predictions = PlacesApi.placeAutocomplete(geoApiContext, queryAddress, sessionToken)
                    .components(ComponentFilter.country("us"))
                    .language("en")
                    .await();

            if (predictions != null) {
                for (AutocompletePrediction prediction : predictions) {
                    resultList.add(prediction.description);
                }
            }

        } catch (ApiException | InterruptedException | IOException e) {
            e.printStackTrace();
            log.error("AddressSearchService autocompleteAddress error! ");
            log.error(String.valueOf(e));
            log.error(e.getMessage());
        }
        log.info("AddressSearchService autocompleteAddress end");
        return resultList;
    }

    /**
     * 根据地址获取邮编
     *
     * @param address 地址文本
     * @return 邮编，如果未找到则返回null
     */
    @Cacheable(cacheNames = "ThirtyMinutesBiDataConfig", key = "'knet:google:maps:postalcode:' + #address")
    @TrimParam
    public String getPostalCode(String address) {
        log.info("AddressSearchService getPostalCode start address = {}", address);
        if (ObjectUtils.isEmpty(address)) {
            return null;
        }
        try {
            PlaceAutocompleteRequest.SessionToken sessionToken = new PlaceAutocompleteRequest.SessionToken();

            // 先通过自动补全获取placeId
            AutocompletePrediction[] predictions = PlacesApi.placeAutocomplete(geoApiContext, address, sessionToken)
                    .components(ComponentFilter.country("us"))
                    .language("en")
                    .await();

            if (predictions != null && predictions.length > 0) {
                // 获取第一个匹配结果的详细信息
                PlaceDetails placeDetails = PlacesApi.placeDetails(geoApiContext, predictions[0].placeId)
                        .sessionToken(sessionToken)
                        .await();

                // 从地址详细信息中提取邮编
                if (placeDetails.addressComponents != null) {
                    for (AddressComponent component : placeDetails.addressComponents) {
                        if (component.types != null && component.types.length > 0
                            && component.types[0] == AddressComponentType.POSTAL_CODE) {
                            return component.longName;
                        }
                    }
                }
            }
        } catch (ApiException | InterruptedException | IOException e) {
            log.error("AddressSearchService getPostalCode error! ", e);
        }
        log.info("AddressSearchService getPostalCode end");
        return "";
    }

}
