package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.ShopUserTouch;
import com.hzjm.service.model.DTO.ShopUserTouchPageDto;
import com.hzjm.service.model.VO.ShopUserTouchListVo;
import com.hzjm.service.model.VO.ShopUserTouchVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商家的touch账号 服务类
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
public interface IShopUserTouchService extends IService<ShopUserTouch> {

    ShopUserTouch getByIdWithoutLogic(Integer id);

    ShopUserTouchVo getDetail(Integer id, Integer shopId);

    Boolean saveShopUserTouch(ShopUserTouch dto);

    Boolean insertList(List<ShopUserTouch> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<ShopUserTouchListVo> searchList(ShopUserTouchPageDto dto);

    List<ShopUserTouch> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopUserTouch> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
