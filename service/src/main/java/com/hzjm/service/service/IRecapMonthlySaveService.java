package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.RecapMonthly;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/11 14:27
 * @description: RecapMonthly服务类
 */
public interface IRecapMonthlySaveService extends IService<RecapMonthly> {

    /**
     * 批量自定义保存数据
     *
     * @param recapMonthlyList 保存数据
     * @return 保存结果
     */
    Boolean saveBatchCustomList(List<RecapMonthly> recapMonthlyList);
}
