package com.hzjm.service.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysRepairBatchMapper;
import com.hzjm.service.model.DTO.RepairProcessDto;
import com.hzjm.service.model.DTO.SysRepairBatchPageDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维修批次表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-23
 */
@Slf4j
@Service
public class SysRepairBatchServiceImpl extends ServiceImpl<SysRepairBatchMapper, SysRepairBatch> implements ISysRepairBatchService {

    @Resource
    private ISysRepairOrderService sysRepairOrderService;

    @Resource
    private ISysCodePoolService iSysCodePoolService;

    @Resource
    private ISysWareService sysWareService;

    @Resource
    private ISysBillService iSysBillService;

    @Override
    public SysRepairBatch getByIdWithoutLogic(Integer id) {
        SysRepairBatch data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该维修批次表");
        }

        return data;
    }

    /**
     * 根据ID查询维修批次表详情
     */
    @Override
    public SysRepairBatchVo getDetail(Integer id,Integer shopId) {
        SysRepairBatch data = getByIdWithoutLogic(id);

        // 通过维系批次查询维修单详情
        List<SysRepairOrderVo> repairOrderList = sysRepairOrderService.getDetail(data.getRepairNo(),shopId);

        SysRepairBatchVo vo = new SysRepairBatchVo();
        vo.setRepairOrderList(repairOrderList.stream().distinct().collect(Collectors.toList()));

        return vo;
    }

    /**
     * 根据批次号查询维修单列表
     */
    @Override
    public SysRepairBatchVo getDetail(String batchNo,  Integer shopId) {
        List<SysRepairOrderVo> repairOrderList = sysRepairOrderService.getDetail(batchNo,shopId);

        SysRepairBatchVo vo = new SysRepairBatchVo();
        vo.setRepairOrderList(repairOrderList);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @TrimParam
    public Boolean saveSysRepairBatch(SysRepairBatch dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            Assert.notNull(dto,  "新增维修批次，参数不能为空");
            Assert.notEmpty(dto.getRepairOrderId(), "维修单ID不能为空");
            Assert.notNull(dto.getUserId(), "操作人ID不能为空");
            // 新增维修批次，需要所有的维修单都来自同一个仓库
            LambdaQueryWrapper<SysRepairOrder> orderIdWrapper = new LambdaQueryWrapper<>();
            orderIdWrapper.in(SysRepairOrder::getId, dto.getRepairOrderId());
            List<SysRepairOrder> orderList = sysRepairOrderService.list(orderIdWrapper);
            Assert.notEmpty(orderList, "维修单查询失败");
            Assert.isTrue(orderList.size() == dto.getRepairOrderId().size(),"存在查询不到的维修单");
            // 判断仓库不能存在空的
            orderList.forEach(e -> {
                Assert.notNull(e.getWareId(), "维修单的仓库不能为空");
            });

            Assert.isTrue(orderList.stream().map(SysRepairOrder::getWareId).distinct().count() == 1, "新增维修批次，需要所有的维修单都来自同一个仓库");
            // 维修批次号
            String repairNo = iSysCodePoolService.build(29, 1).get(0);

            // 新增维修批次，需要更新维修单的批次号
            LambdaUpdateWrapper<SysRepairOrder> sysRepairOrderLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            sysRepairOrderLambdaUpdateWrapper.in(SysRepairOrder::getId, dto.getRepairOrderId());
            sysRepairOrderLambdaUpdateWrapper.set(SysRepairOrder::getRepairBatchNo, repairNo);
            sysRepairOrderService.update(sysRepairOrderLambdaUpdateWrapper);
            // 新增维修批次
            dto.setRepairNo(repairNo)
                    .setWareId(orderList.get(0).getWareId())
                    .setNumber(orderList.size())
                    .setBatchStatus(SysRepairBatchStatustEnum.PENDING)
                    .setUserId(dto.getUserId());
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            SysRepairBatch repairBatch = this.getByIdWithoutLogic(dto.getId());
            // 分配人
            if (dto.getBatchStatus() == SysRepairBatchStatustEnum.ALLOCATED
                    && !ObjectUtils.isEmpty(dto.getRepairStaffId())
                    && dto.getRepairStaffId() != 0){

                // 修改维修单的维修状态
                sysRepairOrderService.lambdaUpdate()
                        .set(SysRepairOrder::getRepairStatus, RepairStatusEnum.REPAIR_PENDING)
                        .set(SysRepairOrder::getRepairStaffId, dto.getRepairStaffId())

                        .eq(SysRepairOrder::getRepairBatchNo, repairBatch.getRepairNo())
                        .update();
            }
            // 撤销分配人
            if (dto.getBatchStatus() == SysRepairBatchStatustEnum.PENDING
                    && !ObjectUtils.isEmpty(dto.getRepairStaffId())
                    && dto.getRepairStaffId() == 0
                    ) {
                return this.lambdaUpdate()
                    .set(SysRepairBatch::getRepairStaffId, null)
                    .set(SysRepairBatch::getBatchStatus, SysRepairBatchStatustEnum.PENDING)
                    .eq(SysRepairBatch::getId, dto.getId())
                    .update();
            }
            // 维修完成，批量处理需要退款的维修单
            if (dto.getBatchStatus() == SysRepairBatchStatustEnum.COMPLETED) {
                // 查询包裹丢失和维修失败的进行退款
                LambdaUpdateWrapper<SysRepairOrder> sysLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
                sysLambdaUpdateWrapper.eq(SysRepairOrder::getRepairBatchNo, repairBatch.getRepairNo());
                sysLambdaUpdateWrapper.in(SysRepairOrder::getRepairStatus, Arrays.asList(RepairStatusEnum.REPAIR_LOST_PARCEL, RepairStatusEnum.REPAIR_FAILURE));
                List<SysRepairOrder> sysRepairOrderList = sysRepairOrderService.list(sysLambdaUpdateWrapper);
                // 调用新的方法签名
                List<RepairProcessDto> repairProcessDtoList = new ArrayList<>();

                Map<Integer, BigDecimal> feeMap = sysRepairOrderService.getRepairOrderFee(
                        sysRepairOrderList.stream().map(SysRepairOrder::getId).collect(Collectors.toList()));

                log.info("SysRepairBatchServiceImpl saveSysRepairBatch sysRepairOrderList: {}",
                        JSON.toJSONString(sysRepairOrderList.stream().map(SysRepairOrder::getId).collect(Collectors.toList())));

                for (SysRepairOrder sysRepairOrder : sysRepairOrderList) {
                    BigDecimal totalFee = feeMap.get(sysRepairOrder.getId());

                    // 退款 - 创建 RepairProcessDto 对象
                    RepairProcessDto repairProcessDto = new RepairProcessDto();
                    repairProcessDto.setTotalFee(totalFee);
                    repairProcessDto.setShopId(sysRepairOrder.getShopId());
                    repairProcessDto.setRepairId(sysRepairOrder.getId());
                    repairProcessDto.setRepairNo(sysRepairOrder.getRepairNo());
                    repairProcessDto.setRemark(sysRepairOrder.getRepairNo() + "维修失败返还");
                    repairProcessDto.setOperationType(SysBill.TypeShopDrawRepairRefund); // 使用退款类型

                    repairProcessDtoList.add(repairProcessDto);
                }
                log.info("repair batch refund process start, repair repairProcessDtoList: {}", JSON.toJSONString(repairProcessDtoList));
                iSysBillService.processRepairRecords(repairProcessDtoList);
            }

            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    private LambdaQueryWrapper<SysRepairBatch> buildQueryWrapper(SysRepairBatchPageDto dto) {

        LambdaQueryWrapper<SysRepairBatch> qw = Wrappers.<SysRepairBatch>lambdaQuery();

        List<SysRepairOrder> sysRepairOrderList = null;
        List<String> repairNoList = new ArrayList<>();
        // oneID数组 维修单号数组
        if (!ObjectUtils.isEmpty(dto.getOneIdList())
                || !ObjectUtils.isEmpty(dto.getRepairNoList())) {
            sysRepairOrderList = sysRepairOrderService.list(Wrappers.<SysRepairOrder>lambdaQuery()
                    .in(!ObjectUtils.isEmpty(dto.getOneIdList()), SysRepairOrder::getOneId, dto.getOneIdList())
                    .in(!ObjectUtils.isEmpty(dto.getRepairNoList()), SysRepairOrder::getRepairNo, dto.getRepairNoList())
            );

            if  (!ObjectUtils.isEmpty(sysRepairOrderList)) {
                repairNoList = sysRepairOrderList.stream().map(SysRepairOrder::getRepairBatchNo).filter(repairNo -> !ObjectUtils.isEmpty(repairNo)).distinct().collect(Collectors.toList());
            }else {
                repairNoList.add("1");
            }
        }

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysRepairBatch::getGmtCreate)
                .eq(!ObjectUtils.isEmpty(dto.getRepairStaffId() ), SysRepairBatch::getRepairStaffId, dto.getRepairStaffId())
                .eq(!ObjectUtils.isEmpty(dto.getBatchStatus() ), SysRepairBatch::getBatchStatus, dto.getBatchStatus())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysRepairBatch::getGmtCreate, dto.getBeginTime())
                .eq(!ObjectUtils.isEmpty(dto.getSearchValue()), SysRepairBatch::getRepairNo, dto.getSearchValue())
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysRepairBatch::getWareId, dto.getWareIdList())
                .in(!ObjectUtils.isEmpty(repairNoList), SysRepairBatch::getRepairNo, repairNoList)
                .lt(!ObjectUtils.isEmpty(endTime), SysRepairBatch::getGmtCreate, endTime);

        return qw;
    }


    @Override
    @TrimParam
    @ReadOnly
    public IPage<SysRepairBatchListVo> searchList(SysRepairBatchPageDto dto) {

        LambdaQueryWrapper<SysRepairBatch> qw = this.buildQueryWrapper(dto);

        IPage<SysRepairBatch> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        if (ObjectUtils.isEmpty(pageResult.getRecords())) {
            return new Page<>();
        }

        List<SysRepairBatchListVo> voList = new ArrayList<>();

        List<Integer> wareIdList = pageResult.getRecords().stream().map(SysRepairBatch::getWareId).distinct().collect(Collectors.toList());
        List<SysWare> wareList = sysWareService.list(Wrappers.<SysWare>lambdaQuery().in(SysWare::getId, wareIdList));
        Map<Integer, String> wareMap = wareList.stream()
                .collect(Collectors.toMap(SysWare::getId, SysWare::getName, (key1, key2) -> key2));

        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysRepairBatchListVo vo = new SysRepairBatchListVo();
                BeanUtils.copyProperties(data, vo);
                vo.setWareName(wareMap.get(data.getWareId()));

                voList.add(vo);
            });
        }

        IPage<SysRepairBatchListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysRepairBatch> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysRepairBatch> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysRepairBatch> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }



    @Override
    @TrimParam
    @ReadOnly
    public SysRepairBatchCountVo searchListCount(SysRepairBatchPageDto dto) {
        dto.setBatchStatus(null);
        SysRepairBatchCountVo sysRepairBatchCountVo = new SysRepairBatchCountVo(0,0,0,0);

        List<SysRepairBatchListVo>  sysRepairBatches = this.searchList(dto).getRecords();

        if(ObjectUtils.isEmpty(sysRepairBatches)) {
            return sysRepairBatchCountVo;
        }

        sysRepairBatchCountVo.setPendingNum(sysRepairBatches.stream().filter(e -> e.getBatchStatus() == SysRepairBatchStatustEnum.PENDING).count());
        sysRepairBatchCountVo.setAllocatedNum(sysRepairBatches.stream().filter(e -> e.getBatchStatus() == SysRepairBatchStatustEnum.ALLOCATED).count());
        sysRepairBatchCountVo.setCompletedNum(sysRepairBatches.stream().filter(e -> e.getBatchStatus() == SysRepairBatchStatustEnum.COMPLETED).count());

        return sysRepairBatchCountVo;
    }
}
