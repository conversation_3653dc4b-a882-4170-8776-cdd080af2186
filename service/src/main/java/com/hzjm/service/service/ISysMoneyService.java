package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysMoney;
import com.hzjm.service.model.DTO.SysMoneyPageDto;
import com.hzjm.service.model.DTO.SysMoneySaveDto;
import com.hzjm.service.model.VO.SysMoneyListVo;
import com.hzjm.service.model.VO.SysMoneyVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 用户钱包 服务类
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
public interface ISysMoneyService extends IService<SysMoney> {

    SysMoney getByIdWithoutLogic(Integer id);

    SysMoneyVo getDetail(Integer id);

    Boolean saveSysMoneyMany(SysMoney dto);

    Boolean saveSysMoney(SysMoney dto);

    Boolean insertList(List<SysMoney> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysMoneyListVo> searchList(SysMoneyPageDto dto);

    SysMoneyVo getContBalance(Integer shopId);

    Map<String, List<SysMoneyVo>> getMyMap();

    List<SysMoney> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysMoney> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean change(int type, Integer userId, BigDecimal amount);

    SysMoneyVo getMy();

    /**
     * 检查条件
     *
     * @param dto dto
     */
    void checkCondition(SysMoneySaveDto dto);

}
