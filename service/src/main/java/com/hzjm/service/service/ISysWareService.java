package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

import com.hzjm.service.entity.SysProdSale;
import com.hzjm.service.entity.SysWare;
import com.hzjm.service.model.DTO.SysWarePageDto;
import com.hzjm.service.model.VO.SysWareAllListVo;
import com.hzjm.service.model.VO.SysWareListVo;
import com.hzjm.service.model.VO.SysWareVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 仓库 服务类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
public interface ISysWareService extends IService<SysWare> {

    SysWare getByIdWithoutLogic(Integer id);

    SysWareVo getDetail(Integer id);

    Boolean saveSysWare(SysWare dto);

    Boolean insertList(List<SysWare> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWareListVo> searchList(SysWarePageDto dto);

    List<SysWare> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWare> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<SysWareAllListVo> listAll(SysWarePageDto dto);

    Boolean isPremiumWare(Integer wareId);

}
