package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopUserAddress;
import com.hzjm.service.model.DTO.ShopUserAddressPageDto;
import com.hzjm.service.model.VO.ShopUserAddressListVo;
import com.hzjm.service.model.VO.ShopUserAddressVo;

import java.util.List;
import java.util.Map;

/**
 * 商家地址 服务类
 *
 * <AUTHOR>
 * @since 2023-04-10
 */
public interface IShopUserAddressService extends IService<ShopUserAddress> {

    ShopUserAddress getByIdWithoutLogic(Integer id);

    ShopUserAddressVo getDetail(Integer id);

    Boolean saveShopUserAddress(ShopUserAddress dto);

    Boolean insertList(List<ShopUserAddress> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopUserAddressListVo> searchList(ShopUserAddressPageDto dto);

    List<ShopUserAddress> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopUserAddress> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Map<String,String> queryShopUserAddress();

    /**
     * 根据收件人姓名查询身份证号
     * @param recipientName 收件人姓名
     * @return 身份证号，如果未找到则返回null
     */
    String queryIdCardByRecipientName(String recipientName);
}
