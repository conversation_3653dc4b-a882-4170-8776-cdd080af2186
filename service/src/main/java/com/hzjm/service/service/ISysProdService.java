package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.service.entity.ExtKnetProductListing;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.entity.SysProdUpdateRepairFlagDto;
import com.hzjm.service.model.DTO.*;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.model.enums.SysUploadRecordUserTypeEnum;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品信息 服务类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface ISysProdService extends IService<SysProd> {

    SysProd getByIdWithoutLogic(Integer id);

    SysProdVo getDetail(Integer id, String oneId);

    Boolean saveSysProd(SysProd sysProd);

    @ReadOnly
    List<SysProdListVo> getProductGroupBySku(SysProdPageDto dto);

    @ReadOnly
    List<SysProdListVo> getProductGroupBySkuCount(SysProdPageDto dto);

    @ReadOnly
    SysProdCountV2Vo getCountV2(SysProdPageDto dto);

    Boolean insertList(List<SysProd> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProdListVo> searchList(SysProdPageDto dto);

    List<SysProd> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    IPage<SysProdListVo> offList(PageBaseSearchDto dto);

    IPage<SysProdListVo> onList(SysWareShelvesProdPageDto dto);

    SysProdCountVo getCount(SysProdPageDto dto);

    IPage<SysProdListVo> searchListGroupBySkuAndSize(SysProdPageDto dto);

    Long searchListCount(SysProdPageDto dto);

    List<String> updateCostPrice(List<UpdateSysProdCostPriceDTO> dto);

    /**
     *
     * @param dto
     * @param groupType 1-sku分类，2-货架分类
     * @return
     */
    IPage<SysProdGroupListVo> searchGroup(SysProdPageDto dto, Integer groupType);

    void batchDealCheck(SysProdDealDto dto);

    String batchDeal(SysProdDealDto dto);

    Boolean release(List<Integer> idList, Integer type);

    Boolean pay(Integer id, Integer type);

    Boolean endEvent(Integer id, Integer type);

    /**
     *
     * @param sortType 1-销量排名，2-热度排名，3-库存排名
     * @param shopId
     */
    List<SysSkuRankListVo> rankList(int sortType, Integer shopId);

    BigDecimal sumCost(SysProdPageDto dto);

    void buildSearch();

    List<String> fixProd(List<String> oneIdList);

    Boolean fixProd2(List<String> oneIdList);

    Boolean fixProd3(List<String> oneIdList);

    List<SysProd> getDistinctSkuAndSpec();

    int updateKnetProductListingForCost(ExtKnetProductListing extKnetProductListing);

    boolean consignedInPremiumWare(String oneId);

    boolean releaseSysProd(String oneId);

    List<String> querySysProdLock(List<String> oneIdList);

    String batchDealFile(MultipartFile xlsxFile
            , MultipartFile archiveFile
            , Integer userId
            , SysUploadRecordUserTypeEnum userType) throws IOException;

    /**
     * 按品名分类获取商品列表(分页)
     * @param dto 查询条件
     * @return 分页结果
     */
    IPage<SysProdListVo> getProductGroupBySkuPage(SysProdPageDto dto);

    @Transactional(rollbackFor = Exception.class)
    void changeOwner(List<SysProdSearch> sysProdSearchList);

    void updateRepairFlag(SysProdUpdateRepairFlagDto dto);

    /**
     *  获取所有在售的商品种类分类
     * @return
     */
    List<String> getCrossListingSkuCategories();
}
