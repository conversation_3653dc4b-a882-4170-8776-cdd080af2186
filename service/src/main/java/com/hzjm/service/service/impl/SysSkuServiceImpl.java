package com.hzjm.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysSkuMapper;
import com.hzjm.service.model.DTO.SysSkuPageDto;
import com.hzjm.service.model.VO.SysSkuListVo;
import com.hzjm.service.model.VO.SysSkuRankListVo;
import com.hzjm.service.model.VO.SysSkuVo;
import com.hzjm.service.service.*;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.KG_SYS_SKU_CACHE_KEY_PREFIX;
import static com.hzjm.service.constants.ServiceConstants.KG_SYS_SKU_EXPIRED_TIME;

/**
 * sku池 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Slf4j
@Service
public class SysSkuServiceImpl extends ServiceImpl<SysSkuMapper, SysSku> implements ISysSkuService {

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysSkuService iSysSkuService;

    @Autowired
    private TouchUtils touchUtils;
    /**
     * sku更新批处理批次
     */
    public static final int GET_GOAT_API_BATCH_SIZE = 50;

    @Resource
    public RedisTemplate<String, String> redisTemplate;

    @Override
    public SysSku getByIdWithoutLogic(Integer id) {
        SysSku data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该sku池"));
        }

        return data;
    }

    @Override
    public SysSkuVo getDetail(Integer id) {
        SysSku data = getByIdWithoutLogic(id);

        SysSkuVo vo = new SysSkuVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    /**
     * sku 转换
     */
    @Override
    public List<String> skuConvertSkuList(String sku) {
        List<String> searechList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(sku)) {
            searechList.add(sku.trim());

            // 判断 dto.getSearch() 语句中间是否存在空格
            if (sku.trim().contains(" ")) {
                // 把中间空格转成符号 -
                searechList.add(sku.trim().replaceAll(" ", "-"));
            } else if (sku.trim().contains("-")) {
                // 把中间空格转成符号 -
                searechList.add(sku.trim().replaceAll("-", " "));
            }
        }
        return searechList;
    }

    /**
     * 通过SKU查询详情，主要是获取图片和品名
     */
    @Override
    public SysSku getDetail(String sku) {
        List<String> searechList = this.skuConvertSkuList(sku);

        LambdaQueryWrapper<SysSku> sysSkuLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysSkuLambdaQueryWrapper.in(SysSku::getSku, searechList);
        sysSkuLambdaQueryWrapper.orderByDesc(SysSku::getGmtCreate);
        List<SysSku> sysSkuList = baseMapper.selectList(sysSkuLambdaQueryWrapper);

        for (SysSku sysSku : sysSkuList) {
            // 如果品名和图片都不存在，就结束本次循环，进入下次一次循环
            if (ObjectUtils.isEmpty(sysSku.getRemarks()) && ObjectUtils.isEmpty(sysSku.getImg())) {
                continue;
            }
            // 如果品名和图片都存在，就直接返回
            if (!ObjectUtils.isEmpty(sysSku.getRemarks()) && !ObjectUtils.isEmpty(sysSku.getImg())) {
                return sysSku;
            }
        }

        // 品名和图片没有同时存在，就从列表中组合拼接一个新的对象，进行返回
        SysSku sysSkuReturn = new SysSku();
        for (SysSku s : sysSkuList) {
            if (!ObjectUtils.isEmpty(sysSkuReturn.getRemarks()) && !ObjectUtils.isEmpty(sysSkuReturn.getImg())) {
                return sysSkuReturn;
            }
            if (!ObjectUtils.isEmpty(s.getRemarks())) {
                sysSkuReturn.setRemarks(s.getRemarks());
            }
            if (!ObjectUtils.isEmpty(s.getImg())) {
                sysSkuReturn.setImg(s.getImg());
            }
        }

        return sysSkuReturn;
    }

    @Override
    @Transactional
    public Boolean saveSysSku(SysSku dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();
        SysSku data = null;

        if (ObjectUtils.isEmpty(dto.getId())) {
            // 新增校验参数
            this.checkSysSku(dto, "insert");
            dto.setCreateById(JwtContentHolder.getUserId());

            rs = baseMapper.insert(dto) > 0;

            data = dto;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            data = getById(dto.getId());
            if (!ObjectUtils.isEmpty(dto.getPku()) && count(Wrappers.<SysSku>lambdaQuery()
                    .ne(SysSku::getId, dto.getId())
                    .eq(SysSku::getPku, dto.getPku())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("pku已存在"));
            }
//            dto.setPku(null); // pku不可修改
            this.checkSysSku(dto, "update");

            rs = baseMapper.updateById(dto) > 0;
        }

        if (!isDelete) {
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .select(SysProd::getId)
                    .eq(SysProd::getPku, data.getPku()));
            List<Integer> prodIdList = prodList.stream().map(SysProd::getId).collect(Collectors.toList());
            prodList.clear();

            if (!ObjectUtils.isEmpty(prodIdList)) {
                // 替换商品的pku信息
                iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                        .set(SysProd::getDelFlag, 0)
                        .set(!ObjectUtils.isEmpty(dto.getPku()), SysProd::getPku, dto.getPku())
                        // .set(!ObjectUtils.isEmpty(dto.getImg()), SysProd::getImg, dto.getImg())
                        // .set(!ObjectUtils.isEmpty(dto.getRemarks()), SysProd::getRemarks,
                        // dto.getRemarks())
                        .set(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                        .set(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec())
                        .eq(SysProd::getPku, data.getPku()));

                // 更新相同SKU商品的名字和图片
                if (!ObjectUtils.isEmpty(dto.getSku())
                        && !(ObjectUtils.isEmpty(dto.getImg())
                        && ObjectUtils.isEmpty(dto.getRemarks()))) {
                    iSysSkuService.update(Wrappers.<SysSku>lambdaUpdate()
                            .set(!ObjectUtils.isEmpty(dto.getImg()), SysSku::getImg, dto.getImg())
                            .set(!ObjectUtils.isEmpty(dto.getRemarks()), SysSku::getRemarks, dto.getRemarks())
                            .set(!ObjectUtils.isEmpty(dto.getBrand()), SysSku::getBrand, dto.getBrand())
                            .set(!ObjectUtils.isEmpty(dto.getGender()), SysSku::getGender, dto.getGender())
                            .set(!ObjectUtils.isEmpty(dto.getColor()), SysSku::getColor, dto.getColor())
                            .set(!ObjectUtils.isEmpty(dto.getProductType()), SysSku::getProductType, dto.getProductType())
                            .set(!ObjectUtils.isEmpty(dto.getProductCategory()), SysSku::getProductCategory, dto.getProductCategory())
                            .eq(SysSku::getSku, data.getSku()));

                    iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                            .set(!ObjectUtils.isEmpty(dto.getImg()), SysProd::getImg, dto.getImg())
                            .set(!ObjectUtils.isEmpty(dto.getRemarks()), SysProd::getRemarks, dto.getRemarks())
                            .eq(SysProd::getSku, data.getSku()));
                }
                // 更新相同SKU商品的名字和图片 - sys_prod_search 没有图片，只更新名字
                if (!ObjectUtils.isEmpty(dto.getSku()) && !ObjectUtils.isEmpty(dto.getRemarks())) {
                    iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                            .set(!ObjectUtils.isEmpty(dto.getRemarks()), SysProdSearch::getRemarks, dto.getRemarks())
                            .eq(SysProdSearch::getSku, dto.getSku()));
                }

                // 替换操作记录的pku信息
                iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                        .set(SysProdDeal::getDelFlag, 0)
                        .set(!ObjectUtils.isEmpty(dto.getSku()), SysProdDeal::getSku, dto.getSku())
                        .eq(SysProdDeal::getPku, data.getPku()));

                // search同步更新
                iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                        .set(SysProdSearch::getDelFlag, 0)
                        .set(!ObjectUtils.isEmpty(dto.getRemarks()), SysProdSearch::getRemarks, dto.getRemarks())
                        .set(!ObjectUtils.isEmpty(dto.getSku()), SysProdSearch::getSku, dto.getSku())
                        .set(!ObjectUtils.isEmpty(dto.getSpec()), SysProdSearch::getSpec,
                                BaseUtils.dealSizeStr(dto.getSpec()))
                        .in(SysProdSearch::getProdId, prodIdList)
                        .eq(SysProdSearch::getSearchType, 1));
            }
        }
        return rs;
    }

    @Override
    public IPage<SysSkuListVo> searchList(SysSkuPageDto dto) {
        List<String> searechList = new ArrayList<>();

        if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getSearch())) {
            if (dto.getSearch().contains("'")) {
                throw new BaseException("为保证搜索的准确性，请不要在关键字中使用单引号。");
            }

            searechList.add(dto.getSearch().trim());

            // 判断 dto.getSearch() 语句中间是否存在空格
            if (dto.getSearch().trim().contains(" ")) {
                // 把中间空格转成符号 -
                searechList.add(dto.getSearch().trim().replaceAll(" ", "-"));
            } else if (dto.getSearch().trim().contains("-")) {
                // 把中间空格转成符号 -
                searechList.add(dto.getSearch().trim().replaceAll("-", " "));
            }
        }

        LambdaQueryWrapper<SysSku> qw = Wrappers.<SysSku>lambdaQuery();

        if (dto.search.equals("NO_IMAGE_OR_NAME")) {
            qw.orderByDesc(SysSku::getGmtCreate)
                    .isNull(SysSku::getImg)
                    .or()
                    .isNull(SysSku::getRemarks);
        } else {
            Date endTime = dto.dealEndTime();
            qw.orderByDesc(SysSku::getGmtCreate)
                    .in(!ObjectUtils.isEmpty(dto.getIdList()), SysSku::getId, dto.getIdList())
                    .like(!ObjectUtils.isEmpty(dto.getPku()), SysSku::getPku, dto.getPku())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), SysSku::getSku, dto.getSku())
                    .like(!ObjectUtils.isEmpty(dto.getBrand()), SysSku::getBrand, dto.getBrand())
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysSku::getSpec, dto.getSpec())
                    .like(!ObjectUtils.isEmpty(dto.getRemarks()), SysSku::getRemarks, dto.getRemarks())
                    .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysSku::getGmtCreate, dto.getBeginTime())
                    .lt(!ObjectUtils.isEmpty(endTime), SysSku::getGmtCreate, endTime);

            if (!ObjectUtils.isEmpty(dto.getSpecList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSpecList().forEach(spec -> {
                    sb.append("(spec = '").append(spec).append("') or ");
                });
                qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            if (!ObjectUtils.isEmpty(dto.getSkuList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSkuList().forEach(sku -> {
                    sb.append("(sku = '").append(sku).append("') or ");
                });
                qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            // 判断 searechList 不为空，则模糊匹配 Sku 和 pku 和 brand 和 remarks 和 color
            if (!CollectionUtils.isEmpty(searechList)) {
                StringBuffer sb = new StringBuffer();
                searechList.forEach(search ->
                        sb.append("(sku like '%")
                                .append(search)
                                .append("%' or pku like '%")
                                .append(search)
                                .append("%' or brand like '%")
                                .append(search)
                                .append("%' or remarks like '%")
                                .append(search)
                                .append("%' or color like '%")
                                .append(search)
                                .append("%') or ")
                );
                qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

        }

        // log.info("sku searchList sql: {}", qw.getCustomSqlSegment());

        IPage<SysSku> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize())
                && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        if (!ObjectUtils.isEmpty(dto.getSearch())
                && !CollectionUtils.isEmpty(pageResult.getRecords())
                && !dto.search.equals("NO_IMAGE_OR_NAME")) {
            List<SysSku> sysSkuList = pageResult.getRecords();
            sysSkuList.sort(Comparator.comparingDouble(sku -> {
                if (ObjectUtils.isEmpty(sku.getSpec())) {
                    return Double.MIN_VALUE;
                }
                if (BaseUtils.isNumeric(sku.getSpec())) {
                    return Double.parseDouble(sku.getSpec());
                } else {
                    return sku.getSpec().chars().sum();
                }
            }));

            pageResult.setRecords(sysSkuList);
        }

        List<SysSkuListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery()
                    .isNotNull(SysUser::getNickname)
                    .in(SysUser::getId,
                            pageResult.getRecords().stream().map(SysSku::getCreateById).collect(Collectors.toList())));
            Map<Integer, String> userMap = userList.stream().filter(a -> !ObjectUtils.isEmpty(a.getNickname()))
                    .collect(Collectors.toMap(SysUser::getId, SysUser::getNickname));

            pageResult.getRecords().forEach(data -> {
                SysSkuListVo vo = new SysSkuListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setCreateBy(userMap.get(data.getCreateById()));
                voList.add(vo);
            });
        }

        IPage<SysSkuListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    @Transactional
    public Boolean insertList(List<SysSku> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        List<SysSku> addList = new ArrayList<>();
        List<String> pkuList = dataList.stream().map(SysSku::getPku).collect(Collectors.toList());
        if (pkuList.size() != pkuList.stream().distinct().collect(Collectors.toList()).size()) {
            throw new BaseException(LanguageConfigService.i18nForMsg("pku不可重复"));
        }

        List<SysSku> existList = list(Wrappers.<SysSku>lambdaQuery().in(SysSku::getPku, pkuList));
        Map<String, SysSku> existMap = existList.stream().collect(Collectors.toMap(SysSku::getPku, a -> a));
        Integer userId = JwtContentHolder.getUserId();

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            SysSku sku = existMap.get(data.getPku());
            if (ObjectUtils.isEmpty(sku)) {

                // 校验参数
                this.checkSysSku(data, "insert");

                // 新增
                data.setCreateById(userId);
                data.setGmtCreate(date);
                data.setGmtModify(date);

                data.setDelFlag(0);
                addList.add(data);
            } else {

                // 校验参数
                this.checkSysSku(data, "update");

                // 全量编辑
                data.setId(sku.getId());
                updateById(data);
            }
        });

        if (ObjectUtils.isEmpty(addList)) {
            return true;
        }

        int num = 1000;
        int total = addList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(addList.subList(begin, end));
        }

        return true;
    }

    void checkSysSku(SysSku sku, String type) {
        if (ObjectUtils.isEmpty(sku)) {
            log.error("SysSkuServiceImpl checkSysSku SysSku is null,type={}", type);
            throw new BaseException(LanguageConfigService.i18nForMsg("请检查参数"));
        }
        if (ObjectUtils.isEmpty(sku.getPku()) || ObjectUtils.isEmpty(sku.getSku())
                || ObjectUtils.isEmpty(sku.getSpec())) {
            log.error("SysSkuServiceImpl checkSysSku sku or pku or size is null , sku={} type={}",
                    JSON.toJSONString(sku), type);
            throw new BaseException(LanguageConfigService.i18nForMsg("pku、sku、尺码为必填项"));
        }

        if (!ObjectUtils.isEmpty(sku.getPku())) {
            sku.setPku(sku.getPku().trim());
        }

        if (!ObjectUtils.isEmpty(sku.getSpec())) {
            sku.setSpec(sku.getSpec().trim());
        }

        if (!"update".equals(type) && count(Wrappers.<SysSku>lambdaQuery().eq(SysSku::getPku, sku.getPku())) > 0) {
            log.error("SysSkuServiceImpl checkSysSku pku is error , sku={} type={}", JSON.toJSONString(sku), type);
            throw new BaseException(LanguageConfigService.i18nForMsg("pku已存在"));
        }

        if (!ObjectUtils.isEmpty(sku.getSku())) {
            sku.setSku(sku.getSku().trim());
            sku.setSkuIndexed(sku.getSku().replaceAll("[\\s-]", ""));
        }

        // 校验 sku 的数据格式 ， 不允许出现连续的空格
        Pattern skuPattern = Pattern.compile("^[^\\s]+(\\s[^\\s]+)*$");
        if (!skuPattern.matcher(sku.getSku()).matches()) {
            log.error("SysSkuServiceImpl checkSysSku skuPattern is error , sku={} type={}", JSON.toJSONString(sku),
                    type);
            throw new BaseException(LanguageConfigService.i18nForMsg("请检查sku的格式[") + sku.getSku()
                    + LanguageConfigService.i18nForMsg("]，勿出现连续的空格"));
        }

        // 校验 sku 的数据中间不能出现横杠
        if (sku.getSku().contains("-")
                || sku.getSku().contains("—")
        ) {
            log.error("SysSkuServiceImpl checkSysSku is '-' skuPattern2 is error , sku={} type={}", JSON.toJSONString(sku),
                    type);
            throw new BaseException(LanguageConfigService.i18nForMsg("请检查sku的格式[") + sku.getSku()
                    + LanguageConfigService.i18nForMsg("]，中间勿出现横杠"));
        }

        // 校验size的首尾现符号不允许出现符号
        Pattern sizePattern1 = Pattern.compile("^[A-Za-z0-9].*[A-Za-z0-9]$");
        Pattern sizePattern2 = Pattern.compile("^[A-Za-z0-9].*$");
        if (sku.getSpec().length() > 1) {
            if (!sizePattern1.matcher(sku.getSpec()).matches()) {
                log.error("SysSkuServiceImpl checkSysSku sizePattern1 is error , sku={} type={}",
                        JSON.toJSONString(sku), type);
                throw new BaseException(LanguageConfigService.i18nForMsg("请检查尺码的格式[") + sku.getSpec()
                        + LanguageConfigService.i18nForMsg("]，首尾部分勿出现符号"));
            }
        } else {
            if (!sizePattern2.matcher(sku.getSpec()).matches()) {
                log.error("SysSkuServiceImpl checkSysSku sizePattern2 is error , sku={} type={}",
                        JSON.toJSONString(sku), type);
                throw new BaseException(LanguageConfigService.i18nForMsg("请检查尺码的格式[") + sku.getSpec()
                        + LanguageConfigService.i18nForMsg("]，首尾部分勿出现符号"));
            }
        }

    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysSku> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysSkuRankListVo> rankList(int sortType, Integer shopId) {
        List<SysSkuRankListVo> voList = new ArrayList<>();
        int rankNum = 10; // 只展示前10的sku

        QueryWrapper dealQw = new QueryWrapper();
        dealQw.select("count(0) type, sku");
        dealQw.eq("del_flag", 0);
        dealQw.eq("type", SysProdEvent.TypeSale);
        dealQw.in("`status`", 1, 3);
        dealQw.groupBy("sku");
        if (sortType == 1) {
            dealQw.eq(!ObjectUtils.isEmpty(shopId), "shop_id", shopId);
        }
        if (sortType == 2) {
            List<SysSku> skuList = list(Wrappers.<SysSku>lambdaQuery()
                    .select(SysSku::getSku)
                    .orderByAsc(SysSku::getHotRankNum)
                    .last("limit " + rankNum));
            dealQw.in("sku", skuList.stream().map(SysSku::getSku).collect(Collectors.toList()));
        }
        List<SysProdDeal> dealList = iSysProdDealService.list(dealQw);

        for (SysProdDeal deal : dealList.stream()
                .sorted(Comparator.comparing(SysProdDeal::getType).reversed())
                .collect(Collectors.toList())) {
            if (voList.size() >= rankNum) {
                break;
            }

            SysSkuRankListVo vo = new SysSkuRankListVo();
            vo.setSku(deal.getSku());
            vo.setSaleNum(deal.getType());
            voList.add(vo);
        }

        List<SysSku> skuList = list(Wrappers.<SysSku>lambdaQuery().orderByAsc(SysSku::getHotRankNum));
        Map<String, SysSku> skuMap = skuList.stream().collect(Collectors.toMap(SysSku::getSku, a -> a));
        // if (skuList.size() < rankNum) {
        // rankNum = skuList.size();
        // }

        voList.forEach(vo -> {
            SysSku sku = skuMap.get(vo.getSku());
            if (!ObjectUtils.isEmpty(sku)) {
                vo.setHotRankNum(sku.getHotRankNum());
                vo.setImg(sku.getImg());
                vo.setId(sku.getId());
                skuMap.remove(vo.getSku());
            }
        });

        // 补空
        for (String s : skuMap.keySet()) {
            if (voList.size() >= rankNum) {
                break;
            }
            SysSkuRankListVo vo = new SysSkuRankListVo();
            SysSku sku = skuMap.get(s);
            vo.setId(sku.getId());
            vo.setSku(sku.getSku());
            vo.setHotRankNum(sku.getHotRankNum());
            vo.setImg(sku.getImg());
            vo.setSaleNum(0);
            voList.add(vo);
        }

        return voList;
    }

    /**
     * @return
     */
    @Override
    public List<SysSku> getDistinctSkuAndSpec() {
        return baseMapper.selectDistinctSkuAndSpec();
    }

    /**
     * @param sku
     * @param spec
     * @return
     */
    @Override
    public List<SysSku> selectBySkuWithoutSpacesAndSpec(String sku, String spec) {
        return baseMapper.selectBySkuWithoutSpacesAndSpec(sku, spec);
    }

    /**
     * @param time
     * @return
     */
    @Override
    public List<SysSku> selectSkuAfter(LocalDateTime time) {
        return baseMapper.selectCustom(time, 0);
    }


    @Override
    public List<SysSku> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    @AcquireTaskLock(name = "batchUpdateSkuExtraData", timeout = 86400)
    public Boolean batchUpdateExtraData(List<String> skuList) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //1.获取需要被更新数据集合
        List<String> skuData = skuList;
        if (CollUtil.isEmpty(skuList)) {
            skuData = getNeedUpdateSkuData();
        }
        stopWatch.stop();
        log.info("批量更新sku: brand gender color type数据 需要更新数据条数:{},查询数据耗时:{} ms", skuData.size(), stopWatch.getTotalTimeMillis());
        stopWatch.start();
        //2. 批量调用goat,批量入库
        for (int i = 0; i < skuData.size(); i += GET_GOAT_API_BATCH_SIZE) {
            int end = Math.min(i + GET_GOAT_API_BATCH_SIZE, skuData.size());
            List<String> batch = skuData.subList(i, end);
            // 调用goat获取sku数据
            List<SysSku> skus = getSysSkus(batch);
            if (CollUtil.isNotEmpty(skus)) {
                baseMapper.batchUpdateCustom(skus);
            }
        }
        stopWatch.stop();
        log.info("批量更新sku: brand gender color type数据,更新完成，更新耗时:{} ms", stopWatch.getTotalTimeMillis());
        return true;
    }

    @Override
    @ReadOnly
    public Map<String, SysSku> selectFirstSkuForEachIndexed(List<String> skus) {
        if (CollUtil.isEmpty(skus)) {
            log.error("selectFirstSkuForEachIndexed skus is empty");
            return Collections.emptyMap();
        }
        List<SysSku> sysSkus = baseMapper.selectFirstSkuForEachIndexed(skus);
        return sysSkus.stream().collect(Collectors.toMap(SysSku::getSkuIndexed, Function.identity()));
    }

    @Override
    public List<SysSku> queryDataGroupSkuAndSpec() {
        return baseMapper.queryDataGroupSkuAndSpec();
    }

    @Override
    public Map<String, String> getSkuCacheMap(String sku, Boolean ignoreSpace) {
        if (StrUtil.isBlank(sku)) {
            return Collections.emptyMap();
        }
        String redisSysSkuKey = String.format(KG_SYS_SKU_CACHE_KEY_PREFIX, sku);
        Map<Object, Object> skuMap = hmget(redisSysSkuKey);
        if (MapUtil.isEmpty(skuMap)) {
            LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
            if (ignoreSpace) {
                queryWrapper
                        .select(SysSku::getRemarks, SysSku::getBrand, SysSku::getImg)
                        .eq(SysSku::getSku, sku)
                        .isNotNull(SysSku::getRemarks)
                        .isNotNull(SysSku::getBrand)
                        .isNotNull(SysSku::getImg)
                        .last("LIMIT 1");
            } else {
                queryWrapper
                        .select(SysSku::getRemarks, SysSku::getBrand, SysSku::getImg)
                        .eq(SysSku::getSkuIndexed, sku)
                        .isNotNull(SysSku::getRemarks)
                        .isNotNull(SysSku::getBrand)
                        .isNotNull(SysSku::getImg)
                        .last("LIMIT 1");
            }
            SysSku sysSku = baseMapper.selectOne(queryWrapper);
            if (BeanUtil.isEmpty(sysSku)) {
                return Collections.emptyMap();
            }
            Map<String, Object> cacheMap = new HashMap<>(4);
            String skuRemarks = StrUtil.emptyToDefault(sysSku.getRemarks(), "");
            String skuBrand = StrUtil.emptyToDefault(sysSku.getBrand(), "");
            String skuImg = StrUtil.emptyToDefault(sysSku.getImg(), "");
            cacheMap.put("remarks", skuRemarks);
            cacheMap.put("brand", skuBrand);
            cacheMap.put("imgUrl", skuImg);
            hmset(redisSysSkuKey, cacheMap, KG_SYS_SKU_EXPIRED_TIME);
            Map<String, String> resultMap = new HashMap<>(2);
            resultMap.put("remarks", skuRemarks);
            resultMap.put("brand", skuBrand);
            cacheMap.put("imgUrl", skuImg);
            return resultMap;
        }
        Map<String, String> resultMap = new HashMap<>(2);
        resultMap.put("remarks", StrUtil.emptyToDefault((String) skuMap.get("remarks"), ""));
        resultMap.put("brand", StrUtil.emptyToDefault((String) skuMap.get("brand"), ""));
        resultMap.put("imgUrl", StrUtil.emptyToDefault((String) skuMap.get("imgUrl"), ""));
        return resultMap;
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }


    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取更新数据集
     * 1个请求暂停0.5秒
     *
     * @param needUpdateSkuData 需要更新数据集合
     * @return skus（组装完毕数据集）
     */
    public List<SysSku> getSysSkus(List<String> needUpdateSkuData) {
        List<SysSku> skus = new ArrayList<>();
        for (String sku : needUpdateSkuData) {
            //2. 调用goat接口获取所需数据
            JSONObject skuDataByGoat = touchUtils.getSkuDataByGoat(sku);
            if (skuDataByGoat.containsKey("productTemplates")) {
                JSONArray jsonArray = skuDataByGoat.getJSONArray("productTemplates");
                if (CollUtil.isNotEmpty(jsonArray)) {
                    skus.add(SysSku.createByJsonObject(jsonArray.getJSONObject(0), sku));
                }
            }
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("get skuDataByGoat sleep is error={},message={}", e, e.getMessage());
                throw new RuntimeException(e);
            }
        }
        return skus;
    }

    /**
     * 获取需要更新Sku数据的集合
     * gender is null
     *
     * @return sku list
     */
    public List<String> getNeedUpdateSkuData() {
        LambdaQueryWrapper<SysSku> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SysSku::getDelFlag, 0)
                .and(wrapper -> wrapper.isNull(SysSku::getBrand)
                        .or().eq(SysSku::getBrand, ""));
        List<SysSku> sysSkuList = baseMapper.selectList(queryWrapper);
        return sysSkuList.stream().map(SysSku::getSku).distinct().collect(Collectors.toList());
    }

}
