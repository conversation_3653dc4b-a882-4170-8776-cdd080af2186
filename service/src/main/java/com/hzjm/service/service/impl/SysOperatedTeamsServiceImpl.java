package com.hzjm.service.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysOperatedShopMembers;
import com.hzjm.service.entity.SysOperatedTeams;
import com.hzjm.service.mapper.SysOperatedTeamsMapper;
import com.hzjm.service.model.DTO.SysOperatedTeamsPageDto;
import com.hzjm.service.model.VO.SysOperatedTeamsVo;
import com.hzjm.service.model.VO.TeamMemberInfoVo;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.ISysOperatedShopMembersService;
import com.hzjm.service.service.ISysOperatedTeamsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 自营团队表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
@Slf4j
@Service
public class SysOperatedTeamsServiceImpl extends ServiceImpl<SysOperatedTeamsMapper, SysOperatedTeams> implements ISysOperatedTeamsService {

    @Override
    public SysOperatedTeams getByIdWithoutLogic(Integer id) {
        SysOperatedTeams data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该自营团队表");
        }

        return data;
    }

    @Override
    public SysOperatedTeamsVo getDetail(Integer id) {
        SysOperatedTeams data = getByIdWithoutLogic(id);

        SysOperatedTeamsVo vo = new SysOperatedTeamsVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Resource
    private ISysOperatedShopMembersService sysOperatedShopMembersService;

    @Resource
    private IShopUserService shopUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSysOperatedTeams(SysOperatedTeams dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            Assert.notNull(dto, "新增自营团队，参数不能为空");
            Assert.notNull(dto.getTeamName(), "新增自营团队，团队名称不可为空");
            // 校验一下名字不要重复
            if (count(Wrappers.<SysOperatedTeams>lambdaQuery().eq(SysOperatedTeams::getTeamName, dto.getTeamName())) > 0) {
                throw new BaseException("团队名称已存在");
            }
            rs = this.save(dto);
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
            // 删除关联关系
            rs = sysOperatedShopMembersService.hardDelete(Wrappers.<SysOperatedShopMembers>lambdaQuery().eq(SysOperatedShopMembers::getTeamId, dto.getId()));
            return rs;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        // 更新团队成员
        if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getShopUids())) {
            LambdaQueryWrapper<ShopUser> shopUserLambdaQueryWrapper = Wrappers.<ShopUser>lambdaQuery()
                    .select(ShopUser::getUid, ShopUser::getId)
                    .in(ShopUser::getUid, dto.getShopUids());
            List<ShopUser> shopUsers = shopUserService.list(shopUserLambdaQueryWrapper).stream().distinct().collect(Collectors.toList());
            Map<String, Integer> shopUidMap = shopUsers.stream().collect(Collectors.toMap(ShopUser::getUid, ShopUser::getId));
            sysOperatedShopMembersService.hardDelete(Wrappers.<SysOperatedShopMembers>lambdaQuery().eq(SysOperatedShopMembers::getTeamId, dto.getId()));
            List<SysOperatedShopMembers> sysOperatedShopMembers = new ArrayList<>();
            for (String uid : dto.getShopUids()) {
                SysOperatedShopMembers member = new SysOperatedShopMembers();
                member.setTeamId(dto.getId());
                member.setShopId(shopUidMap.get(uid));
                sysOperatedShopMembers.add(member);
            }
            rs = sysOperatedShopMembersService.saveBatch(sysOperatedShopMembers);
        }
        return rs;
    }

    @Override
    public Boolean insertList(List<SysOperatedTeams> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysOperatedTeams> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysOperatedTeams> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    public List<SysOperatedTeamsVo> queryTeamList(SysOperatedTeamsPageDto dto) {
        LambdaQueryWrapper<SysOperatedTeams> qw = Wrappers.<SysOperatedTeams>lambdaQuery()
                .like(!ObjectUtils.isEmpty(dto.getTeamName()), SysOperatedTeams::getTeamName, dto.getTeamName());
        List<SysOperatedTeams> teams = list(qw);

        List<SysOperatedTeamsVo> teamVos = new ArrayList<>();
        if (!ObjectUtils.isEmpty(teams)) {
            for (SysOperatedTeams team : teams) {
                SysOperatedTeamsVo teamVo = new SysOperatedTeamsVo();
                BeanUtils.copyProperties(team, teamVo);

                // 查询团队成员
                List<SysOperatedShopMembers> members = sysOperatedShopMembersService.list(Wrappers.<SysOperatedShopMembers>lambdaQuery()
                        .eq(SysOperatedShopMembers::getTeamId, team.getId()));
                List<TeamMemberInfoVo> memberInfos = new ArrayList<>();
                if (!ObjectUtils.isEmpty(members)) {
                    for (SysOperatedShopMembers member : members) {
                        TeamMemberInfoVo memberInfo = new TeamMemberInfoVo();
                        memberInfo.setId(member.getId());
                        memberInfo.setShopId(member.getShopId());

                        // 从 IShopUserService 获取商家名称和 UIID
                        ShopUser shopUser = shopUserService.getById(member.getShopId());
                        if (!ObjectUtils.isEmpty(shopUser)) {
                            memberInfo.setShopRealname(shopUser.getRealname());
                            memberInfo.setUid(shopUser.getUid());
                            memberInfo.setAccount(shopUser.getAccount());
                        }
                        memberInfos.add(memberInfo);
                    }
                }
                teamVo.setMembers(memberInfos);
                teamVos.add(teamVo);
            }
        }
        return teamVos;
    }

}
