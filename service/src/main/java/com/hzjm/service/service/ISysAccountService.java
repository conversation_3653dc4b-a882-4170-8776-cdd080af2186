package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysAccount;
import com.hzjm.service.model.DTO.LoginDto;
import com.hzjm.common.model.LoginUser;

/**
 * 平台账号 服务类
 *
 * <AUTHOR>
 * @since 2020-09-30
 */
public interface ISysAccountService extends IService<SysAccount> {

    SysAccount getByIdWithoutLogic(Integer id);

    Boolean saveSysAccount(SysAccount dto);

    LoginUser login(LoginDto dto);

    LoginUser loginAsUser(LoginDto dto);
}
