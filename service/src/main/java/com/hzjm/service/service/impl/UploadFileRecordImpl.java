package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.UploadFileRecord;
import com.hzjm.service.mapper.UploadFileRecordMapper;
import com.hzjm.service.model.enums.UploadFileType;
import com.hzjm.service.service.IUploadFileRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/1/6 14:51
 * @description:
 */
@Slf4j
@Service
public class UploadFileRecordImpl extends ServiceImpl<UploadFileRecordMapper, UploadFileRecord> implements IUploadFileRecord {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveFiles(List<String> fileUrls, Integer uid, UploadFileType type) {
        if (uid == null) {
            return;
        }
        if (fileUrls == null) {
            return;
        }
        QueryWrapper<UploadFileRecord> wrapper = new QueryWrapper<>();
        wrapper
                .eq("type", type)
                .eq("uid", uid);
        this.remove(wrapper);
        List<UploadFileRecord> records = fileUrls.stream()
                .map(fileUrl -> UploadFileRecord.builder()
                        .uid(uid)
                        .fileUrl(fileUrl)
                        .type(type)
                        .build())
                .collect(Collectors.toList());
        this.saveBatch(records);
    }

    @Override
    public List<String> getFileUrls(Integer uid, UploadFileType type) {
        if (uid == null) {
            return Collections.emptyList();
        }
        QueryWrapper<UploadFileRecord> wrapper = new QueryWrapper<>();
        wrapper
                .eq("type", type)
                .eq("del_flag", 0)
                .eq("uid", uid);
        List<UploadFileRecord> records = this.list(wrapper);
        return records.stream()
                .map(UploadFileRecord::getFileUrl)
                .collect(Collectors.toList());
    }
}
