package com.hzjm.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.service.entity.SysEmailHistory;
import com.hzjm.service.model.enums.EmailTypeEnum;
import com.hzjm.service.service.BaseSendEmailService;
import com.hzjm.service.service.SendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Map;

import static com.hzjm.service.constants.ServiceConstants.*;

@Service
@Slf4j
public class SendEmailServiceImpl implements SendEmailService {

    @Autowired
    private BaseSendEmailService baseSendEmailService;

    String ebayIcon = "https://res.knetgroup.com/image/icon/ebay.png";
    String goatIcon = "https://res.knetgroup.com/image/icon/goat.png";
    String kcIcon = "https://res.knetgroup.com/image/icon/kc.png";
    String knetIcon = "https://res.knetgroup.com/image/icon/knet.png";
    String poizonIcon = "https://res.knetgroup.com/image/icon/poizon.png";
    String stockxIcon = "https://res.knetgroup.com/image/icon/stockx.png";
    String surgeIcon = "https://res.knetgroup.com/image/icon/surge.png";
    String b2bIcon = "https://res.knetgroup.com/image/icon/b2b.png";

    /**
     * 到货发送电子邮件
     */

    @Override
    public void incomingShipmentSendEmail(Map<String, String> map) {

        log.info("SendEmailServiceImpl.incomingShipmentSendEmail  start email={}", JSON.toJSONString(map));

        // 初始化参数
        String email = map.get("email");
        String userName = map.get("userName");
        String trackingNo = map.get("trackingNo");

        // 检查参数
        if (StringUtil.isBlank(email) ||
                StringUtil.isBlank(userName) ||
                StringUtil.isBlank(trackingNo)) {
            log.info("SendEmailServiceImpl.incomingShipmentSendEmail  end email is null");
            return;
        }

        // 组装邮件 ：您的货件已收到
        String subject = "Your Incoming Shipment Has Been Received";
        String htmlContent = null;
        try {
            htmlContent = this.readHtmlTemplate("/template/email/arrived-template.html");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (htmlContent == null) {
            log.info("SendEmailServiceImpl.incomingShipmentSendEmail  end htmlContent is null");
            return;
        }
        String personalizedContent = htmlContent
                .replace("${name}", userName)
                .replace("${trackingNumber}", trackingNo);

        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(trackingNo);
        sysEmailHistory.setBusinessType(EmailTypeEnum.INCOMING_SHIPMENT.value);

        baseSendEmailService.sendEmail(email, SysConstants.INCOMING_SHIPMENT_BCC_EMAIL, personalizedContent, subject, sysEmailHistory);

    }

    /**
     * 包裹查验通知
     */
    @Override
    public void oncomingShipmentSendEmail(Map<String, String> map) {

        log.info("SendEmailServiceImpl.OncomingShipmentSendEmail  start email:" + JSON.toJSONString(map));

        // 初始化参数
        String email = map.get("email");
        String userName = map.get("userName");
        String logNo = map.get("logNo");

        if (StringUtil.isBlank(email) ||
                StringUtil.isBlank(userName)) {
            log.info("SendEmailServiceImpl.OncomingShipmentSendEmail  end email is null");
            return;
        }

        // 组装邮件
        String subject = "Your Incoming Shipment Has Been Checked In";
        String htmlContent = null;
        try {
            htmlContent = this.readHtmlTemplate("/template/email/check-in-template.html");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (htmlContent == null) {
            log.info("SendEmailServiceImpl.OncomingShipmentSendEmail  end htmlContent is null");
            return;
        }
        String personalizedContent = htmlContent
                .replace("${name}", userName)
                .replace("${logNo}", logNo);


        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(logNo);
        sysEmailHistory.setBusinessType(EmailTypeEnum.ONCOMING_SHIPMENT.value);

        baseSendEmailService.sendEmail(email, "", personalizedContent, subject, sysEmailHistory);

    }

    /**
     * 包裹查验通知 - dropoff
     */
    @Override
    @Deprecated
    public void dropOffBatchSendEmail(Map<String, String> map) {

        log.info("SendEmailServiceImpl.dropOffBatchSendEmail  start email={}", JSON.toJSONString(map));

        // 初始化参数
        String email = map.get("email");
        String userName = map.get("userName");
        String batchNo = map.get("batchNo");

        // 检查参数
        if (StringUtil.isBlank(email) ||
                StringUtil.isBlank(userName) ||
                StringUtil.isBlank(batchNo)) {
            log.info("SendEmailServiceImpl.dropOffBatchSendEmail  end email is null");
            return;
        }

        // 组装邮件
        String subject = "Your Drop-Off Batch Has Been Checked In";
        String content = "<p>Hi " + userName + ",</p>" +
                "<p>&nbsp;</p>" +
                "<p> We're excited to let you know that your drop-off batch with batch number: <b>" + batchNo + "</b> has been checked into our inventory. " +
                "You can now view and manage your inventory in the seller portal.</p>" +
                "<p>&nbsp;</p>" +
                "<p> If you have any questions or need assistance,feel free to reach out to us. </p>" +
                "<p> Thank you for choosing Knet Group!</p>" +
                "<p>&nbsp;</p>" +
                "<p>Cheers,</p>" +
                "<p>KNETGROUP Team</p>";

        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(batchNo);
        sysEmailHistory.setBusinessType(EmailTypeEnum.DORP_OFF_BATCH_CHECKED.value);

        baseSendEmailService.sendEmail(email, "", content, subject, sysEmailHistory);

    }


    /**
     * 批次到货通知，批次快递已收到
     */
    @Override
    @Deprecated
    public void dropOffBatchReceivedSendEmail(Map<String, String> map) {

        log.info("SendEmailServiceImpl.dropOffBatchReceivedSendEmail  start email={}", JSON.toJSONString(map));

        // 初始化参数
        String email = map.get("email");
        String userName = map.get("userName");
        String batchNo = map.get("batchNo");

        // 检查参数
        if (StringUtil.isBlank(email) ||
                StringUtil.isBlank(userName) ||
                StringUtil.isBlank(batchNo)) {
            log.info("SendEmailServiceImpl.dropOffBatchReceivedSendEmail  end email is null");
            return;
        }

        // 组装邮件 您的投递批次已收到
        String subject = "Your Drop-Off Batch Has Been Received";
        String content = "<p>Hi " + userName + ",</p>" +
                "<p>&nbsp;</p>" +
                "<p> We're happy to inform you that your drop-off batch with batch number: <b>" + batchNo + "</b> has been received. </p>" +
                "<p>&nbsp;</p>" +
                "<p>Our team is currently processing the items, and you'll be able to view and manage your inventory in the seller portal once they are checked in.</p>" +
                "<p>&nbsp;</p>" +
                "<p> Thank you for choosing Knet Group!</p>" +
                "<p>&nbsp;</p>" +
                "<p>Cheers,</p>" +
                "<p>KNETGROUP Team</p>";

        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(batchNo);
        sysEmailHistory.setBusinessType(EmailTypeEnum.DORP_OFF_BATCH_RECEIVED.value);

        baseSendEmailService.sendEmail(email, "", content, subject, sysEmailHistory);

    }


    /**
     * 订单卖出，发送邮件告知商家
     */

    @Override
    public void productSoldSendEmail(Map<String, Object> map) {

        log.info("SendEmailServiceImpl.productSoldSendEmail  start email={}", JSON.toJSONString(map));

        // 初始化参数
        String email = map.get("email") != null ? map.get("email").toString() : "";
        String userName = map.get("userName") != null ? map.get("userName").toString() : "";
        String platform = map.get("platform") != null ? map.get("platform").toString() : "";
        String imageUrl = map.get("imageUrl") != null ? map.get("imageUrl").toString() : "";
        String itemName = map.get("itemName") != null ? map.get("itemName").toString() : "";
        String sku = map.get("sku") != null ? map.get("sku").toString() : "";
        String size = map.get("size") != null ? map.get("size").toString() : "";
        String salePrice = map.get("amount") != null ? map.get("amount").toString() : "0";
        String sellerOwning = map.get("owning") != null ? map.get("owning").toString() : "0";
        String orderNumber = map.get("orderNumber") != null ? map.get("orderNumber").toString() : "";

        // 判断是哪一个平台，设置对相应的图标
        String iconImage = "";

        if (platform.equalsIgnoreCase("EBay")) {
            iconImage = this.ebayIcon;
        } else if (platform.equalsIgnoreCase("StockX")) {
            iconImage = this.stockxIcon;
        } else if (platform.equalsIgnoreCase("goat")) {
            iconImage = this.goatIcon;
        } else if (platform.equalsIgnoreCase("kc")
                || platform.equalsIgnoreCase("Kicks Crew")) {
            iconImage = this.kcIcon;
        } else if (platform.equalsIgnoreCase("poizon")) {
            iconImage = this.poizonIcon;
        } else if (platform.equalsIgnoreCase("knet")) {
            iconImage = this.knetIcon;
        } else if (platform.equalsIgnoreCase("SURGE")) {
            iconImage = this.surgeIcon;
        } else if (platform.equalsIgnoreCase("KNET B2B")) {
            iconImage = this.b2bIcon;
        }

        // 检查参数
        if (StringUtil.isBlank(email) ||
                StringUtil.isBlank(userName)) {
            log.info("SendEmailServiceImpl.productSoldSendEmail  end email is null");
            return;
        }

        // 组装邮件 好消息！您的上架商品已售出
        String subject = "Good News! Your Cross-Listed Product SOLD";
        String htmlContent = null;
        try {
            htmlContent = this.readHtmlTemplate("/template/email/product-sold-template.html");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (htmlContent == null) {
            log.info("SendEmailServiceImpl.productSoldSendEmail  end htmlContent is null");
            return;
        }
        // 填充邮件内容
        String personalizedContent = htmlContent
                .replace("${userName}", userName)
                .replace("${platform}", platform)
                .replace("${imageUrl}", imageUrl)
                .replace("${itemName}", itemName)
                .replace("${sku}", sku)
                .replace("${size}", size)
                .replace("${salePrice}", salePrice)
                .replace("${iconImage}", iconImage)
                .replace("${sellerOwning}", sellerOwning);


        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(orderNumber);
        sysEmailHistory.setBusinessType(EmailTypeEnum.KNET_LISTING_ID.value);

        baseSendEmailService.sendEmail(email, "", personalizedContent, subject, sysEmailHistory);

    }

    /**
     * 订单完成，提现通知
     */

    @Override
    public void fundAreAvailableSendEmail(Map<String, String> map) {

        log.info("SendEmailServiceImpl.fundAreAvailableSendEmail  start email={}", JSON.toJSONString(map));

        // 初始化参数
        String email = map.get("email");
        String userName = map.get("userName");
        String orderNo = map.get("orderNo");

        // 检查参数
        if (StringUtil.isBlank(email) ||
                StringUtil.isBlank(userName) ||
                StringUtil.isBlank(orderNo)) {
            log.info("SendEmailServiceImpl.fundAreAvailableSendEmail  end email is null");
            return;
        }

        // 组装邮件 恭喜！您的资金可以提取
        String subject = "Congratulations! Your Funds Are Available for Withdraw";
        String htmlContent = null;
        try {
            htmlContent = this.readHtmlTemplate("/template/email/fund-are-available-template.html");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (htmlContent == null) {
            log.info("SendEmailServiceImpl.fundAreAvailableSendEmail  end htmlContent is null");
            return;
        }
        String personalizedContent = htmlContent
                .replace("${userName}", userName)
                .replace("${orderNo}", orderNo);

        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(orderNo);
        sysEmailHistory.setBusinessType(EmailTypeEnum.FUNDS_ARE_AVAILABLE.value);

        baseSendEmailService.sendEmail(email, "", personalizedContent, subject, sysEmailHistory);

    }

    @Override
    public void sendRecapMonthly(Map<String, String> map) {
        if (BeanUtil.isEmpty(map)) {
            log.info("发送商家月度统计邮件.sendRecapMonthly  end map is null");
            return;
        }
        String email = map.get("email");
        String content = map.get("content");
        String subject = String.format(EMAIL_RECAP_MONTHLY_SUB, map.get("month"));
        log.info("发送商家月度统计邮件  start emailAddress={}", email);
        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(map.get("businessId"));
        sysEmailHistory.setBusinessType(EmailTypeEnum.RECAP_MONTHLY.toString());
        baseSendEmailService.sendEmail(email, "", content, subject, sysEmailHistory);
        log.info("发送商家月度统计邮件 结束  start emailAddress={}", email);
    }

    @Override
    public void sendRecapYearly(Map<String, String> map) {
        if (BeanUtil.isEmpty(map)) {
            log.info("发送商家年度统计邮件.sendRecapMonthly  end map is null");
            return;
        }
        String email = map.get("email");
        String content = map.get("content");
        log.info("发送商家年度统计邮件  start emailAddress={}", email);
        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(map.get("businessId"));
        sysEmailHistory.setBusinessType(EmailTypeEnum.RECAP_YEARLY.toString());
        baseSendEmailService.sendEmail(email, "", content, EMAIL_RECAP_YEARLY_SUB, sysEmailHistory);
        log.info("发送商家年度统计邮件 结束  start emailAddress={}", email);
    }

    @Override
    public void sendRulerNotedEmail(Map<String, String> map) {
        if (BeanUtil.isEmpty(map)) {
            log.info("发送商家将要被封禁邮件.sendRulerNotedEmail  end map is null");
            return;
        }
        String email = map.get("email");
        String content = map.get("content");
        log.info("发送商家将要被封禁邮件  start emailAddress={}", email);
        SysEmailHistory sysEmailHistory = new SysEmailHistory();
        sysEmailHistory.setBusinessId(map.get("businessId"));
        sysEmailHistory.setBusinessType(EmailTypeEnum.RULER_NOTED.toString());
        baseSendEmailService.sendEmail(email, "", content, RULER_NOTED_EMAIL_SUB, sysEmailHistory);
        log.info("发送商家将要被封禁邮件 结束  start emailAddress={}", email);
    }

    /**
     * 读取 html模版文件
     */
    String readHtmlTemplate(String resourcePath) throws IOException {
        StringBuilder content = new StringBuilder();
        try (InputStream inputStream = SendEmailServiceImpl.class.getResourceAsStream(resourcePath);
             BufferedReader br = new BufferedReader(new InputStreamReader(inputStream))) {

            if (inputStream == null) {
                throw new IOException("Resource not found: " + resourcePath);
            }

            String line;
            while ((line = br.readLine()) != null) {
                content.append(line);
            }
        }
        return content.toString();
    }

}
