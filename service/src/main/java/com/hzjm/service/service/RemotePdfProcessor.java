package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.AwsS3Utils;
import com.hzjm.common.utils.FileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;

@Slf4j
@Service
public class RemotePdfProcessor {

    /**
     *  处理 stockX 远程的 PDF 文件
     * @param fileUrl 远程的 pdf 地址
     * @param fileName 上传到 S3 的文件名
     * @return
     */
    public String processRemotePdf(String fileUrl, String fileName) {

        //1. 远程获取 PDF 的输入流
        File pdfFile = null;
        try {
            pdfFile = FileUtils.downloadPdf(fileUrl);
        }catch (Exception e) {
            throw new BaseException("processRemotePdf 远程获取 pdf 文件 失败: " + e.getMessage());
        }

        if (ObjectUtils.isEmpty(pdfFile)) {
            throw  new BaseException("processRemotePdf 远程获取 pdf 文件为空");
        }

        //2. 处理 PDF 文件
        // 取 pdf 文件的 第二页 并向左旋转 90度
        File rotatedPdf = null;
        try {
            rotatedPdf = FileUtils.extractRotateCropSecondPageByContent(pdfFile);
        } catch (Exception e) {
            throw new BaseException("processRemotePdf 尝试截取并旋转 pdf 文件失败: " + e.getMessage());
        }

        // 读取文件字节
        try {
            byte[] fileBytes = Files.readAllBytes(rotatedPdf.toPath());

            // 调用你已有工具类上传方法
            return AwsS3Utils.uploadFile(fileBytes, fileName);
        } catch (Exception e) {
            throw new BaseException("processRemotePdf 上传处理后的 pdf 文件到 S3 失败: " + e.getMessage());
        } finally {
            if (pdfFile.exists()) {
                pdfFile.delete();
            }
            if (rotatedPdf.exists()) {
                rotatedPdf.delete();
            }
        }

    }
}
