package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.SysFile;
import com.hzjm.service.mapper.SysFileMapper;
import com.hzjm.service.service.ISysFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台文件 服务实现类
 *
 * <AUTHOR>
 * @since 2020-10-28
 */
@Slf4j
@Service
public class SysFileServiceImpl extends ServiceImpl<SysFileMapper, SysFile> implements ISysFileService {

    @Override
    public SysFile getByIdWithoutLogic(Integer id) {
        return baseMapper.selectByIdWithoutLogic(id);
    }

    @Override
    public Boolean saveSysFile(SysFile dto) {
        Boolean rs = false;
        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (!ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag()) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public Boolean relateFile(List<String> urlList, Integer relationId, Integer relationType) {
        if (ObjectUtils.isEmpty(urlList)) {
            return false;
        }

        List<SysFile> fileList = new ArrayList<>();
        urlList.forEach(fileUrl -> {
            SysFile file = new SysFile();
            file.setFileUrl(fileUrl);
            file.setRelationId(relationId);
            file.setRelationType(relationType);
            fileList.add(file);
        });
        return saveBatch(fileList);
    }

    @Override
    public Boolean resetFile(List<String> imgList, Integer relationId, Integer relationType) {
        baseMapper.delete(Wrappers.<SysFile>lambdaQuery()
                .eq(SysFile::getRelationId, relationId).eq(SysFile::getRelationType, relationType));

        return relateFile(imgList, relationId, relationType);
    }

    @Override
    public List<String> getFileUrl(Integer relationId, Integer relationType) {
        List<String> fileUrlList = new ArrayList<>();
        List<SysFile> fileList = baseMapper.selectList(Wrappers.<SysFile>lambdaQuery().orderByAsc(SysFile::getGmtCreate)
                .eq(SysFile::getRelationId, relationId).eq(SysFile::getRelationType, relationType));
        if (!ObjectUtils.isEmpty(fileList)) {
            fileList.forEach(file -> {
                fileUrlList.add(file.getFileUrl());
            });
        }
        return fileUrlList;
    }

    @Override
    public Map<Integer, List<String>> getFileMap(List<Integer> relationIdList, Integer relationType) {
        Map<Integer, List<String>> map = new HashMap<>();
        List<SysFile> fileList = baseMapper.selectList(Wrappers.<SysFile>lambdaQuery()
                .orderByAsc(SysFile::getGmtCreate)
                .in(!ObjectUtils.isEmpty(relationIdList), SysFile::getRelationId, relationIdList)
                .eq(SysFile::getRelationType, relationType));
        if (!ObjectUtils.isEmpty(fileList)) {
            fileList.forEach(file -> {
                List<String> fileUrlList = map.get(file.getRelationId());
                if (ObjectUtils.isEmpty(fileUrlList)) {
                    fileUrlList = new ArrayList<>();
                    map.put(file.getRelationId(), fileUrlList);
                }
                fileUrlList.add(file.getFileUrl());
            });
        }
        return map;
    }

}
