package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysReturnHandlerConfig;
import com.hzjm.service.mapper.SysReturnHandlerConfigMapper;
import com.hzjm.service.model.DTO.SysReturnHandlerConfigPageDto;
import com.hzjm.service.model.VO.SysReturnHandlerConfigListVo;
import com.hzjm.service.model.VO.SysReturnHandlerConfigVo;
import com.hzjm.service.service.ISysReturnHandlerConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 退货处理用户配置表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@Slf4j
@Service
public class SysReturnHandlerConfigServiceImpl extends ServiceImpl<SysReturnHandlerConfigMapper, SysReturnHandlerConfig> implements ISysReturnHandlerConfigService {

    @Override
    public SysReturnHandlerConfig getByIdWithoutLogic(Integer id) {
        SysReturnHandlerConfig data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该退货处理用户配置表");
        }

        return data;
    }

    @Override
    public SysReturnHandlerConfigVo getDetail(Integer id) {
        SysReturnHandlerConfig data = getByIdWithoutLogic(id);

        SysReturnHandlerConfigVo vo = new SysReturnHandlerConfigVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysReturnHandlerConfig(SysReturnHandlerConfig dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysReturnHandlerConfigListVo> searchList(SysReturnHandlerConfigPageDto dto) {

        LambdaQueryWrapper<SysReturnHandlerConfig> qw = Wrappers.<SysReturnHandlerConfig>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysReturnHandlerConfig::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysReturnHandlerConfig::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysReturnHandlerConfig::getGmtCreate, endTime);

        IPage<SysReturnHandlerConfig> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysReturnHandlerConfigListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysReturnHandlerConfigListVo vo = new SysReturnHandlerConfigListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysReturnHandlerConfigListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysReturnHandlerConfig> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysReturnHandlerConfig> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysReturnHandlerConfig> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
