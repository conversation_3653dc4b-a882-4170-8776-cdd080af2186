package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.service.entity.SysWareOutBatchProd;
import com.hzjm.service.model.DTO.SysWareOutBatchProdPageDto;
import com.hzjm.service.model.VO.SysWareBatchCountVo;
import com.hzjm.service.model.VO.SysWareOutBatchListVo;
import com.hzjm.service.model.VO.SysWareOutBatchProdListVo;
import com.hzjm.service.model.VO.SysWareOutBatchProdVo;

import java.util.List;

/**
 * 出库批次商品 服务类
 *
 * <AUTHOR>
 * @since 2023-05-25
 */
public interface ISysWareOutBatchProdService extends IService<SysWareOutBatchProd> {

    SysWareOutBatchProd getByIdWithoutLogic(Integer id);

    SysWareOutBatchProdVo getDetail(Integer id);

    Boolean saveSysWareOutBatchProd(SysWareOutBatchProd dto);

    Boolean insertList(List<SysWareOutBatchProd> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWareOutBatchProdListVo> searchList(SysWareOutBatchProdPageDto dto);

    List<SysWareOutBatchProd> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareOutBatchProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    @Deprecated
    List<SysWareOutBatchListVo> searchGroup(SysWareOutBatchProdPageDto dto, String timezone);

    List<SysWareOutBatchProd> statistic(QueryWrapper qw);

    @Deprecated
    SysWareBatchCountVo groupCount(SysWareOutBatchProdPageDto dto, String timezone);

    @ReadOnly
    IPage<SysWareOutBatchListVo> searchGroupNew(SysWareOutBatchProdPageDto dto, String timezone);

    @ReadOnly
    List<SysWareOutBatchProdListVo> searchListNew(SysWareOutBatchProdPageDto dto);

    @ReadOnly
    SysWareBatchCountVo groupCountNew(SysWareOutBatchProdPageDto dto, String timezone);

    @ReadOnly
    List<String> selectWareBatchOutToExportCsvData(SysWareOutBatchProdPageDto dto, String language);
}
