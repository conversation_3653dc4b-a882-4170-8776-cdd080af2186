package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpPageResult;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysAccount;
import com.hzjm.service.entity.SysUser;
import com.hzjm.service.entity.SysUserPermission;
import com.hzjm.service.mapper.SysUserMapper;
import com.hzjm.service.model.DTO.SysUserPageDto;
import com.hzjm.service.model.VO.SysWareUserDto;
import com.hzjm.service.service.*;
import com.hzjm.service.utils.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 后管用户 服务实现类
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements ISysUserService {

    @Autowired
    private ISysAccountService iSysAccountService;
    @Autowired
    private ISysPermissionService iSysPermissionService;
    @Autowired
    private ISysUserPermissionService iSysUserPermissionService;
    @Autowired
    private RedisUtils redisUtils;
    @Resource
    private IShopUserService iShopUserService;

    @Value("${spring.application.name}")
    private String applicationName;

    @Override
    public SysUser getByIdWithoutLogic(Integer id) {
        SysUser data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }


        // 获取用户权限
        data.setPermissionList(iSysPermissionService.getPermission(id, 1));

        List<SysUserPermission> permList = iSysUserPermissionService.list(Wrappers.<SysUserPermission>lambdaQuery().eq(SysUserPermission::getUserId, data.getId()));
        data.setPermissionIdList(permList.stream().map(SysUserPermission::getPermissionId).collect(Collectors.toList()));
        return data;
    }

    @Override
    public Boolean saveSysUser(SysUser dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getAccount())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请填写账号"));
            }
            if (ObjectUtils.isEmpty(dto.getPassword())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请填写密码"));
            }

            if (iSysAccountService.count(Wrappers.<SysAccount>lambdaQuery()
                    .eq(SysAccount::getUserType, 1)
                    .eq(SysAccount::getAccount, dto.getAccount())
                    .eq(SysAccount::getAccountType, 1)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("登录账号已存在"));
            }

            rs = baseMapper.insert(dto) > 0;

            // 新建账号
            SysAccount account = new SysAccount();
            account.setUserType(1);
            account.setUserId(dto.getId());
            account.setPwd(BaseUtils.md5(applicationName, dto.getPassword()));
            account.setAccount(dto.getAccount());
            account.setAccountType(1);
            iSysAccountService.saveSysAccount(account);
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;

            // 删除账号
            iSysAccountService.remove(Wrappers.<SysAccount>lambdaQuery().eq(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 1));
        } else {
            rs = baseMapper.updateById(dto) > 0;

            // 修改 sys_user 表中的 nickName 时，同步 shop_user 表中的 customerManager
            if (!ObjectUtils.isEmpty(dto.getNickname())) {
                iShopUserService.update(Wrappers.<ShopUser>lambdaUpdate()
                        .set(ShopUser::getCustomerManager, dto.getNickname())
                        .eq(ShopUser::getCustomerManagerId, dto.getId()));
            }

            // 修改账号
            if (!ObjectUtils.isEmpty(dto.getAccount())) {
                if (iSysAccountService.count(Wrappers.<SysAccount>lambdaQuery()
                        .ne(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 1)
                        .eq(SysAccount::getAccount, dto.getAccount()).eq(SysAccount::getAccountType, 1)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("登录账号已存在"));
                }
                iSysAccountService.update(Wrappers.<SysAccount>lambdaUpdate()
                        .set(SysAccount::getAccount, dto.getAccount())
                        .eq(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 1)
                        .eq(SysAccount::getAccountType, 1));
            }

            if (!ObjectUtils.isEmpty(dto.getPassword())) {
                iSysAccountService.update(Wrappers.<SysAccount>lambdaUpdate()
                        .set(SysAccount::getPwd, BaseUtils.md5(applicationName, dto.getPassword()))
                        .eq(SysAccount::getUserId, dto.getId())
                        .eq(SysAccount::getAccountType, 1)
                        .eq(SysAccount::getUserType, 1));
            }
            redisUtils.delete(SysConstants.manageToken + dto.getId());
            redisUtils.delete(SysConstants.wareToken + dto.getId());
        }

        if (ObjectUtils.isEmpty(dto.getStatus()) && !isDelete) {
            // 绑定权限
            iSysUserPermissionService.reset(dto.getId(), dto.getPermissionIdList());
        }

        return rs;
    }

    @Override
    public List<SysUser> listAll() {
        return baseMapper.listAll();
    }

    @Override
    @ReadOnly
    public HttpPageResult<SysUser> searchList(SysUserPageDto dto) {

        Date endTime = dto.dealEndTime();

        LambdaQueryWrapper<SysUser> qw = Wrappers.<SysUser>lambdaQuery().orderByDesc(SysUser::getGmtCreate)
                .ne(SysUser::getId, 1) // 排除admin账号
                .eq(!ObjectUtils.isEmpty(dto.getWarePower()), SysUser::getWarePower, dto.getWarePower())
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysUser::getId, dto.getIdList())
                .like(!ObjectUtils.isEmpty(dto.getAccount()), SysUser::getAccount, dto.getAccount())
                .like(!ObjectUtils.isEmpty(dto.getNickname()), SysUser::getNickname, dto.getNickname())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysUser::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysUser::getGmtCreate, endTime);

        if (!ObjectUtils.isEmpty(dto.getPowerType())) {
            qw.eq(dto.getPowerType() == 1, SysUser::getAdminPower, 1);
            qw.eq(dto.getPowerType() == 2, SysUser::getWarePower, 1);
        }
        // 设置排序
        if (!ObjectUtils.isEmpty(dto.getSortField()) && "nickname".equals(dto.getSortField())) {
            if ("ascend".equals(dto.getSortOrder())) {
                qw.orderByAsc(SysUser::getNickname);
            } else {
                qw.orderByDesc(SysUser::getNickname);
            }
        }

        HttpPageResult pageResult = new HttpPageResult();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            IPage<SysUser> iPage = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
            BeanUtils.copyProperties(iPage, pageResult);
        } else {
            pageResult.setRecords(list(qw));
        }
        return pageResult;
    }

    /**
     * @param userId
     * @return
     */
    @Override
    public boolean isAdmin(Integer userId) {
        SysUser user = getById(userId);
        // 在 SysUser 表里无数据 不是超管
        if (ObjectUtils.isEmpty(user)) {
            return false;
        }
        // 状态不是禁用且其具有超管权限
        return Objects.equals(user.getStatus(), 0) && Objects.equals(user.getAdminPower(), 1);
    }

    /**
     * Check if the given user ID belongs to an administrator.
     *
     * @param userId The ID of the user to check.
     * @return true if the user is an admin, false otherwise.
     */
    @Override
    public boolean isAdminer(Integer userId) {
        Integer status = userId == 1 ? 0 : 1; // admin超管的status是0，其他用户需要判断 status = 1
        // 查询SKU是否对应一个用户
        SysUser user = this.baseMapper.selectOne(
                new QueryWrapper<SysUser>().lambda()
                        .select(SysUser::getAdminPower)
                        .eq(SysUser::getDelFlag, 0) // 是否删除，0为非
                        .eq(SysUser::getStatus, status) // 1-正常，2-禁用
                        .eq(SysUser::getId, userId)
                        .last("LIMIT 1")
        );
        return user != null && user.getAdminPower() == 1; // 超管权限，1-有，2-无
    }

    @Override
    @ReadOnly
    public List<SysWareUserDto> querySysWareUserList() {
        return baseMapper.querySysWareUserList();
    }
}
