package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysApplyKnet;
import com.hzjm.service.mapper.SysApplyKnetMapper;
import com.hzjm.service.service.ISysApplyKnetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;

/**
 * 申请加入knet 服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
@Slf4j
@Service
public class SysApplyKnetServiceImpl extends ServiceImpl<SysApplyKnetMapper, SysApplyKnet> implements ISysApplyKnetService {

    @Override
    public Boolean saveSysApplyKnet(SysApplyKnet dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public Boolean insertList(List<SysApplyKnet> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {


            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public int hardUpdate(Integer id) {
        return baseMapper.updateHasNotifiedById(id);
    }

    @Override
    public IPage<SysApplyKnet> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysApplyKnet> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
