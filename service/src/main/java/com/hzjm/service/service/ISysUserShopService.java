package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysUserShop;
import com.hzjm.service.model.DTO.SysUserShopPageDto;
import com.hzjm.service.model.DTO.SysUserShopSaveDto;
import com.hzjm.service.model.VO.SysUserShopListVo;
import com.hzjm.service.model.VO.SysUserShopVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商家数据权限 服务类
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface ISysUserShopService extends IService<SysUserShop> {

    SysUserShop getByIdWithoutLogic(Integer id);

    SysUserShopVo getDetail(Integer id);

    Boolean saveSysUserShop(SysUserShop dto);

    Boolean insertList(List<SysUserShop> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysUserShopListVo> searchList(SysUserShopPageDto dto);

    List<SysUserShop> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysUserShop> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean reset(SysUserShopSaveDto dto);
}
