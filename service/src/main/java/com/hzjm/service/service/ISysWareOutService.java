package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysWareOut;
import com.hzjm.service.model.DTO.SysWareOutPageDto;
import com.hzjm.service.model.VO.SysWareOutListVo;
import com.hzjm.service.model.VO.SysWareOutVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 出库单 服务类
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
public interface ISysWareOutService extends IService<SysWareOut> {

    SysWareOut getByIdWithoutLogic(Integer id);

    SysWareOutVo getDetail(Integer id, String oddNo);

    Boolean saveSysWareOut(SysWareOut dto);

    Boolean insertList(List<SysWareOut> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWareOutListVo> searchList(SysWareOutPageDto dto);

    List<SysWareOut> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareOut> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    IPage<SysWareOutListVo> searchSale(SysWareOutPageDto dto);

    Integer sumProdNum(SysWareOutPageDto dto);

    Boolean resetProdNum(Integer id, Integer relationId, Integer relationType);

    @Transactional
    void stocksFlexWareOut(List<String> oneIds);

    /**
     * 查询用户出库单是否存在
     *
     * @param shopUser 用户
     * @param dateTime 时间
     * @param offset   偏移量 整数 为当天 -1 为昨天
     * @return 查询出库单是否存在
     */
    boolean queryBlockedUser(ShopUser shopUser, Date dateTime, Integer offset);

    /**
     * 查询用户订单是否存在-返回不存在的商家
     *
     * @param shopUsers 用户s
     * @param dateTime  时间
     * @param offset    偏移量 整数 为当天 -1 为昨天
     * @return 返回不存在订单的商家
     */
    Set<Integer> queryBlockedUserReturnIds(List<ShopUser> shopUsers, Date dateTime, Integer offset);
}
