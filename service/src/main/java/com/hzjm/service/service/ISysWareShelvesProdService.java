package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysWareShelvesProd;
import com.hzjm.service.model.DTO.SysWareShelvesDealDto;
import com.hzjm.service.model.DTO.SysWareShelvesProdPageDto;
import com.hzjm.service.model.VO.SysWareShelvesProdListVo;
import com.hzjm.service.model.VO.SysWareShelvesProdVo;

import java.util.List;

/**
 * 货架商品 服务类
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
public interface ISysWareShelvesProdService extends IService<SysWareShelvesProd> {

    SysWareShelvesProd getByIdWithoutLogic(Integer id);

    SysWareShelvesProdVo getDetail(Integer id);

    Boolean saveSysWareShelvesProd(SysWareShelvesProd dto);

    Boolean insertList(List<SysWareShelvesProd> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWareShelvesProdListVo> searchList(SysWareShelvesProdPageDto dto);

    List<SysWareShelvesProd> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareShelvesProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean batchDeal(SysWareShelvesDealDto dto);

    List<SysWareShelvesProd> qyeryByWareShelvesProdByProdId(Integer prodId);
}
