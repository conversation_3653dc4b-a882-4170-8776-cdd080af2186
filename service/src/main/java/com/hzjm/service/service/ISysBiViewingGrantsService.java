package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysBiViewingGrants;
import com.hzjm.service.model.DTO.SysBiViewingGrantsPageDto;
import com.hzjm.service.model.VO.SysBiViewingGrantsListVo;
import com.hzjm.service.model.VO.SysBiViewingGrantsVo;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
public interface ISysBiViewingGrantsService extends IService<SysBiViewingGrants> {

    SysBiViewingGrants getByIdWithoutLogic(Integer id);

    SysBiViewingGrantsVo getDetail(Integer id);

    Boolean saveSysBiViewingGrants(SysBiViewingGrants dto);

    Boolean insertList(List<SysBiViewingGrants> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysBiViewingGrantsListVo> searchList(SysBiViewingGrantsPageDto dto);

    List<SysBiViewingGrants> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysBiViewingGrants> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
