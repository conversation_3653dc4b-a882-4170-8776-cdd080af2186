package com.hzjm.service.service.export.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.DownloadFileRecord;
import com.hzjm.service.entity.SysBill;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.entity.SysTask;
import com.hzjm.service.model.DTO.SysBillExportPageReq;
import com.hzjm.service.model.DTO.SysBillPageDto;
import com.hzjm.service.model.VO.SysBillListVo;
import com.hzjm.service.model.enums.SourceType;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.model.enums.SysTaskType;
import com.hzjm.service.model.event.SysTaskEvent;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.ISysBillService;
import com.hzjm.service.service.LanguageConfigService;
import com.hzjm.service.service.export.ISysBillExportService;
import com.hzjm.service.service.job.IDownloadFileRecordService;
import com.hzjm.service.service.job.ISysTaskSaveService;
import com.hzjm.service.utils.TokenUtils;
import com.hzjm.service.utils.common.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.*;

/**
 * <AUTHOR>
 * @date 2024/12/26 16:32
 * @description: ISysBill导出实现
 */
@Slf4j
@Service
public class SysBillExportServiceImpl implements ISysBillExportService {
    @Resource
    private RedisUtils redisUtils;
    @Resource
    private ISysBillService iSysBillService;
    @Resource
    private IShopUserService iShopUserService;
    @Resource
    private ISysTaskSaveService iSysTaskSaveService;
    @Resource
    private IDownloadFileRecordService iDownloadFileRecordService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Override
    public HashMap<String, String> exportShopWallet(SysBillExportPageReq req, String fileName) {
        log.info("导出商家钱包数据 , req:{}", req);
        req.setJwtContentHolder();
        HashMap<String, String> resultMap = new HashMap<>(2);
        List<List<String>> dataList = new ArrayList<>();
        dataList.add(LanguageConfigService.i18nForMsg(SYS_PROD_WALLET_EXCEL_HEADER_TEMPLATE, req.getLanguage()));
        // 当所有查询条件为空时，设置查询时间范围限制条件
        if (ObjectUtils.isEmpty(req.getOddNo()) && ObjectUtils.isEmpty(req.getOutTradeNo()) && ObjectUtils.isEmpty(req.getPlatOrderNo())
                && ObjectUtils.isEmpty(req.getShopName()) && ObjectUtils.isEmpty(req.getShopUid()) && ObjectUtils.isEmpty(req.getRelationTypeList())) {
            if (ObjectUtils.isEmpty(req.getBeginTime()) && ObjectUtils.isEmpty(req.getEndTime())) {
                // 默认查询近30天数据
                Calendar c = Calendar.getInstance();
                c.setTime(DateTimeUtils.getNow());
                c.add(Calendar.DAY_OF_MONTH, -29);
                Date start = DateTimeUtils.getMonthStart(c.getTime());
                req.setBeginTime(start);
            }
            // 如果开始时间和结束时间的跨度超过90天，则返回错误
            if (req.getBeginTime() != null && req.getEndTime() != null) {
                long diff = (req.getEndTime().getTime() - req.getBeginTime().getTime()) / 1000;
                if (diff > 3600 * 24 * 90) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("查询时间跨度不能超过90天"));
                }
            }
        }
        List<SysBillListVo> entityList = iSysBillService.searchList(req).getRecords();
        List<Map<String, String>> list = iShopUserService.getOrderIdAndPlatform();
        Map<String, String> platForm = list.stream()
                .collect(Collectors.toMap(
                        entry -> entry.get("order_number"),
                        entry -> entry.get("account")
                        , (newVal, oldValue) -> newVal));
        Map<String, String> knetOwningMap = list.stream()
                .collect(Collectors.toMap(
                        entry -> entry.get("order_number"),
                        entry -> String.valueOf(entry.get("knet_owning"))
                        , (newVal, oldValue) -> newVal));
        list.clear();
        // 填充数据
        int i = 1;
        for (SysBillListVo data : entityList) {
            List<String> cowList = new ArrayList<>();
            Map<String, String> params = BaseUtils.queryToMap(data.getAttach());
            String platOrderNo = params.get("platOrderNo");
            cowList.add(data.getOutTradeNo());
            cowList.add(BaseUtils.covertString(data.getOddNo()));
            switch (data.getRelationType()) {
                case SysBill.TypeShopCharge:
                    cowList.add(LanguageConfigService.i18nForMsg("充值", req.getLanguage()));
                    break;
                case SysBill.TypeShopDraw:
                    cowList.add(LanguageConfigService.i18nForMsg("提现", req.getLanguage()));
                    break;
                case SysProdEvent.TypeSend:
                    cowList.add(LanguageConfigService.i18nForMsg("代发", req.getLanguage()));
                    break;
                case SysProdEvent.TypeTransport:
                    cowList.add(LanguageConfigService.i18nForMsg("转运", req.getLanguage()));
                    break;
                case SysProdEvent.TypeCash:
                    cowList.add(LanguageConfigService.i18nForMsg("套现", req.getLanguage()));
                    break;
                case SysProdEvent.TypeSale:
                    cowList.add(LanguageConfigService.i18nForMsg("寄卖", req.getLanguage()));
                    break;
                case SysProdEvent.TypeTransfer:
                    cowList.add(LanguageConfigService.i18nForMsg("平台内转移", req.getLanguage()));
                    break;
                case SysBill.TypePlatSale:
                    cowList.add(LanguageConfigService.i18nForMsg("寄卖费用", req.getLanguage()));
                    break;
                case SysBill.TypeShopDeduction:
                    cowList.add(LanguageConfigService.i18nForMsg("商家扣款", req.getLanguage()));
                    break;
                case SysBill.TypeShopDrawRefund:
                    cowList.add(LanguageConfigService.i18nForMsg("提现退款", req.getLanguage()));
                    break;
                case SysBill.TypeSaleCancel:
                    cowList.add(LanguageConfigService.i18nForMsg("平台退货扣款", req.getLanguage()));
                    break;
                default:
                    cowList.add("");
            }
            cowList.set(cowList.size() - 1, cowList.get(cowList.size() - 1));
            cowList.add(BaseUtils.covertString(platOrderNo));
            cowList.add(DateTimeUtils.getDateToTimeZone(req.getTimeZone(), data.getGmtCreate()));
            if (data.getStatus() != 1) {
                cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtModify()));
            } else {
                cowList.add("");
            }
            cowList.add(BaseUtils.covertString(data.getShopUid()));
            cowList.add(BaseUtils.covertString(data.getShopName()));
            cowList.add(BaseUtils.covertString(data.getNewMoney()));
            switch (data.getStatus()) {
                case 1:
                    cowList.add(LanguageConfigService.i18nForMsg("申请中", req.getLanguage()));
                    break;
                case 2:
                    cowList.add(LanguageConfigService.i18nForMsg("成功", req.getLanguage()));
                    break;
                case 3:
                    cowList.add(LanguageConfigService.i18nForMsg("取消", req.getLanguage()));
                    break;
                default:
                    cowList.add("");
            }
            cowList.set(cowList.size() - 1, cowList.get(cowList.size() - 1));
            if (data.getRelationType() == SysProdEvent.TypeSale) {
                cowList.add(BaseUtils.covertString(params.get("remarks")));
                cowList.add(BaseUtils.covertString(params.get("sku")));
                cowList.add(BaseUtils.covertString(params.get("spec")));
                cowList.add(BaseUtils.covertString(params.get("oneId")));
                cowList.add(BaseUtils.covertString(params.get("wareName")));
                // 寄售价格
                cowList.add(BaseUtils.covertString(params.get("salePrice")));
                // 寄售到手价
                cowList.add(BaseUtils.covertString(params.get("platSoldPrice")));
                // 服务费
                cowList.add(BaseUtils.covertString(params.get("serviceFee")));
            } else {
                cowList.add("");
                cowList.add("");
                cowList.add("");
                cowList.add("");
                cowList.add("");
                cowList.add("");
                cowList.add("");
                cowList.add("");
            }
            // 最终到手价
            cowList.add((data.getIeType() == 1 ? "+" : "-") + data.getTotalFee());
            //备注
            cowList.add(data.getRemark());
            // 售卖来源
            String saleSource = ObjectUtils.isEmpty(params.get("saleSource")) ? "" : params.get("saleSource");
            String sysBillSource = iSysBillService.getSysBillSource(data.getPlatOrderNo());
            if (sysBillSource.isEmpty()) {
                cowList.add("");
            } else {
                cowList.add(saleSource.isEmpty() ? SysConstants.SYS_BILL_SOURCE_CROSS_LISTING : saleSource);
            }
            // 订单编号不能为空
            String platform = "";
            if (ObjectUtils.isEmpty(BaseUtils.covertString(platOrderNo))) {
                cowList.add("");
                cowList.add("");
            } else {
                // 寄卖平台
                platform = this.orderPlatformDisplay(platForm.get(BaseUtils.covertString(platOrderNo)));
                cowList.add(platform);
                // knet 到手价
                String knetOwning = knetOwningMap.get(BaseUtils.covertString(platOrderNo));
                if (knetOwning == null || knetOwning.equalsIgnoreCase("null") || knetOwning.trim().isEmpty()) {
                    cowList.add("");
                } else {
                    cowList.add(String.format("%.2f", Double.parseDouble(knetOwning) / 100));
                }
//                String knetOwning = knetOwningMap.get(BaseUtils.covertString(platOrderNo));
//                cowList.add(BaseUtils.covertString(knetOwning).isEmpty() ? "" : String.format("%.2f", Double.parseDouble(knetOwning) / 100));
            }
            // TTS 订单号 order_id
            if (!ObjectUtils.isEmpty(platform) && platform.equalsIgnoreCase("TTS")) {
                cowList.add(data.getPlatOrderNoTTS());
            }

            dataList.add(cowList);
            i = i + 1;
        }
        iSysBillService.setOrderNoList();
        log.info("导出商家钱包-数组组装完成");
        String fileUrl;
        try {
            fileUrl = ExcelReader.exportExcel(dataList, fileName);
            resultMap.put("fileUrl", fileUrl);
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }
        entityList.clear();
        dataList.clear();
        JwtContentHolder.clearAll();
        log.info("导出商家钱包-导出完成");
        return resultMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createExportShopWalletTask(SysBillPageDto dto, String timeZone, String language) {
        SysBillExportPageReq req = new SysBillExportPageReq();
        BeanUtil.copyProperties(dto, req);
        req.setToken(TokenUtils.getToken());
        req.setTimeZone(timeZone);
        req.setDefaultLanguage(language);
        //创建导出任务
        Integer userId = JwtContentHolder.getUserId();
        SysTask build = SysTask.builder()
                .uid(userId)
                .type(SysTaskType.DOWNLOAD_WALLET)
                .params(JSON.toJSONString(req))
                .language(req.getLanguage())
                .status(SysTaskStatus.PENDING)
                .build();
        Integer taskId = iSysTaskSaveService
                .createTask(build);
        if (taskId > 0) {
            log.info("创建商家钱包导出任务。成功，userId:{},taskId:{}", userId, taskId);
            String fileName = String.format(EXPORT_FILE_NAME_TEMPLATE,
                    LanguageConfigService.i18nForMsg("商家钱包", req.getLanguage()),
                    DateTimeUtils.getFileSuffix(),
                    BaseUtils.getRandomStr(3));
            redisUtils.putForValueAndTimeOut(DOWNLOAD_FILE_TASK_ID.concat(String.valueOf(taskId)), fileName, TWENTY_FOUR_HOUR);
            iDownloadFileRecordService.create(DownloadFileRecord.builder()
                    .uid(userId)
                    .taskId(taskId)
                    .fileName(fileName)
                    .status(SysTaskStatus.PENDING)
                    .sourceType(SourceType.ADMIN)
                    .build());
            SysTaskEvent sysTaskEvent = new SysTaskEvent(this, build);
            applicationEventPublisher.publishEvent(sysTaskEvent);
            return true;
        }
        log.error("创建商家钱包导出任务。失败 userId:{}", userId);
        return false;
    }


    /**
     * 订单平台展示
     *
     * @param raw 订单平台
     * @return 订单平台展示
     */
    private String orderPlatformDisplay(String raw) {
        if (ObjectUtils.isEmpty(raw)) {
            return "";
        }
        switch (raw) {
            case "STOCK_X_CONSIGNMENT":
                return "StockX";
            case "GOAT_STV_CONSIGNMENT":
                return "Goat";
            case "GOAT_STV_DEFECT":
                return "Goat STV";
            case "GOAT_INSTANT_SHIP_CONSIGNMENT":
                return "Knet";
            case "KICKS_CREW_CONSIGNMENT":
                return "KICKS CREW";
            case "EBAY_CONSIGNMENT":
                return "eBay";
            case "POIZON_CONSIGNMENT":
                return "POIZON";
            case "TIKTOK_SHOP":
                return "TTS";
            case "B2B_SHOP":
                return "B2B";
            default:
                return "Unknown Platform";
        }
    }
}
