package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.PlatformListingFlexStatusCount;
import com.hzjm.service.entity.StockxFlexPre;
import com.hzjm.service.model.DTO.StockxFlexPreDto;
import com.hzjm.service.model.DTO.StockxFlexPreSearchDto;
import com.hzjm.service.model.DTO.StockxFlexProdDto;
import com.hzjm.service.model.enums.FlexProdStatus;

import java.util.List;

public interface IStockxFlexPreService extends IService<StockxFlexPre>  {

    /**
     *  创建一个 新的 stockX Flex 预报
     * @param userId 用户信息
     * @return
     */
    StockxFlexPreDto create(Integer userId);

    /**
     *  根据 Pre ID 获取一个 stockX Flex 预报的详细信息
     * @param preId
     * @return
     */
    StockxFlexPre getDetailBy(String preId);


    PlatformListingFlexStatusCount searchListCount(StockxFlexPreSearchDto searchDto);

    /**
     *  获取 stockX Flex Pre 列表
     * @param searchDto
     * @return
     */
    IPage<StockxFlexPre> searchList(StockxFlexPreSearchDto searchDto);

    /**
     *  获取已经处理过的 stockX Flex pre
     * @return
     */
    List<StockxFlexPre> getProcessedFlexPre();

    /**
     * 为某一个 预报新增商品
     * @param preId 预报单号
     * @param prodDto
     * @return
     */
    boolean addProducts(String preId, List<StockxFlexProdDto> prodDto);

    /**
     *  为某一个 预报 删除商品
     * @param preId 预报单号
     * @param removeIds
     * @return
     */
    boolean removeProducts(String preId, List<Integer> removeIds);

    /**
     * 更新 预报 商品 的 状态
     * @param oneId
     * @param status
     * @return
     */
    boolean updateProductsStatus(String oneId, FlexProdStatus status);

    /**
     *  删除该 预报
     * @param preId 预报单号
     * @return
     */
    boolean deletePre(String preId);

}
