package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysWareOutBatchMapper;
import com.hzjm.service.mapper.SysWareOutBatchProdMapper;
import com.hzjm.service.model.DTO.SysWareOutBatchPageDto;
import com.hzjm.service.model.VO.SysWareOutBatchListVo;
import com.hzjm.service.model.VO.SysWareOutBatchProdListVo;
import com.hzjm.service.model.VO.SysWareOutBatchVo;
import com.hzjm.service.service.*;
import com.hzjm.service.touch.SystemServiceEndpoint;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 出库批次 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
@Slf4j
@Service
public class SysWareOutBatchServiceImpl extends ServiceImpl<SysWareOutBatchMapper, SysWareOutBatch> implements ISysWareOutBatchService {

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysAuditService iSysAuditService;

    @Autowired
    private ISysWareOutBatchUserService iSysWareOutBatchUserService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private TouchUtils touchUtils;

    @Autowired
    private SystemServiceEndpoint systemServiceEndpoint;

    @Autowired
    private SysWareOutBatchProdMapper sysWareOutBatchProdMapper;

    @Override
    public SysWareOutBatch getByIdWithoutLogic(Integer id) {
        SysWareOutBatch data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该出库批次"));
        }

        return data;
    }

    @Override
    @ReadOnly
    public SysWareOutBatchVo getDetail(Integer id) {
        SysWareOutBatch data = getByIdWithoutLogic(id);

        SysWareOutBatchVo vo = new SysWareOutBatchVo();
        BeanUtils.copyProperties(data, vo);

        List<SysWareOutBatchProdListVo> itemList = new ArrayList<>();
        vo.setBatchProdList(itemList);

        List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .eq(SysWareOutBatchProd::getBatchId, id));
        if (!ObjectUtils.isEmpty(batchProdList)) {
//            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
//                    .in(SysWareOutProd::getId, batchProdList.stream().map(SysWareOutBatchProd::getOutProdId).collect(Collectors.toList())));
//            Map<Integer, Integer> prodOutMap = outProdList.stream().collect(Collectors.toMap(SysWareOutProd::getId, SysWareOutProd::getProdId));

            List<Integer> prodIdList = batchProdList.stream().map(SysWareOutBatchProd::getProdId).collect(Collectors.toList());

            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
            Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
            prodList.clear();

            List<Integer> outIdList = BaseUtils.initList();
            outIdList.addAll(batchProdList.stream().map(SysWareOutBatchProd::getOutId).collect(Collectors.toList()));
            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                    .in(SysWareOut::getId, outIdList));
            Map<Integer, SysWareOut> outOddMap = outList.stream().collect(Collectors.toMap(SysWareOut::getId, a -> a));

            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(outList.stream().filter(a -> a.getType() == SysProdEvent.TypeSale).map(SysWareOut::getRelationId).collect(Collectors.toList()));
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
            Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
            saleList.clear();
            outList.clear();

            Map<Integer, Integer> shelvesIdMap = new HashMap<>();
            Map<Integer, String> shelvesMap = new HashMap<>();
            List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getProdId, prodIdList).in(SysWareOutProd::getOutId, outIdList));
            List<SysWareOutProd> outProdShelveList = outProdList.stream().filter(a -> !ObjectUtils.isEmpty(a.getShelvesId())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(outProdShelveList)) {
                shelvesIdMap.putAll(outProdShelveList.stream().collect(Collectors.toMap(SysWareOutProd::getProdId, SysWareOutProd::getShelvesId)));

                List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                        .in(SysWareShelves::getId, outProdShelveList.stream().map(SysWareOutProd::getShelvesId).collect(Collectors.toList())));
                shelvesMap.putAll(shelvesList.stream().collect(Collectors.toMap(SysWareShelves::getId, SysWareShelves::getName)));
            }
            outProdShelveList.clear();
            outProdList.clear();


            batchProdList.forEach(batchProd -> {
                SysWareOutBatchProdListVo item = new SysWareOutBatchProdListVo();
                BeanUtils.copyProperties(batchProd, item);

                // 历史状态回显
                if (batchProd.getStatus() == 1) {
                    if (!ObjectUtils.isEmpty(batchProd.getGmtPack())) {
                        item.setStatus(3);
                    } else if (!ObjectUtils.isEmpty(batchProd.getGmtScan())) {
                        item.setStatus(2);
                    }
                }

                // 商品信息
                SysProd prod = prodMap.get(batchProd.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    item.setOneId(prod.getOneId());
                    item.setSku(prod.getSku());
                    item.setSpec(prod.getSpec());
                }

                // 出库单信息
                SysWareOut out = outOddMap.get(batchProd.getOutId());
                if (!ObjectUtils.isEmpty(out)) {
                    item.setOutType(out.getType());
                    item.setOutOddNo(out.getOddNo());
                    item.setLabelImg(out.getLabelImg());

                    if (out.getType() == SysProdEvent.TypeSale) {
                        SysProdSale sale = saleMap.get(out.getRelationId());
                        if (!ObjectUtils.isEmpty(sale)) {
                            item.setPlatName(sale.getPlatName());
                            item.setPlatOrderNo(sale.getPlatOrderNo());
                        }
                    }
                }

                // 货架信息
                String shelvesName = shelvesMap.get(shelvesIdMap.get(prod.getId()));
                if (ObjectUtils.isEmpty(shelvesName)) {
                    shelvesName = "";
                }
                item.setShelvesName(shelvesName);

                itemList.add(item);
            });

            vo.setBatchProdList(itemList.stream().sorted(Comparator.comparing(SysWareOutBatchProdListVo::getShelvesName)).collect(Collectors.toList()));

            SysWareOutBatchProdListVo tmp = vo.getBatchProdList().get(0);
            vo.setOutType(tmp.getOutType());
            vo.setPlatName(tmp.getPlatName());
        }

        return vo;
    }

    @Override
    public Boolean saveSysWareOutBatch(SysWareOutBatch dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getOutIdList())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意出库单"));
            }

            if (iSysWareOutService.count(Wrappers.<SysWareOut>lambdaQuery()
                    .notIn(SysWareOut::getStatus, 1, 4)
                    .in(SysWareOut::getId, dto.getOutIdList())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("存在出库单出现在待处理的出库批次中"));
            }

            if (iSysWareOutService.count(Wrappers.<SysWareOut>lambdaQuery()
                    .gt(SysWareOut::getProdNum, 1)
                    .in(SysWareOut::getId, dto.getOutIdList())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("商品数量大于1的记录不可批量处理"));
            }

            if (iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                    .in(SysWareOutBatchProd::getOutId, dto.getOutIdList())
                    .eq(SysWareOutBatchProd::getBatchStatus, 1)
                    .isNull(SysWareOutBatchProd::getBatchId)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("存在出库单正在操作"));
            }
            if (iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                    .eq(SysWareOutBatchProd::getStatus, 5)
                    .in(SysWareOutBatchProd::getOutId, dto.getOutIdList())
                    .eq(SysWareOutBatchProd::getBatchStatus, 1)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("存在丢失的商品，无法生成出库批次"));
            }

            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                    .select(SysWareOut::getId, SysWareOut::getWareId, SysWareOut::getType, SysWareOut::getRelationId)
                    .in(SysWareOut::getId, dto.getOutIdList()));

            if (outList.stream().map(SysWareOut::getType).distinct().collect(Collectors.toList()).size() > 1) {
                throw new BaseException(LanguageConfigService.i18nForMsg("相同出库类别可生成批次"));
            }

            List<Integer> saleIdList = outList.stream().filter(a -> a.getType() == SysProdEvent.TypeSale).map(SysWareOut::getRelationId).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(saleIdList)) {
                List<SysProdSale> plats = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery()
                        .groupBy(SysProdSale::getPlatName)
                        .in(SysProdSale::getId, saleIdList));
                if (plats.size() > 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("相同寄售平台可生成批次"));
                }
            }

            if (JwtContentHolder.getRoleType() == 1) {
                Map<Integer, List<SysWareOut>> outGroup = outList.stream().collect(Collectors.groupingBy(SysWareOut::getWareId));
                if (outGroup.keySet().size() > 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("选中的出库单不在同一个仓库"));
                }
                dto.setWareId(outList.get(0).getWareId());
            } else {
                dto.setWareId(JwtContentHolder.getWareId());
            }
            dto.setCheckerId(JwtContentHolder.getUserId());
            dto.setBatchNo(iSysCodePoolService.build(-1, 1).get(0));
            rs = baseMapper.insert(dto) > 0;

            if (JwtContentHolder.getRoleType() == 4) {
                // 仓储人员生成批次时，默认拥有权限
                SysWareOutBatchUser batchUser = new SysWareOutBatchUser();
                batchUser.setBatchId(dto.getId());
                batchUser.setUserId(JwtContentHolder.getUserId());
                iSysWareOutBatchUserService.saveSysWareOutBatchUser(batchUser);
            }

            // 更新出库单状态
            iSysWareOutService.update(Wrappers.<SysWareOut>lambdaUpdate()
                    .in(SysWareOut::getId, dto.getOutIdList())
                    .set(SysWareOut::getStatus, 2));

            // 记录出库批次商品
            List<SysWareOutBatchProd> batchProdList = new ArrayList<>();
            List<SysWareOutProd> prodList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery().in(SysWareOutProd::getOutId, dto.getOutIdList()));
            if (!ObjectUtils.isEmpty(prodList)) {
                prodList.forEach(prod -> {
                    if (prod.getProdId() <= 0) {
                        return;
                    }
                    SysWareOutBatchProd batchProd = new SysWareOutBatchProd();
                    batchProd.setBatchId(dto.getId());
                    batchProd.setOutProdId(prod.getId());
                    batchProd.setOutId(prod.getOutId());
                    batchProd.setProdId(prod.getProdId());
                    batchProd.setStatus(1);
                    batchProdList.add(batchProd);
                });
                iSysWareOutBatchProdService.saveBatch(batchProdList);
            }
        } else if (isDelete) {
            SysWareOutBatch data = getById(dto.getId());
            if (ObjectUtils.isEmpty(data) || data.getStatus() == 2) {
                throw new BaseException(LanguageConfigService.i18nForMsg("出库批次已完成，无法删除"));
            }
            rs = baseMapper.deleteById(dto.getId()) > 0;

            List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                    .eq(SysWareOutBatchProd::getBatchId, dto.getId()));
            if (!ObjectUtils.isEmpty(batchProdList)) {
                List<Integer> outIdList = batchProdList.stream().map(SysWareOutBatchProd::getOutId).collect(Collectors.toList());

                // 重置出库单状态：仅未发货的可以重置
                iSysWareOutService.update(Wrappers.<SysWareOut>lambdaUpdate()
                        .set(SysWareOut::getStatus, 1)
                        .eq(SysWareOut::getStatus, 2)
                        .in(SysWareOut::getId, outIdList));

                // 重置出库商品状态：仅未发货的可以重置
                iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaUpdate()
                        .eq(SysWareOutBatchProd::getBatchId, dto.getId())
                        .in(SysWareOutBatchProd::getStatus, 1, 2, 3, 5));

                // 丢失的商品，流程单状态回滚
                List<Integer> loseOutIdList = batchProdList.stream().filter(a -> {
                    return a.getStatus() == 5;
                }).map(SysWareOutBatchProd::getOutId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(loseOutIdList)) {
                    List<SysWareOut> loseOutList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                            .in(SysWareOut::getType, SysProdEvent.TypeSend, SysProdEvent.TypeTransport)
                            .in(SysWareOut::getId, loseOutIdList));
                    iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                            .in(SysProdTransport::getId, loseOutList.stream().map(SysWareOut::getRelationId).collect(Collectors.toList()))
                            .eq(SysProdTransport::getStatus, 7)
                            .set(SysProdTransport::getStatus, 4));
                }
            }
        } else {
            SysWareOutBatch data = getById(dto.getId());
            if (!ObjectUtils.isEmpty(dto.getStatus()) && data.getStatus().intValue() != dto.getStatus()) {
                switch (dto.getStatus()) {
                    case 2:
                        List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                .eq(SysWareOutBatchProd::getBatchId, data.getId()));

                        if (batchProdList.stream().filter(a -> a.getStatus() == 4).collect(Collectors.toList()).size() != batchProdList.size()) {
                            // 出库可出库的
                            out(batchProdList.stream().filter(a -> {
                                return a.getStatus() == 3;
                            }).collect(Collectors.toList()), 5);

                            // 未出库任意商品的出库单状态还原为待出库
                            List<SysWareOutBatchProd> removeList = batchProdList.stream().filter(a -> {
                                return a.getStatus() == 1 || a.getStatus() == 2;
                            }).collect(Collectors.toList());
                            if (!ObjectUtils.isEmpty(removeList)) {
                                iSysWareOutService.update(Wrappers.<SysWareOut>lambdaUpdate()
                                        .set(SysWareOut::getStatus, 1)
                                        .in(SysWareOut::getId, removeList.stream().map(SysWareOutBatchProd::getOutId).collect(Collectors.toList())));
                                // 移除未出库的商品
                                iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                                        .in(SysWareOutBatchProd::getId, removeList.stream().map(SysWareOutBatchProd::getId).collect(Collectors.toList())));
                            }
                        } else {
                            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                                    .in(SysWareOut::getId, batchProdList.stream().map(SysWareOutBatchProd::getOutId).collect(Collectors.toList())));
                            Map<Integer, List<SysWareOut>> outGroup = outList.stream().collect(Collectors.groupingBy(SysWareOut::getType));

                            Date now = DateTimeUtils.getNow();
                            outGroup.keySet().forEach(type -> {
                                if (type == SysProdEvent.TypeSale) {
                                    // 流程结束：三方寄售单 完成
                                    iSysProdSaleService.update(Wrappers.<SysProdSale>lambdaUpdate()
                                            .set(SysProdSale::getStatus, 5)
                                            .set(SysProdSale::getGmtModify, now)
                                            .eq(SysProdSale::getId, outGroup.get(type).stream().map(SysWareOut::getRelationId).collect(Collectors.toList())));
                                } else if (type == SysProdEvent.TypeTransport || type == SysProdEvent.TypeSend) {
                                    // 流程结束：境外代发&转运 完成
                                    iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                            .set(SysProdTransport::getStatus, 5)
                                            .set(SysProdTransport::getGmtModify, now)
                                            .eq(SysProdTransport::getId, outGroup.get(type).stream().map(SysWareOut::getRelationId).collect(Collectors.toList())));

                                    // 更新平台审核状态：出库
                                    iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                            .set(SysAudit::getGmtModify, now)
                                            .set(SysAudit::getStatus, 6)
                                            .eq(SysAudit::getRelationId, outGroup.get(type).stream().map(SysWareOut::getRelationId).collect(Collectors.toList())));
                                }
                            });
                        }

                        iSysWareOutBatchProdService.update(Wrappers.<SysWareOutBatchProd>lambdaUpdate()
                                .set(SysWareOutBatchProd::getBatchStatus, 2)
                                .eq(SysWareOutBatchProd::getBatchId, data.getId()));

                        dto.setCheckerId(JwtContentHolder.getUserId());
                        break;
                }
            }
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysWareOutBatchListVo> searchList(SysWareOutBatchPageDto dto) {

        LambdaQueryWrapper<SysWareOutBatch> qw = buildQw(dto);

        IPage<SysWareOutBatch> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareOutBatchListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<SysWare> wareList = iSysWareService.list(Wrappers.<SysWare>lambdaQuery());
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
            wareList.clear();

            pageResult.getRecords().forEach(data -> {
                SysWareOutBatchListVo vo = new SysWareOutBatchListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setWareName(wareMap.get(data.getWareId()));
                voList.add(vo);
            });
        }

        IPage<SysWareOutBatchListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    private LambdaQueryWrapper<SysWareOutBatch> buildQw(SysWareOutBatchPageDto dto) {
        try {
            LambdaQueryWrapper<SysWareOutBatch> qw = Wrappers.<SysWareOutBatch>lambdaQuery();
            Date endTime = dto.dealEndTime();
            qw.orderByDesc(SysWareOutBatch::getGmtCreate)
                    .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysWareOutBatch::getStatus, dto.getStatus())
                    .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysWareOutBatch::getWareId, dto.getWareIdList())
                    .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareOutBatch::getWareId, dto.getWareId())
                    .in(!ObjectUtils.isEmpty(dto.getIdList()), SysWareOutBatch::getId, dto.getIdList())
                    .notIn(!ObjectUtils.isEmpty(dto.getNotIdList()), SysWareOutBatch::getId, dto.getNotIdList())
                    .like(!ObjectUtils.isEmpty(dto.getBatchNo()), SysWareOutBatch::getBatchNo, dto.getBatchNo())
                    .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareOutBatch::getGmtCreate, dto.getBeginTime())
                    .lt(!ObjectUtils.isEmpty(endTime), SysWareOutBatch::getGmtCreate, endTime);

            Integer wareId = JwtContentHolder.getWareId();
            if (!ObjectUtils.isEmpty(wareId)) {
                qw.eq(SysWareOutBatch::getWareId, wareId);

                // 不展示非今天完成的出库批次
                qw.apply(" if(gmt_modify < '" + DateTimeUtils.format(DateTimeUtils.sdfDay, DateTimeUtils.getNow())
                        + "', status != 2, 1) ");
            }

            // 任务管理，批次管理中不应该存在已完成的批次信息
            if (!ObjectUtils.isEmpty(dto.getAssign())) {
                if (!dto.getAssign()) {
                    qw.eq(SysWareOutBatch::getStatus, 1);
                } else {
                    qw.ne(ObjectUtils.isEmpty(dto.getStatus()), SysWareOutBatch::getStatus, 2);
                }
            }

            if (JwtContentHolder.getRoleType() == 4) {
                // 查看权限
                List<SysWareOutBatchUser> outUserList = iSysWareOutBatchUserService.list(Wrappers.<SysWareOutBatchUser>lambdaQuery()
                        .eq(SysWareOutBatchUser::getUserId, JwtContentHolder.getUserId()));
                List<Integer> batchIdList = BaseUtils.initList();
                batchIdList.addAll(outUserList.stream().map(SysWareOutBatchUser::getBatchId).collect(Collectors.toList()));
                qw.in(SysWareOutBatch::getId, batchIdList);
            }

            return qw;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("SysWareOutBatchServiceImpl buildQw is error ", e);
            log.error("SysWareOutBatchServiceImpl buildQw is error e.message={} ", e.getMessage());
            throw new BaseException("系统异常：" + e.getMessage());
        }

    }

    @Override
    public Boolean insertList(List<SysWareOutBatch> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareOutBatch> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean scanByOneId(String oneId, Integer type, Integer relationId, Integer relationType) {
        if (ObjectUtils.isEmpty(relationType) || ObjectUtils.isEmpty(relationId)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("清单对象不明"));
        }
        SysProd prod = iSysProdService.getOne(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getOneId, oneId));
        if (ObjectUtils.isEmpty(prod)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("无效的oneId"));
        }

        SysWareOutProd outProd = iSysWareOutProdService.getOne(Wrappers.<SysWareOutProd>lambdaQuery()
                .eq(SysWareOutProd::getProdId, prod.getId())
                .orderByDesc(SysWareOutProd::getGmtCreate).last("limit 1"));
        if (ObjectUtils.isEmpty(outProd)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("商品无需出库"));
        }
/*
        SysWareOutBatchProd batchProd = null;
        if (relationType == 1) {
            batchProd = iSysWareOutBatchProdService.getOne(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                    .orderByDesc(SysWareOutBatchProd::getGmtCreate).last("limit 1")
                    .eq(SysWareOutBatchProd::getBatchId, relationId)
                    .eq(SysWareOutBatchProd::getOutProdId, outProd.getId()));
            if (ObjectUtils.isEmpty(batchProd)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("该商品不在此出库批次中"));
            }
        } else {
            if (outProd.getOutId().intValue() != relationId) {
                throw new BaseException(LanguageConfigService.i18nForMsg("该商品不在此出库单中"));
            }
            batchProd = iSysWareOutBatchProdService.getOne(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                    .isNull(SysWareOutBatchProd::getBatchId)
                    .eq(SysWareOutBatchProd::getOutProdId, outProd.getId()));
        }*/
        SysWareOutBatchProd batchProd = iSysWareOutBatchProdService.getOne(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .orderByDesc(SysWareOutBatchProd::getGmtCreate).last("limit 1")
                .eq(SysWareOutBatchProd::getBatchStatus, 1)
                .eq(SysWareOutBatchProd::getOutProdId, outProd.getId()));

        if (ObjectUtils.isEmpty(batchProd)) {
            // 单一出库的商品记录，在扫描时生成
            batchProd = new SysWareOutBatchProd();
            batchProd.setStatus(1);
            batchProd.setOutId(outProd.getOutId());
            batchProd.setOutProdId(outProd.getId());
            batchProd.setProdId(outProd.getProdId());
            if (relationType == 1) {
                batchProd.setBatchId(relationId);
            }
        } else if (relationType == 2 && !ObjectUtils.isEmpty(batchProd.getBatchId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("该商品正在待处理的出库批次中"));
        }

        switch (relationType) {
            case 2:
                if (outProd.getOutId().intValue() != relationId) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该商品不在此出库单中"));
                }
                break;
            case 1:
                if (batchProd.getBatchId().intValue() != relationId) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该商品不在此出库批次中"));
                }
                break;
        }

        if (batchProd.getStatus() == 5) {
            throw new BaseException(LanguageConfigService.i18nForMsg("商品已丢失，如已找到请先取消丢失"));
        }

//        Calendar c = Calendar.getInstance();
//c.setTime();
        Date time = DateTimeUtils.getNow();
        batchProd.setGmtModify(time);
        switch (type) {
            // 1-拣货
            case 1:
                if (batchProd.getStatus() > 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("商品已拣货"));
                }
                batchProd.setStatus(2);
                batchProd.setGmtScan(time);

                //SysWareShelvesProd shelvesProd = iSysWareShelvesProdService.getOne(Wrappers.<SysWareShelvesProd>lambdaQuery().eq(SysWareShelvesProd::getProdId, prod.getId()));
                List<SysWareShelvesProd> list = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                        .eq(SysWareShelvesProd::getProdId, prod.getId())
                        .eq(SysWareShelvesProd::getDelFlag, 0)
                );
                SysWareShelvesProd shelvesProd = null;
                if (!list.isEmpty() && list.size() > 0) {
                    shelvesProd = list.get(0);
                }
                if (!ObjectUtils.isEmpty(shelvesProd)) {
                    iSysWareShelvesProdService.removeById(shelvesProd.getId());
                    // 商品事件：下架
                    SysProdEvent event = new SysProdEvent();
                    event.setProdId(batchProd.getProdId());
//                    event.setShopId(deal.getShopId());
                    event.setDescription("商品下架");
                    event.setType(SysProdEvent.TypeShelvesOff);
                    event.setRelationId(shelvesProd.getShelvesId());
                    event.insert();
                }
                break;
            // 2-打包
            case 2:
                if (batchProd.getStatus() > 2) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("商品已打包"));
                } else if (batchProd.getStatus() < 2) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("商品未拣货"));
                }
                batchProd.setStatus(3);
                batchProd.setGmtPack(time);
                break;
        }

        return batchProd.insertOrUpdate();
    }

    @Override
    public Boolean lose(Integer id, Integer prodId, Integer outId) {
        SysWareOutBatchProd batchProd = iSysWareOutBatchProdService.getById(id);

        if (ObjectUtils.isEmpty(batchProd)) {
            if (!ObjectUtils.isEmpty(prodId) && !ObjectUtils.isEmpty(outId)) {
                batchProd = iSysWareOutBatchProdService.getOne(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                        .eq(SysWareOutBatchProd::getOutId, outId)
                        .eq(SysWareOutBatchProd::getProdId, prodId)
                        .orderByDesc(SysWareOutBatchProd::getGmtCreate).last("limit 1")
                );

                if (ObjectUtils.isEmpty(batchProd)) {
                    SysWareOutProd outProd = iSysWareOutProdService.getOne(Wrappers.<SysWareOutProd>lambdaQuery()
                            .eq(SysWareOutProd::getOutId, outId).eq(SysWareOutProd::getProdId, prodId));
                    if (ObjectUtils.isEmpty(outProd)) {
                        throw new BaseException(LanguageConfigService.i18nForMsg("该商品无需出库"));
                    }

                    batchProd = new SysWareOutBatchProd();
                    batchProd.setProdId(prodId);
                    batchProd.setOutProdId(outProd.getId());
                    batchProd.setOutId(outId);
                    batchProd.setStatus(1);
                    batchProd.setBatchStatus(1);
                    batchProd.insert();
                }
            } else {
                throw new BaseException(LanguageConfigService.i18nForMsg("记录已失效"));
            }
        }

        if (batchProd.getStatus() == 4) {
            throw new BaseException(LanguageConfigService.i18nForMsg("商品已出库"));
        }

//        Calendar c = Calendar.getInstance();
//c.setTime(DateTimeUtils.getNow());
        Date now = DateTimeUtils.getNow();
        batchProd.setGmtModify(now);

        if (batchProd.getStatus() == 5) {
            if (!ObjectUtils.isEmpty(batchProd.getGmtPack())) {
                batchProd.setStatus(3);
            } else if (!ObjectUtils.isEmpty(batchProd.getGmtScan())) {
                batchProd.setStatus(2);
            } else {
                batchProd.setStatus(1);
            }

            // 变更流程单状态
            SysWareOut out = iSysWareOutService.getById(batchProd.getOutId());
            if (out.getType() == SysProdEvent.TypeSend || out.getType() == SysProdEvent.TypeTransport) {
                SysProdTransport transport = iSysProdTransportService.getById(out.getRelationId());
                if (!ObjectUtils.isEmpty(transport) && transport.getStatus() == 7) {
                    transport.setStatus(4);
                    transport.updateById();
                }
            }
        } else {
            batchProd.setStatus(5);

            // 变更流程单状态
            SysWareOut out = iSysWareOutService.getById(batchProd.getOutId());
            if (out.getType() == SysProdEvent.TypeSend || out.getType() == SysProdEvent.TypeTransport) {
                SysProdTransport transport = iSysProdTransportService.getById(out.getRelationId());
                if (!ObjectUtils.isEmpty(transport)) {
                    transport.setStatus(7);
                    transport.updateById();
                }
            }
        }
        return batchProd.updateById();
    }

    @Override
    public Boolean dealLose(Integer prodId, Integer type, Integer dealType) {
        SysWareOutBatchProd batchProd = iSysWareOutBatchProdService.getOne(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .eq(SysWareOutBatchProd::getProdId, prodId)
                .eq(SysWareOutBatchProd::getStatus, 5)
                .orderByDesc(SysWareOutBatchProd::getGmtCreate).last("limit 1"));
        if (ObjectUtils.isEmpty(batchProd)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("商品未丢失，无需操作"));
        }

        switch (dealType) {
            // 1-补发：出库商品状态回到待拣货
            case 1:
                iSysWareOutBatchProdService.update(Wrappers.<SysWareOutBatchProd>lambdaUpdate()
                        .setSql(" status = 1, gmt_pack = null, gmt_scan = null ")
                        .eq(SysWareOutBatchProd::getStatus, 5)
                        .eq(SysWareOutBatchProd::getProdId, batchProd.getProdId()));
                if (iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                        .eq(SysWareOutBatchProd::getStatus, 5)
                        .eq(SysWareOutBatchProd::getOutId, batchProd.getOutId())) == 0) {
                    SysWareOut out = iSysWareOutService.getById(batchProd.getOutId());
                    if (!ObjectUtils.isEmpty(out)) {
                        switch (out.getType()) {
                            case SysProdEvent.TypeSend:
                                iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                        .eq(SysProdTransport::getId, out.getRelationId())
                                        .eq(SysProdTransport::getStatus, 7).set(SysProdTransport::getStatus, 4));
                                break;
                            case SysProdEvent.TypeTransport:
                                iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                        .eq(SysProdTransport::getId, out.getRelationId())
                                        .eq(SysProdTransport::getStatus, 7).set(SysProdTransport::getStatus, 4));
                                break;
                        }
                    }
                }
                break;
            // 2-确认丢失：鞋子出库
            case 2:
                // 可出库商品
                iSysWareOutBatchProdService.update(Wrappers.<SysWareOutBatchProd>lambdaUpdate()
                        .set(SysWareOutBatchProd::getStatus, 3)
                        .eq(SysWareOutBatchProd::getStatus, 5)
                        .eq(SysWareOutBatchProd::getProdId, batchProd.getProdId()));
                batchProd.setStatus(3);
                List<SysWareOutBatchProd> batchProdList = new ArrayList<>(Arrays.asList(batchProd));

                // 出库单状态-是否完全出库：总数 == 已出库商品 + 即将出库数量 ? 完全出库 : 部分出库
                SysWareOut data = iSysWareOutService.getById(batchProd.getOutId());
                int status = data.getProdNum() == iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery().eq(SysWareOutBatchProd::getStatus, 4)) + batchProdList.size() ? 5 : 4;
                out(batchProdList, status);
                break;
        }

        return true;
    }

    @Override
    public void out(List<SysWareOutBatchProd> batchProdList, int status) {
        if (ObjectUtils.isEmpty(batchProdList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("至少要有一个已打包的商品"));
        }
        if (!ObjectUtils.isEmpty(batchProdList)) {
            Date now = DateTimeUtils.getNow();
            List<Integer> outIdList = batchProdList.stream().map(SysWareOutBatchProd::getOutId).collect(Collectors.toList());

            // 变更流程单状态：判断鞋子数量为单/多
            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                    .in(SysWareOut::getId, outIdList));
            if (!ObjectUtils.isEmpty(outList)) {
                // 审批流出库
                List<Integer> flowIdList = outList.stream().filter(a -> {
                    return a.getType() == SysProdEvent.TypeSend || a.getType() == SysProdEvent.TypeTransport;
                }).map(SysWareOut::getRelationId).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(flowIdList)) {
                    if (status == 5) {
                        // 流程结束：境外代发&转运 完成
                        iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                .set(SysProdTransport::getStatus, 5)
                                .set(SysProdTransport::getGmtModify, now)
                                .in(SysProdTransport::getId, flowIdList));

                        // 更新平台审核状态：出库
                        iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                                .set(SysAudit::getGmtModify, now)
                                .set(SysAudit::getStatus, 6)
                                .in(SysAudit::getRelationId, flowIdList));
                    }

                    List<SysProdEvent> eventList = new ArrayList<>();
                    List<Integer> prodIdList = batchProdList.stream().filter(a -> {
                        return outIdList.indexOf(a.getOutId()) != -1;
                    }).map(SysWareOutBatchProd::getProdId).collect(Collectors.toList());

                    // 商品：归属人重置，所属仓库重置
                    iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                            .in(SysProd::getId, prodIdList)
                            .setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', gmt_out = '" + DateTimeUtils.getNowByStr() + "', `status` = 6, ware_id = null, shop_id = null"));

                    List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                            .in(SysProdDeal::getRelationId, flowIdList)
                            .in(SysProdDeal::getProdId, prodIdList)
                            .in(SysProdDeal::getType, SysProdEvent.TypeSend, SysProdEvent.TypeTransport));

                    List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
                    Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
                    prodList.clear();

                    dealList.forEach(deal -> {
                        // 商品事件：出库
                        SysProdEvent event = new SysProdEvent();
                        event.setProdId(deal.getProdId());
                        event.setShopId(deal.getShopId());
                        event.setType(SysProdEvent.TypeOut);
                        event.setDescription("商品出库");
//                        event.setRelationId(data.getId());
                        if (event.getProdId() > 0)
                            eventList.add(event);
/*
                        if (deal.getType() == SysProdEvent.TypeSale) {
                            // 商品事件：寄售完成
                            SysProdEvent event6 = new SysProdEvent();
                            event6.setProdId(deal.getProdId());
                            event6.setShopId(deal.getShopId());
                            event6.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, SysProdEvent.TypeSale)));
                            event6.setDescription("商品出库，寄售完成");
                            event6.setRelationId(deal.getRelationId());
                            if (event6.getProdId() > 0)
                                eventList.add(event6);
                        }*/

                        deal.setStatus(3);
                        deal.setGmtModify(now);
                        deal.setGmtOut(now);

                        SysProd prod = prodMap.get(deal.getProdId());
                        if (!ObjectUtils.isEmpty(prod)) {
                            deal.setSupply(prod.getSupply());
                            deal.setCostPrice(prod.getCostPrice());
                            deal.setGmtIn(prod.getGmtIn());
                        }
                        deal.updateById();
                    });

                    iSysProdEventService.insertList(eventList);
                }

                if (status == 5) {
                    // 三方寄售单出库
                    List<Integer> saleOrderIdList = outList.stream().filter(a -> {
                        return a.getType() == SysProdEvent.TypeSale;
                    }).map(SysWareOut::getRelationId).collect(Collectors.toList());

                    if (!ObjectUtils.isEmpty(saleOrderIdList)) {
                        // 流程结束：三方寄售单 完成
                        iSysProdSaleService.update(Wrappers.<SysProdSale>lambdaUpdate()
                                .set(SysProdSale::getStatus, 5)
                                .set(SysProdSale::getGmtModify, now)
                                .in(SysProdSale::getId, saleOrderIdList));
                    }
                }

                // 寄售商品出库
                List<Integer> saleOutIdList = outList.stream().filter(a -> {
                    return a.getType() == SysProdEvent.TypeSale;
                }).map(SysWareOut::getId).collect(Collectors.toList());
                Map<Integer, List<SysWareOutBatchProd>> outGroup = batchProdList.stream().filter(a -> {
                    return saleOutIdList.indexOf(a.getOutId()) != -1;
                }).collect(Collectors.groupingBy(SysWareOutBatchProd::getOutId));
                outGroup.keySet().forEach(outId -> {
                    List<SysWareOutBatchProd> prodList = outGroup.get(outId);
                    List<Integer> prodIdList = prodList.stream().map(SysWareOutBatchProd::getProdId).collect(Collectors.toList());
                    iSysProdDealService.saleOut(prodIdList, outId);
                });

                iSysWareOutProdService.update(Wrappers.<SysWareOutProd>lambdaUpdate()
                        .in(SysWareOutProd::getId, batchProdList.stream().map(SysWareOutBatchProd::getOutProdId).collect(Collectors.toList()))
                        .set(SysWareOutProd::getCheckId, JwtContentHolder.getUserId()));

                // 出库后商品自动下架
                List<SysWareShelvesProd> shelvesProds = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                        .in(SysWareShelvesProd::getProdId, batchProdList.stream().map(SysWareOutBatchProd::getProdId).collect(Collectors.toList())));
                if (!ObjectUtils.isEmpty(shelvesProds)) {
                    iSysWareShelvesProdService.removeByIds(shelvesProds.stream().map(SysWareShelvesProd::getId).collect(Collectors.toList()));
                    List<SysProdEvent> eventList = new ArrayList<>();
                    shelvesProds.forEach(shelvesProd -> {
                        // 商品事件：下架
                        SysProdEvent event = new SysProdEvent();
                        event.setProdId(shelvesProd.getProdId());
//                    event.setShopId(deal.getShopId());
                        event.setDescription("商品下架");
                        event.setType(SysProdEvent.TypeShelvesOff);
                        event.setRelationId(shelvesProd.getShelvesId());
//                        event.insert();
                        eventList.add(event);
                    });
                    iSysProdEventService.insertList(eventList);
                }

                // search同步更新
                Map<Integer, String> outNoMap = outList.stream().collect(Collectors.toMap(SysWareOut::getId, SysWareOut::getOddNo));
                List<SysWareOutProd> outProdList = iSysWareOutProdService.list(Wrappers.<SysWareOutProd>lambdaQuery()
                        .in(SysWareOutProd::getId, batchProdList.stream().map(SysWareOutBatchProd::getOutProdId).collect(Collectors.toList())));
                Map<Integer, Integer> dealOutMap = outProdList.stream().collect(Collectors.toMap(SysWareOutProd::getDealId, SysWareOutProd::getOutId));
                List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                        .in(SysProdDeal::getId, outProdList.stream().map(SysWareOutProd::getDealId).collect(Collectors.toList())));
                outProdList.clear();

                List<Integer> saleIdList = BaseUtils.initList();
                saleIdList.addAll(dealList.stream().filter(a -> a.getType() == SysProdEvent.TypeSale).map(SysProdDeal::getSaleId).collect(Collectors.toList()));
                List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
                Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
                saleList.clear();

                dealList.forEach(deal -> {
                    Integer outId = dealOutMap.get(deal.getId());
                    SysProdSale sale = saleMap.get(deal.getSaleId());

                    LambdaUpdateWrapper<SysProdSearch> qw = Wrappers.<SysProdSearch>lambdaUpdate()
                            .set(SysProdSearch::getDelFlag, 0)
                            .set(SysProdSearch::getDealId, deal.getId())
                            .set(SysProdSearch::getGmtOut, now)
                            .set(SysProdSearch::getThirdPlatId, deal.getThirdPlatId())
                            .set(SysProdSearch::getPlatOrderNo, deal.getPlatOrderNo())
                            .set(SysProdSearch::getStatus, 6)
                            .eq(SysProdSearch::getProdId, deal.getProdId())
                            .eq(SysProdSearch::getSearchType, 1);

                    if (!ObjectUtils.isEmpty(outId)) {
                        qw.set(SysProdSearch::getOutNo, outNoMap.get(outId));
                    }
                    if (!ObjectUtils.isEmpty(sale)) {
                        qw.set(SysProdSearch::getThirdPlatName, sale.getPlatName());
                    }

                    // search同步更新
                    iSysProdSearchService.update(qw);
                });
                dealList.clear();
            }

            // 更新出库批次商品记录的状态以及出库时间
            iSysWareOutBatchProdService.update(Wrappers.<SysWareOutBatchProd>lambdaUpdate()
                    .set(SysWareOutBatchProd::getStatus, 4)
                    .set(SysWareOutBatchProd::getGmtOut, now)
                    .in(SysWareOutBatchProd::getId, batchProdList.stream().map(SysWareOutBatchProd::getId).collect(Collectors.toList())));

            // 变更出库单状态：判断鞋子数量为单/多
            iSysWareOutService.update(Wrappers.<SysWareOut>lambdaUpdate().in(SysWareOut::getId, outIdList)
                    .set(SysWareOut::getStatus, status));

            // 商品出库
            List<Integer> prodIdList = batchProdList.stream().map(SysWareOutBatchProd::getProdId).collect(Collectors.toList());
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .select(SysProd::getOneId)
                    .in(SysProd::getId, prodIdList));
            List<String> oneIdList = prodList.stream().map(SysProd::getOneId).collect(Collectors.toList());
            try {
                touchUtils.outProduct(oneIdList);
            } catch (BaseException e) {
                log.error("Error during outProduct operation with oneIdList: {}", e.getMessage());
                e.printStackTrace();
//                AliyunSmsUtils.sendSmsByCode("17858955747", "000500");
            }
            prodList.clear();
        }
    }

    @Override
    public Integer sumProdNum(SysWareOutBatchPageDto dto) {
        LambdaQueryWrapper<SysWareOutBatch> qw = buildQw(dto);

        qw.select(SysWareOutBatch::getId);
        List<SysWareOutBatch> list = list(qw);
        List<Integer> batchIdList = BaseUtils.initList();
        batchIdList.addAll(list.stream().map(SysWareOutBatch::getId).collect(Collectors.toList()));
        list.clear();

        return iSysWareOutBatchProdService.count(Wrappers.<SysWareOutBatchProd>lambdaQuery().in(SysWareOutBatchProd::getBatchId, batchIdList));
    }

    @Override
    public List<SysWareOutBatch> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }


}
