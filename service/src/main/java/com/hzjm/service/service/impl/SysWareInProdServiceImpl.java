package com.hzjm.service.service.impl;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.ShopPackMapper;
import com.hzjm.service.mapper.SysWareInProdMapper;
import com.hzjm.service.model.DTO.SysWareInProdPageDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 入库商品 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Slf4j
@Service
public class SysWareInProdServiceImpl extends ServiceImpl<SysWareInProdMapper, SysWareInProd> implements ISysWareInProdService {

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysFileService iSysFileService;

    @Autowired
    private ISysWareInService iSysWareInService;

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Autowired
    ShopPackMapper shopPackMapper;

    @Override
    public SysWareInProd getByIdWithoutLogic(Integer id) {
        SysWareInProd data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该入库商品"));
        }

        return data;
    }

    @Override
    public SysWareInProdVo getDetail(Integer id) {
        SysWareInProd data = getByIdWithoutLogic(id);

        SysWareInProdVo vo = new SysWareInProdVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareInProd(SysWareInProd dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            SysWareIn in = iSysWareInService.getById(dto.getInId());
            if (ObjectUtils.isEmpty(in)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("入库单已失效"));
            }

            if (in.getStatus() == 3) {
                throw new BaseException(LanguageConfigService.i18nForMsg("核验已完成"));
            }

            if (ObjectUtils.isEmpty(dto.getOneId())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("商品缺少oneId"));
            }

            if (count(Wrappers.<SysWareInProd>lambdaQuery()
                    .eq(SysWareInProd::getProdId, dto.getProdId())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("商品已入库"));
            }

            if (!ObjectUtils.isEmpty(dto.getPackId()) && dto.getPackId() != 0) {
                ShopPack pack = iShopPackService.getById(dto.getPackId());
                if (ObjectUtils.isEmpty(pack)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该包裹未曾预报"));
                }

                // 商品事件：预报
                SysProdEvent event = new SysProdEvent();
                event.setProdId(dto.getProdId());
                event.setShopId(pack.getShopId());
                event.setDescription("商品预报");
                event.setType(SysProdEvent.TypePreReport);
                event.setRelationId(pack.getId());
                event.setGmtCreate(pack.getGmtCreate());
                event.insert();
            }

            if (ObjectUtils.isEmpty(dto.getPackId())) {
                dto.setPackId(0);
            }

//            dto.setCheckId(JwtContentHolder.getUserId());
            dto.setWareId(in.getWareId());
            rs = baseMapper.insert(dto) > 0;

            // 更新入库单商品数量
            iSysWareInService.update(Wrappers.<SysWareIn>lambdaUpdate()
                    .eq(SysWareIn::getId, in.getId())
                    .set(SysWareIn::getNum, count(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getInId, in.getId()))));
        } else if (isDelete) {
            SysWareInProd inProd = getById(dto.getId());
            SysProd prod = iSysProdService.getById(inProd.getProdId());
            if (!ObjectUtils.isEmpty(prod)) {
                if (prod.getStatus() != 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("商品已入库且非空闲状态，不可删除"));
                }
                iSysProdService.removeById(prod.getId());
            }
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            SysWareInProd data = getById(dto.getId());
            SysWareIn in = iSysWareInService.getById(data.getInId());
            if (in.getStatus() == 3) {
                throw new BaseException(LanguageConfigService.i18nForMsg("入库已完成，商品信息不可修改"));
            }

            rs = baseMapper.updateById(dto) > 0;
        }

        if (!isDelete) {
            iSysFileService.resetFile(dto.getImgList(), dto.getId(), SysFile.TypeInProdCheck);
        }

        return rs;
    }

    @Override
    @ReadOnly
    @Deprecated
    public IPage<SysWareInProdListVo> searchList(SysWareInProdPageDto dto) {

        LambdaQueryWrapper<SysWareInProd> qw = buildQw(dto);

        IPage<SysWareInProd> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareInProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                    .in(ShopPack::getId, pageResult.getRecords().stream().filter(a -> {
                        return !ObjectUtils.isEmpty(a.getPackId());
                    }).map(SysWareInProd::getPackId).distinct().collect(Collectors.toList())));
            Map<Integer, ShopPack> packMap = packList.stream().collect(Collectors.toMap(ShopPack::getId, a -> a));
            packList.clear();

            List<ShopUser> shopList = iShopUserService.list();
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
            shopList.clear();

            List<SysWareIn> inList = iSysWareInService.listWithoutLogic(Wrappers.<SysWareIn>lambdaQuery()
                    .in(SysWareIn::getId, pageResult.getRecords().stream().map(SysWareInProd::getInId).collect(Collectors.toList())));
            Map<Integer, SysWareIn> inMap = inList.stream().collect(Collectors.toMap(SysWareIn::getId, a -> a));
            inList.clear();

            List<SysWare> wareList = iSysWareService.list(Wrappers.<SysWare>lambdaQuery()
                    .in(SysWare::getId, pageResult.getRecords().stream().map(SysWareInProd::getWareId).collect(Collectors.toList())));
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
            wareList.clear();

            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery()
                    .isNotNull(SysUser::getNickname));
            Map<Integer, String> userMap = userList.stream().filter(a -> !ObjectUtils.isEmpty(a.getNickname())).collect(Collectors.toMap(SysUser::getId, SysUser::getNickname));
            userList.clear();

            List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                    .in(SysWareShelvesProd::getProdId, pageResult.getRecords().stream().map(SysWareInProd::getProdId).collect(Collectors.toList())));
            Map<Integer, Integer> prodShelvesMap = shelvesProdList.stream().collect(Collectors.toMap(SysWareShelvesProd::getProdId, SysWareShelvesProd::getShelvesId
                    // 当出现多个相同的prodID时，取最新的一条
                    , (k1, k2) -> k2
            ));
            List<Integer> shelvesIdList = BaseUtils.initList();
            shelvesIdList.addAll(shelvesProdList.stream().map(SysWareShelvesProd::getShelvesId).collect(Collectors.toList()));
            shelvesProdList.clear();
            List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery().in(SysWareShelves::getId, shelvesIdList));
            Map<Integer, String> shelvesMap = shelvesList.stream().collect(Collectors.toMap(SysWareShelves::getId, SysWareShelves::getName));
            shelvesList.clear();

            pageResult.getRecords().forEach(data -> {
                SysWareInProdListVo vo = new SysWareInProdListVo();
                BeanUtils.copyProperties(data, vo);

                ShopPack pack = packMap.get(data.getPackId());
                if (!ObjectUtils.isEmpty(pack)) {
                    vo.setLogNo(pack.getLogNo());
                }

                SysWareIn in = inMap.get(data.getInId());
                if (!ObjectUtils.isEmpty(in)) {
                    vo.setInBatchNo(in.getBatchNo());
                    vo.setInType(in.getType());
                    vo.setGmtIn(in.getGmtModify());
                    vo.setChecker(userMap.get(Optional.ofNullable(in.getStaffId()).orElse(in.getCreateById())));

                    // 归属者
                    ShopUser shop = shopMap.get(in.getShopId());
                    if (!ObjectUtils.isEmpty(shop)) {
                        vo.setShopUid(shop.getUid());
                        vo.setShopName(shop.getRealname());
                    }
                }

                Integer shelvesId = prodShelvesMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(shelvesId)) {
                    vo.setShelvesName(shelvesMap.get(shelvesId));
                }

                vo.setWareName(wareMap.get(data.getWareId()));
//                vo.setChecker(userMap.get(data.getCheckId())); // 此处的操作员意为入库单生成人，因此不使用此行代码

                voList.add(vo);
            });
        }

        IPage<SysWareInProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    private LambdaQueryWrapper<SysWareInProd> buildQw(SysWareInProdPageDto dto) {

        LambdaQueryWrapper<SysWareInProd> qw = Wrappers.<SysWareInProd>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareInProd::getGmtCreate)
                .eq(SysWareInProd::getDelFlag, 0)
                .like(!ObjectUtils.isEmpty(dto.getSku()), SysWareInProd::getSku, dto.getSku())
                .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysWareInProd::getSku, dto.getSkuList())
                .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysWareInProd::getSpec, dto.getSpec())
                .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysWareInProd::getSpec, dto.getSpecList())
                .like(!ObjectUtils.isEmpty(dto.getRemarks()), SysWareInProd::getRemarks, dto.getRemarks())
                .like(!ObjectUtils.isEmpty(dto.getBrand()), SysWareInProd::getBrand, dto.getBrand())
                .like(!ObjectUtils.isEmpty(dto.getOneId()), SysWareInProd::getOneId, dto.getOneId())
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysWareInProd::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysWareInProd::getWareId, dto.getWareId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareInProd::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareInProd::getGmtCreate, endTime);

        if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
            StringBuffer sb = new StringBuffer();
            dto.getOneIdList().forEach(oneId -> {
                sb.append("(one_id like '%" + oneId + "%') or ");
            });
            qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
        }

        if (!ObjectUtils.isEmpty(dto.getType())
                || !ObjectUtils.isEmpty(dto.getInBatchNo()) || !ObjectUtils.isEmpty(dto.getInBatchNoList())
                || !ObjectUtils.isEmpty(dto.getIdList()) || !ObjectUtils.isEmpty(dto.getShopUid())) {
            List<Integer> shopIdList = null;
            if (!ObjectUtils.isEmpty(dto.getShopUid()) && !dto.getShopUid().equals("无主件")) {
                List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().like(ShopUser::getUid, dto.getShopUid()));
                shopIdList = shopList.stream().map(ShopUser::getId).collect(Collectors.toList());
                shopList.clear();
            }

            String apply = "";
            if (!ObjectUtils.isEmpty(dto.getInBatchNoList())) {
                StringBuffer sb = new StringBuffer();
                dto.getInBatchNoList().forEach(batchNo -> {
                    sb.append("(batch_no like '%" + batchNo + "%') or ");
                });
                apply = " (" + sb.substring(0, sb.length() - 4) + ") ";
            }

            List<Integer> inIdList = BaseUtils.initList();
            List<SysWareIn> inList = iSysWareInService.list(Wrappers.<SysWareIn>lambdaQuery().select(SysWareIn::getId)
                    .in(!ObjectUtils.isEmpty(dto.getIdList()), SysWareIn::getId, dto.getIdList())
                    .apply(!ObjectUtils.isEmpty(apply), apply)
                    .isNull(!ObjectUtils.isEmpty(dto.getShopUid()) && dto.getShopUid().equals("无主件"), SysWareIn::getShopId)
                    .in(!ObjectUtils.isEmpty(shopIdList), SysWareIn::getShopId, shopIdList)
                    .like(!ObjectUtils.isEmpty(dto.getInBatchNo()), SysWareIn::getBatchNo, dto.getInBatchNo())
                    .eq(!ObjectUtils.isEmpty(dto.getType()), SysWareIn::getType, dto.getType()));
            dto.setInIdList(inList.stream().map(SysWareIn::getId).collect(Collectors.toList()));

            inIdList.addAll(inList.stream().map(SysWareIn::getId).collect(Collectors.toList()));
            qw.in(SysWareInProd::getInId, inIdList);

            inList.clear();
        }

        if (!ObjectUtils.isEmpty(dto.getChecker())) {
            List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery().like(SysUser::getNickname, dto.getChecker()));
            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(userList.stream().map(SysUser::getId).collect(Collectors.toList()));
            qw.in(SysWareInProd::getCheckId, userIdList);
            userList.clear();
        }

        if (ObjectUtils.isEmpty(dto.getLogNoList()) && !ObjectUtils.isEmpty(dto.getLogNo())) {
            dto.setLogNoList(new ArrayList<>(Arrays.asList(dto.getLogNo())));
        }
        if (!ObjectUtils.isEmpty(dto.getLogNoList())) {
            StringBuffer sb = new StringBuffer();
            dto.getLogNoList().forEach(logNo -> {
                sb.append("(log_no like '%" + logNo + "%') or ");
            });
            List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery().ne(ShopPack::getStatus, 3)
                            .apply(" (" + sb.substring(0, sb.length() - 4) + ") ")
//                    .like(ShopPack::getLogNo, dto.getLogNo())
            );
            List<Integer> packIdList = BaseUtils.initList();
            packIdList.addAll(packList.stream().map(ShopPack::getId).collect(Collectors.toList()));
            packList.clear();

            qw.in(SysWareInProd::getPackId, packIdList);
        }
        return qw;
    }

    @Override
    public Boolean insertList(List<SysWareInProd> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareInProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Map<String, List<SysWareInProdListVo>> listByIn(SysWareIn in) {
        Map<String, List<SysWareInProdListVo>> voMap = new HashMap<>();
        if (ObjectUtils.isEmpty(in)) {
            return voMap;
        }

        List<SysWareInProd> list = list(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getInId, in.getId()));

        List<Integer> packIdList = BaseUtils.initList();
        Map<Integer, SysProd> prodMap = new HashMap<>();
        Map<Integer, List<String>> fileMap = new HashMap<>();
        Map<Integer, List<SysWareInProd>> packTree = new HashMap<>();

        List<ShopPack> scanPackList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                .eq(ShopPack::getInId, in.getId())
                .in(ShopPack::getStatus, 2, 4));
        if (!ObjectUtils.isEmpty(scanPackList)) {
            packIdList.addAll(scanPackList.stream().map(ShopPack::getId).collect(Collectors.toList()));
        }

        // 已添加商品的
        if (!ObjectUtils.isEmpty(list)) {
            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .in(SysProd::getId, list.stream().map(SysWareInProd::getProdId).collect(Collectors.toList())));
            prodMap.putAll(prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a)));

            fileMap.putAll(iSysFileService.getFileMap(
                    list.stream().map(SysWareInProd::getId).collect(Collectors.toList()), SysFile.TypeInProdCheck));

            packTree.putAll(list.stream().collect(Collectors.groupingBy(SysWareInProd::getPackId)));
            packIdList.addAll(packTree.keySet().stream().collect(Collectors.toList()));
        }

        List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                .in(ShopPack::getId, packIdList));
        Map<Integer, String> noMap = new HashMap<Integer, String>() {{
            put(0, "NO_LOGNO");
        }};
        noMap.putAll(packList.stream().collect(Collectors.toMap(ShopPack::getId, ShopPack::getLogNo)));

        noMap.keySet().forEach(packId -> {
            List<SysWareInProdListVo> voList = new ArrayList<>();

            List<SysWareInProd> itemList = packTree.get(packId);
            if (!ObjectUtils.isEmpty(itemList)) {
                itemList.stream().sorted(Comparator.comparing(SysWareInProd::getGmtCreate).reversed())
                        .forEach(data -> {
                            SysWareInProdListVo vo = new SysWareInProdListVo();
                            BeanUtils.copyProperties(data, vo);

                            SysProd prod = prodMap.get(data.getProdId());
                            if (!ObjectUtils.isEmpty(prod)) {
                                vo.setSpec(prod.getSpec());
                                vo.setImg(prod.getImg());
                                vo.setSku(prod.getSku());
                                vo.setOneId(prod.getOneId());
                            }

                            vo.setImgList(fileMap.get(data.getId()));
                            List<ShopPack> shopPacks = shopPackMapper.listWithoutLogic(Wrappers.<ShopPack>lambdaQuery()
                                    .eq(ShopPack::getInId, data.getInId())
                                    .eq(ShopPack::getDelFlag, 0)
                                    .isNotNull(ShopPack::getReason)
                                    .orderByDesc(ShopPack::getGmtCreate)
                                    .last("LIMIT 1")
                            );
                            vo.setReturnedReason(shopPacks.size() > 0 ? shopPacks.get(0).getReason() : "");
                            voList.add(vo);
                        });
            }

            String logNo = noMap.get(packId);
            if (!logNo.equals("NO_LOGNO") || voList.size() > 0) {
                voMap.put(logNo, voList);
            }
        });

        return voMap;
    }

    @Override
    public Map<String, SysWareInPackListVo> packGroup(Map<Integer, String> packLogNoMap) {
        Map<String, SysWareInPackListVo> voMap = new HashMap<>();

        List<SysWareInProd> list = list(Wrappers.<SysWareInProd>lambdaQuery()
                .in(SysWareInProd::getPackId, packLogNoMap.keySet().stream().collect(Collectors.toList())));
        if (ObjectUtils.isEmpty(list)) {
            return new HashMap<>();
        }

        Map<Integer, Integer> packInIdMap = new HashMap<>();
        list.forEach(item -> {
            packInIdMap.put(item.getPackId(), item.getInId());
        });

        List<SysWareIn> inList = iSysWareInService.list(Wrappers.<SysWareIn>lambdaQuery()
                .in(SysWareIn::getId, list.stream().map(SysWareInProd::getInId).collect(Collectors.toList())));
        Map<Integer, SysWareIn> inMap = inList.stream().collect(Collectors.toMap(SysWareIn::getId, a -> a));

        List<SysWare> wareList = iSysWareService.list(Wrappers.<SysWare>lambdaUpdate().in(SysWare::getId, inList.stream().map(SysWareIn::getWareId).collect(Collectors.toList())));
        Map<Integer, String> nameMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));

        Map<Integer, List<SysWareInProd>> packMap = list.stream().collect(Collectors.groupingBy(SysWareInProd::getPackId));
        packMap.keySet().forEach(packId -> {
            SysWareInPackListVo vo = new SysWareInPackListVo();

            List<SysWareInProd> itemList = packMap.get(packId);

            List<SysWareInPackListVo.SysWareInPackProdListVo> prodList = new ArrayList<>();
            Map<String, List<SysWareInProd>> groupMap = itemList.stream().collect(Collectors.groupingBy(a -> {
                return a.getSku() + "=#=" + a.getSpec();
            }));

            Integer totalNum = 0;
            for (String key : groupMap.keySet()) {
                List<SysWareInProd> prods = groupMap.get(key);
                SysWareInProd prod = prods.get(0);

                SysWareInPackListVo.SysWareInPackProdListVo item = new SysWareInPackListVo.SysWareInPackProdListVo();
                item.setOneId(prod.getOneId());
                item.setSku(prod.getSku());
                item.setSpec(prod.getSpec());
                item.setRemarks(prod.getRemarks());
                item.setImg(prod.getImg());
                item.setNum(prods.size());
                prodList.add(item);

                totalNum = totalNum + item.getNum();
            }

            SysWareIn in = inMap.get(packInIdMap.get(packId));
            if (!ObjectUtils.isEmpty(in)) {
                vo.setInId(in.getId());
                vo.setGmtIn(in.getGmtModify());
                vo.setInBatchNo(in.getBatchNo());
                vo.setWareName(nameMap.get(in.getWareId()));
                vo.setProdNum(totalNum);
                vo.setProdList(prodList);
            }

            voMap.put(packLogNoMap.get(packId), vo);
        });

        return voMap;
    }

    @Override
    @ReadOnly
    @Deprecated
    public List<SysWareInListVo> searchGroup(SysWareInProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getEndTime()) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone));
        }

        List<SysWareInListVo> groupList = new ArrayList<>();
        List<SysWareInProdListVo> dataList = searchList(dto).getRecords();

        Map<Integer, List<SysWareInProdListVo>> keyMap = new HashMap<>();
        keyMap.putAll(dataList.stream().collect(Collectors.groupingBy(SysWareInProdListVo::getInId)));
/*
        // TODO 关闭未完成验货的：暂不开启，代码保留
        if (ObjectUtils.isEmpty(dto.getSpecList()) && ObjectUtils.isEmpty(dto.getSpec())
                && ObjectUtils.isEmpty(dto.getSkuList()) && ObjectUtils.isEmpty(dto.getSku())
                && ObjectUtils.isEmpty(dto.getLogNoList()) && ObjectUtils.isEmpty(dto.getLogNo())
                && ObjectUtils.isEmpty(dto.getBrand()) && ObjectUtils.isEmpty(dto.getRemarks())
                && ObjectUtils.isEmpty(dto.getOneId()) && ObjectUtils.isEmpty(dto.getShopUid())) {
            List<SysWareIn> inList = iSysWareInService.list(Wrappers.<SysWareIn>lambdaQuery().eq(SysWareIn::getNum, 0)
                    .in(!ObjectUtils.isEmpty(dto.getInIdList()), SysWareIn::getId, dto.getInIdList()));
            Map<Integer, SysWareIn> inMap = inList.stream().collect(Collectors.toMap(SysWareIn::getId, a -> a));
            List<Integer> inIdList = inList.stream().map(SysWareIn::getId).collect(Collectors.toList());
            inList.clear();

            if (!ObjectUtils.isEmpty(inIdList)) {
                List<ShopUser> shopList = iShopUserService.list();
                Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
                shopList.clear();

                List<SysWare> wareList = iSysWareService.list();
                Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
                wareList.clear();

                List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery().isNotNull(SysUser::getNickname));
                Map<Integer, String> userMap = userList.stream().filter(a -> !ObjectUtils.isEmpty(a.getNickname())).collect(Collectors.toMap(SysUser::getId, SysUser::getNickname));
                userList.clear();

                inIdList.forEach(inId -> {
                    SysWareIn in = inMap.get(inId);

                    SysWareInListVo vo = new SysWareInListVo();
                    BeanUtils.copyProperties(in, vo);

                    vo.setChecker(userMap.get(in.getCreateById()));
                    vo.setWareName(wareMap.get(in.getWareId()));

                    // 归属者
                    ShopUser shop = shopMap.get(in.getShopId());
                    if (!ObjectUtils.isEmpty(shop)) {
                        vo.setShopUid(shop.getUid());
                        vo.setShopName(shop.getRealname());
                    }

                    groupList.add(vo);
                });
            }
        }
*/

        dataList.clear();

        keyMap.keySet().forEach(key -> {
            SysWareInListVo vo = new SysWareInListVo();

            List<SysWareInProdListVo> prodList = keyMap.get(key);
            SysWareInProdListVo sample = prodList.get(0);
            vo.setId(sample.getInId());
            vo.setBatchNo(sample.getInBatchNo());
            vo.setType(sample.getInType());
            vo.setLogNo(sample.getLogNo());
            vo.setShopUid(sample.getShopUid());
            vo.setShopName(sample.getShopName());
            vo.setGmtCreate(sample.getGmtCreate());
            vo.setWareName(sample.getWareName());
            vo.setChecker(sample.getChecker());
            vo.setProdList(prodList.stream().sorted(Comparator.comparing(a -> {
                return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                        .map(spec -> BaseUtils.dealSizeStr(spec).toString()) // 加10000是防止出现10小于2的情况
                        .orElse("");
            })).collect(Collectors.toList()));
            vo.setNum(prodList.size());
            groupList.add(vo);
        });

        return groupList.stream().sorted(Comparator.comparing(SysWareInListVo::getGmtCreate).reversed()).collect(Collectors.toList());
    }

    @ReadOnly
    @Override
    public IPage<SysWareInListVo> searchGroupNew(SysWareInProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getEndTime()) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone));
        }

        List<SysWareInProdListVo> list = this.searchListNew(dto);

        if (ObjectUtils.isEmpty(list)){
            return new Page<>(dto.getCurrent(),dto.getSize());
        }

        // 按照批次号分组
        Map<String, List<SysWareInProdListVo>> sysWareInProdListVoGroupBy = list.stream()
                .filter(vo -> !ObjectUtils.isEmpty(vo) && !ObjectUtils.isEmpty(vo.getInBatchNo()))
                .collect(Collectors.groupingBy(SysWareInProdListVo::getInBatchNo, Collectors.toList()));

        // 统计其中相同入库批次的数量
        Map<String, Integer> prodNumMap = list.stream()
                .filter(vo -> !ObjectUtils.isEmpty(vo) && !ObjectUtils.isEmpty(vo.getInBatchNo()))
                .collect(Collectors.groupingBy(
                        vo -> vo.inBatchNo,
                        Collectors.collectingAndThen(Collectors.counting(), Long::intValue)
                ));

        List<SysWareInListVo> groupList = new ArrayList<>();

        for (String batchNo : sysWareInProdListVoGroupBy.keySet()) {
            Assert.notEmpty(batchNo,"system error sysWareInProdListVo batchNo is null");

            List<SysWareInProdListVo> itemList = sysWareInProdListVoGroupBy.get(batchNo);
            // 按照gmtCreate时间倒序排列
            itemList.sort(Comparator.comparing(SysWareInProdListVo::getGmtCreate).reversed());
            
            SysWareInProdListVo sysWareInProdListVo  = new SysWareInProdListVo();
            if (!ObjectUtils.isEmpty(itemList)) {
                sysWareInProdListVo = itemList.get(0);
            }

            SysWareInListVo vo = new SysWareInListVo();
            BeanUtils.copyProperties(sysWareInProdListVo, vo);
            vo.setBatchNo(batchNo);
            vo.setNum(prodNumMap.get(batchNo));
            vo.setType(sysWareInProdListVo.getInType());
            vo.setProdList(itemList);
            groupList.add(vo);
        }
        
        // 按照创建时间倒序排列groupList
        groupList.sort(Comparator.comparing(SysWareInListVo::getGmtCreate).reversed());

        IPage<SysWareInListVo> voResult = new Page<>(dto.getCurrent(),dto.getSize());
        voResult.setRecords(groupList);
        voResult.setTotal(iSysWareInService.selectWareBatchCount(dto).getNum());
        return voResult;
    }


    @ReadOnly
    @Override
    public List<SysWareInProdListVo> searchListNew(SysWareInProdPageDto dto) {
        Assert.notNull(dto,"system error : dto is null");
        Assert.notNull(dto.getSize(),"system error : dto size is null");
        Assert.notNull(dto.getCurrent(),"system error : dto current is null");

        // 商家权限控制
        if (JwtContentHolder.getRoleType() == 5) {
            dto.setShopId(JwtContentHolder.getUserId());
        }

        return iSysWareInService.selectWareBatchList(dto);
    }

    @Override
    @ReadOnly
    @Deprecated
    public SysWareBatchCountVo groupCount(SysWareInProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getEndTime()) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone));
        }

        LambdaQueryWrapper<SysWareInProd> qw = buildQw(dto);
        qw.eq(SysWareInProd::getDelFlag, 0);

        return baseMapper.groupCount(qw);
    }

    @ReadOnly
    @Override
    @Cacheable(value = "ware_in_count", key = "#root.target.generateWareInCacheKey(#dto, #timezone)", unless = "#result == null")
    public SysWareBatchCountVo groupCountNew(SysWareInProdPageDto dto, String timezone) {
        if (BaseUtils.checkObjAllFieldsIsNull(dto) && ObjectUtils.isEmpty(dto.getEndTime()) && ObjectUtils.isEmpty(dto.getBeginTime())) {
            dto.setBeginTime(DateTimeUtils.getZonedDateStart(timezone));
        }
        return iSysWareInService.selectWareBatchCount(dto);
    }

    /**
     * @return
     */
    @Override
    public List<SysWareInProd> getDistinctSkuAndSpec() {
       return baseMapper.selectDistinctSkuAndSpec();
    }

    @Override
    public List<SysWareInProd> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 生成入库统计缓存键
     */
    public String generateWareInCacheKey(SysWareInProdPageDto dto, String timezone) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("ware_in_count:");
        keyBuilder.append("timezone:").append(timezone != null ? timezone : "").append(":");
        keyBuilder.append("beginTime:").append(dto.getBeginTime() != null ? dto.getBeginTime().getTime() : "").append(":");
        keyBuilder.append("endTime:").append(dto.getEndTime() != null ? dto.getEndTime().getTime() : "").append(":");
        keyBuilder.append("wareId:").append(dto.getWareId() != null ? dto.getWareId() : "").append(":");
        keyBuilder.append("wareIdList:").append(dto.getWareIdList() != null ? dto.getWareIdList().toString() : "").append(":");
        keyBuilder.append("shopId:").append(dto.getShopId() != null ? dto.getShopId() : "").append(":");
        keyBuilder.append("shopUid:").append(dto.getShopUid() != null ? dto.getShopUid() : "").append(":");
        keyBuilder.append("type:").append(dto.getType() != null ? dto.getType() : "").append(":");
        keyBuilder.append("inBatchNo:").append(dto.getInBatchNo() != null ? dto.getInBatchNo() : "").append(":");
        keyBuilder.append("inBatchNoList:").append(dto.getInBatchNoList() != null ? dto.getInBatchNoList().toString() : "").append(":");
        keyBuilder.append("inIdList:").append(dto.getInIdList() != null ? dto.getInIdList().toString() : "").append(":");
        keyBuilder.append("logNo:").append(dto.getLogNo() != null ? dto.getLogNo() : "").append(":");
        keyBuilder.append("logNoList:").append(dto.getLogNoList() != null ? dto.getLogNoList().toString() : "").append(":");
        keyBuilder.append("checker:").append(dto.getChecker() != null ? dto.getChecker() : "").append(":");
        keyBuilder.append("spec:").append(dto.getSpec() != null ? dto.getSpec() : "").append(":");
        keyBuilder.append("specList:").append(dto.getSpecList() != null ? dto.getSpecList().toString() : "").append(":");
        keyBuilder.append("sku:").append(dto.getSku() != null ? dto.getSku() : "").append(":");
        keyBuilder.append("skuList:").append(dto.getSkuList() != null ? dto.getSkuList().toString() : "").append(":");
        keyBuilder.append("oneId:").append(dto.getOneId() != null ? dto.getOneId() : "").append(":");
        keyBuilder.append("oneIdList:").append(dto.getOneIdList() != null ? dto.getOneIdList().toString() : "").append(":");
        keyBuilder.append("brand:").append(dto.getBrand() != null ? dto.getBrand() : "").append(":");
        keyBuilder.append("remarks:").append(dto.getRemarks() != null ? dto.getRemarks() : "");
        return keyBuilder.toString();
    }

}
