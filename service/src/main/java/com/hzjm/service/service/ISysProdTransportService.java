package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysProdTransport;
import com.hzjm.service.model.DTO.SysProdTransportPageDto;
import com.hzjm.service.model.VO.SysProdTransportCountVo;
import com.hzjm.service.model.VO.SysProdTransportListVo;
import com.hzjm.service.model.VO.SysProdTransportVo;

import java.util.List;

/**
 * 转运&代发 服务类
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface ISysProdTransportService extends IService<SysProdTransport> {

    SysProdTransport getByIdWithoutLogic(Integer id);

    SysProdTransportVo getDetail(Integer id, String oddNo);

    Boolean saveSysProdTransport(SysProdTransport dto);

    Boolean insertList(List<SysProdTransport> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProdTransportListVo> searchList(SysProdTransportPageDto dto);

    SysProdTransportCountVo getCount(SysProdTransportPageDto dto);

    List<SysProdTransport> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdTransport> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    void audit(Integer status, List<Integer> idList, String reason);

}
