package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysOperatedTeams;
import com.hzjm.service.model.DTO.SysOperatedTeamsPageDto;
import com.hzjm.service.model.VO.SysOperatedTeamsVo;

import java.util.List;

/**
 * 自营团队表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface ISysOperatedTeamsService extends IService<SysOperatedTeams> {

    SysOperatedTeams getByIdWithoutLogic(Integer id);

    SysOperatedTeamsVo getDetail(Integer id);

    Boolean saveSysOperatedTeams(SysOperatedTeams dto);

    Boolean insertList(List<SysOperatedTeams> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    // IPage<SysOperatedTeamsListVo> searchList(SysOperatedTeamsPageDto dto);

    List<SysOperatedTeamsVo> queryTeamList(SysOperatedTeamsPageDto dto);

    List<SysOperatedTeams> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysOperatedTeams> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
