package com.hzjm.service.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.ShopPackMapper;
import com.hzjm.service.model.DTO.ShopPackPageDto;
import com.hzjm.service.model.VO.ShopPackListVo;
import com.hzjm.service.model.VO.ShopPackVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 预报包裹 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-17
 */
@Slf4j
@Service
public class ShopPackServiceImpl extends ServiceImpl<ShopPackMapper, ShopPack> implements IShopPackService {

    @Autowired
    private IShopPackProdService iShopPackProdService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private IShopPreService iShopPreService;

    @Autowired
    private ISysWareInService iSysWareInService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Override
    public ShopPack getByIdWithoutLogic(Integer id) {
        ShopPack data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该预报包裹"));
        }

        return data;
    }

    @Override
    @ReadOnly
    public ShopPackVo getDetail(Integer id, String logNo) {
        ShopPack data = null;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<ShopPack>lambdaQuery().ne(ShopPack::getStatus, 3).eq(ShopPack::getLogNo, logNo));
            if (ObjectUtils.isEmpty(data)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("包裹未预约"));
            }
            id = data.getId();
        }

//        if (!ObjectUtils.isEmpty(data.getWareId()) && !ObjectUtils.isEmpty(JwtContentHolder.getWareId())
//                && data.getWareId().intValue() != JwtContentHolder.getWareId()) {
//            throw new BaseException(LanguageConfigService.i18nForMsg("该仓库没有该包裹的录入权限"));
//        }

        ShopPackVo vo = new ShopPackVo();
        BeanUtils.copyProperties(data, vo);

        // 包裹货品
        vo.setProdList(iShopPackProdService.list(Wrappers.<ShopPackProd>lambdaQuery().eq(ShopPackProd::getPackId, id)));

        ShopUser shop = iShopUserService.getById(data.getShopId());
        if (!ObjectUtils.isEmpty(shop)) {
            vo.setShopUid(shop.getUid());
            vo.setShopName(shop.getRealname());
        }

        SysWare ware = iSysWareService.getById(data.getWareId());
        if (!ObjectUtils.isEmpty(ware)) {
            vo.setWareName(ware.getName());
        }

        ShopPre pre = iShopPreService.getById(data.getPreId());
        if (!ObjectUtils.isEmpty(pre)) {
            vo.setNote(pre.getNote());
            vo.setPreBatchNo(pre.getBatchNo());
        }

        // 入库商品
        vo.setInProdGroup(iSysWareInProdService.packGroup(new HashMap<Integer, String>() {{
            put(vo.getId(), vo.getLogNo());
        }}));

        return vo;
    }

    /**
     *  传入物流单号，如果是未认领的返回ID，否则返回null
     */
    @Override
    public Integer getIdForLogNo(ShopPack dto){
        if (ObjectUtils.isEmpty(dto) || ObjectUtils.isEmpty(dto.getLogNo())) {
            return null;
        }

        ShopPack shopPack = this.getShopPackForLogNo(dto);

        if (ObjectUtils.isEmpty(shopPack) || ObjectUtils.isEmpty(shopPack.getId())) {
            return null;
        }
        // 未认领返回ID
        if (ObjectUtils.isEmpty(shopPack.getShopId())){
            return shopPack.getId();
        }

        return null;
    }

    /**
     *  通过物流单号，查预报信息
     */
    @Override
    public ShopPack getShopPackForLogNo(ShopPack dto){
        if (ObjectUtils.isEmpty(dto) || ObjectUtils.isEmpty(dto.getLogNo())) {
            return null;
        }
        // 使用物流单号的后九位查询
        ShopPack shopPack = this.getOne(Wrappers.<ShopPack>lambdaQuery()

                        .select(ShopPack::getId,ShopPack::getShopId)

                        .ne(ShopPack::getStatus, 3)
                        .eq(ShopPack::getDelFlag, 0)
                        .eq(ShopPack::getLogNoSuffix, dto.getLogNo().substring(dto.getLogNo().length() - 9))

                        .orderByDesc(ShopPack::getGmtCreate)
                , false);

        if (ObjectUtils.isEmpty(shopPack) || ObjectUtils.isEmpty(shopPack.getId())) {
            return null;
        }

        return shopPack;
    }




    @Override
    public Boolean saveShopPack(ShopPack dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        // 查询是否无主件，无主件时赋值ID， 执行修改
        if (!ObjectUtils.isEmpty(dto.getLogNo() )){
            ShopPack shopPack = getOne(
                    Wrappers.<ShopPack>lambdaQuery()
                            .eq(ShopPack::getLogNo, dto.getLogNo())
                            .isNull(ShopPack::getShopId)
                            .ne(ShopPack::getStatus, 3) // 排除平台打回
                    ,false
            );
            // 存在无主件
            log.info("ShopPackServiceImpl saveShopPack shopPack  = {}", JSON.toJSONString(shopPack));
            // shopPack 存在记录且无主件时， dto 传入商家ID
            if (!ObjectUtils.isEmpty(shopPack)) {
                dto.setId(shopPack.getId());
                dto.setShopId(dto.getShopId());
            }

        }

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (dto.getType() == 1) {
                String logNoSuffix = dto.getLogNoSuffix();
                if (ObjectUtils.isEmpty(logNoSuffix)) {
                    if (!ObjectUtils.isEmpty(dto.getLogNo())) {
                        logNoSuffix = dto.getLogNo().substring(dto.getLogNo().length() - 9);
                        dto.setLogNoSuffix(logNoSuffix);
                    }
                }
                if (count(Wrappers.<ShopPack>lambdaQuery()
                        .ne(ShopPack::getStatus, 3)
                        .eq(ShopPack::getLogNoSuffix, logNoSuffix)) > 0) {
                    log.info("ShopPackServiceImpl saveShopPack count dto = {}", JSON.toJSONString(dto));
                    throw new BaseException(LanguageConfigService.i18nForMsg("该物流单已提交预约单"));
                }
            }

            if (ObjectUtils.isEmpty(dto.getShopId())) {
                dto.setShopId(JwtContentHolder.getShopId());
            }
            rs = baseMapper.insert(dto) > 0;

            if (!ObjectUtils.isEmpty(dto.getProdList())) {
                List<ShopPackProd> prodList = new ArrayList<>();
                dto.getProdList().forEach(item -> {
                    ShopPackProd prod = new ShopPackProd();
                    BeanUtils.copyProperties(item, prod);

                    prod.setPackId(dto.getId());
                    prod.setShopId(dto.getShopId());
                    prodList.add(prod);
                });
                iShopPackProdService.insertList(prodList);
            }
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            ShopPack data = getById(dto.getId());

            dto.setLogNo(null); // 物流单号不可修改
            Date gmtIn = null;
            // 包裹认领
            if (ObjectUtils.isEmpty(data.getShopId()) && !ObjectUtils.isEmpty(dto.getShopId())) {
                SysWareIn in = iSysWareInService.getById(data.getInId());
                if (!ObjectUtils.isEmpty(in)) {
                    if (ObjectUtils.isEmpty(in.getShopId())) {
                        in.setShopId(dto.getShopId()); // 无主件认领
                        in.updateById();
                    }
                    // 入库单下的商品认领
                    List<SysWareInProd> inProds = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getInId, in.getId()));
                    List<Integer> prodIdList = BaseUtils.initList();
                    prodIdList.addAll(inProds.stream().map(SysWareInProd::getProdId).collect(Collectors.toList()));
                    iSysProdService.update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).isNull(SysProd::getShopId).set(SysProd::getShopId, dto.getShopId()));
                    // 商品表 和 search同步更新
                    ShopUser shop = iShopUserService.getById(dto.getShopId());
                    if (!ObjectUtils.isEmpty(shop)) {
                        iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                                .set(SysProd::getShopId, shop.getId())
                                .isNull(SysProd::getShopId)
                                .in(SysProd::getId, prodIdList));

                        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                                .set(SysProdSearch::getShopId, shop.getId())
                                .set(SysProdSearch::getShopUid, shop.getUid())
                                .set(SysProdSearch::getShopName, shop.getRealname())
                                .eq(SysProdSearch::getSearchType, 1)
                                .isNull(SysProdSearch::getShopId)
                                .in(SysProdSearch::getProdId, prodIdList));
                    }
                    if (!ObjectUtils.isEmpty(inProds)) {
                        gmtIn = inProds.get(0).getGmtCreate();
                    }
                    inProds.clear();
                }
            }

            if (!ObjectUtils.isEmpty(dto.getStatus()) && data.getStatus().intValue() != dto.getStatus()) {
                switch (dto.getStatus()) {
                    case 1:
                        if (data.getStatus() != 1 && data.getStatus() != 3) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("包裹已入库，您没有操作权限"));
                        }

                        // 更换货品信息
                        iShopPackProdService.remove(Wrappers.<ShopPackProd>lambdaQuery().eq(ShopPackProd::getPackId, data.getId()));
                        if (!ObjectUtils.isEmpty(dto.getProdList())) {
                            List<ShopPackProd> prodList = new ArrayList<>();
                            dto.getProdList().forEach(item -> {
                                ShopPackProd prod = new ShopPackProd();
                                BeanUtils.copyProperties(item, prod);

                                prod.setPackId(data.getId());
                                prod.setShopId(data.getShopId());
                                prodList.add(prod);
                            });
                            iShopPackProdService.insertList(prodList);
                        }
                        break;
                    case 2:
                        if (JwtContentHolder.getRoleType() != 4) {
                            throw new BaseException(LanguageConfigService.i18nForMsg("只有仓库人员可操作"));
                        }
                        break;
                }
            }

            rs = baseMapper.updateById(dto) > 0;
            if (!ObjectUtils.isEmpty(gmtIn)) {
                baseMapper.saveGmtIn(dto.getId(), DateTimeUtils.format(DateTimeUtils.sdfTime, gmtIn));
            }
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<ShopPackListVo> searchList(ShopPackPageDto dto) {

        LambdaQueryWrapper<ShopPack> qw = Wrappers.<ShopPack>lambdaQuery();

        Date endTime = dto.dealEndTime();
        Date wareEndTime = dto.dealWareEndTime();
        Date inEndTime = dto.dealInEndTime();
        qw
                .eq(!ObjectUtils.isEmpty(dto.getShopId()), ShopPack::getShopId, dto.getShopId())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), ShopPack::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getType()), ShopPack::getType, dto.getType())
                .ge(!ObjectUtils.isEmpty(dto.getWareBeginTime()), ShopPack::getGmtWare, dto.getWareBeginTime())
                .lt(!ObjectUtils.isEmpty(wareEndTime), ShopPack::getGmtWare, wareEndTime)
                .ge(!ObjectUtils.isEmpty(dto.getInBeginTime()), ShopPack::getGmtIn, dto.getInBeginTime())
                .lt(!ObjectUtils.isEmpty(inEndTime), ShopPack::getGmtIn, inEndTime)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopPack::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopPack::getGmtCreate, endTime);

        if (ObjectUtils.isEmpty(dto.getStatusList())) {
            qw.eq(!ObjectUtils.isEmpty(dto.getStatus()), ShopPack::getStatus, dto.getStatus());
        }
        else {
            qw.in(ShopPack::getStatus, dto.getStatusList());
        }

        if (!ObjectUtils.isEmpty(dto.getWareIdList())) {
            qw.in(ShopPack::getWareId, dto.getWareIdList());
        }

        if (ObjectUtils.isEmpty(dto.getLogNoList()) && !ObjectUtils.isEmpty(dto.getLogNo())) {
            dto.setLogNoList(new ArrayList<>(Arrays.asList(dto.getLogNo())));
        }
        if (!ObjectUtils.isEmpty(dto.getLogNoList())) {
            StringBuffer sb = new StringBuffer();
            dto.getLogNoList().forEach(logNo -> {
                sb.append("(log_no like '%" + logNo + "%') or ");
            });
            qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
        }

        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            qw.in(ShopPack::getShopId, shopIdPowerList);
        }

        if (!ObjectUtils.isEmpty(dto.getShopUid())) {
            if (dto.getShopUid().equals("无主件")) {
                qw.isNull(ShopPack::getShopId);
            } else {
                List<Integer> shopIdList = BaseUtils.initList();
                List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().like(ShopUser::getUid, dto.getShopUid()));
                shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
                qw.in(ShopPack::getShopId, shopIdList);
            }
        }

        if (!ObjectUtils.isEmpty(dto.getSku()) || !ObjectUtils.isEmpty(dto.getSpec())
                || !ObjectUtils.isEmpty(dto.getSkuList()) || !ObjectUtils.isEmpty(dto.getSpecList())) {
            List<Integer> packIdList = BaseUtils.initList();
            LambdaQueryWrapper<ShopPackProd> qw1 = Wrappers.<ShopPackProd>lambdaQuery()
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), ShopPackProd::getSpec, dto.getSpec())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), ShopPackProd::getSku, dto.getSku());

            if (!ObjectUtils.isEmpty(dto.getSpecList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSpecList().forEach(spec -> {
                    sb.append("(spec = '" + spec + "') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            if (!ObjectUtils.isEmpty(dto.getSkuList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSkuList().forEach(sku -> {
                    sb.append("(sku = '" + sku + "') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            List<ShopPackProd> prodList = iShopPackProdService.list(qw1);
            packIdList.addAll(prodList.stream().map(ShopPackProd::getPackId).distinct().collect(Collectors.toList()));
            qw.in(ShopPack::getId, packIdList);
        }

        if (!ObjectUtils.isEmpty(dto.getPreBatchNo())) {
            List<ShopPre> preList = iShopPreService.list(Wrappers.<ShopPre>lambdaQuery()
                    .select(ShopPre::getId)
                    .like(ShopPre::getBatchNo, dto.getPreBatchNo()));
            List<Integer> preIdList = BaseUtils.initList();
            preIdList.addAll(preList.stream().map(ShopPre::getId).collect(Collectors.toList()));
            qw.in(ShopPack::getPreId, preIdList);
        }

        if (!ObjectUtils.isEmpty(dto.getInBatchNo())) {
            dto.setInBatchNoList(new ArrayList<>(Arrays.asList(dto.getInBatchNo())));
        }

        if (!ObjectUtils.isEmpty(dto.getInBatchNoList())) {
            String apply = "";
            if (!ObjectUtils.isEmpty(dto.getInBatchNoList())) {
                StringBuffer sb = new StringBuffer();
                dto.getInBatchNoList().forEach(batchNo -> {
                    sb.append("(batch_no like '%" + batchNo + "%') or ");
                });
                apply = " (" + sb.substring(0, sb.length() - 4) + ") ";
            }
            List<SysWareIn> inList = iSysWareInService.list(Wrappers.<SysWareIn>lambdaQuery()
                    .apply(!ObjectUtils.isEmpty(apply), apply)
            );
            List<Integer> inIdList = BaseUtils.initList();
            inIdList.addAll(inList.stream().map(SysWareIn::getId).collect(Collectors.toList()));
            qw.in(ShopPack::getInId, inIdList);
        }

        Integer userId = JwtContentHolder.getUserId();
        Integer roleType = JwtContentHolder.getRoleType();
        switch (roleType) {
            case 4:
                qw.in(ShopPack::getStatus, 1, 2, 4);
                qw.and(a -> a.eq(ShopPack::getWareId, JwtContentHolder.getWareId()).or().isNull(ShopPack::getWareId));
                break;
            case 5:
                qw.eq(ShopPack::getShopId, userId);
                break;
        }

        // 传入 searchIdList
        if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getSearchIdList())){
            qw.in(ShopPack::getId, dto.getSearchIdList());
        }

        if (!ObjectUtils.isEmpty(dto.getStatus()) && dto.getStatus() == 4) {
            qw.orderByDesc(ShopPack::getGmtIn);
        } else {
            qw.orderByDesc(ShopPack::getGmtCreate);
        }

        IPage<ShopPack> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopPackListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {

            List<Integer> wareIdList = BaseUtils.initList();
            wareIdList.addAll(pageResult.getRecords().stream().filter(a -> {
                return !ObjectUtils.isEmpty(a.getWareId());
            }).map(ShopPack::getWareId).distinct().collect(Collectors.toList()));
            List<SysWare> wareList = iSysWareService.list(Wrappers.<SysWare>lambdaQuery().in(SysWare::getId, wareIdList));
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));

            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().map(ShopPack::getShopId).distinct().collect(Collectors.toList()));
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIdList));
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
            shopList.clear();

            List<ShopPre> preList = iShopPreService.list(Wrappers.<ShopPre>lambdaQuery()
                    .in(ShopPre::getId, pageResult.getRecords().stream().map(ShopPack::getPreId).collect(Collectors.toList())));
            Map<Integer, String> preMap = preList.stream().collect(Collectors.toMap(ShopPre::getId, ShopPre::getBatchNo));

            List<Integer> inIdList = BaseUtils.initList();
            inIdList.addAll(pageResult.getRecords().stream().filter(a -> {
                return !ObjectUtils.isEmpty(a.getInId());
            }).map(ShopPack::getInId).collect(Collectors.toList()));
            List<SysWareIn> inList = iSysWareInService.list(Wrappers.<SysWareIn>lambdaQuery().in(SysWareIn::getId, inIdList));
            Map<Integer, SysWareIn> inMap = inList.stream().collect(Collectors.toMap(SysWareIn::getId, a -> a));

            // 包裹内预报商品信息
            List<ShopPackProd> prodList = iShopPackProdService.list(Wrappers.<ShopPackProd>lambdaQuery()
                    .in(ShopPackProd::getPackId, pageResult.getRecords().stream().map(ShopPack::getId).collect(Collectors.toList())));
            Map<Integer, List<ShopPackProd>> prodGroup = prodList.stream().collect(Collectors.groupingBy(ShopPackProd::getPackId));

            pageResult.getRecords().forEach(data -> {
                ShopPackListVo vo = new ShopPackListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setWareName(wareMap.get(data.getWareId()));
                vo.setBatchNo(preMap.get(data.getPreId()));

                ShopUser shop = shopMap.get(data.getShopId());
                if (ObjectUtils.isEmpty(shop)) {
                    vo.setShopUid("无主件");
                } else {
                    vo.setShopUid(shop.getUid());
                    vo.setShopName(shop.getRealname());
                }
                vo.setOwnerUid(vo.getShopUid());

                SysWareIn in = inMap.get(data.getInId());
                if (!ObjectUtils.isEmpty(in)) {
                    vo.setInBatchNo(in.getBatchNo());
                    if (in.getStatus() == 3) {
                        // 查验时间
                        vo.setGmtModify(in.getGmtModify()); // 临时用
                    }
                }

                if (data.getStatus() == 4) {
                    vo.setInNum(iSysWareInProdService.count(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getPackId, data.getId())));
                }

                vo.setProdList(prodGroup.get(data.getId()));

                voList.add(vo);
            });
        }

        IPage<ShopPackListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopPack> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Integer shopId = JwtContentHolder.getShopId();
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopPack> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopPack> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    public Boolean fixLogNo(Integer id) {
        // 根据ID查询记录
        ShopPack pack = this.getById(id);
        if (ObjectUtils.isEmpty(pack)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未找到对应记录"));
        }

        // 校验状态是否可修改
        if (pack.getStatus() != 4) { // 只有已经查验的包裹才能修改物流单号
            throw new BaseException(LanguageConfigService.i18nForMsg("该记录状态不允许修改"));
        }

        // 修改物流单号
        String newLogNo = pack.getLogNo() + "_fix";
        String newLogNoSuffix = pack.getLogNoSuffix() + "_fix";

        pack.setLogNo(newLogNo);
        pack.setLogNoSuffix(newLogNoSuffix);

        // 更新记录
        return this.updateById(pack);
    }

}
