package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysBill;
import com.hzjm.service.entity.SysCharge;
import com.hzjm.service.entity.SysMoney;
import com.hzjm.service.mapper.SysChargeMapper;
import com.hzjm.service.model.DTO.SysChargePageDto;
import com.hzjm.service.model.VO.SysChargeListVo;
import com.hzjm.service.model.VO.SysChargeVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 充值记录 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Slf4j
@Service
public class SysChargeServiceImpl extends ServiceImpl<SysChargeMapper, SysCharge> implements ISysChargeService {

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Override
    public SysCharge getByIdWithoutLogic(Integer id) {
        SysCharge data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该充值记录"));
        }

        return data;
    }

    @Override
    public SysChargeVo getDetail(Integer id) {
        SysCharge data = getByIdWithoutLogic(id);

        SysChargeVo vo = new SysChargeVo();
        BeanUtils.copyProperties(data, vo);

        SysBill bill = iSysBillService.getOne(Wrappers.<SysBill>lambdaQuery().eq(SysBill::getOutTradeNo, data.getOutTradeNo()));
        if (!ObjectUtils.isEmpty(bill)) {
            Map<String, String> query = BaseUtils.queryToMap(bill.getAttach());
            if (query.containsKey("newMoney")) {
                vo.setNewMoney(new BigDecimal(query.get("newMoney")));
            }
        }
        if (ObjectUtils.isEmpty(vo.getNewMoney())) {
            SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, data.getUserId()).eq(SysMoney::getType, 5));
            if (!ObjectUtils.isEmpty(money)) {
                vo.setNewMoney(money.getMoney().add(data.getAmount()));
            }
        }
        return vo;
    }

    @Override
    public Boolean saveSysCharge(SysCharge dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getNote())) {
            dto.setNote("");
        }
        //校验随机数
        if (ObjectUtils.isEmpty(dto.getCode())) {
            dto.setCode(BaseUtils.getRandomStr(11).toUpperCase(Locale.ROOT));
        }
        QueryWrapper<SysCharge> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("code", dto.getCode());
        Integer count = baseMapper.selectCount(queryWrapper);
        if (count > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("The code number is already exists"));
        }

        if (ObjectUtils.isEmpty(dto.getId())) {
            Integer roleType = JwtContentHolder.getRoleType();
            /*
            if (ObjectUtils.isEmpty(dto.getUid())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("识别码为必填项"));
            }*/

            dto.setChargeType(1);
            //if (ObjectUtils.isEmpty(dto.getChargeType())) {
            //    throw new BaseException(LanguageConfigService.i18nForMsg("请选择充值方式"));
            //}

            if (!ObjectUtils.isEmpty(dto.getNote()) && dto.getNote().length() > 100) {
                throw new BaseException(LanguageConfigService.i18nForMsg("Remark number of words is greater than 100"));
            }

            if (roleType == 1) {
                if (ObjectUtils.isEmpty(dto.getUserId())) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("请选择要充值的对象"));
                }
            } else {
                dto.setUserId(JwtContentHolder.getUserId());
            }
            ShopUser shop = iShopUserService.getById(dto.getUserId());
            dto.setUid(shop.getUid());
            dto.setName(shop.getRealname());


            String outTradeNo = BaseUtils.getOutTradeNo(SysBill.TypeShopCharge, dto.getChargeType());
            dto.setOutTradeNo(outTradeNo);

            rs = baseMapper.insert(dto) > 0;

            // 生成流水：充值
            SysBill bill = new SysBill();
            bill.setUserId(dto.getUserId());
            bill.setUserType(5);
            bill.setIeType(1);
            bill.setOutTradeNo(outTradeNo);
            bill.setPayType(dto.getChargeType());
            bill.setTotalFee(dto.getAmount());
            bill.setRelationType(SysBill.TypeShopCharge);
            bill.setRelationId(dto.getId());
            bill.setAttach("note=" + dto.getNote() + "&");
            bill.setRemark(dto.getNote());
            iSysBillService.saveSysBill(bill);
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysChargeListVo> searchList(SysChargePageDto dto) {

        LambdaQueryWrapper<SysCharge> qw = Wrappers.<SysCharge>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysCharge::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysCharge::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysCharge::getGmtCreate, endTime);

        IPage<SysCharge> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysChargeListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysChargeListVo vo = new SysChargeListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysChargeListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysCharge> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysCharge> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysCharge> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
