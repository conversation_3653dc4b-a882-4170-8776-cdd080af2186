package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpPageResult;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysPermission;
import com.hzjm.service.entity.SysUserPermission;
import com.hzjm.service.entity.SysWareUserPermission;
import com.hzjm.service.mapper.SysPermissionMapper;
import com.hzjm.service.model.DTO.SysPermissionPageDto;
import com.hzjm.service.model.VO.SysPermissionListVo;
import com.hzjm.service.model.VO.SysPermissionVo;
import com.hzjm.service.service.ISysPermissionService;
import com.hzjm.service.service.ISysUserPermissionService;
import com.hzjm.service.service.ISysWareUserPermissionService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 后管权限 服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
@Slf4j
@Service
public class SysPermissionServiceImpl extends ServiceImpl<SysPermissionMapper, SysPermission> implements ISysPermissionService {

    @Autowired
    private ISysUserPermissionService iSysUserPermissionService;

    @Autowired
    private ISysWareUserPermissionService iSysWareUserPermissionService;

    @Override
    public SysPermission getByIdWithoutLogic(Integer id) {
        SysPermission data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public SysPermissionVo getDetail(Integer id) {
        SysPermission data = getByIdWithoutLogic(id);

        SysPermissionVo vo = new SysPermissionVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysPermission(SysPermission dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public Boolean insertList(List<SysPermission> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {
            data.setDelFlag(0);
        });

        return baseMapper.insertList(dataList) > 0;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public HttpPageResult<SysPermissionListVo> searchList(SysPermissionPageDto dto) {
        Date endTime = dto.dealEndTime();

        LambdaQueryWrapper<SysPermission> qw = Wrappers.<SysPermission>lambdaQuery()
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysPermission::getId, dto.getIdList());

        HttpPageResult<SysPermission> pageResult = new HttpPageResult();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            IPage<SysPermission> iPage = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
            BeanUtils.copyProperties(iPage, pageResult);
        } else {
            pageResult.setRecords(list(qw));
        }

        List<SysPermissionListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysPermissionListVo vo = new SysPermissionListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        HttpPageResult voResult = new HttpPageResult();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public IPage<SysPermission> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysPermission> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    @ReadOnly
    public List<SysPermission> getPermission(Integer userId, Integer type) {
        List<SysPermission> dataList = list(Wrappers.<SysPermission>lambdaQuery().eq(!ObjectUtils.isEmpty(type), SysPermission::getType, type));

        if (!ObjectUtils.isEmpty(userId)) {
            List<Integer> ids = new ArrayList<>();
            if (type == 1) {
                List<SysUserPermission> userPermissionList = iSysUserPermissionService.list(
                        Wrappers.<SysUserPermission>lambdaQuery().eq(SysUserPermission::getUserId, userId));
                if (!ObjectUtils.isEmpty(userPermissionList)) {
                    ids.addAll(userPermissionList.stream().map(SysUserPermission::getPermissionId).collect(Collectors.toList()));
                }
            }
            if (type == 2) {
                List<SysWareUserPermission> wareUserPermissionList = iSysWareUserPermissionService.list(Wrappers.<SysWareUserPermission>lambdaQuery()
                        .eq(SysWareUserPermission::getWareId, JwtContentHolder.getWareId())
                        .eq(SysWareUserPermission::getUserId, userId));
                if(!ObjectUtils.isEmpty(wareUserPermissionList)) {
                    ids.addAll(wareUserPermissionList.stream().map(SysWareUserPermission::getPermId).collect(Collectors.toList()));
                }
            }
            dataList.forEach(data -> {
                data.setIsOwn(ids.contains(data.getId()));
            });
        }

        Map<Integer, List<SysPermission>> group = dataList.stream().collect(Collectors.groupingBy(SysPermission::getParentId));
        List<SysPermission> voList = group.get(0);
        voList.forEach(vo -> {
            vo.setName(LanguageConfigService.i18nForMsg(vo.getName()));
            List<SysPermission> itemList1 = group.get(vo.getId());
            if(!ObjectUtils.isEmpty(itemList1)) {
                vo.setItemList(itemList1);
                itemList1.forEach(item1 -> {
                    item1.setName(LanguageConfigService.i18nForMsg(item1.getName()));
                    List<SysPermission> itemList2 = group.get(item1.getId());
                    if(!ObjectUtils.isEmpty(itemList2)) {
                        item1.setItemList(itemList2);
                    }
                });
            }
        });

        return voList;
    }
}
