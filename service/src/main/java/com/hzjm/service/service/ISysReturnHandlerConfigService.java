package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysReturnHandlerConfig;
import com.hzjm.service.model.DTO.SysReturnHandlerConfigPageDto;
import com.hzjm.service.model.VO.SysReturnHandlerConfigListVo;
import com.hzjm.service.model.VO.SysReturnHandlerConfigVo;

import java.util.List;

/**
 * 退货处理用户配置表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
public interface ISysReturnHandlerConfigService extends IService<SysReturnHandlerConfig> {

    SysReturnHandlerConfig getByIdWithoutLogic(Integer id);

    SysReturnHandlerConfigVo getDetail(Integer id);

    Boolean saveSysReturnHandlerConfig(SysReturnHandlerConfig dto);

    Boolean insertList(List<SysReturnHandlerConfig> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysReturnHandlerConfigListVo> searchList(SysReturnHandlerConfigPageDto dto);

    List<SysReturnHandlerConfig> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysReturnHandlerConfig> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
