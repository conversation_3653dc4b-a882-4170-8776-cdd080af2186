package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysWareOutUser;
import com.hzjm.service.model.DTO.SysWareOutUserPageDto;
import com.hzjm.service.model.VO.SysWareOutUserListVo;
import com.hzjm.service.model.VO.SysWareOutUserVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 出库单任务情况 服务类
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface ISysWareOutUserService extends IService<SysWareOutUser> {

    SysWareOutUser getByIdWithoutLogic(Integer id);

    SysWareOutUserVo getDetail(Integer id);

    Boolean saveSysWareOutUser(SysWareOutUser dto);

    Boolean insertList(List<SysWareOutUser> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysWareOutUserListVo> searchList(SysWareOutUserPageDto dto);

    List<SysWareOutUser> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareOutUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
