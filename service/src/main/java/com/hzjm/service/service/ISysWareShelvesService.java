package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysWareShelves;
import com.hzjm.service.model.DTO.PageBaseSearchDto;
import com.hzjm.service.model.DTO.SysWareShelvesBatchSaveDto;
import com.hzjm.service.model.DTO.SysWareShelvesPageDto;
import com.hzjm.service.model.VO.SysWareShelvesListVo;
import com.hzjm.service.model.VO.SysWareShelvesVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 仓库货架 服务类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
public interface ISysWareShelvesService extends IService<SysWareShelves> {

    SysWareShelves getByIdWithoutLogic(Integer id);

    SysWareShelvesVo getDetail(Integer id);

    Boolean saveSysWareShelves(SysWareShelves dto);

    Boolean insertList(List<SysWareShelves> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysWareShelvesListVo> searchList(SysWareShelvesPageDto dto);

    List<SysWareShelves> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareShelves> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<SysWareShelvesListVo> saveList(SysWareShelvesBatchSaveDto dto);

}
