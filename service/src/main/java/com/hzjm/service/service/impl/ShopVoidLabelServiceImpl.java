package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ShopVoidLabel;
import com.hzjm.service.mapper.ShopVoidLabelMapper;
import com.hzjm.service.model.DTO.ShopVoidLabelPageDto;
import com.hzjm.service.model.VO.ShopVoidLabelListVo;
import com.hzjm.service.model.VO.ShopVoidLabelVo;
import com.hzjm.service.service.IShopVoidLabelService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 已撤销的label记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
@Slf4j
@Service
public class ShopVoidLabelServiceImpl extends ServiceImpl<ShopVoidLabelMapper, ShopVoidLabel> implements IShopVoidLabelService {

    @Override
    public ShopVoidLabel getByIdWithoutLogic(Integer id) {
        ShopVoidLabel data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该已撤销的label记录表"));
        }

        return data;
    }

    @Override
    public ShopVoidLabelVo getDetail(Integer id) {
        ShopVoidLabel data = getByIdWithoutLogic(id);

        ShopVoidLabelVo vo = new ShopVoidLabelVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveShopVoidLabel(ShopVoidLabel dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<ShopVoidLabelListVo> searchList(ShopVoidLabelPageDto dto) {

        LambdaQueryWrapper<ShopVoidLabel> qw = Wrappers.<ShopVoidLabel>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopVoidLabel::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopVoidLabel::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopVoidLabel::getGmtCreate, endTime);

        IPage<ShopVoidLabel> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopVoidLabelListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopVoidLabelListVo vo = new ShopVoidLabelListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<ShopVoidLabelListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopVoidLabel> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopVoidLabel> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopVoidLabel> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
