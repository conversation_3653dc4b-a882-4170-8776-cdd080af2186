package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysOperatedShopMembers;
import com.hzjm.service.model.DTO.SysOperatedShopMembersPageDto;
import com.hzjm.service.model.VO.SysOperatedShopMembersListVo;
import com.hzjm.service.model.VO.SysOperatedShopMembersVo;

import java.util.List;

/**
 * 自营团队与商家成员关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface ISysOperatedShopMembersService extends IService<SysOperatedShopMembers> {

    SysOperatedShopMembers getByIdWithoutLogic(Integer id);

    SysOperatedShopMembersVo getDetail(Integer id);

    Boolean saveSysOperatedShopMembers(SysOperatedShopMembers dto);

    Boolean insertList(List<SysOperatedShopMembers> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysOperatedShopMembersListVo> searchList(SysOperatedShopMembersPageDto dto);

    List<SysOperatedShopMembers> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysOperatedShopMembers> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
