package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysWareOutBatchUser;
import com.hzjm.service.model.DTO.SysWareOutBatchUserPageDto;
import com.hzjm.service.model.VO.SysWareOutBatchUserListVo;
import com.hzjm.service.model.VO.SysWareOutBatchUserVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 出库批次任务情况 服务类
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
public interface ISysWareOutBatchUserService extends IService<SysWareOutBatchUser> {

    SysWareOutBatchUser getByIdWithoutLogic(Integer id);

    SysWareOutBatchUserVo getDetail(Integer id);

    Boolean saveSysWareOutBatchUser(SysWareOutBatchUser dto);

    Boolean insertList(List<SysWareOutBatchUser> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysWareOutBatchUserListVo> searchList(SysWareOutBatchUserPageDto dto);

    List<SysWareOutBatchUser> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareOutBatchUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
