package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysNoticeUser;
import com.hzjm.service.model.DTO.SysNoticeUserPageDto;
import com.hzjm.service.model.VO.SysNoticeUserListVo;
import com.hzjm.service.model.VO.SysNoticeUserVo;

import java.util.List;

/**
 * 公告用户关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-03-11
 */
public interface ISysNoticeUserService extends IService<SysNoticeUser> {

    SysNoticeUser getByIdWithoutLogic(Integer id);

    SysNoticeUserVo getDetail(Integer id);

    Boolean saveSysNoticeUser(SysNoticeUser dto);

    Boolean insertList(List<SysNoticeUser> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysNoticeUserListVo> searchList(SysNoticeUserPageDto dto);

    List<SysNoticeUser> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysNoticeUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
