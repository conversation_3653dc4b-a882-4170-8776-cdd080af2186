package com.hzjm.service.service;

import com.hzjm.service.model.DTO.req.SourcingOpportunitiesReq;

/**
 * <AUTHOR>
 * @date 2025/5/20 10:55
 * @description: 采购机会导出服务接口定义
 */
public interface ISourcingOpportunitiesExportService {

    /**
     * 导出采购机会列表
     *
     * @param req      查询条件
     * @param language 语言
     * @param timeZone 时区
     * @return 导出文件地址
     */
    String export(SourcingOpportunitiesReq req, String timeZone, String language);
}
