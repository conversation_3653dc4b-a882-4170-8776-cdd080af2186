package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpPageResult;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.entity.SysAccount;
import com.hzjm.service.service.ISysAccountService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.AppUserPageDto;
import com.hzjm.service.model.VO.AppUserListVo;
import com.hzjm.service.model.VO.AppUserVo;
import com.hzjm.service.entity.AppUser;
import com.hzjm.service.mapper.AppUserMapper;
import com.hzjm.service.service.IAppUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 用户 服务实现类
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
@Slf4j
@Service
public class AppUserServiceImpl extends ServiceImpl<AppUserMapper, AppUser> implements IAppUserService {

    @Autowired
    private ISysAccountService iSysAccountService;

    @Override
    public AppUser getByIdWithoutLogic(Integer id) {
        AppUser data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public AppUserVo getDetail(Integer id) {
        AppUser data = getByIdWithoutLogic(id);

        AppUserVo vo = new AppUserVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveAppUser(AppUser dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            if (!ObjectUtils.isEmpty(dto.getPhone())) {
                // 修改手机号登录
                if (iSysAccountService.count(Wrappers.<SysAccount>lambdaQuery()
                        .ne(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 2)
                        .eq(SysAccount::getAccount, dto.getPhone()).eq(SysAccount::getAccountType, 2)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该手机号已被占用"));
                }
                iSysAccountService.update(Wrappers.<SysAccount>lambdaUpdate()
                        .set(SysAccount::getAccount, dto.getPhone())
                        .eq(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 2)
                        .eq(SysAccount::getAccountType, 2));
            }

            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public Boolean insertList(List<AppUser> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {
            data.setGmtCreate(date);
            data.setGmtModify(date);
            data.setDelFlag(0);
        });

        return baseMapper.insertList(dataList) > 0;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public HttpPageResult<AppUserListVo> searchList(AppUserPageDto dto) {
        Date endTime = dto.dealEndTime();

        LambdaQueryWrapper<AppUser> qw = Wrappers.<AppUser>lambdaQuery().orderByDesc(AppUser::getGmtCreate)
            .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), AppUser::getGmtCreate, dto.getBeginTime())
            .lt(!ObjectUtils.isEmpty(endTime), AppUser::getGmtCreate, endTime);

        HttpPageResult<AppUser> pageResult = new HttpPageResult();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            IPage<AppUser> iPage = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
            BeanUtils.copyProperties(iPage, pageResult);
        } else {
            pageResult.setRecords(list(qw));
        }

        List<AppUserListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                AppUserListVo vo = new AppUserListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        HttpPageResult voResult = new HttpPageResult();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public IPage<AppUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<AppUser> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
