package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.ShopUserPlat;
import com.hzjm.service.model.DTO.ShopUserPlatPageDto;
import com.hzjm.service.model.VO.ShopUserPlatListVo;
import com.hzjm.service.model.VO.ShopUserPlatVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商户寄售权限 服务类
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
public interface IShopUserPlatService extends IService<ShopUserPlat> {

    ShopUserPlat getByIdWithoutLogic(Integer id);

    ShopUserPlatVo getDetail(Integer id);

    Boolean saveShopUserPlat(ShopUserPlat dto);

    Boolean insertList(List<ShopUserPlat> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopUserPlatListVo> searchList(ShopUserPlatPageDto dto);

    List<ShopUserPlat> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopUserPlat> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean reset(Integer shopId, List<Integer> platIdList);

    List<Integer> getIdList(Integer shopId);

    List<ShopUserPlat> getPlatList(Integer shopId);

    boolean hasRights(Integer shopId, Integer platId);

    ShopUserPlat getShopPlat(Integer shopId, Integer thirdPlatId);
}
