package com.hzjm.service.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.ShopPackMapper;
import com.hzjm.service.mapper.ShopPreMapper;
import com.hzjm.service.mapper.SysWareInMapper;
import com.hzjm.service.model.DTO.ShopPrePageDto;
import com.hzjm.service.model.VO.ShopPreListVo;
import com.hzjm.service.model.VO.ShopPreVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预报批次 服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-25
 */
@Slf4j
@Service
public class ShopPreServiceImpl extends ServiceImpl<ShopPreMapper, ShopPre> implements IShopPreService {

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;
    @Resource
    private ISysProdService iSysProdService;

    @Autowired
    private IShopPackProdService iShopPackProdService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Resource
    private SysWareInMapper sysWareInMapper;

    @Resource
    private ShopPackMapper shopPackMapper;

    @Override
    public ShopPre getByIdWithoutLogic(Integer id) {
        ShopPre data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该预报批次"));
        }

        return data;
    }

    @Override
    public ShopPreVo getDetail(Integer id) {
        ShopPre data = getByIdWithoutLogic(id);

        ShopPreVo vo = new ShopPreVo();
        BeanUtils.copyProperties(data, vo);

        List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery().eq(ShopPack::getPreId, id));
        if (!ObjectUtils.isEmpty(packList)) {
            List<Integer> packIdList = packList.stream().map(ShopPack::getId).collect(Collectors.toList());
            List<ShopPackProd> prodList = iShopPackProdService.list(Wrappers.<ShopPackProd>lambdaQuery().in(ShopPackProd::getPackId, packIdList));
            if (!ObjectUtils.isEmpty(prodList)) {
                Map<Integer, List<ShopPackProd>> prodMap = prodList.stream().collect(Collectors.groupingBy(ShopPackProd::getPackId));
                packList.forEach(pack -> {
                    if (ObjectUtils.isEmpty(pack.getLogNo())) {
                        pack.setLogNo("");
                    }
                    pack.setProdList(prodMap.get(pack.getId()));
                });
            }
        }
        vo.setPackList(packList);

        // 入库商品
        vo.setInProdGroup(iSysWareInProdService.packGroup(packList.stream().collect(Collectors.toMap(ShopPack::getId, ShopPack::getLogNo))));

        return vo;
    }

    // Boolean re :  true = 预报类型为退回
    public String exceptionThrows(Integer shopId1, Integer shopId2, String logNo, Boolean re) {

        if (ObjectUtils.isEmpty(shopId1) || ObjectUtils.isEmpty(shopId2)) { // 无主件允许商家预报
            return "";
        }

        if (Objects.equals(shopId1, shopId2) && !re) { // 商家自己重复预报  进行提示
            log.info("exceptionThrows 1 shopId1={}, shopId2={} ,logNo ={} ,re ={}", shopId1, shopId2, logNo, re);
            return LanguageConfigService.i18nForMsg("包裹[") + logNo + LanguageConfigService.i18nForMsg("]已有归属者");
        }

        if (!Objects.equals(shopId1, shopId2)) {
            log.info("exceptionThrows 2 shopId1={}, shopId2={} ,logNo ={} ,re ={}", shopId1, shopId2, logNo, re);
            return LanguageConfigService.i18nForMsg("这个运单号[") + logNo + LanguageConfigService.i18nForMsg("]好像遇到了点麻烦，请联系客服人员解决。");
        }
        return "";
    }

    /**
     * 校验物流单号是否可用
     */
    @Override
    public void verifyLogNo(List<String> logNos){
        for (String logNo : logNos){
            // 检查运单号是否为空
            if (ObjectUtils.isEmpty(logNo)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("运单号不能为空"));
            }
            // 检查运单号长度是否小于 9
            if (logNo.length() < 9) {
                throw new BaseException(LanguageConfigService.i18nForMsg("运单号至少要有9位"));
            }
        }

        // 队列防重
        if (logNos.stream().distinct().count() < logNos.size()) {
            throw new BaseException(LanguageConfigService.i18nForMsg("存在相同的物流单号"));
        }

        // 物流单号的后9位
        List<String> logNoSuffixs = logNos.stream().map(logNo -> logNo.substring(logNo.length() - 9)).collect(Collectors.toList());

        // 校验后9位
        if (iShopPackService.count(Wrappers.<ShopPack>lambdaQuery()
                .ne(ShopPack::getStatus, 3)
                .isNotNull(ShopPack::getShopId) // 排除无主键
                .in(ShopPack::getLogNoSuffix, logNoSuffixs)) > 0) {
            log.info("【verifyLogNo】 ShopPackServiceImpl saveShopPack  count dto = {}", JSON.toJSONString(logNos));
            throw new BaseException(LanguageConfigService.i18nForMsg("该物流单已提交预约单"));
        }

        // 校验预报信息: 物流单号已经被分配给其他商家时，
        List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery()
                .ne(ShopPack::getStatus, 3) // 排除平台打回
                .isNotNull(ShopPack::getShopId) // 排除无主键
                .in(ShopPack::getLogNo, logNos));
        if (!ObjectUtils.isEmpty(packList)){
            log.info("【verifyLogNo】 saveShopPre is error ShopPack packList ={}, currentID ={} ",JSON.toJSONString(packList), JwtContentHolder.getUserId());
            throw new BaseException(
                    LanguageConfigService.i18nForMsg("运单号[")
                            + packList.stream().map(ShopPack::getLogNo).collect(Collectors.toList()) +
                            LanguageConfigService.i18nForMsg("]好像遇到了点麻烦，请联系客服人员解决。"));
        }

        // 校验三方销售单内是否存在已分配商家物流单号，存在时返回错误信息
        List<SysProdSale> sysProdSales = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery()
                .eq(SysProdSale::getDelFlag, 0)
                .in(SysProdSale::getLogNo, logNos)
                .isNotNull(SysProdSale::getShopId) // 排除无主键
        );
        if (!ObjectUtils.isEmpty(sysProdSales)){
            log.info("【verifyLogNo】 saveShopPre is error sysProdSales ={}, currentID ={} ",JSON.toJSONString(sysProdSales), JwtContentHolder.getUserId());
            throw new BaseException(
                    LanguageConfigService.i18nForMsg("运单号[")
                            + sysProdSales.stream().map(SysProdSale::getLogNo).collect(Collectors.toList()) +
                            LanguageConfigService.i18nForMsg("]好像遇到了点麻烦，请联系客服人员解决。"));
        }

        // 校验库存内是否存在已分配商家物流单号，存在时返回错误信息
        List<SysProdSearch> sysProdSearches = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                .eq(SysProdSearch::getDelFlag, 0)
                .in(SysProdSearch::getInLogNo, logNos)
                .eq(SysProdSearch::getSearchType, 1)
                .isNotNull(SysProdSearch::getShopId) // 排除无主键
        );
        if (!ObjectUtils.isEmpty(sysProdSearches)){
            log.info("【verifyLogNo】 saveShopPre is error sysProdSearches ={}, currentID ={} ",JSON.toJSONString(sysProdSearches), JwtContentHolder.getUserId());
            throw new BaseException(
                    LanguageConfigService.i18nForMsg("运单号[")
                            + sysProdSearches.stream().map(SysProdSearch::getInLogNo).collect(Collectors.toList()) +
                            LanguageConfigService.i18nForMsg("]好像遇到了点麻烦，请联系客服人员解决。"));
        }


    }

    @Override
    public Boolean saveShopPre(ShopPre dto) {
        log.info("saveShopPre start dto ={}" ,JSON.toJSONString(dto));

        boolean rs ;
        boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        // 校验
        if (!isDelete && !ObjectUtils.isEmpty(dto.getPackList())) {
            dto.getPackList().forEach(pack -> {
                if (!ObjectUtils.isEmpty(pack.getLogNo())) {
                    pack.setLogNo(pack.getLogNo().trim());
                }
            });

            List<String> logNos = dto.getPackList().stream()
                    .map(ShopPack::getLogNo)
                    .collect(Collectors.toList()); // 运单号

            // 物流单号
            this.verifyLogNo(logNos);
        }

        // 保存信息
        if (ObjectUtils.isEmpty(dto.getId())) {
            if (count(Wrappers.<ShopPre>lambdaQuery().eq(ShopPre::getBatchNo, dto.getBatchNo())) > 0) {
//                throw new BaseException(LanguageConfigService.i18nForMsg("该预报批次已被使用，刷新页面后重试"));
                dto.setBatchNo(null);
            }
            if (ObjectUtils.isEmpty(dto.getBatchNo())) {
                dto.setBatchNo(iSysCodePoolService.build(2, 1).get(0));
            }
            dto.setStatus(1);
            if (ObjectUtils.isEmpty(dto.getShopId())) {
                dto.setShopId(JwtContentHolder.getShopId());
            }
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;

            iShopPackService.remove(Wrappers.<ShopPack>lambdaQuery().eq(ShopPack::getPreId, dto.getId()));
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }

        if (!ObjectUtils.isEmpty(dto.getPackList())) {
            ShopPre data = getById(dto.getId());
            Date now = DateTimeUtils.getNow();

            // 保存包裹信息
            dto.getPackList().forEach(pack -> {
                pack.setShopId(data.getShopId());
                pack.setType(data.getType());
                pack.setNote(data.getNote());
                pack.setGmtCreate(now);

                if (ObjectUtils.isEmpty(pack.getId())) {
                    pack.setWareId(data.getWareId());
                }
                pack.setPreId(data.getId());
                pack.setStatus(null); // 防止异常修改

                iShopPackService.saveShopPack(pack);
            });
        }

        return rs;
    }

    @Override
    public IPage<ShopPreListVo> searchList(ShopPrePageDto dto) {

        LambdaQueryWrapper<ShopPre> qw = Wrappers.<ShopPre>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopPre::getGmtCreate)
                .like(!ObjectUtils.isEmpty(dto.getBatchNo()), ShopPre::getBatchNo, dto.getBatchNo())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), ShopPre::getStatus, dto.getStatus())
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), ShopPre::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), ShopPre::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getShopId()), ShopPre::getShopId, dto.getShopId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopPre::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopPre::getGmtCreate, endTime);

        Integer shopId = JwtContentHolder.getShopId();
        qw.eq(!ObjectUtils.isEmpty(shopId), ShopPre::getShopId, shopId);

        Integer wareId = JwtContentHolder.getWareId();
        qw.eq(!ObjectUtils.isEmpty(wareId), ShopPre::getShopId, wareId);

        if (!ObjectUtils.isEmpty(dto.getShopUid())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .select(ShopUser::getId)
                    .like(ShopUser::getUid, dto.getShopUid()));
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            qw.in(ShopPre::getShopId, shopIdList);
            shopList.clear();
        }

        IPage<ShopPre> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopPreListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().filter(a -> {
                return !ObjectUtils.isEmpty(a.getShopId());
            }).map(ShopPre::getShopId).collect(Collectors.toList()));
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIdList));
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
            shopList.clear();

            pageResult.getRecords().forEach(data -> {
                ShopPreListVo vo = new ShopPreListVo();
                BeanUtils.copyProperties(data, vo);

                ShopUser shop = shopMap.get(data.getShopId());
                if (!ObjectUtils.isEmpty(shop)) {
                    vo.setShopUid(shop.getUid());
                    vo.setShopName(shop.getRealname());
                }
                voList.add(vo);
            });
        }

        IPage<ShopPreListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopPre> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopPre> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopPre> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
