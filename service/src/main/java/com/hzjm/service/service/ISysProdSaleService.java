package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.service.entity.SysProdSale;
import com.hzjm.service.model.DTO.SysProdSalePageDto;
import com.hzjm.service.model.DTO.SysProdThirdSaleDealDto;
import com.hzjm.service.model.VO.SysProdSaleCountVo;
import com.hzjm.service.model.VO.SysProdSaleListVo;
import com.hzjm.service.model.VO.SysProdSaleVo;
import com.hzjm.service.model.touch.TouchProdStatusRequest;
import com.hzjm.service.model.touch.TouchSettleRequest;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 三方寄售单 服务类
 *
 * <AUTHOR>
 * @since 2023-06-07
 */
public interface ISysProdSaleService extends IService<SysProdSale> {

    SysProdSale getByIdWithoutLogic(Integer id);

    SysProdSaleVo getDetail(Integer id);

    Boolean saveSysProdSale(SysProdSale dto);

    Boolean insertList(List<SysProdSale> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    @TrimParam
    IPage<SysProdSaleListVo> searchListNew(SysProdSalePageDto dto);

    @TrimParam
    List<SysProdSaleListVo> searchListAll(SysProdSalePageDto dto);

    SysProdSaleCountVo searchSaleListCountNew(SysProdSalePageDto dto);

    IPage<SysProdSaleListVo> searchList(SysProdSalePageDto dto);

    List<SysProdSale> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdSale> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean batchDeal(SysProdThirdSaleDealDto dto);

    Boolean settle(TouchSettleRequest dto);

    Boolean prodStatus(TouchProdStatusRequest dto);

    List<String> platList();

    Boolean close(Integer id);

    void batchLabelSave(MultipartFile archiveFile, Integer tabType);
}
