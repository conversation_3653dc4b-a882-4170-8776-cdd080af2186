package com.hzjm.service.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysMoneyManyMapper;
import com.hzjm.service.mapper.SysWithdrawMapper;
import com.hzjm.service.model.DTO.SysWithdrawPageDto;
import com.hzjm.service.model.VO.SysWithdrawListVo;
import com.hzjm.service.model.VO.SysWithdrawVo;
import com.hzjm.service.model.VO.WithdrawOutTradeNoVO;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 提现 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
@Slf4j
@Service
public class SysWithdrawServiceImpl extends ServiceImpl<SysWithdrawMapper, SysWithdraw> implements ISysWithdrawService {

    @Autowired
    private ISysMoneyService iSysMoneyService;
    @Resource
    private SysMoneyManyMapper sysMoneyManyMapper;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysBillService iSysBillService;

    @Override
    public SysWithdraw getByIdWithoutLogic(Integer id) {
        SysWithdraw data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该提现"));
        }

        return data;
    }

    @Override
    public SysWithdrawVo getDetail(Integer id) {
        SysWithdraw data = getByIdWithoutLogic(id);

        SysWithdrawVo vo = new SysWithdrawVo();
        BeanUtils.copyProperties(data, vo);

        // 银行卡识别码
        LambdaQueryWrapper<SysMoneyMany> sysMoneyManyLambdaQueryWrapper = Wrappers.<SysMoneyMany>lambdaQuery()
                .select(SysMoneyMany::getRoutingNumber, SysMoneyMany::getAccountHolderType, SysMoneyMany::getCountry)
                .eq(SysMoneyMany::getCardAccount, data.getDrawAccount())
                .isNotNull(SysMoneyMany::getRoutingNumber)
                .last("limit 1");
        SysMoneyMany sysMoney = sysMoneyManyMapper.selectOne(sysMoneyManyLambdaQueryWrapper);
        if (!ObjectUtils.isEmpty(sysMoney)
                && !ObjectUtils.isEmpty(sysMoney.getRoutingNumber())
        ) {
            vo.setRoutingNumber(sysMoney.getRoutingNumber());
        }
        // 账户持有人类型
        if (!ObjectUtils.isEmpty(sysMoney)
                && !ObjectUtils.isEmpty(sysMoney.getAccountHolderType())
        ) {
            vo.setAccountHolderType(sysMoney.getAccountHolderType());
        }
        // 国家
        if (!ObjectUtils.isEmpty(sysMoney)
                && !ObjectUtils.isEmpty(sysMoney.getCountry())
        ) {
            vo.setCountry(sysMoney.getCountry());
        }

        return vo;
    }

    /**
     * 查询所有提现过的银行卡号
     */
    @Override
    public List<String> queryAllDrawAccount() {
        return baseMapper.selectAllDrawAccount();
    }

    /**
     * 钱包和提现的关联查询
     */
    @Override
    public List<String> queryWithdrawOutTradeNo(WithdrawOutTradeNoVO dto) {
        return baseMapper.selectWithdrawOutTradeNo(dto);
    }

    /**
     * 钱包和提现的关联查询 - 支付宝
     */
    @Override
    public List<String> queryWithdrawOutTradeNoByAlibaba(WithdrawOutTradeNoVO dto) {
        return baseMapper.selectWithdrawOutTradeNoByAlibaba(dto);
    }

    @Override
    @AcquireTaskLock(exceptionMessage = "有未完成任务，请稍后再试！")
    public Boolean saveSysWithdraw(SysWithdraw dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getAmount())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请填写充值金额"));
            }

            if (ObjectUtils.isEmpty(dto.getDrawAccount())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请填写收款账号"));
            }

            // 24 小时内的提现记录
            List<SysWithdraw> withdrawals = baseMapper.getMerchantWithdrawalsLast24Hours(dto.userId);

            // 24小时内提现超过两次
            if (!ObjectUtils.isEmpty(withdrawals) && withdrawals.size() >= 2) {
                // Your withdrawal request has been denied: You have exceeded 2 withdrawals in the past 24 hours. Please try again later.
                throw new BaseException(LanguageConfigService.i18nForMsg("您的提现请求已被拒绝：在过去24小时内，您已超过2次提现。请稍后再试。"));
            }
            // 24小时内已经使用过该账号
            if (!ObjectUtils.isEmpty(withdrawals) && withdrawals.stream().anyMatch(item -> item.getDrawAccount().equals(dto.getDrawAccount()))) {
                // Your withdrawal request has been denied: You have used this account in the past 24 hours. Please try again later.
                throw new BaseException(LanguageConfigService.i18nForMsg("您的提现请求已被拒绝：在过去24小时内，您已使用过该账号。请稍后再试。"));
            }

            switch (dto.getUserType()) {
                case 5:
                    SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery()
                            .eq(SysMoney::getUserId, dto.getUserId())
                            .eq(SysMoney::getType, dto.getUserType()));
                    if (money.getMoney().compareTo(dto.getAmount()) < 0) {
                        throw new BaseException(LanguageConfigService.i18nForMsg("可提现金额不足"));
                    }
                    dto.setNewMoney(money.getMoney().subtract(dto.getAmount()));

                    ShopUser shop = iShopUserService.getById(dto.getUserId());
                    if (ObjectUtils.isEmpty(dto.getShopUid())) {
                        dto.setShopUid(shop.getUid());
                    }
                    if (ObjectUtils.isEmpty(dto.getApplyName())) {
                        dto.setApplyName(shop.getRealname());
                    }
                    break;
            }

            dto.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypeShopDraw, 4));

            rs = baseMapper.insert(dto) > 0;

            // 生成流水：提现
            SysBill bill = new SysBill();
            bill.setUserId(dto.getUserId());
            bill.setUserType(dto.getUserType());
            bill.setIeType(-1);
            bill.setOutTradeNo(dto.getOutTradeNo());
            bill.setPayType(4);
            bill.setTotalFee(dto.getAmount());
            bill.setRelationType(SysBill.TypeShopDraw);
            bill.setRelationId(dto.getId());
            bill.setAttach("newMoney=" + dto.getNewMoney() + "&");
            // 提现备注带到资金流水里
            if (!ObjectUtils.isEmpty(dto.getNote())) {
                bill.setRemark(dto.getNote());
            }
            iSysBillService.saveSysBill(bill);

            // 扣款
            iSysMoneyService.change(dto.getUserType(), dto.getUserId(), dto.getAmount().negate());
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysWithdrawListVo> searchList(SysWithdrawPageDto dto) {

        LambdaQueryWrapper<SysWithdraw> qw = Wrappers.<SysWithdraw>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWithdraw::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWithdraw::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWithdraw::getGmtCreate, endTime);

        IPage<SysWithdraw> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWithdrawListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysWithdrawListVo vo = new SysWithdrawListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysWithdrawListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWithdraw> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWithdraw> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysWithdraw> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
