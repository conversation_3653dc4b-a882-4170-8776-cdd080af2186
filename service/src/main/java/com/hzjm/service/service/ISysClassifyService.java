package com.hzjm.service.service;

import com.alibaba.fastjson.JSONObject;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysClassify;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 平台类别 服务类
 *
 * <AUTHOR>
 * @since 2021-02-04
 */
public interface ISysClassifyService extends IService<SysClassify> {

    SysClassify getByIdWithoutLogic(Integer id);

    Boolean saveSysClassify(SysClassify dto);

    JSONObject getParentInfo(Integer id);

    JSONObject getParentInfo(List<Integer> idList);

    Boolean insertList(List<SysClassify> dataList);

    Map<Integer, String> nameMap(Integer type);
    
}
