package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysProdDealMapper;
import com.hzjm.service.mapper.SysProdTransportMapper;
import com.hzjm.service.model.DTO.SysProdDealNotePageDto;
import com.hzjm.service.model.DTO.SysProdDealPageDto;
import com.hzjm.service.model.DTO.SysProdTransportPageDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 转运&代发 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
@Slf4j
@Service
public class SysProdTransportServiceImpl extends ServiceImpl<SysProdTransportMapper, SysProdTransport> implements ISysProdTransportService {

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdDealNoteService iSysProdDealNoteService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysAuditService iSysAuditService;

    @Autowired
    private ISysParamSetService iSysParamSetService;

    @Autowired
    private IShopUserAddressService iShopUserAddressService;

    @Override
    public SysProdTransport getByIdWithoutLogic(Integer id) {
        SysProdTransport data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该转运&代发"));
        }

        return data;
    }

    @Resource
    private SysProdDealMapper sysProdDealMapper;

    @Override
    public SysProdTransportVo getDetail(Integer id, String oddNo) {
        SysProdTransport data;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<SysProdTransport>lambdaQuery().eq(SysProdTransport::getOddNo, oddNo));
            if (ObjectUtils.isEmpty(data)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未查询到该单号的信息"));
            }
        }

        SysProdTransportVo vo = new SysProdTransportVo();
        BeanUtils.copyProperties(data, vo);

        List<SysProdDealListVo> prodVoList = iSysProdDealService.dealList(data.getId(), data.getType(), new SysProdDealPageDto());
        vo.setProdList(prodVoList);

        if (!ObjectUtils.isEmpty(data.getGmtPayValid())) {
            vo.setRestMill(data.getGmtPayValid().getTime() - DateTimeUtils.getNow().getTime());
        }

        // 仓储费用
        BigDecimal wareTotalFee = SysConstants.zero;
        for (SysProdDealListVo prodVo : prodVoList) {
            if (!ObjectUtils.isEmpty(prodVo)) {
                if (ObjectUtils.isEmpty(prodVo.getWareFee())){
                    prodVo.setWareFee(SysConstants.zero);
                }
                wareTotalFee = wareTotalFee.add(prodVo.getWareFee());
            }
        }

        vo.setWareFee(wareTotalFee);
        if (!ObjectUtils.isEmpty(data.getFreeFee()) && !ObjectUtils.isEmpty(data.getDeliveryFee()) && !ObjectUtils.isEmpty(data.getPlatFee())) {
            vo.setTotalFee(vo.getWareFee().add(vo.getDeliveryFee()).add(vo.getPlatFee().multiply(new BigDecimal(prodVoList.size()))).subtract(vo.getFreeFee()));
        }

        // 留言信息
        SysProdDealNotePageDto noteDto = new SysProdDealNotePageDto();
        noteDto.setType(data.getType());
        noteDto.setRelationId(data.getId());
        vo.setNoteList(iSysProdDealNoteService.searchList(noteDto).getRecords());

        // 身份证号 : 通过收件人姓名查询
        if (!ObjectUtils.isEmpty(data.getReceiveName())) {
            String idCardNumber = iShopUserAddressService.queryIdCardByRecipientName(data.getReceiveName());
            vo.setIdCardNumber(idCardNumber);
        }

        return vo;
    }

    @Override
    public Boolean saveSysProdTransport(SysProdTransport dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            PlatDefaultPriceVo defaultPrice = iSysParamSetService.defaultPrice(null);
            if (ObjectUtils.isEmpty(dto.getPlatFee())) {
                dto.setPlatFee(defaultPrice.getPlatFee());
            }
            if (ObjectUtils.isEmpty(dto.getDeliveryFee())) {
                dto.setPlatFee(defaultPrice.getDeliveryFee());
            }
            if (ObjectUtils.isEmpty(dto.getFreeFee())) {
                dto.setPlatFee(defaultPrice.getFreeFee());
            }
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            SysProdTransport data = getById(dto.getId());

            // 仓储费用/费用合计：不可编辑
            dto.setTotalFee(null);
            dto.setWareFee(null);

            // 运费/服务费/平台优惠：非超管不可编辑，支付完成后不可修改价格
            if (JwtContentHolder.getRoleType() != 1 || (data.getStatus() != 1 && data.getStatus() != 3)) {
                dto.setDeliveryFee(null);
                dto.setFreeFee(null);
                dto.setPlatFee(null);
            }

            if (!ObjectUtils.isEmpty(dto.getStatus()) && dto.getStatus() != data.getStatus().intValue()) {
                audit(dto.getStatus(), new ArrayList<>(Arrays.asList(dto.getId())), dto.getReason());
                return true;
            }

            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public void audit(Integer status, List<Integer> idList, String reason) {
        if (ObjectUtils.isEmpty(idList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意记录"));
        }

        List<SysProdTransport> dataList = list(Wrappers.<SysProdTransport>lambdaQuery().in(SysProdTransport::getId, idList));
        if (dataList.stream().filter(a -> {
            return a.getStatus() != 1;
        }).collect(Collectors.toList()).size() > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("存在记录无需审批"));
        }
        Map<Integer, SysProdTransport> dataMap = dataList.stream().collect(Collectors.toMap(SysProdTransport::getId, a -> a));
        List<String> oddNoList = dataList.stream().map(SysProdTransport::getOddNo).collect(Collectors.toList());
        dataList.clear();

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .in(SysProdDeal::getRelationId, idList)
                .in(SysProdDeal::getType, SysProdEvent.TypeTransport, SysProdEvent.TypeSend));
        Map<Integer, List<SysProdDeal>> dealMap = dealList.stream().collect(Collectors.groupingBy(SysProdDeal::getRelationId));
        List<Integer> prodIds = BaseUtils.initList();
        prodIds.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
        dealList.clear();

        // 支付过期时间：1天
        Date gmtPayValid = null;
        List<SysProdEvent> eventList = new ArrayList<>();
        switch (status) {
            // 审核拒绝
            case 2:
                // 拒绝后释放商品
                iSysProdService.update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIds)
                        .setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

                dealMap.keySet().forEach(relationId -> {
                    SysProdTransport data = dataMap.get(relationId);

                    // 商品事件：审核拒绝
                    for (Integer prodId : dealMap.get(relationId).stream().map(SysProdDeal::getProdId).collect(Collectors.toList())) {
                        SysProdEvent event = new SysProdEvent();
                        event.setProdId(prodId);
                        event.setShopId(data.getShopId());
                        event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, data.getType())));
                        event.setDescription((data.getType() == SysProdEvent.TypeSend ? "代发" : "转运") + "审核被拒：" + (ObjectUtils.isEmpty(reason) ? "" : reason));
                        event.setRelationId(data.getId());
                        if (event.getProdId() > 0)
                            eventList.add(event);
                    }
                });

                // 流程结束：审核被拒
                iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                        .in(SysProdDeal::getRelationId, idList)
                        .in(SysProdDeal::getType, SysProdEvent.TypeTransport, SysProdEvent.TypeSend)
                        .set(SysProdDeal::getGmtModify, DateTimeUtils.getNow())
                        .set(SysProdDeal::getStatus, 2));

                // 更新平台审核状态：审核拒绝
                iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                        .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                        .set(SysAudit::getStatus, 2)
                        .set(SysAudit::getOperatorId, JwtContentHolder.getUserId())
                        .in(SysAudit::getOddNo, oddNoList));

                // search同步更新
                iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                        .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                        .in(SysProdSearch::getProdId, prodIds)
                        .eq(SysProdSearch::getSearchType, 1));
                break;
            // 审核通过
            case 3:
                Calendar c = Calendar.getInstance();
                c.setTime(DateTimeUtils.getNow());
                c.add(Calendar.DATE, 1);
                gmtPayValid = c.getTime();

                for (Integer relationId : dealMap.keySet()) {
                    SysProdTransport data = dataMap.get(relationId);

                    // 商品事件：审核通过
                    for (Integer prodId : dealMap.get(relationId).stream().map(SysProdDeal::getProdId).collect(Collectors.toList())) {
                        SysProdEvent event = new SysProdEvent();
                        event.setProdId(prodId);
                        event.setShopId(data.getShopId());
                        event.setDescription((data.getType() == SysProdEvent.TypeSend ? "代发" : "转运") + "审核通过");
                        event.setRelationId(data.getId());
                        if (event.getProdId() > 0)
                            eventList.add(event);
                    }

                    // 若为代发，无需确认，通过后免确认直接支付
                    if (data.getType() == SysProdEvent.TypeSend && data.getStatus() == 1) {
                        try {
                            update(Wrappers.<SysProdTransport>lambdaUpdate()
                                    .set(SysProdTransport::getStatus, status)
                                    .set(status == 2, SysProdTransport::getReason, reason)
                                    .set(status == 3, SysProdTransport::getGmtPayValid, gmtPayValid)
                                    .eq(SysProdTransport::getId, relationId));

                            iSysProdService.pay(data.getId(), data.getType());
                            idList.remove(relationId);
                            oddNoList.remove(data.getOddNo());
                        } catch (BaseException e) {
                            log.info("自动扣款失败：" + e.getMsg());
                        }
                    }
                }

                if (!ObjectUtils.isEmpty(oddNoList)) {
                    // 更新平台审核状态：审核通过
                    iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                            .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                            .set(SysAudit::getStatus, 3)
                            .set(SysAudit::getOperatorId, JwtContentHolder.getUserId())
                            .in(SysAudit::getOddNo, oddNoList));
                }
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("暂不支持此操作"));
        }

        iSysProdEventService.insertList(eventList);

        if (!ObjectUtils.isEmpty(idList)) {
            update(Wrappers.<SysProdTransport>lambdaUpdate()
                    .set(SysProdTransport::getStatus, status)
                    .set(status == 2, SysProdTransport::getReason, reason)
                    .set(status == 3, SysProdTransport::getGmtPayValid, gmtPayValid)
                    .in(SysProdTransport::getId, idList));
        }
    }

    @Override
    @ReadOnly
    @TrimParam
    public IPage<SysProdTransportListVo> searchList(SysProdTransportPageDto dto) {
        // 构建关联查询，减少数据库交互
        MPJLambdaWrapper<SysProdTransport> wrapper = new MPJLambdaWrapper<SysProdTransport>()
                .selectAll(SysProdTransport.class)
                .select(ShopUser::getUid, ShopUser::getRealname)
                .selectAs(ShopUser::getUid, SysProdTransportListVo::getShopUid)
                .selectAs(ShopUser::getRealname, SysProdTransportListVo::getShopName)
                .leftJoin(ShopUser.class, ShopUser::getId, SysProdTransport::getShopId);

        // 构建基础查询条件
        this.buildBaseQueryConditions(wrapper, dto);

        // 处理时间相关查询
        if (!ObjectUtils.isEmpty(dto.getDealTime())) {
            switch (dto.getDealTime()) {
                case 1: // 待处理
                    wrapper.in(SysProdTransport::getStatus, SysProdTransport.dealingStatus)
                            .gt(SysProdTransport::getGmtDeal, DateTimeUtils.getNow());
                    break;
                case 2: // 今日待处理
                    wrapper.in(SysProdTransport::getStatus, SysProdTransport.dealingStatus)
                            .le(SysProdTransport::getGmtDeal, DateTimeUtils.getNow());
                    break;
                case 3: // 待出库
                    wrapper.in(SysProdTransport::getStatus, SysProdTransport.outingStatus);
                    break;
                case 4: // 已完成
                    wrapper.in(SysProdTransport::getStatus, SysProdTransport.finishStatus)
                            .orderByDesc(SysProdTransport::getGmtModify);
                    break;
            }
        }

        // 基础排序
        wrapper.orderByAsc(SysProdTransport::getGmtCreate);

        // 执行查询
        IPage<SysProdTransportListVo> pageResult = new Page<>();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = baseMapper.selectJoinPage(new Page<>(dto.getCurrent(), dto.getSize()), SysProdTransportListVo.class, wrapper);
        } else {
            List<SysProdTransportListVo> records = baseMapper.selectJoinList(SysProdTransportListVo.class, wrapper);
            pageResult.setRecords(records);
            pageResult.setTotal(records.size());
        }

        // 如果没有结果，直接返回
        if (ObjectUtils.isEmpty(pageResult.getRecords()) || pageResult.getRecords().isEmpty()) {
            return pageResult;
        }

        // 获取所有转运记录ID，用于批量查询商品信息
        List<Integer> transportIds = pageResult.getRecords().stream()
                .map(SysProdTransportListVo::getId)
                .collect(Collectors.toList());

        // 批量查询商品信息 - 使用关联查询一次性获取所有商品和仓库信息
        MPJLambdaWrapper<SysProdDeal> dealWrapper = new MPJLambdaWrapper<SysProdDeal>()
                .selectAll(SysProdDeal.class)
                .select(SysProd::getImg, SysProd::getOneId, SysProd::getSku, SysProd::getSpec,
                        SysProd::getRemarks, SysProd::getCostPrice, SysProd::getSupply, SysProd::getGmtIn)
                .select(SysWare::getName)
                .selectAs(SysWare::getName, SysProdDealListVo::getWareName);

        // 根据是否有商品筛选条件决定使用 innerJoin 还是 leftJoin
        boolean hasProductFilter = !ObjectUtils.isEmpty(dto.getOneIdList()) ||
                                  !ObjectUtils.isEmpty(dto.getSpecList()) ||
                                  !ObjectUtils.isEmpty(dto.getSkuList());

        if (hasProductFilter) {
            // 有商品筛选条件时使用 innerJoin，确保只返回匹配的记录
            dealWrapper.innerJoin(SysProd.class, SysProd::getId, SysProdDeal::getProdId);

            // 添加商品筛选条件
            if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
                dealWrapper.in(SysProd::getOneId, dto.getOneIdList());
            }
            if (!ObjectUtils.isEmpty(dto.getSpecList())) {
                dealWrapper.in(SysProd::getSpec, dto.getSpecList());
            }
            if (!ObjectUtils.isEmpty(dto.getSkuList())) {
                dealWrapper.in(SysProd::getSku, dto.getSkuList());
            }
        } else {
            // 没有商品筛选条件时使用 leftJoin，保证所有记录都能查询到
            dealWrapper.leftJoin(SysProd.class, SysProd::getId, SysProdDeal::getProdId);
        }

        dealWrapper.leftJoin(SysWare.class, SysWare::getId, SysProdDeal::getWareId)
                   .in(SysProdDeal::getRelationId, transportIds)
                   .in(SysProdDeal::getType, SysProdEvent.TypeSend, SysProdEvent.TypeTransport)
                   .in(SysProdDeal::getStatus, 1, 3); // 处理中和已完成

        List<SysProdDealListVo> dealList = sysProdDealMapper.selectJoinList(SysProdDealListVo.class, dealWrapper);

        // 按关系ID和类型分组
        Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealProdMap = dealList.stream()
                .collect(Collectors.groupingBy(SysProdDealListVo::getType,
                        Collectors.groupingBy(SysProdDealListVo::getRelationId)));

        // 处理结果
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(DateTimeUtils.getNow());

        for (SysProdTransportListVo vo : pageResult.getRecords()) {
            // 获取商品清单
            List<SysProdDealListVo> prodList = Optional.ofNullable(dealProdMap.get(vo.getType()))
                    .map(dealMap -> dealMap.get(vo.getId()))
                    .orElse(new ArrayList<>());

            // 计算仓储费用和设置基础信息
            BigDecimal wareFee = SysConstants.zero;
            String wareName = null;

            if (!ObjectUtils.isEmpty(prodList)) {
                for (SysProdDealListVo prod : prodList) {
                    if (ObjectUtils.isEmpty(prod.getWareFee())) {
                        prod.setWareFee(SysConstants.zero);
                    }
                    wareFee = wareFee.add(prod.getWareFee());
                    if (ObjectUtils.isEmpty(wareName)) {
                        wareName = prod.getWareName();
                    }
                }
            }

            vo.setWareFee(wareFee);
            vo.setWareName(wareName);
            vo.setProdNum(prodList.size());
            vo.setProdList(prodList);

            // 计算预计出库时间
            if (!ObjectUtils.isEmpty(vo.getGmtPayValid())) {
                calendar.setTime(vo.getGmtPayValid());
                calendar.add(Calendar.DATE, -1); // 支付过期时间前1天
                vo.setGmtAudit(calendar.getTime());
            }
        }

        return pageResult;
    }

    @Override
    @ReadOnly
    @Cacheable(value = "transport_count", key = "#root.target.generateCacheKey(#dto)", unless = "#result == null")
    public SysProdTransportCountVo getCount(SysProdTransportPageDto dto) {
        SysProdTransportCountVo vo = new SysProdTransportCountVo();

        // 只有在需要 uid 相关查询时才 JOIN ShopUser 表
        boolean needJoinShopUser = !ObjectUtils.isEmpty(dto.getUid()) || !ObjectUtils.isEmpty(dto.getUidListDisplay());

        // 使用一次聚合查询获取所有统计数据
        MPJLambdaWrapper<SysProdTransport> wrapper = new MPJLambdaWrapper<SysProdTransport>()
                .select("IFNULL(SUM(CASE WHEN t.status IN (1, 7) AND t.gmt_deal > NOW() THEN 1 ELSE 0 END), 0) as dealingOrderNum")
                .select("IFNULL(SUM(CASE WHEN t.status IN (1, 7) AND t.gmt_deal <= NOW() THEN 1 ELSE 0 END), 0) as dayDealingOrderNum")
                .select("IFNULL(SUM(CASE WHEN t.status IN (3, 4) THEN 1 ELSE 0 END), 0) as outingOrderNum")
                .select("IFNULL(SUM(CASE WHEN t.status IN (2, 5) THEN 1 ELSE 0 END), 0) as finishOrderNum");

        if (needJoinShopUser) {
            wrapper.leftJoin(ShopUser.class, ShopUser::getId, SysProdTransport::getShopId);
        }

        // 构建基础查询条件
        this.buildBaseQueryConditions(wrapper, dto);

        // 使用 selectJoinList 查询并获取第一条记录的 Map 形式
        List<Map> resultList = baseMapper.selectJoinList(Map.class, wrapper);
        Map result = resultList.get(0);

        // 设置统计结果
        vo.setDealingOrderNum(((Number) result.get("dealingOrderNum")).intValue());
        vo.setDayDealingOrderNum(((Number) result.get("dayDealingOrderNum")).intValue());
        vo.setOutingOrderNum(((Number) result.get("outingOrderNum")).intValue());
        vo.setFinishOrderNum(((Number) result.get("finishOrderNum")).intValue());

        return vo;
    }

    /**
     * 生成缓存键，基于查询参数和用户权限
     * @param dto 查询参数
     * @return 缓存键
     */
    public String generateCacheKey(SysProdTransportPageDto dto) {
        StringBuilder keyBuilder = new StringBuilder();

        keyBuilder.append("knet:cache:transport:count:");

        // 添加用户权限相关信息到缓存键
        keyBuilder.append("role:").append(JwtContentHolder.getRoleType()).append(":");

        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            keyBuilder.append("shops:").append(shopIdPowerList.hashCode()).append(":");
        }

        // 添加查询参数到缓存键 - 使用hashCode简化
        keyBuilder.append("params:");
        if (!ObjectUtils.isEmpty(dto.getIdList())) keyBuilder.append("idList:").append(dto.getIdList().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getType())) keyBuilder.append("type:").append(dto.getType()).append("_");
        if (!ObjectUtils.isEmpty(dto.getStatus())) keyBuilder.append("status:").append(dto.getStatus()).append("_");
        if (!ObjectUtils.isEmpty(dto.getWareIdList())) keyBuilder.append("wareIdList:").append(dto.getWareIdList().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getWareId())) keyBuilder.append("wareId:").append(dto.getWareId()).append("_");
        if (!ObjectUtils.isEmpty(dto.getWareIdListDisplay())) keyBuilder.append("wareIdListDisplay:").append(dto.getWareIdListDisplay().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getProdNum())) keyBuilder.append("prodNum:").append(dto.getProdNum()).append("_");
        if (!ObjectUtils.isEmpty(dto.getOddNo())) keyBuilder.append("oddNo:").append(dto.getOddNo().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getBeginTime())) keyBuilder.append("beginTime:").append(dto.getBeginTime().getTime()).append("_");
        if (!ObjectUtils.isEmpty(dto.getEndTime())) keyBuilder.append("endTime:").append(dto.getEndTime().getTime()).append("_");
        if (!ObjectUtils.isEmpty(dto.getOutBeginTime())) keyBuilder.append("outBeginTime:").append(dto.getOutBeginTime().getTime()).append("_");
        if (!ObjectUtils.isEmpty(dto.getOutEndTime())) keyBuilder.append("outEndTime:").append(dto.getOutEndTime().getTime()).append("_");
        if (!ObjectUtils.isEmpty(dto.getOneId())) keyBuilder.append("oneId:").append(dto.getOneId().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getOneIdList())) keyBuilder.append("oneIdList:").append(dto.getOneIdList().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getSpec())) keyBuilder.append("spec:").append(dto.getSpec().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getSpecList())) keyBuilder.append("specList:").append(dto.getSpecList().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getSku())) keyBuilder.append("sku:").append(dto.getSku().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getSkuList())) keyBuilder.append("skuList:").append(dto.getSkuList().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getUid())) keyBuilder.append("uid:").append(dto.getUid().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getUidListDisplay())) keyBuilder.append("uidListDisplay:").append(dto.getUidListDisplay().hashCode()).append("_");
        if (!ObjectUtils.isEmpty(dto.getSearchIdList())) keyBuilder.append("searchIdList:").append(dto.getSearchIdList().hashCode()).append("_");

        // 生成最终的缓存键，如果太长则使用整体hashCode
        String finalKey = keyBuilder.toString();
        if (finalKey.length() > 200) {
            return "knet:cache:transport:count:" + finalKey.hashCode();
        }
        return finalKey;
    }

    /**
     * 构建基础查询条件
     * @param wrapper 查询包装器
     * @param dto 查询参数
     */
    private void buildBaseQueryConditions(MPJLambdaWrapper<SysProdTransport> wrapper, SysProdTransportPageDto dto) {
        // 基础查询条件
        Date endTime = dto.dealEndTime();
        Date outEndTime = dto.dealOutEndTime();

        wrapper.in(!ObjectUtils.isEmpty(dto.getIdList()), SysProdTransport::getId, dto.getIdList())
                .eq(!ObjectUtils.isEmpty(dto.getType()), SysProdTransport::getType, dto.getType())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysProdTransport::getStatus, dto.getStatus())
                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysProdTransport::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysProdTransport::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getProdNum()), SysProdTransport::getProdNum, dto.getProdNum())
                .eq(!ObjectUtils.isEmpty(dto.getOddNo()), SysProdTransport::getOddNo, dto.getOddNo())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdTransport::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdTransport::getGmtCreate, endTime)
                .ge(!ObjectUtils.isEmpty(dto.getOutBeginTime()), SysProdTransport::getGmtModify, dto.getOutBeginTime())
                .lt(!ObjectUtils.isEmpty(outEndTime), SysProdTransport::getGmtModify, outEndTime);

        // 处理 wareIdListDisplay 过滤 - 使用多个 ne 条件
        if (!ObjectUtils.isEmpty(dto.getWareIdListDisplay())) {
            for (Integer wareId : dto.getWareIdListDisplay()) {
                wrapper.ne(SysProdTransport::getWareId, wareId);
            }
        }

        // 商品相关查询条件
        if (!ObjectUtils.isEmpty(dto.getOneId()) || !ObjectUtils.isEmpty(dto.getOneIdList())
                || !ObjectUtils.isEmpty(dto.getSpecList()) || !ObjectUtils.isEmpty(dto.getSkuList())
                || !ObjectUtils.isEmpty(dto.getSpec()) || !ObjectUtils.isEmpty(dto.getSku())) {

            wrapper.leftJoin(SysProdDeal.class, SysProdDeal::getRelationId, SysProdTransport::getId)
                    .leftJoin(SysProd.class, SysProd::getId, SysProdDeal::getProdId);

            // 商品查询条件
            wrapper.like(!ObjectUtils.isEmpty(dto.getOneId()), SysProd::getOneId, dto.getOneId())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec())
                    .in(SysProdDeal::getType,Arrays.asList(3,4));

            // 多条件查询
            if (!ObjectUtils.isEmpty(dto.getSpecList())) {
                wrapper.in(SysProd::getSpec, dto.getSpecList());
            }
            if (!ObjectUtils.isEmpty(dto.getSkuList())) {
                wrapper.in(SysProd::getSku, dto.getSkuList());
            }
            if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
                wrapper.in(SysProd::getOneId, dto.getOneIdList());
            }
        }

        // 商家权限过滤
        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            wrapper.in(SysProdTransport::getShopId, shopIdPowerList);
        }

        // 根据 uid 查询
        if (!ObjectUtils.isEmpty(dto.getUid())) {
            wrapper.in(ShopUser::getUid, dto.getUid());
        }

        // 不展示的识别码 - 使用多个 ne 条件
        if (!ObjectUtils.isEmpty(dto.getUidListDisplay())) {
            for (String uid : dto.getUidListDisplay()) {
                wrapper.ne(ShopUser::getUid, uid);
            }
        }

        // 超管端不展示已取消的记录
        if (JwtContentHolder.getRoleType() == 1) {
            wrapper.ne(SysProdTransport::getStatus, 6);
        }

        // 搜索结果过滤
        if (!ObjectUtils.isEmpty(dto.getSearchIdList())) {
            wrapper.in(SysProdTransport::getId, dto.getSearchIdList());
        }
    }

    @Override
    public Boolean insertList(List<SysProdTransport> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdTransport> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdTransport> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
