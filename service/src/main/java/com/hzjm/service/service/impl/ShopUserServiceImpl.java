package com.hzjm.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.ShopUserMapper;
import com.hzjm.service.mapper.ShopUserPlatMapper;
import com.hzjm.service.mapper.SysThirdPlatMapper;
import com.hzjm.service.model.DTO.ShopUserPageDto;
import com.hzjm.service.model.VO.ShopUserAllListVo;
import com.hzjm.service.model.VO.ShopUserListVo;
import com.hzjm.service.model.VO.ShopUserPlatListVo;
import com.hzjm.service.model.VO.ShopUserVo;
import com.hzjm.service.model.enums.UploadFileType;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.ONE_DAYS_IN_MILLISECONDS;
import static com.hzjm.service.constants.ServiceConstants.REGISTRATION_TIME_START;

/**
 * 商家 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-16
 */
@Slf4j
@Service
public class ShopUserServiceImpl extends ServiceImpl<ShopUserMapper, ShopUser> implements IShopUserService {

    @Autowired
    private ISysAccountService iSysAccountService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private IShopUserPlatService iShopUserPlatService;

    @Autowired
    private IShopUserTouchService iShopUserTouchService;

    @Autowired
    private IShopUserMagicService iShopUserMagicService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysThirdPlatService iSysThirdPlatService;
    @Resource
    private SysThirdPlatMapper sysThirdPlatMapper;

    @Resource
    private ShopUserPlatMapper shopUserPlatMapper;

    @Resource
    LanguageInitService languageInitService;

    @Resource
    private IUploadFileRecord iUploadFileRecord;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 初始化自营门店
     */
    @PostConstruct
    public void init() {

        ShopUser shop = getById(1);
        if (ObjectUtils.isEmpty(shop)) {
            shop = new ShopUser();
            shop.setId(1);
            shop.setUid("XXXX");
            shop.setAccount("admin");
            shop.setRealname("平台自营");
            shop.setNickname("平台自营");
            shop.setStatus(1);
            shop.setGroupLevel(1);
            shop.insert();

            shop.setId(1);
            shop.updateById();
        }

        SysMoney money = iSysMoneyService.getById(1);
        if (ObjectUtils.isEmpty(money)) {
            money = new SysMoney();
            money.setId(1);
            money.setType(5);
            money.setUserId(1);
            money.setIntegral(0);
            money.setMoney(SysConstants.zero);
            money.insert();

            money.setId(1);
            money.updateById();
        }
    }

    @Override
    public ShopUser getByIdWithoutLogic(Integer id) {
        ShopUser data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商家"));
        }

        return data;
    }

    @Override
    public ShopUserVo getDetail(Integer id) {
        ShopUser data = getByIdWithoutLogic(id);

        ShopUserVo vo = new ShopUserVo();
        BeanUtils.copyProperties(data, vo);

        // 引荐人修改为文本录入，取消关联关系
        /*if (JwtContentHolder.getRoleType() != 1) {
            if(!ObjectUtils.isEmpty(data.getParentId()) && data.getParentId() == -1 && !ObjectUtils.isEmpty(data.getAccount())){
                ShopUser shopUser = this.getOne(Wrappers.<ShopUser>lambdaQuery().eq(ShopUser::getAccount, data.getAccount()),false);
                if (!ObjectUtils.isEmpty(shopUser)){
                    vo.setParentUid(shopUser.getNickname());
                }
            }else if (!ObjectUtils.isEmpty(data.getParentId()) && data.getParentId() != -1){
                SysUser parent = iSysUserService.getById(data.getParentId());
                if (!ObjectUtils.isEmpty(parent)) {
                    vo.setParentUid(parent.getNickname());
                }
            }

        }*/

        List<Integer> thirdPlatIdList = iShopUserPlatService.getIdList(id);
        vo.setPlatIdList(thirdPlatIdList);

        List<ShopUserPlatListVo> shopUserPlatListVoList = new ArrayList<>();
        List<SysThirdPlat> SysThirdPlatList = iSysThirdPlatService.list();
        Map<Integer, SysThirdPlat> platMap = SysThirdPlatList.stream().collect(Collectors.toMap(SysThirdPlat::getId, a -> a));

        if (!ObjectUtils.isEmpty(thirdPlatIdList)) {
            thirdPlatIdList.forEach(shopPlatId -> {
                ShopUserPlatListVo item = new ShopUserPlatListVo();
                if (platMap.containsKey(shopPlatId)) {
                    SysThirdPlat plat = platMap.get(shopPlatId);
                    BeanUtils.copyProperties(plat, item);
                    item.setShopId(id);
                    item.setPlatId(shopPlatId);
                    if (!ObjectUtils.isEmpty(plat.getName()) && "GOAT IS".equals(plat.getName())) {
                        item.setPlatName("KNET");
                    } else {
                        item.setPlatName(plat.getName());
                    }
                }
                shopUserPlatListVoList.add(item);
            });
        }

        vo.setPlatList(shopUserPlatListVoList);

        ShopUserTouch touch = iShopUserTouchService.getOne(Wrappers.<ShopUserTouch>lambdaQuery().eq(ShopUserTouch::getShopId, id).last("limit 1"));
        if (!ObjectUtils.isEmpty(touch)) {
            vo.setTouchUserId(touch.getTouchUserId());
            vo.setTouchUsername(touch.getTouchUsername());
        }

        ShopUserMagic magic = iShopUserMagicService.getOne(Wrappers.<ShopUserMagic>lambdaQuery().eq(ShopUserMagic::getShopId, id).last("limit 1"));
        if (!ObjectUtils.isEmpty(magic)) {
            vo.setMagicUserId(magic.getMagicUserId());
            vo.setMagicUsername(magic.getMagicUsername());
        }
        vo.setFileUrlList(iUploadFileRecord.getFileUrls(id, UploadFileType.ID_MATERIALS));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveShopUser(ShopUser dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        // customerManagerId 不为空时，查询平台账号的 nickName 作为 customerManager .
        if (!ObjectUtils.isEmpty(dto.getCustomerManagerId())) {
            SysUser sysUser = iSysUserService.getById(dto.getCustomerManagerId());
            if (!ObjectUtils.isEmpty(sysUser)) {
                dto.setCustomerManager(sysUser.getNickname());
            }
        }

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getAccount())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("账号名称不得为空"));
            }

            if (ObjectUtils.isEmpty(dto.getPassword())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("账号密码不得为空"));
            }

            if (iSysAccountService.count(Wrappers.<SysAccount>lambdaQuery()
                    .eq(SysAccount::getUserType, 5)
                    .eq(SysAccount::getAccount, dto.getAccount())
                    .eq(SysAccount::getAccountType, 1)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("登录账号已存在"));
            }


            dto.setUid(getUid(1));
            rs = baseMapper.insert(dto) > 0;

            // 生成账号
            SysAccount account = new SysAccount();
            account.setAccount(dto.getAccount());
            account.setAccountType(1);
            account.setUserType(5);
            account.setUserId(dto.getId());
            account.setPwd(BaseUtils.md5("ocean-flow-shop", dto.getPassword()));
            iSysAccountService.saveSysAccount(account);

            /*
            不再自动生成邮箱账号
            if (!ObjectUtils.isEmpty(dto.getEmail())) {
                // 生成账号：邮箱
                SysAccount emailAccount = new SysAccount();
                emailAccount.setAccount(dto.getEmail());
                emailAccount.setAccountType(1);
                emailAccount.setUserType(5);
                emailAccount.setUserId(dto.getId());
                emailAccount.setPwd(BaseUtils.md5("ocean-flow-shop", dto.getPassword()));
                iSysAccountService.saveSysAccount(emailAccount);
            }*/

            // 生成资金池
            SysMoney money = new SysMoney();
            money.setUserId(dto.getId());
            money.setType(5);
            iSysMoneyService.saveSysMoney(money);
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;

            iSysMoneyService.remove(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, dto.getId()).eq(SysMoney::getType, 5));
        } else {
            ShopUser data = getById(dto.getId());

            // 识别标识不可修改
            dto.setUid(null);

            // 引荐人不可修改
            /*if (ObjectUtils.isEmpty(data.getParentId())) {


            } else {
                dto.setParentId(null);
                dto.setParentUid(null);
            }*/

            // 修改账号
            if (!ObjectUtils.isEmpty(dto.getAccount())) {
                if (iSysAccountService.count(Wrappers.<SysAccount>lambdaQuery()
                        .ne(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 5)
                        .eq(SysAccount::getAccount, dto.getAccount()).eq(SysAccount::getAccountType, 1)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("登录账号已存在"));
                }
                iSysAccountService.update(Wrappers.<SysAccount>lambdaUpdate()
                        .set(SysAccount::getAccount, dto.getAccount())
                        .eq(SysAccount::getAccount, data.getAccount())
                        .eq(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 5)
                        .eq(SysAccount::getAccountType, 1));
            }

            // 修改邮箱
            if (!ObjectUtils.isEmpty(dto.getEmail())) {
                if (iSysAccountService.count(Wrappers.<SysAccount>lambdaQuery()
                        .ne(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 5)
                        .eq(SysAccount::getAccount, dto.getEmail()).eq(SysAccount::getAccountType, 1)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("This email address has already been associated with an account."));
                }
                if (!ObjectUtils.isEmpty(data.getEmail())) {
                    iSysAccountService.update(Wrappers.<SysAccount>lambdaUpdate()
                            .set(SysAccount::getAccount, dto.getEmail())
                            .eq(SysAccount::getAccount, data.getEmail())
                            .eq(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 5)
                            .eq(SysAccount::getAccountType, 1));
                } else {
                    SysAccount account = iSysAccountService.getOne(Wrappers.<SysAccount>lambdaQuery()
                            .eq(SysAccount::getAccountType, 1)
                            .eq(SysAccount::getUserType, 5)
                            .eq(SysAccount::getUserId, data.getId())
                            .like(SysAccount::getAccount, "%@%") // 查找邮箱账号
                            .last("LIMIT 1") // TODO：修复 存在 更新邮箱的错误，需要修正逻辑
                    );

                    if (ObjectUtils.isEmpty(account)) {
                       /* // 不再自动生成邮箱账号
                        SysAccount emailAccount = new SysAccount();
                        emailAccount.setAccount(dto.getEmail());
                        emailAccount.setAccountType(1);
                        emailAccount.setUserType(5);
                        emailAccount.setUserId(dto.getId());

                        if (!ObjectUtils.isEmpty(dto.getPassword())) {
                            emailAccount.setPwd(BaseUtils.md5("ocean-flow-shop", dto.getPassword()));
                            iSysAccountService.saveSysAccount(emailAccount);
                        }*/
                    } else {

                        account.setAccount(dto.getEmail());

                        if (!ObjectUtils.isEmpty(dto.getPassword())) {
                            account.setPwd(BaseUtils.md5("ocean-flow-shop", dto.getPassword()));
                            iSysAccountService.saveSysAccount(account);
                        }

                    }
                }
            }

            // 修改密码
            if (!ObjectUtils.isEmpty(dto.getPassword()) && JwtContentHolder.getRoleType() == 5) {
                iSysAccountService.update(Wrappers.<SysAccount>lambdaUpdate()
                        .set(SysAccount::getPwd, BaseUtils.md5("ocean-flow-shop", dto.getPassword()))
                        .eq(SysAccount::getUserId, dto.getId()).eq(SysAccount::getUserType, 5)
                        .eq(SysAccount::getAccountType, 1));
            }

            // 修改交易账号
            iSysMoneyService.update(Wrappers.<SysMoney>lambdaUpdate()
                    .set(SysMoney::getDelFlag, 0)
                    .set(!ObjectUtils.isEmpty(dto.getAliAccount()), SysMoney::getAliAccount, dto.getAliAccount())
                    .set(!ObjectUtils.isEmpty(dto.getAliName()), SysMoney::getAliName, dto.getAliName())
                    .set(!ObjectUtils.isEmpty(dto.getCardAccount()), SysMoney::getCardAccount, dto.getCardAccount())
                    .set(!ObjectUtils.isEmpty(dto.getCardBank()), SysMoney::getCardBank, dto.getCardBank())
                    .set(!ObjectUtils.isEmpty(dto.getCardBankSub()), SysMoney::getCardBankSub, dto.getCardBankSub())
                    .set(!ObjectUtils.isEmpty(dto.getCardName()), SysMoney::getCardName, dto.getCardName())
                    .eq(SysMoney::getUserId, dto.getId()).eq(SysMoney::getType, 5));

            // 修改语言
            if (!ObjectUtils.isEmpty(dto.getLanguage())) {
                String id = "type" + JwtContentHolder.getRoleType() + ":" + JwtContentHolder.getUserId();
                languageInitService.updateUserLanguage(id, dto.getLanguage());
            }

            // 修改名称，search同步更新
            if (!ObjectUtils.isEmpty(dto.getRealname())) {
                iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                        .set(SysProdSearch::getDelFlag, 0)
                        .set(!ObjectUtils.isEmpty(dto.getRealname()), SysProdSearch::getShopName, dto.getRealname())
                        .eq(SysProdSearch::getShopId, data.getId())
                        .eq(SysProdSearch::getSearchType, 1));
            }

            rs = baseMapper.updateById(dto) > 0;
        }

        if (!isDelete && JwtContentHolder.getRoleType() == 1) {
            // 则根据权限设置用户等级
            boolean isPremium = dto.platIdList.contains(11002) || dto.platIdList.contains(11003);
            log.info("正在修改用户等级, dto 信息: " + dto.platIdList + ", 判断是否是高级用户: " + isPremium);
            setUserLevel(dto.getId(), isPremium);

            // 新增用户默认拥有UPS权限
            dto.getPlatIdList().add(20015);

            Boolean isSuccess = iShopUserPlatService.reset(dto.getId(), dto.getPlatIdList());
        }

        return rs;
    }

    /**
     * 包含 11002 或 11003， 即 GOAT 或 GOAT IS 权限的用户默认为 等级 1 ，Premium 用户
     * 其他的 为 等级 0 ，普通用户
     *
     * @param shopId
     * @param isPremium
     */
    private void setUserLevel(Integer shopId, boolean isPremium) {
        Integer userLevel = isPremium ? 1 : 0;
        LambdaUpdateWrapper<ShopUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper
                .set(ShopUser::getLevel, userLevel)
                .eq(ShopUser::getId, shopId);
        baseMapper.update(null, updateWrapper);
    }

    @Override
    @ReadOnly
    public IPage<ShopUserListVo> searchList(ShopUserPageDto dto) {

        LambdaQueryWrapper<ShopUser> qw = Wrappers.<ShopUser>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw
//                .ne(ShopUser::getId, 1)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), ShopUser::getId, dto.getIdList())
                .like(!ObjectUtils.isEmpty(dto.getUid()), ShopUser::getUid, dto.getUid())
                .like(!ObjectUtils.isEmpty(dto.getParentUid()), ShopUser::getParentUid, dto.getParentUid())
                .like(!ObjectUtils.isEmpty(dto.getAccount()), ShopUser::getAccount, dto.getAccount())
                .like(!ObjectUtils.isEmpty(dto.getRealname()), ShopUser::getRealname, dto.getRealname())
                .like(!ObjectUtils.isEmpty(dto.getCustomerManager()), ShopUser::getCustomerManager, dto.getCustomerManager())
                .eq(!ObjectUtils.isEmpty(dto.getOcFee()), ShopUser::getOcFee, dto.getOcFee())
                .ge(!ObjectUtils.isEmpty(dto.getOcFeeMin()), ShopUser::getOcFee, dto.getOcFeeMin())
                .le(!ObjectUtils.isEmpty(dto.getOcFeeMax()), ShopUser::getOcFee, dto.getOcFeeMax())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), ShopUser::getStatus, dto.getStatus())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUser::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUser::getGmtCreate, endTime);

        if (!ObjectUtils.isEmpty(dto.getSortField())) {
            if (ObjectUtils.isEmpty(dto.getSortOrder())) {
                dto.setSortOrder("descend");
            }
            switch (dto.getSortField()) {
                case "gmtLastLogin":
                    qw.orderBy(true, dto.getSortOrder().equals("ascend"), ShopUser::getGmtLastLogin);
                    break;
                case "ocFee":
                    qw.orderBy(true, dto.getSortOrder().equals("ascend"), ShopUser::getOcFee);
                    break;
            }
        }
        qw.orderByDesc(ShopUser::getGmtCreate);

        IPage<ShopUser> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopUserListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> shopIdList = pageResult.getRecords().stream().map(ShopUser::getId).collect(Collectors.toList());

            List<ShopUserTouch> touchList = iShopUserTouchService.list(Wrappers.<ShopUserTouch>lambdaQuery().in(ShopUserTouch::getShopId, shopIdList));
            Map<Integer, ShopUserTouch> touchMap = touchList.stream().collect(Collectors.toMap(ShopUserTouch::getShopId, a -> a));
            List<ShopUserMagic> magicList = iShopUserMagicService.list(Wrappers.<ShopUserMagic>lambdaQuery().in(ShopUserMagic::getShopId, shopIdList));
            Map<Integer, ShopUserMagic> magicMap = magicList.stream().collect(Collectors.toMap(ShopUserMagic::getShopId, a -> a));

            List<ShopUserPlat> platIdList = iShopUserPlatService.list(Wrappers.<ShopUserPlat>lambdaQuery().in(ShopUserPlat::getShopId, shopIdList));
            Map<Integer, List<ShopUserPlat>> platListMapByShopId = platIdList.stream().collect(Collectors.groupingBy(ShopUserPlat::getShopId));


            pageResult.getRecords().forEach(data -> {
                ShopUserListVo vo = new ShopUserListVo();
                BeanUtils.copyProperties(data, vo);

                // 获取 shop user plat list
                List<ShopUserPlat> platList = platListMapByShopId.get(data.getId());
                if (!ObjectUtils.isEmpty(platList)) {
                    List<ShopUserPlatListVo> shopUserPlats = platList
                            .stream()
                            .map(a -> {
                                ShopUserPlatListVo platListVo = new ShopUserPlatListVo();
                                BeanUtils.copyProperties(a, platListVo);
                                return platListVo;
                            }).collect(Collectors.toList());
                    vo.setPlatList(shopUserPlats);
                }

                ShopUserTouch touch = touchMap.get(data.getId());
                if (!ObjectUtils.isEmpty(touch)) {
                    vo.setTouchUsername(touch.getTouchUsername());
                }

                ShopUserMagic magic = magicMap.get(data.getId());
                if (!ObjectUtils.isEmpty(magic)) {
                    vo.setMagicUsername(magic.getMagicUsername());
                }

                voList.add(vo);
            });
        }

        IPage<ShopUserListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopUser> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopUser> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 生成uid
     */
    private String getUid(int i) {
        if (i > 10) {
            throw new BaseException(LanguageConfigService.i18nForMsg("账号标识生成繁忙，请稍后重试"));
        }
        String uid = BaseUtils.getRandomChar(4);
        if (count(Wrappers.<ShopUser>lambdaQuery().eq(ShopUser::getUid, uid)) > 0) {
            i = i + 1;
            return getUid(i);
        }
        return uid;
    }

    /**
     * 检查引荐人
     *
     * @param dto
     */
    @Deprecated
    private void checkParent(ShopUser dto) {
        /*if (!ObjectUtils.isEmpty(dto.getParentUid())) {
            SysUser user = iSysUserService.getOne(Wrappers.<SysUser>lambdaQuery().eq(SysUser::getAccount, dto.getParentUid()),false);
            if (ObjectUtils.isEmpty(user)) {
                log.info("ShopUserServiceImpl checkParent user = {}", user);
                user = new SysUser();
                ShopUser shopUser = getOne(Wrappers.<ShopUser>lambdaQuery().eq(ShopUser::getAccount, dto.getParentUid()),false);
                if (ObjectUtils.isEmpty(shopUser)){
                    throw new BaseException(LanguageConfigService.i18nForMsg("无效的引荐人"));
                }
                user.setId(-1);
                user.setAccount(shopUser.getAccount());
            }
            dto.setParentId(user.getId());
        }*/
    }

    /**
     * 通过uid查询
     */
    @Override
    public ShopUser getByUid(String uid) {
        return getOne(Wrappers.<ShopUser>lambdaQuery().eq(ShopUser::getUid, uid));
    }

    /**
     * 查询用户平台费率
     */
    @Override
    public List<ShopUserPlatListVo> queryUserPlatFeeByShopId(Integer shopId) {
        log.info("ShopUserServiceImpl queryUserPlatFeeByShopId shop = {}", shopId);

        if (ObjectUtils.isEmpty(shopId)) {
            return null;
        }

        List<ShopUserPlatListVo> shopUserPlatListVoArrayList = new ArrayList<>();

        // 查询所有的平台名称
        LambdaQueryWrapper<SysThirdPlat> sysThirdPlatLambdaQueryWrapper = Wrappers.<SysThirdPlat>lambdaQuery()
                .select(SysThirdPlat::getId, SysThirdPlat::getName)
                .eq(SysThirdPlat::getDelFlag, 0);
        List<SysThirdPlat> sysThirdPlats = sysThirdPlatMapper.listWithoutLogic(sysThirdPlatLambdaQueryWrapper);

        Map<Integer, String> sysThirPlatMap = sysThirdPlats.stream()
                .collect(Collectors.toMap(SysThirdPlat::getId, SysThirdPlat::getName));
        // 查询商家所有的平台
        LambdaQueryWrapper<ShopUserPlat> shopUserPlatLambdaQueryWrapper = Wrappers.<ShopUserPlat>lambdaQuery()
                .eq(ShopUserPlat::getShopId, shopId)
                .eq(ShopUserPlat::getDelFlag, 0);
        List<ShopUserPlat> shopUserPlats = shopUserPlatMapper.listWithoutLogic(shopUserPlatLambdaQueryWrapper);

        for (ShopUserPlat i : shopUserPlats) {
            ShopUserPlatListVo shopUserPlatListVo = new ShopUserPlatListVo();
            BeanUtils.copyProperties(i, shopUserPlatListVo);
            shopUserPlatListVo.setPlatName(sysThirPlatMap.get(i.getPlatId()));
            // shippingFee
            shopUserPlatListVo.setShippingFee(new BigDecimal(i.getShippingFee() / 100));
            shopUserPlatListVoArrayList.add(shopUserPlatListVo);
        }

        return shopUserPlatListVoArrayList;
    }


    @Override
    public List<ShopUserAllListVo> listAll(ShopUserPageDto dto) {

        LambdaQueryWrapper<ShopUser> qw = Wrappers.<ShopUser>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopUser::getGmtCreate)
//                .ne(ShopUser::getId, 1)
                .like(!ObjectUtils.isEmpty(dto.getUid()), ShopUser::getUid, dto.getUid())
                .like(!ObjectUtils.isEmpty(dto.getParentUid()), ShopUser::getParentUid, dto.getParentUid())
                .like(!ObjectUtils.isEmpty(dto.getAccount()), ShopUser::getAccount, dto.getAccount())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), ShopUser::getStatus, dto.getStatus())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUser::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUser::getGmtCreate, endTime);

        IPage<ShopUser> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopUserAllListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopUserAllListVo vo = new ShopUserAllListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        return voList;
    }

    /**
     * 返回订单号与订单平台的查询
     * s
     */
    @Override
    public List<Map<String, String>> getOrderIdAndPlatform() {
        return baseMapper.getOrderIdAndPlatform();
    }

    @Override
    public Map<Integer, String> getShopEmails(List<Long> shopIds) {
        log.info("ShopUserServiceImpl getShopEmails shopIds = {}", shopIds);
        List<ShopUser> shopUsers = query().in("id", shopIds).select("id", "email").list();
        log.info("ShopUserServiceImpl getShopEmails shopUsers = {}", shopUsers);
        return shopUsers.stream()
                .filter(shopUser -> shopUser.getEmail() != null)
                .collect(Collectors.toMap(ShopUser::getId, ShopUser::getEmail, (existing, replacement) -> existing));
    }

    @ReadOnly
    @Override
    public List<ShopUser> getActiveUsersRegisteredOverDays() {
        LambdaQueryWrapper<ShopUser> queryWrapper = new LambdaQueryWrapper<>();
        DateTime dateTime = DateUtil.parse(REGISTRATION_TIME_START);
        log.info(" 查询某个注册时间节点之后的用户列表 dateTime = {}", dateTime);
        queryWrapper
                .eq(ShopUser::getStatus, 1)
                .ge(ShopUser::getGmtCreate, dateTime);
        return this.list(queryWrapper);
    }

    @Override
    public boolean registerCondition(ShopUser shopUser, Date dateTime, Integer gap) {
        log.info("判断用户是否注册超过xx天，开始，shopUser:{}, dateTime:{},天数毫秒: {}", shopUser, dateTime, gap);
        long timeDiff = DateUtil.betweenMs(shopUser.getGmtCreate(), dateTime);
        boolean isConditionMet = timeDiff > ONE_DAYS_IN_MILLISECONDS * gap;
        log.info("判断用户是否注册超过xx天，结束，条件是否满足: {}", isConditionMet);
        return isConditionMet;
    }

    @Override
    public ShopUser getShopUserWithCache(String id) {
        if (StrUtil.isBlank(id)) {
            return null;
        }
        String keyName = "knet:cache:user:" + id;
        Object obj = redisTemplate.opsForValue().get(keyName);
        if (obj instanceof ShopUser) {
            return (ShopUser) obj;
        }
        try {
            Integer userId = Integer.valueOf(id);
            ShopUser data = this.getById(userId);
            if (BeanUtil.isNotEmpty(data)) {
                redisTemplate.opsForValue().set(keyName, data, 30, TimeUnit.MINUTES);
                return data;
            }
        } catch (NumberFormatException e) {
            log.error("无效的用户ID格式: {}", id, e);
        }
        return null;
    }

    @Override
    public boolean allowCreatedForB2b(Integer shopUserId) {
        //todo 具体不能上架b2b平台的规则
        return false;
    }

    /**
     * 获取商家列表，放到redis里面，设置缓存时间为一分钟，过期了就重新设置，存在时就直接返回
     * key命名格式为 knet:dashboard:user1
     * valuse 存放格式为 Set<Integer>
     */
    @Override
    @ReadOnly
    @SuppressWarnings("unchecked")
    public Set<Integer> shopListByUserId(Integer userId) {
        if (ObjectUtils.isEmpty(userId)) {
            return new HashSet<>();
        }

        String keyName = "knet:dashboard:user" + userId;

        if (Boolean.TRUE.equals(redisTemplate.hasKey(keyName))) {
            Object obj = redisTemplate.opsForValue().get(keyName);

            if (ObjectUtils.isEmpty(obj) || !(obj instanceof Set<?>)) {
                return new HashSet<>();
            }

            return (Set<Integer>) obj;
        } else {
            // 查询下属商户
            LambdaQueryWrapper<ShopUser> lambdaQueryWrapper = Wrappers.<ShopUser>lambdaQuery();
            lambdaQueryWrapper.select(ShopUser::getId);
            lambdaQueryWrapper.eq(ShopUser::getCustomerManagerId, userId);
            Set<Integer> data = this.list(lambdaQueryWrapper).stream().map(ShopUser::getId).collect(Collectors.toSet());

            redisTemplate.opsForValue().set(keyName, data);
            // 设置Redis键的过期时间为1分钟
            redisTemplate.expire(keyName, 1, TimeUnit.MINUTES);
            return data;
        }
    }

    @Override
    public boolean checkKey(String key,String  password) {
        SysAccount sysAccount = iSysAccountService.getOne(Wrappers.<SysAccount>lambdaQuery()
                .eq(SysAccount::getUserType, 5)
                .eq(SysAccount::getUserId, JwtContentHolder.getShopId()));
        if (sysAccount == null) {
            return false;
        }
        if (sysAccount.getPwd() == null) {
            return false;
        }
        return BaseUtils.md5(key, password).equals(sysAccount.getPwd());
    }
}
