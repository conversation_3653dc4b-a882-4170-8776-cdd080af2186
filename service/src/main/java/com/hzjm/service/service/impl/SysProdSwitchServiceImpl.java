package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;

import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;

import java.util.ArrayList;
import java.util.Map;
import java.util.stream.Collectors;

import com.hzjm.service.entity.*;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysProdSwitchPageDto;
import com.hzjm.service.model.VO.SysProdSwitchListVo;
import com.hzjm.service.model.VO.SysProdSwitchVo;
import com.hzjm.service.mapper.SysProdSwitchMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * 转仓 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Slf4j
@Service
public class SysProdSwitchServiceImpl extends ServiceImpl<SysProdSwitchMapper, SysProdSwitch> implements ISysProdSwitchService {

    @Resource
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysProdSwitchItemService iSysProdSwitchItemService;

    @Override
    public SysProdSwitch getByIdWithoutLogic(Integer id) {
        SysProdSwitch data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该转仓"));
        }

        return data;
    }

    @Override
    public SysProdSwitchVo getDetail(Integer id) {
        SysProdSwitch data = getByIdWithoutLogic(id);

        SysProdSwitchVo vo = new SysProdSwitchVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdSwitch(SysProdSwitch dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getProdIdList())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意商品"));
            }

            if (ObjectUtils.isEmpty(dto.getNewWareId())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未选中目标仓库"));
            }

            if (iSysWareOutProdService.count(Wrappers.<SysWareOutProd>lambdaQuery()
                    .in(SysWareOutProd::getProdId, dto.getProdIdList())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("出库单中的商品不可被选中"));
            }

            if (iSysProdSwitchItemService.count(Wrappers.<SysProdSwitchItem>lambdaQuery()
                    .in(SysProdSwitchItem::getProdId, dto.getProdIdList())
                    .ne(SysProdSwitchItem::getStatus, 4)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("存在商品已处于转仓中"));
            }

            dto.setGmtDeal(BaseUtils.getGmtDeal());
            dto.setOddNo(iSysCodePoolService.build(SysProdEvent.TypeSwitch, 1).get(0));

            rs = baseMapper.insert(dto) > 0;

            // 状态同步
            iSysProdSwitchItemService.update(Wrappers.<SysProdSwitchItem>lambdaUpdate()
                    .in(SysProdSwitchItem::getProdId, dto.getProdIdList())
                    .set(SysProdSwitchItem::getSearchType, 2));

            iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                    .in(SysProdSearch::getProdId, dto.getProdIdList())
                    .eq(SysProdSearch::getSearchType, 1)
                    .set(SysProdSearch::getTransferStatus, 2));

            // 记录转仓明细
            List<SysProdSearch> prodList = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                    .select(SysProdSearch::getProdId, SysProdSearch::getWareId, SysProdSearch::getOneId, SysProdSearch::getSku, SysProdSearch::getSpec,SysProdSearch::getShelvesId)
                    .in(SysProdSearch::getProdId, dto.getProdIdList()).eq(SysProdSearch::getSearchType, 1));
            Map<Integer, SysProdSearch> prodMap = prodList.stream().collect(Collectors.toMap(SysProdSearch::getProdId, a -> a));
            prodList.clear();

            List<SysProdSwitchItem> dataList = new ArrayList<>();
            dto.getProdIdList().forEach(prodId -> {
                SysProdSwitchItem data = new SysProdSwitchItem();
                data.setSwitchId(dto.getId());
                data.setOddNo(dto.getOddNo());
                data.setStatus(1);
                data.setSearchType(1);

                SysProdSearch prod = prodMap.get(prodId);
                Integer shelvesId = prod.getShelvesId();
                // 如果商品表没有货架ID ，则去入库的库存表查询一下
                if (ObjectUtils.isEmpty(shelvesId) ){
                    log.info("SysProdSwitchServiceImpl saveSysProdSwitch prod shelvesId is null prod = {}" , JSON.toJSONString(prod));

                    SysWareShelvesProd  sysWareShelvesProd = iSysWareShelvesProdService.getOne(Wrappers.<SysWareShelvesProd>lambdaQuery()
                            .select(SysWareShelvesProd::getShelvesId)
                            .eq(SysWareShelvesProd::getProdId,prodId)
                            ,false);

                    if (!ObjectUtils.isEmpty(sysWareShelvesProd) && !ObjectUtils.isEmpty(sysWareShelvesProd.getShelvesId())){
                        shelvesId = sysWareShelvesProd.getShelvesId();
                    }

                }

                data.setProdId(prod.getProdId());
                data.setOldWareId(prod.getWareId());
                data.setOldShelvesId(shelvesId);
                data.setOneId(prod.getOneId());
                data.setSpec(prod.getSpec());
                data.setSku(prod.getSku());
                dataList.add(data);
            });
            iSysProdSwitchItemService.insertList(dataList);

        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdSwitchListVo> searchList(SysProdSwitchPageDto dto) {

        LambdaQueryWrapper<SysProdSwitch> qw = Wrappers.<SysProdSwitch>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdSwitch::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdSwitch::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdSwitch::getGmtCreate, endTime);

        IPage<SysProdSwitch> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdSwitchListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysProdSwitchListVo vo = new SysProdSwitchListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysProdSwitchListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdSwitch> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdSwitch> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdSwitch> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
