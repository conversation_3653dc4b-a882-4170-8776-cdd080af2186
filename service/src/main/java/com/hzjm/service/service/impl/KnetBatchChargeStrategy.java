package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.SysBill;
import com.hzjm.service.entity.SysMoney;
import com.hzjm.service.model.DTO.KnetBatchRecordDto;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.ISysBillService;
import com.hzjm.service.service.ISysMoneyService;
import com.hzjm.service.strategy.IKnetBatchOperationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钱包批量充值
 */
@Slf4j
@Service
public class KnetBatchChargeStrategy implements IKnetBatchOperationStrategy {

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Autowired
    private IShopUserService iShopUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean execute(List<KnetBatchRecordDto> records) {
        log.info("KnetBatchChargeStrategy execute start, record count: {}", records.size());

        List<String> shopUidList = records.stream()
                .map(KnetBatchRecordDto::getShopUid)
                .collect(Collectors.toList());

        List<ShopUser> shopUserList = iShopUserService.list(
                com.baomidou.mybatisplus.core.toolkit.Wrappers.<ShopUser>lambdaQuery()
                        .select(ShopUser::getId, ShopUser::getUid, ShopUser::getRealname)
                        .in(ShopUser::getUid, shopUidList)
        );
        Map<String, ShopUser> shopUserMap = shopUserList.stream()
                .collect(Collectors.toMap(ShopUser::getUid, shop -> shop));

        for (KnetBatchRecordDto record : records) {
            ShopUser shopUser = shopUserMap.get(record.getShopUid());
            if (ObjectUtils.isEmpty(shopUser)) {
                throw new BaseException("Shop UID does not exist: " + record.getShopUid());
            }

            Integer shopId = shopUser.getId();

            iSysMoneyService.change(5, shopId, record.getAmount());

            SysMoney money = iSysMoneyService.getOne(
                    com.baomidou.mybatisplus.core.toolkit.Wrappers.<SysMoney>lambdaQuery()
                            .eq(SysMoney::getUserId, shopId)
                            .eq(SysMoney::getType, 5)
            );
            if (ObjectUtils.isEmpty(money) || ObjectUtils.isEmpty(money.getMoney())) {
                throw new BaseException("Failed to query balance");
            }

            String attach = "note=" + BaseUtils.covertString(record.getRemark()) + "&";
            attach += "newMoney=" + money.getMoney() + "&";

            SysBill bill = new SysBill();
            bill.setStatus(2); // Completed
            bill.setUserId(shopId);
            bill.setUserType(5); // Shop type
            bill.setIeType(1); // Income
            bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypeShopCharge, 4));
            bill.setPayType(4);
            bill.setTotalFee(record.getAmount());
            bill.setRelationType(SysBill.TypeShopCharge);
            bill.setAttach(attach);
            bill.setRemark(record.getRemark());

            iSysBillService.save(bill);
        }

        return true;
    }

    @Override
    public String getStrategyType() {
        return "CHARGE";
    }
}
