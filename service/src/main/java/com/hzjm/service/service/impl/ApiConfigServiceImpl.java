package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.entity.ApiConfig;
import com.hzjm.service.mapper.ApiConfigMapper;
import com.hzjm.service.service.IApiConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-13
 */
@Slf4j
@Service
public class ApiConfigServiceImpl extends ServiceImpl<ApiConfigMapper, ApiConfig> implements IApiConfigService {

    @Override
    public ApiConfig getByIdWithoutLogic(Integer id) {
        ApiConfig data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public ApiConfig getDetail(Integer id) {
        ApiConfig data = getByIdWithoutLogic(id);

        return data;
    }

    @Override
    public Boolean saveApiConfig(ApiConfig dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

}
