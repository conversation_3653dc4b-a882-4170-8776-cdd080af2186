package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.HttpPageResult;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.PlatFormOrder;
import com.hzjm.service.mapper.SysBillMapper;
import com.hzjm.service.mapper.SysMoneyManyMapper;
import com.hzjm.service.model.DTO.RepairProcessDto;
import com.hzjm.service.model.DTO.ShopBillPageDto;
import com.hzjm.service.model.DTO.SysBillPageDto;
import com.hzjm.service.model.DTO.SysDeductionSaveDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import com.hzjm.service.utils.common.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台交易流水 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-28
 */
@Slf4j
@Service
public class SysBillServiceImpl extends ServiceImpl<SysBillMapper, SysBill> implements ISysBillService {

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private ISysChargeService iSysChargeService;

    @Autowired
    private ISysWithdrawService iSysWithdrawService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Resource
    private SysMoneyManyMapper sysMoneyManyMapper;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysProdCashService iSysProdCashService;

    @Autowired
    private ISysProdTransferService iSysProdTransferService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Resource
    private ISysProdService iSysProdService;
    @Resource
    private ISysProdSaleService iSysProdSaleService;
    @Autowired
    private IShopLabelCenterService iShopLabelCenterService;

    @Override
    public SysBill getByIdWithoutLogic(Integer id) {
        SysBill data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public SysBillVo getDetail(Integer id) {
        SysBill data = getByIdWithoutLogic(id);

        SysBillVo vo = new SysBillVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysBill(SysBill dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getStatus())) {
                Integer roleType = JwtContentHolder.getRoleType();
                if (roleType == 1) {
                    if (ObjectUtils.isEmpty(dto.getRelationType())) {
                        throw new BaseException(LanguageConfigService.i18nForMsg("账单类型不明"));
                    }
                    dto.setStatus(2);

                    // 通过流程
                    billSuccess(dto);
                } else {
                    dto.setStatus(1);
                }
            }

            if (dto.getStatus() == 2 && dto.getUserType() != 1) {
                SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, dto.getUserId()).eq(SysMoney::getType, dto.getUserType()));
                if (!ObjectUtils.isEmpty(money)) {
                    String attach = dto.getAttach();
                    if (ObjectUtils.isEmpty(attach)) {
                        attach = "";
                    }
                    dto.setAttach(attach + "newMoney=" + money.getMoney() + "&"); // 记录变更后余额
                }
            }

            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            SysBill data = getById(dto.getId());
            dto.setTotalFee(null); // 金额不可修改
            String attach = data.getAttach();
            Map<String, String> query = BaseUtils.queryToMap(attach);

            // 设置原流水备注
            if (!ObjectUtils.isEmpty(dto.getNote())) {
                data.setRemark(dto.getNote());
            }

            if (!ObjectUtils.isEmpty(dto.getStatus()) && data.getStatus().intValue() != dto.getStatus()) {
                if (data.getStatus() != 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("审批已完成"));
                }

                switch (dto.getStatus()) {
                    case 2:
                        // 通过流程
                        data.setImg(dto.getImg());
                        billSuccess(data);
                        break;
                    case 3:
                        billRefuse(data);

                        if (data.getRelationType() == SysBill.TypeShopDraw) {
                            // 拒绝提现时，提前结束，仅更改状态，不更新变更后余额
                            return true;
                        }
                        break;
                }

                SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, data.getUserId()).eq(SysMoney::getType, data.getUserType()));
                if (!ObjectUtils.isEmpty(money)) {
                    query.put("newMoney", money.getMoney() + "&"); // 记录变更后余额
                }
            }

            // TODO: 这里为什么要把 note 设置到 attach 里？
            if (!ObjectUtils.isEmpty(dto.getNote())) {
                query.put("note", dto.getNote() + "&");
            }
            if (data.getRelationType() == 2 && dto.getStatus() == 2) {
                // 提现通过，不更新交易后余额
            } else {
                dto.setAttach(BaseUtils.mapToQuery(query));
            }


            rs = baseMapper.updateById(dto) > 0;

            // 提现通过不更改入账时间
            if (data.getRelationType() == 2 && dto.getStatus() == 2) {
                baseMapper.resetGmtIn(data.getId());
            }
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysBillListVo> searchList(SysBillPageDto dto) {
        Map<String, SysCharge> chargeMap = new HashMap<>();
        Map<String, SysWithdraw> drawMap = new HashMap<>();
        LambdaQueryWrapper<SysBill> qw = this.buildQw(dto, chargeMap, drawMap);

        log.info("sysBill searchSqk = {}", qw.getCustomSqlSegment());
        HttpPageResult<SysBill> pageResult = new HttpPageResult();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            IPage<SysBill> iPage = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
            BeanUtils.copyProperties(iPage, pageResult);
        } else {
            pageResult.setRecords(list(qw));
        }

        Map<String, String> routingNumberMap = new HashMap<>();
        Map<String, String> accountInfoSupplyMap = new HashMap<>();
        Map<String, String> accountCurrencyMap = new HashMap<>();
        Map<String, String> accountHolderTypeMap = new HashMap<>();

        if (!ObjectUtils.isEmpty(pageResult) && !ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Long> userIds = pageResult.getRecords().stream()
                    .map(SysBill::getUserId)
                    .filter(Objects::nonNull)
                    .map(Integer::longValue)
                    .collect(Collectors.toList());


            List<SysMoneyMany> sysMonies = sysMoneyManyMapper.listWithoutLogic(
                    Wrappers.<SysMoney>lambdaQuery().in(SysMoney::getUserId, userIds)
            );

            for (SysMoneyMany e : sysMonies) {
                String cardAccount = e.getCardAccount();
                String cardBank = e.getCardBank();
                String cardName = e.getCardName();

                // 组装联合key
                if (!ObjectUtils.isEmpty(cardAccount) && !ObjectUtils.isEmpty(cardBank) && !ObjectUtils.isEmpty(cardName)) {
                    String key = cardAccount + "-" + cardBank + "-" + cardName;

                    if (!ObjectUtils.isEmpty(cardAccount) && !ObjectUtils.isEmpty(e.getRoutingNumber())) {
                        routingNumberMap.put(key, e.getRoutingNumber());
                    }
                    if (!ObjectUtils.isEmpty(e.getAccountInfoSupply())) {
                        accountInfoSupplyMap.put(key, e.getAccountInfoSupply());
                    }
                    if (!ObjectUtils.isEmpty(e.getAccountCurrency())) {
                        accountCurrencyMap.put(key, e.getAccountCurrency());
                    }
                    if (!ObjectUtils.isEmpty(e.getAccountHolderType())) {
                        accountHolderTypeMap.put(key, e.getAccountHolderType());
                    }
                }
            }

            sysMonies.clear();
        }

        List<PlatFormOrder> ttsOrder = baseMapper.selectTTSOrder();
        Map<String, String> ttsOrderMap = ttsOrder.stream().collect(Collectors.toMap(PlatFormOrder::getOrderNumber, PlatFormOrder::getOrderId));

        List<SysBillListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            // 充值流水
            if (ObjectUtils.isEmpty(chargeMap)) {
                List<String> chargeNoList = new ArrayList<>(Arrays.asList(""));
                chargeNoList.addAll(pageResult.getRecords().stream().filter(a -> {
                    return a.getRelationType() == SysBill.TypeShopCharge;
                }).map(SysBill::getOutTradeNo).collect(Collectors.toList()));
                List<SysCharge> chargeList = iSysChargeService.list(Wrappers.<SysCharge>lambdaQuery().in(SysCharge::getOutTradeNo, chargeNoList));
                chargeMap.putAll(chargeList.stream().collect(Collectors.toMap(SysCharge::getOutTradeNo, a -> a)));
                chargeList.clear();
            }

            // 提现流水
            if (ObjectUtils.isEmpty(drawMap)) {
                List<String> drawNoList = new ArrayList<>(Arrays.asList(""));
                drawNoList.addAll(pageResult.getRecords().stream().filter(a -> {
                    return a.getRelationType() == SysBill.TypeShopDraw;
                }).map(SysBill::getOutTradeNo).collect(Collectors.toList()));
                List<SysWithdraw> drawList = iSysWithdrawService.list(Wrappers.<SysWithdraw>lambdaQuery().in(SysWithdraw::getOutTradeNo, drawNoList));
                drawMap.putAll(drawList.stream().collect(Collectors.toMap(SysWithdraw::getOutTradeNo, a -> a)));
                drawList.clear();
            }
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().filter(a -> {
                return a.getUserType() == 5;
            }).map(SysBill::getUserId).distinct().collect(Collectors.toList()));
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIdList));
            Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
            shopList.clear();
/*
            List<SysMoney> shopMoneyList = iSysMoneyService.list(Wrappers.<SysMoney>lambdaQuery().in(SysMoney::getUserId, shopIdList));
            Map<Integer, BigDecimal> shopMoneyMap = shopMoneyList.stream().collect(Collectors.toMap(SysMoney::getUserId, SysMoney::getMoney));
            */

            Map<Integer, Map<Integer, String>> oddTree = new HashMap<Integer, Map<Integer, String>>() {{
                put(3, new HashMap<>());
                put(4, new HashMap<>());
                put(5, new HashMap<>());
                put(6, new HashMap<>());
                put(7, new HashMap<>());
                put(11, new HashMap<>());
                put(12, new HashMap<>());
                put(24, new HashMap<>());
                put(25, new HashMap<>());
            }};
            List<Integer> idList3 = new ArrayList<>(); // 转运&代发的关联id
            List<Integer> idList5 = new ArrayList<>(); // 套现的关联id
            List<Integer> idList6 = new ArrayList<>(); // 寄售的关联id
            List<Integer> idList7 = new ArrayList<>(); // 转移的关联id
            List<Integer> idList12 = new ArrayList<>(); // 回收报价
            List<Integer> idList24 = new ArrayList<>(); // 物流单
            List<Integer> idList25 = new ArrayList<>(); // 物流单
            pageResult.getRecords().forEach(data -> {
                switch (data.getRelationType()) {
                    case SysProdEvent.TypeSend:
                        idList3.add(data.getRelationId());
                        break;
                    case SysProdEvent.TypeTransport:
                        idList3.add(data.getRelationId());
                        break;
                    case SysProdEvent.TypeCash:
                        idList5.add(data.getRelationId());
                        break;
                    case SysProdEvent.TypeSale:
                        idList6.add(data.getRelationId());
                        break;
                    case SysProdEvent.TypeTransfer:
                        idList7.add(data.getRelationId());
                        break;
                    case SysBill.TypePlatSale:
                        idList6.add(data.getRelationId());
                        break;
                    case SysBill.TypeSaleCancel:
                        idList6.add(data.getRelationId());
                        break;
                    case SysBill.TypePlatCash:
                        idList12.add(data.getRelationId());
                        break;
                    case SysBill.TypeShopDrawLabelRefund:
                        idList24.add(data.getRelationId());
                        break;
                    case SysBill.TypeShopDrawLabelFee:
                        idList25.add(data.getRelationId());
                        break;
                }
            });
            if (!ObjectUtils.isEmpty(idList3)) {
                List<SysProdTransport> list = iSysProdTransportService.list(Wrappers.<SysProdTransport>lambdaQuery().in(SysProdTransport::getId, idList3));
                list.forEach(item -> {
                    Map<Integer, String> oddMap = oddTree.get(item.getType());
                    oddMap.put(item.getId(), item.getOddNo());
                });
                list.clear();
            }
            if (!ObjectUtils.isEmpty(idList5)) {
                List<SysProdCash> list = iSysProdCashService.list(Wrappers.<SysProdCash>lambdaQuery().in(SysProdCash::getId, idList5));
                Map<Integer, String> oddMap = oddTree.get(5);
                list.forEach(item -> {
                    oddMap.put(item.getId(), item.getOddNo());
                });
                list.clear();
            }
            if (!ObjectUtils.isEmpty(idList6)) {
                List<SysWareOut> list = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                        .in(SysWareOut::getId, idList6.stream().distinct().collect(Collectors.toList())));
                Map<Integer, String> oddMap = oddTree.get(6);
                list.forEach(item -> {
                    oddMap.put(item.getId(), item.getOddNo());
                });
                list.clear();
            }
            if (!ObjectUtils.isEmpty(idList7)) {
                List<SysProdTransfer> list = iSysProdTransferService.list(Wrappers.<SysProdTransfer>lambdaQuery().in(SysProdTransfer::getId, idList7));
                Map<Integer, String> oddMap = oddTree.get(7);
                list.forEach(item -> {
                    oddMap.put(item.getId(), item.getOddNo());
                });
                list.clear();
            }
            if (!ObjectUtils.isEmpty(idList12)) {
                List<SysProdCash> list = iSysProdCashService.list(Wrappers.<SysProdCash>lambdaQuery().in(SysProdCash::getId, idList12));
                Map<Integer, String> oddMap = oddTree.get(12);
                list.forEach(item -> {
                    oddMap.put(item.getId(), item.getOddNo());
                });
                list.clear();
            }
            if (!ObjectUtils.isEmpty(idList24)) {
                List<ShopLabelCenter> list = iShopLabelCenterService.list(Wrappers.<ShopLabelCenter>lambdaQuery().in(ShopLabelCenter::getId, idList24));
                Map<Integer, String> oddMap = oddTree.get(24);
                list.forEach(item -> {
                    oddMap.put(item.getId(), item.getTrackingNo());
                });
                list.clear();
            }
            if (!ObjectUtils.isEmpty(idList25)) {
                List<ShopLabelCenter> list = iShopLabelCenterService.list(Wrappers.<ShopLabelCenter>lambdaQuery().in(ShopLabelCenter::getId, idList25));
                Map<Integer, String> oddMap = oddTree.get(25);
                list.forEach(item -> {
                    oddMap.put(item.getId(), item.getTrackingNo());
                });
                list.clear();
            }


            for (SysBill data : pageResult.getRecords()) {
                SysBillListVo vo = new SysBillListVo();
                BeanUtils.copyProperties(data, vo);

                Integer relationType = data.getRelationType();
                switch (relationType) {
                    case SysBill.TypeShopCharge:
                        SysCharge charge = chargeMap.get(data.getOutTradeNo());
                        if (!ObjectUtils.isEmpty(charge)) {
                            vo.setName(charge.getName());
                            vo.setNote(charge.getNote());
                        }
                    case SysBill.TypeShopDraw:
                        SysWithdraw draw = drawMap.get(data.getOutTradeNo());
                        if (!ObjectUtils.isEmpty(draw)) {
                            vo.setName(draw.getApplyName());
                            vo.setNote(draw.getNote());
                            vo.setStatus(draw.getStatus());
                            StringBuffer sb = new StringBuffer();
                            if (!ObjectUtils.isEmpty(draw.getDrawAccount())) {
                                sb.append(draw.getDrawAccount() + "/");
                            }
                            if (!ObjectUtils.isEmpty(draw.getDrawName())) {
                                sb.append(draw.getDrawName() + "/");
                            }
                            if (!ObjectUtils.isEmpty(draw.getDrawBank())) {
                                sb.append(draw.getDrawBank() + "/");
                            }
                            vo.setBillAccount(sb.substring(0, sb.length() > 0 ? sb.length() - 1 : 0));
                            // 提现时，查找银行卡识别码
                            String mapKey = draw.getDrawAccount() + "-" + draw.getDrawBank() + "-" + draw.getDrawName();
                            vo.setRoutingNumber(routingNumberMap.get(mapKey));
                            vo.setAccountInfoSupply(accountInfoSupplyMap.get(mapKey));
                            vo.setAccountCurrency(accountCurrencyMap.get(mapKey));
                            vo.setAccountHolderType(accountHolderTypeMap.get(mapKey));

                            // 支付宝的货币类型设置为CNY
                            if (!ObjectUtils.isEmpty(draw.getDrawType()) && draw.getDrawType() == 2) {
                                vo.setDrawType(draw.getDrawType()); // 提款渠道
                                vo.setAccountCurrency("CNY");
                            }
                        }
                        break;
                    default:
                        relationType = relationType == SysBill.TypePlatSale ? SysProdEvent.TypeSale : relationType;
                        vo.setOddNo(Optional.ofNullable(oddTree.get(relationType)).map(map -> map.get(data.getRelationId())).orElse(""));
                        if (relationType == SysProdEvent.TypeSale
                                || relationType == SysProdEvent.TypeSend
                                || relationType == SysProdEvent.TypePlatOutReturn
                                || relationType == SysBill.TypePlatCash
                                || relationType == SysProdEvent.TypeTransport) {
                            vo.setOutNo(vo.getOddNo());

                            if (relationType == SysProdEvent.TypeSale || relationType == SysProdEvent.TypePlatOutReturn) {
                                try {
                                    Map<String, String> attach = BaseUtils.queryToMap(data.getAttach());
                                    vo.setPlatOrderNo(attach.get("platOrderNo"));
                                    vo.setPlatOrderNoTTS(ttsOrderMap.get(attach.get("platOrderNo")));
                                } catch (Exception e) {
                                    log.info("SysBillServiceImpl searchList data={} error ={} message={}", data, e, e.getMessage());
                                    vo.setPlatOrderNo("");
                                    vo.setPlatOrderNoTTS("");
                                }

                            }
                        }
                }

                if (data.getUserType() == 5) {
                    ShopUser shop = shopMap.get(data.getUserId());
                    if (!ObjectUtils.isEmpty(shop)) {
                        vo.setShopUid(shop.getUid());
                        vo.setShopName(shop.getRealname());
                    }
                }

                // 交易后金额
                Map<String, String> query = BaseUtils.queryToMap(vo.getAttach());
                if (!ObjectUtils.isEmpty(query.get("note"))) {
                    vo.setNote(query.get("note"));
                }
                if (query.containsKey("newMoney") && (data.getStatus() == 2
                        || (data.getRelationType() == SysBill.TypeShopDraw)
                )) {
                    vo.setNewMoney(new BigDecimal(query.get("newMoney")));
                }/* else if(data.getUserType() == 5){
                    vo.setNewMoney(Optional.ofNullable(shopMoneyMap.get(data.getUserId())).map(a-> a.add(data.getTotalFee())).orElse(null));
                }*/
//                if (query.containsKey("oddNo")) {
//                    vo.setOddNo(query.get("oddNo"));
//                }

                vo.setRemark(data.getRemark());

                voList.add(vo);
            }
        }

        IPage<SysBillListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);

        // 根据 sortOrder 的值，对routing number 字段进行排序。升序 ascend 降序 descend
        if (!ObjectUtils.isEmpty(voList)
                && !ObjectUtils.isEmpty(dto.getSortField())
                && "routingNumber".equals(dto.getSortField())) {
            if (!ObjectUtils.isEmpty(dto.getSortOrder()) && dto.getSortOrder().equals("ascend")) {
                // getRoutingNumber 如果为null 则在最后显示
                log.info("sysBill list sort asc");
                voList.sort(Comparator.comparing(SysBillListVo::getRoutingNumber, Comparator.nullsLast(Comparator.naturalOrder())));
            } else if (!ObjectUtils.isEmpty(dto.getSortOrder()) && dto.getSortOrder().equals("descend")) {
                // getRoutingNumber 如果为null 则在最初显示
                log.info("sysBill list sort dasc");
                voList.sort(Comparator.comparing(SysBillListVo::getRoutingNumber, Comparator.nullsFirst(Comparator.naturalOrder())));
            }
        }

        // sortField = 'accountCurrency'  根据 sortOrder 的值 对 accountCurrency 字段进行排序。升序 ascend 降序 descend
        if (!ObjectUtils.isEmpty(voList)
                && !ObjectUtils.isEmpty(dto.getSortField())
                && "accountCurrency".equals(dto.getSortField())) {

            Comparator<String> currencyComparator = (c1, c2) -> {
                if (c1 == null && c2 == null) return 0;
                if (c1 == null) return 1;  // null 值排在最后
                if (c2 == null) return -1;

                // USD 优先级最高
                if (c1.equals("USD") && !c2.equals("USD")) return -1;
                if (!c1.equals("USD") && c2.equals("USD")) return 1;

                // 其次是 CNY
                if (c1.equals("CNY") && !c2.equals("CNY")) return -1;
                if (!c1.equals("CNY") && c2.equals("CNY")) return 1;

                // 其他货币按字母顺序
                return c1.compareTo(c2);
            };

            if (!ObjectUtils.isEmpty(dto.getSortOrder()) && dto.getSortOrder().equals("ascend")) {
                voList.sort(
                        Comparator.comparing(SysBillListVo::getAccountCurrency, Comparator.nullsLast(currencyComparator))
                                .thenComparing(SysBillListVo::getGmtCreate, Comparator.nullsLast(Comparator.reverseOrder()))
                );
            } else if (!ObjectUtils.isEmpty(dto.getSortOrder()) && dto.getSortOrder().equals("descend")) {
                voList.sort(
                        Comparator.comparing(SysBillListVo::getAccountCurrency, Comparator.nullsLast(currencyComparator.reversed()))
                                .thenComparing(SysBillListVo::getGmtCreate, Comparator.nullsLast(Comparator.reverseOrder()))
                );
            }
        }

        voResult.setRecords(voList);

        return voResult;
    }

    private LambdaQueryWrapper<SysBill> buildQw(SysBillPageDto dto, Map<String, SysCharge> chargeMap, Map<String, SysWithdraw> drawMap) {

        Date endTime = dto.dealEndTime();
        Date finishEndTime = dto.dealFinishEndTime();

        LambdaQueryWrapper<SysBill> qw = Wrappers.<SysBill>lambdaQuery().orderByDesc(SysBill::getGmtModify).orderByDesc(SysBill::getId)
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysBill::getId, dto.getIdList())
                .like(!ObjectUtils.isEmpty(dto.getOutTradeNo()), SysBill::getOutTradeNo, dto.getOutTradeNo())
                .eq(!ObjectUtils.isEmpty(dto.getRelationType()), SysBill::getRelationType, dto.getRelationType())
                .in(!ObjectUtils.isEmpty(dto.getRelationTypeList()), SysBill::getRelationType, dto.getRelationTypeList())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysBill::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysBill::getGmtCreate, endTime)
                .ge(!ObjectUtils.isEmpty(dto.getFinishBeginTime()), SysBill::getGmtModify, dto.getFinishBeginTime())
                .gt(!ObjectUtils.isEmpty(dto.getIsToday()) && !dto.getIsToday(), SysBill::getGmtModify, DateTimeUtils.getTenAMEastern())
                .le(!ObjectUtils.isEmpty(dto.getIsToday()) && dto.getIsToday(), SysBill::getGmtModify, DateTimeUtils.getTenAMEastern())
                .lt(!ObjectUtils.isEmpty(finishEndTime), SysBill::getGmtModify, finishEndTime);

        if (!ObjectUtils.isEmpty(dto.getStatus())) {
            if (dto.getStatus() <= 4) {
                qw.eq(SysBill::getStatus, dto.getStatus());
            } else {
                qw.eq(SysBill::getStatus, 2);
                List<Integer> drawIdList = BaseUtils.initList();
                List<SysWithdraw> drawList = iSysWithdrawService.list(Wrappers.<SysWithdraw>lambdaQuery()
                        .select(SysWithdraw::getId)
                        .eq(SysWithdraw::getStatus, dto.getStatus()));
                if (!ObjectUtils.isEmpty(drawList)) {
                    drawIdList.addAll(drawList.stream().map(SysWithdraw::getId).collect(Collectors.toList()));
                    drawList.clear();
                }
                qw.and(draw -> draw.eq(SysBill::getRelationType, 2).in(SysBill::getRelationId, drawIdList));
            }
        }
        Integer roleType = JwtContentHolder.getRoleType();
        if (roleType == 1) {
            if (ObjectUtils.isEmpty(dto.getSearchType()) || dto.getSearchType() == 1) {
                qw.in(SysBill::getRelationType, SysBill.readyList);
                // 筛选 备注 和 币种 和 提现账户
                if (!ObjectUtils.isEmpty(dto.getNote())
                        || !ObjectUtils.isEmpty(dto.getDrawAccount())
                        || !ObjectUtils.isEmpty(dto.getCurrency())) {
                    //  关联表查询
                    WithdrawOutTradeNoVO withdrawOutTradeNoVO = new WithdrawOutTradeNoVO();
                    if (!ObjectUtils.isEmpty(dto.getCurrency())) {
                        withdrawOutTradeNoVO.setAccountCurrency(dto.getCurrency());
                    }
                    if (!ObjectUtils.isEmpty(dto.getNote())) {
                        withdrawOutTradeNoVO.setNote(dto.getNote().trim());
                    }
                    if (!ObjectUtils.isEmpty(dto.getDrawAccount())) {
                        withdrawOutTradeNoVO.setDrawAccount(dto.getDrawAccount().trim());
                    }
                    // 获取关联表的关系
                    List<String> drawStringList = iSysWithdrawService.queryWithdrawOutTradeNo(withdrawOutTradeNoVO);
                    // 当搜索币种为 CNY 时，需要考虑支付宝的账户
                    if (!ObjectUtils.isEmpty(dto.getCurrency()) && dto.getCurrency().equals("CNY")) {
                        List<String> drawStringList2 = iSysWithdrawService.queryWithdrawOutTradeNoByAlibaba(withdrawOutTradeNoVO);
                        if (!ObjectUtils.isEmpty(drawStringList2)) {
                            drawStringList.addAll(drawStringList2);
                        }
                    }
                    if (ObjectUtils.isEmpty(drawStringList)) {
                        // 没有关联到需要的搜索项结束查询
                        qw.eq(SysBill::getId, 0);
                        return qw;
                    }

                    qw.in(SysBill::getOutTradeNo, drawStringList);

                }

                // 搜索名称和商家
                if (!ObjectUtils.isEmpty(dto.getName()) || !ObjectUtils.isEmpty(dto.getShopId())) {
                    chargeMap.put("0", new SysCharge());
                    drawMap.put("0", new SysWithdraw());

                    List<SysCharge> chargeList = iSysChargeService.list(Wrappers.<SysCharge>lambdaQuery()
                            .eq(!ObjectUtils.isEmpty(dto.getShopId()), SysCharge::getUserId, dto.getShopId())
                            .like(!ObjectUtils.isEmpty(dto.getName()), SysCharge::getName, dto.getName()));
                    chargeMap.putAll(chargeList.stream().collect(Collectors.toMap(SysCharge::getOutTradeNo, a -> a)));
                    chargeList.clear();

                    List<SysWithdraw> drawList = iSysWithdrawService.list(Wrappers.<SysWithdraw>lambdaQuery()
                            .eq(!ObjectUtils.isEmpty(dto.getShopId()), SysWithdraw::getUserId, dto.getShopId())
                            .eq(!ObjectUtils.isEmpty(dto.getShopId()), SysWithdraw::getUserType, 5)
                            .like(!ObjectUtils.isEmpty(dto.getName()), SysWithdraw::getApplyName, dto.getName()));
                    drawMap.putAll(drawList.stream().collect(Collectors.toMap(SysWithdraw::getOutTradeNo, a -> a)));
                    drawList.clear();

                    qw.and(a -> a.in(SysBill::getOutTradeNo, chargeMap.keySet().stream().collect(Collectors.toList()))
                            .or(o1 -> o1.in(SysBill::getOutTradeNo, drawMap.keySet().stream().collect(Collectors.toList())))
                    );
                }
            } else if (dto.getSearchType() == 2) {
                qw.eq(!ObjectUtils.isEmpty(dto.getShopId()), SysBill::getUserId, dto.getShopId()).eq(SysBill::getUserType, 5);
            }
        } else {
            qw.eq(SysBill::getUserId, JwtContentHolder.getUserId()).eq(SysBill::getUserType, roleType);
        }

        if (!ObjectUtils.isEmpty(dto.getOutNo())) {
            StringBuffer sb = new StringBuffer();

            List<Integer> idList3 = new ArrayList<>(); // 转运&代发的关联id
            List<Integer> idList6 = new ArrayList<>(); // 寄售的关联id

            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery().like(SysWareOut::getOddNo, dto.getOutNo()));
            if (!ObjectUtils.isEmpty(outList)) {
                outList.forEach(data -> {
                    switch (data.getType()) {
                        case SysProdEvent.TypeSend:
                            idList3.add(data.getRelationId());
                            break;
                        case SysProdEvent.TypeTransport:
                            idList3.add(data.getRelationId());
                            break;
                        case SysProdEvent.TypeSale:
                            idList6.add(data.getId());
                            break;
                        case SysBill.TypePlatSale:
                            idList6.add(data.getId());
                            break;
                        case SysBill.TypeSaleCancel:
                            idList6.add(data.getId());
                            break;
                    }
                });
                if (!ObjectUtils.isEmpty(idList3)) {
                    sb.append("(relation_type in (" + SysProdEvent.TypeSend + "," + SysProdEvent.TypeTransport + ") " +
                            "and relation_id in(" + BaseUtils.listToStr(",", idList3) + ")) or ");
                }
                if (!ObjectUtils.isEmpty(idList6)) {
                    sb.append("(relation_type in (" + SysProdEvent.TypeSale + "," + SysBill.TypePlatSale + "," + SysBill.TypeSaleCancel + ") " +
                            "and relation_id in(" + BaseUtils.listToStr(",", idList6) + ")) or ");
                }
                qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            } else {
                qw.apply(" ( 1 = 0) ");
            }
            outList.clear();
        }
        if (!ObjectUtils.isEmpty(dto.getOddNo())) {
            StringBuffer sb = new StringBuffer();

            List<SysProdTransport> transportList = iSysProdTransportService.list(Wrappers.<SysProdTransport>lambdaQuery().like(SysProdTransport::getOddNo, dto.getOddNo()));
            if (!ObjectUtils.isEmpty(transportList)) {
                sb.append("(relation_type in (" + SysProdEvent.TypeSend + "," + SysProdEvent.TypeTransport + ") " +
                        "and relation_id in(" + BaseUtils.listToStr(",", transportList.stream().map(SysProdTransport::getId).collect(Collectors.toList())) + ")) or ");
            }
            transportList.clear();

            List<SysProdCash> cashList = iSysProdCashService.list(Wrappers.<SysProdCash>lambdaQuery().like(SysProdCash::getOddNo, dto.getOddNo()));
            if (!ObjectUtils.isEmpty(cashList)) {
                sb.append("(relation_type in (" + SysProdEvent.TypeCash + "," + SysProdEvent.TypeEnd + " )  " +
                        "and relation_id in(" + BaseUtils.listToStr(",", cashList.stream().map(SysProdCash::getId).collect(Collectors.toList())) + ")) or ");
            }
            cashList.clear();

            List<SysProdTransfer> transferList = iSysProdTransferService.list(Wrappers.<SysProdTransfer>lambdaQuery().like(SysProdTransfer::getOddNo, dto.getOddNo()));
            if (!ObjectUtils.isEmpty(transferList)) {
                sb.append("(relation_type = " + SysProdEvent.TypeTransfer + " " +
                        "and relation_id in(" + BaseUtils.listToStr(",", transferList.stream().map(SysProdTransfer::getId).collect(Collectors.toList())) + ")) or ");
            }
            transferList.clear();

            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery().like(SysWareOut::getOddNo, dto.getOddNo()));
            if (!ObjectUtils.isEmpty(outList)) {
                sb.append("(relation_type in (" + SysProdEvent.TypeSale + "," + SysBill.TypePlatSale + "," + SysBill.TypeSaleCancel + ") " +
                        "and relation_id in(" + BaseUtils.listToStr(",", outList.stream().map(SysWareOut::getId).collect(Collectors.toList())) + ")) or ");
            }
            outList.clear();

            List<ShopLabelCenter> labelCenters = iShopLabelCenterService.list(Wrappers.<ShopLabelCenter>lambdaQuery().like(ShopLabelCenter::getTrackingNo, dto.getOddNo()));
            if (!ObjectUtils.isEmpty(labelCenters)) {
                sb.append("(relation_type in (" + SysBill.TypeShopDrawLabelRefund + "," + SysBill.TypeShopDrawLabelFee + ") " +
                        "and relation_id in(" + BaseUtils.listToStr(",", labelCenters.stream().map(ShopLabelCenter::getId).collect(Collectors.toList())) + ")) or ");
            }
            labelCenters.clear();

            if (sb.length() > 4) {
                qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            } else {
                qw.apply(" (1 = 0) ");
            }
        }
        if (!ObjectUtils.isEmpty(dto.getPlatOrderNo())) {
            qw.like(SysBill::getAttach, "platOrderNo=" + dto.getPlatOrderNo());
            /*
            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .isNotNull(SysProdDeal::getSaleId)
                    .like(SysProdDeal::getPlatOrderNo, dto.getPlatOrderNo()));
            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(dealList.stream().map(SysProdDeal::getSaleId).collect(Collectors.toList()));
            dealList.clear();

            List<SysWareOut> outList = iSysWareOutService.list(Wrappers.<SysWareOut>lambdaQuery()
                    .in(SysWareOut::getRelationId, saleIdList)
                    .eq(SysWareOut::getType, SysProdEvent.TypeSale));
            List<Integer> outIdList = BaseUtils.initList();
            outIdList.addAll(outList.stream().map(SysWareOut::getId).collect(Collectors.toList()));
            outList.clear();
            qw.apply(" (relation_type in (" + SysProdEvent.TypeSale + "," + SysBill.TypePlatSale + "," + SysBill.TypeSaleCancel + ") " +
                    "and relation_id in (" + BaseUtils.listToStr(",", outIdList) + ")) ");*/
        }
        if (!ObjectUtils.isEmpty(dto.getShopUid()) || !ObjectUtils.isEmpty(dto.getShopName())) {
            List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery()
                    .like(!ObjectUtils.isEmpty(dto.getShopUid()), ShopUser::getUid, dto.getShopUid())
                    .like(!ObjectUtils.isEmpty(dto.getShopName()), ShopUser::getRealname, dto.getShopName()));
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(shopList.stream().map(ShopUser::getId).collect(Collectors.toList()));
            qw.in(SysBill::getUserId, shopIdList).eq(SysBill::getUserType, 5);
            shopList.clear();
        }
        // 账单类型
        if (!ObjectUtils.isEmpty(dto.getTabType())) {
            switch (dto.getTabType()) {
                case SysConstants.SYS_BILL_TAB_INCOME:
                    qw.eq(SysBill::getIeType, 1);
                    qw.eq(SysBill::getStatus, 2);
                    dto.setRelationTypeList(Collections.singletonList(6));
                    break;
                case SysConstants.SYS_BILL_TAB_EXPENSES:
                    qw.eq(SysBill::getIeType, -1);
                    qw.eq(SysBill::getStatus, 2);
                    break;
                case SysConstants.SYS_BILL_TAB_PENDING:
                    qw.eq(SysBill::getStatus, 1);
                    qw.in(SysBill::getRelationType, Arrays.asList(6, 24));
                    break;
                case SysConstants.SYS_BILL_TAB_WITHDRAWALS_DEPOSITS:
                    qw.in(SysBill::getRelationType, Arrays.asList(1, 2, 23));
                    break;
                default:
                    log.info("SysBillServiceImpl buildQw tapType = {}", dto.getTabType());
            }
        }

        // 备注
        // 货币类型
        return qw;
    }

    @Override
    public Boolean insertList(List<SysBill> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {
            data.setGmtCreate(date);
            data.setGmtModify(date);
            data.setDelFlag(0);
        });

        return baseMapper.insertList(dataList) > 0;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysBill> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public SysBill getByOutTradeNo(String outTradeNo) {
        return getOne(Wrappers.<SysBill>lambdaQuery().eq(SysBill::getOutTradeNo, outTradeNo));
    }

    @Override
    @ReadOnly
    public SysBillTableVo shopTable(Integer userId, Integer roleType) {
        SysBillTableVo vo = new SysBillTableVo();
        // 统计周期:本年的收入
        Calendar c = Calendar.getInstance();
        c.setTime(DateTimeUtils.getNow());
        Date endTime = DateTimeUtils.getYearEnd(c.getTime());
        TableDataSearchDto searchDto = BaseUtils.getTableQuery(2, 12, endTime);

        vo.setIncomeList(baseMapper.sum1(userId, roleType, 1, "%Y-%m", searchDto));
        vo.setOutList(baseMapper.sum1(userId, roleType, -1, "%Y-%m", searchDto));
        return vo;
    }

    @Override
    @ReadOnly
    public IPage<ShopBillListVo> shopList(ShopBillPageDto dto) {
        LambdaQueryWrapper<ShopUser> qw = Wrappers.<ShopUser>lambdaQuery();

        Integer current = dto.getCurrent();
        Integer size = dto.getSize();
        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopUser::getGmtCreate)
//                .ne(ShopUser::getId, 1)
                .like(!ObjectUtils.isEmpty(dto.getUid()), ShopUser::getUid, dto.getUid())
                .like(!ObjectUtils.isEmpty(dto.getRealname()), ShopUser::getRealname, dto.getRealname())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUser::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUser::getGmtCreate, endTime);

        dto.setSortType(0);
        if (!ObjectUtils.isEmpty(dto.getSortField())) {
            dto.setCurrent(null);
            dto.setSize(null);

            if (ObjectUtils.isEmpty(dto.getSortOrder())) {
                dto.setSortOrder("descend");
            }

            switch (dto.getSortField()) {
                case "gmtCreate":
                    dto.setSortType(1);
                    break;
                case "money":
                    dto.setSortType(3);
                    break;
                case "monthAmount":
                    dto.setSortType(5);
                    break;
                case "monthNum":
                    dto.setSortType(7);
                    break;
            }

            if (dto.getSortOrder().equals("ascend")) {
                dto.setSortType(dto.getSortType() + 1);
            }
        }

        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            qw.in(ShopUser::getId, shopIdPowerList);
        }

        IPage<ShopUser> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = iShopUserService.page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(iShopUserService.list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopBillListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().map(ShopUser::getId).collect(Collectors.toList()));
            Calendar c = Calendar.getInstance();
            c.setTime(DateTimeUtils.getNow());
            Date start = DateTimeUtils.getMonthStart(c.getTime());

            // 余额
            List<SysMoney> moneyList = iSysMoneyService.list(Wrappers.<SysMoney>lambdaQuery()
                    .in(SysMoney::getUserId, shopIdList)
                    .eq(SysMoney::getType, 5));
            Map<Integer, BigDecimal> moneyMap = moneyList.stream().collect(Collectors.toMap(SysMoney::getUserId, SysMoney::getMoney));

            // 当月交易
            List<Integer> shopBillType = SysBill.shopDealList;
            shopBillType.addAll(SysBill.readyList);
            QueryWrapper<SysBill> sumQw = new QueryWrapper();
            sumQw.eq("del_flag", 0);
            sumQw.in("relation_type", shopBillType);
            sumQw.in("user_id", shopIdList);
            sumQw.eq("user_type", 5);
            sumQw.eq("`status`", 2);
            sumQw.groupBy("user_id");
            sumQw.ge("gmt_create", start);
            sumQw.select("sum(ie_type * total_fee) total_fee", "user_id", "count(0) ie_type", "max(gmt_create) gmt_create");
            List<SysBill> sumList = list(sumQw);
            Map<Integer, SysBill> sumMap = sumList.stream().collect(Collectors.toMap(SysBill::getUserId, a -> a));

            pageResult.getRecords().forEach(data -> {
                ShopBillListVo vo = new ShopBillListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setMoney(moneyMap.get(data.getId()));

                SysBill bill = sumMap.get(data.getId());
                if (!ObjectUtils.isEmpty(bill)) {
                    vo.setMonthAmount(bill.getTotalFee());
                    vo.setMonthNum(bill.getIeType());
                    vo.setGmtCreate(bill.getGmtCreate());
                } else {
                    vo.setMonthNum(0);
                    vo.setMonthAmount(SysConstants.zero);
                }

                voList.add(vo);
            });
        }

        List<ShopBillListVo> sortList;
        // 排序方式，3-余额倒序，4-余额升序，5-本月交易金额倒序，6-本月交易金额升序，7-本月交易单数倒序，8-本月交易单数升序
        switch (dto.getSortType()) {
            case 1:
                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getGmtCreate)).reversed()).collect(Collectors.toList());
                break;
            case 2:
                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getGmtCreate))).collect(Collectors.toList());
                break;
            case 3:

                voList.forEach(e -> {
                    if (ObjectUtils.isEmpty(e.getMoney())) {
                        e.setMoney(BigDecimal.ZERO);
                    }
                });

                sortList = voList.stream().sorted(Comparator.comparing(ShopBillListVo::getMoney).reversed()).collect(Collectors.toList());
                break;
            case 4:

                voList.forEach(e -> {
                    if (ObjectUtils.isEmpty(e.getMoney())) {
                        e.setMoney(BigDecimal.ZERO);
                    }
                });

                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getMoney))).collect(Collectors.toList());
                break;
            case 5:
                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getMonthAmount)).reversed()).collect(Collectors.toList());
                break;
            case 6:
                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getMonthAmount))).collect(Collectors.toList());
                break;
            case 7:
                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getMonthNum)).reversed()).collect(Collectors.toList());
                break;
            case 8:
                sortList = voList.stream().sorted(Comparator.nullsLast(Comparator.comparing(ShopBillListVo::getMonthNum))).collect(Collectors.toList());
                break;
            default:
                IPage<ShopBillListVo> voResult = new Page();
                BeanUtils.copyProperties(pageResult, voResult);
                voResult.setRecords(voList);
                return voResult;
        }

        // 物理分页
        return PageUtils.page(current, size, sortList);
    }

    @Override
    public Boolean deduction(SysDeductionSaveDto dto) {
        // shopId 没有存在 UID 时，通过Uid查询ID
        if (ObjectUtils.isEmpty(dto.getShopId())
                && !ObjectUtils.isEmpty(dto.getShopUid())) {
            LambdaQueryWrapper<ShopUser> shopUserQueryWrapper = Wrappers.<ShopUser>lambdaQuery();
            shopUserQueryWrapper.eq(ShopUser::getUid, dto.getShopUid());
            ShopUser shopUser = iShopUserService.getOne(shopUserQueryWrapper, false);
            dto.setShopId(shopUser.getId());
        }

        if (ObjectUtils.isEmpty(dto.getShopId())) {
            // 存在 UID 时，通过Uid查询ID
            if (!ObjectUtils.isEmpty(dto.getShopUid())) {
                LambdaQueryWrapper<ShopUser> shopUserQueryWrapper = Wrappers.<ShopUser>lambdaQuery();
                shopUserQueryWrapper.eq(ShopUser::getUid, dto.getShopUid());
                ShopUser shopUser = iShopUserService.getOne(shopUserQueryWrapper,false);
                dto.setShopId(shopUser.getId());
            }

            throw new BaseException(LanguageConfigService.i18nForMsg("扣款对象不明"));
        }

        if (ObjectUtils.isEmpty(dto.getMoney()) || dto.getMoney().compareTo(SysConstants.zero) <= 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("扣款金额需大于0"));
        }
        if (!ObjectUtils.isEmpty(dto.getNote()) && dto.getNote().length() > 100) {
            throw new BaseException(LanguageConfigService.i18nForMsg("Remark number of words is greater than 100"));
        }

        iSysMoneyService.change(5, dto.getShopId(), dto.getMoney().negate());

        SysBill bill = new SysBill();
        bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypeShopDeduction, 4));
        bill.setTotalFee(dto.getMoney());
        bill.setRelationType(SysBill.TypeShopDeduction);
        bill.setAttach("note=" + BaseUtils.covertString(dto.getNote()) + "&");
        bill.setStatus(2);
        bill.setUserId(dto.getShopId());
        bill.setUserType(5);
        bill.setIeType(-1);
        bill.setPayType(4);
        bill.setRemark(dto.getNote());
        saveSysBill(bill);
        return true;
    }

    @Override
    @ReadOnly
    public SysBillCountVo getCount(SysBillPageDto dto) {
        SysBillCountVo vo = new SysBillCountVo();
        dto.setCurrent(null);
        dto.setSize(null);

        Map<String, SysCharge> chargeMap = new HashMap<>();
        Map<String, SysWithdraw> drawMap = new HashMap<>();

        SysBillPageDto dto1 = new SysBillPageDto();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setStatus(1);
        dto1.setIsToday(false);
        dto1.setRelationType(null);
        dto1.setRelationTypeList(Arrays.asList(1, 2));
        vo.setDealingNum(count(buildQw(dto1, chargeMap, drawMap)));

        SysBillPageDto dto2 = new SysBillPageDto();
        BeanUtils.copyProperties(dto, dto2);
        dto2.setStatus(2);
        dto2.setRelationType(null);
        dto2.setIsToday(null);
        vo.setFinishNum(count(buildQw(dto2, chargeMap, drawMap)));

        SysBillPageDto dto3 = new SysBillPageDto();
        BeanUtils.copyProperties(dto, dto3);
        dto3.setStatus(1);
        dto3.setIsToday(true);
        dto3.setRelationType(null);
        dto3.setRelationTypeList(Arrays.asList(1, 2));
        vo.setDealingTodayNum(count(buildQw(dto3, chargeMap, drawMap)));

        SysBillPageDto dto4 = new SysBillPageDto();
        BeanUtils.copyProperties(dto, dto4);
        dto4.setStatus(1);
        dto4.setRelationType(1);
        vo.setDepositNum(count(buildQw(dto4, chargeMap, drawMap)));

        SysBillPageDto dto5 = new SysBillPageDto();
        BeanUtils.copyProperties(dto, dto5);
        dto5.setStatus(1);
        dto5.setRelationType(2);
        vo.setWithdrawalNum(count(buildQw(dto5, chargeMap, drawMap)));

        return vo;
    }

    @Override
    public List<SysBill> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    private void billSuccess(SysBill bill) {
        switch (bill.getRelationType()) {
            case SysBill.TypeShopCharge:
                // 1.变更充值记录状态
                if (!iSysChargeService.update(Wrappers.<SysCharge>lambdaUpdate()
                        .eq(SysCharge::getStatus, 1)
                        .eq(SysCharge::getOutTradeNo, bill.getOutTradeNo())
                        .set(!ObjectUtils.isEmpty(bill.getImg()), SysCharge::getImg, bill.getImg())
                        .set(SysCharge::getStatus, 2))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("充值单状态不同步"));
                }

                // 2.加钱
                iSysMoneyService.change(bill.getUserType(), bill.getUserId(), bill.getTotalFee());
                break;
            case SysBill.TypeShopDraw:
                // 1.变更提现记录状态
                if (!iSysWithdrawService.update(Wrappers.<SysWithdraw>lambdaUpdate()
                        .eq(SysWithdraw::getStatus, 1)
                        .eq(SysWithdraw::getOutTradeNo, bill.getOutTradeNo())
                        .set(SysWithdraw::getStatus, 2))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("提现单状态不同步"));
                }

                // 生成流水：平台提现通过
                // 新增数据
                if (ObjectUtils.isEmpty(bill.getId())) {
                    SysBill dto = new SysBill();
                    dto.setTotalFee(bill.getTotalFee());
                    dto.setRelationId(bill.getRelationId());
                    dto.setRelationType(SysBill.TypePlatDraw);
                    dto.setPayType(bill.getPayType());
                    dto.setUserId(0);
                    dto.setUserType(1);
                    dto.setOutTradeNo(BaseUtils.getOutTradeNo(bill.getRelationType(), bill.getPayType()));
                    dto.setStatus(2);
                    if (!ObjectUtils.isEmpty(bill.getRemark())) {
                        dto.setRemark(bill.getRemark());
                    }
                    baseMapper.insert(dto);
                }
                // 更新流水
                else {
                    bill.setStatus(2);
                    if (!ObjectUtils.isEmpty(bill.getRemark())) {
                        bill.setRemark(bill.getRemark());
                    }
                    baseMapper.updateById(bill);
                }

                break;
        }
    }

    private void billRefuse(SysBill bill) {
        switch (bill.getRelationType()) {
            case SysBill.TypeShopCharge:
                // 1.变更充值记录状态
                if (!iSysChargeService.update(Wrappers.<SysCharge>lambdaUpdate()
                        .eq(SysCharge::getStatus, 1)
                        .eq(SysCharge::getOutTradeNo, bill.getOutTradeNo())
                        .set(SysCharge::getStatus, 3))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("充值单状态不同步"));
                }
                break;
            case SysBill.TypeShopDraw:
                // 1.变更提现记录状态
                if (!iSysWithdrawService.update(Wrappers.<SysWithdraw>lambdaUpdate()
                        .eq(SysWithdraw::getStatus, 1)
                        .eq(SysWithdraw::getOutTradeNo, bill.getOutTradeNo())
                        .set(SysWithdraw::getStatus, 3))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("提现单状态不同步"));
                }

                baseMapper.refuse(bill.getId());

                // 2.退还待提现金额
                iSysMoneyService.change(bill.getUserType(), bill.getUserId(), bill.getTotalFee());

                Map<String, String> query = new HashMap<>();
                SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery()
                        .eq(SysMoney::getUserId, bill.getUserId()).eq(SysMoney::getType, bill.getUserType()));
                if (!ObjectUtils.isEmpty(money)) {
                    query.put("newMoney", money.getMoney() + "&"); // 记录变更后余额
                }

                // 生成流水：提现退还
                SysBill dto = new SysBill();
                dto.setTotalFee(bill.getTotalFee());
                dto.setRelationId(bill.getRelationId());
                dto.setRelationType(SysBill.TypeShopDrawRefund);
                dto.setPayType(bill.getPayType());
                dto.setUserId(bill.getUserId());
                dto.setUserType(bill.getUserType());
                dto.setOutTradeNo(bill.getOutTradeNo());
                dto.setStatus(2);
                dto.setAttach(BaseUtils.mapToQuery(query));
                if (!ObjectUtils.isEmpty(bill.getRemark())) {
                    dto.setRemark(bill.getRemark());
                }
                saveSysBill(dto);

                break;
        }
    }


    /**
     * 存放所有的订单编号列表
     */
    List<String> orderNoList = new ArrayList<>();

    /**
     * 传入的订单编号如果在platform_Order表中能查到，表示为 CrossListing
     */
    @Override
    public String getSysBillSource(String platformOrderId) {
        if (ObjectUtils.isEmpty(platformOrderId)) {
            return "";
        }
        if (ObjectUtils.isEmpty(this.orderNoList)) {
            this.orderNoList = baseMapper.getPlatformOrderSize();
        }

        //if (!this.orderNoList.contains(platformOrderId)) {
        //    return "";
        //}
        //
        //return baseMapper.getSaleSource(platformOrderId);

        return this.orderNoList.contains(platformOrderId) ? SysConstants.SYS_BILL_SOURCE_CROSS_LISTING : "";
    }

    @Override
    public void setOrderNoList() {
        this.orderNoList = new ArrayList<>();
    }


    /**
     * 根据前端传入的金额，按照 SysConstants.WITHDRAW_RATE 的费率，返回到手金额,即为 传入金额 -（ 传入金额 * 费率 ）
     * 四舍五入，保留两位小数
     */
    @Override
    public WithdrawRate withdrawRate(BigDecimal parm) {
        Assert.notNull(parm, "money is null");

        BigDecimal fee = parm.multiply(SysConstants.WITHDRAW_RATE).setScale(2, RoundingMode.HALF_UP);
        BigDecimal money = parm.subtract(fee);

        WithdrawRate withdrawRate = new WithdrawRate();
        withdrawRate.setFeeAmount(fee);
        withdrawRate.setMoney(money);

        return withdrawRate;
    }

    /**
     * 校验余额
     */
    @Override
    public void checkBalance(Integer userId, Integer userType, BigDecimal money) {
        Assert.notNull(userId, "userId is null");
        Assert.notNull(userType, "userType is null");
        Assert.notNull(money, "money is null");

        SysMoney sysMoney = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery()
                .eq(SysMoney::getUserId, userId)
                .eq(SysMoney::getType, userType));
        Assert.notNull(sysMoney, "sysMoney is null");

        Assert.isTrue(sysMoney.getMoney().compareTo(money) >= 0, LanguageConfigService.i18nForMsg("余额不足"));
    }

    /**
     * 钱包管理 - 支出与退回时步钱包金额
     */
    @Override
    @AcquireTaskLock(name = "upsSyncWallet", timeout = 300, blocking = true)
    @Transactional(rollbackFor = Exception.class)
    public void syncWallet(SysBill dto, Integer userId, Integer roleType) {
        Assert.notNull(dto, "syncWallet dto is null");
        Assert.notNull(dto.getUserId(), "syncWallet dto getUserId is null");
        Assert.notNull(dto.getIeType(), "syncWallet dto getIeType is null");
        Assert.notNull(dto.getUserType(), "syncWallet dto getUserType is null");
        Assert.notNull(dto.getOutTradeNo(), "syncWallet dto getOutTradeNo is null");
        Assert.notNull(dto.getTotalFee(), "syncWallet dto getTotalFee is null");
        Assert.isTrue(dto.getUserType() == 5, "syncWallet dto shopType is not shop");

        SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery()
                .eq(SysMoney::getUserId, dto.getUserId())
                .eq(SysMoney::getType, dto.getUserType()));

        // 生成流水
        if (dto.getIeType() == -1) { // 生成label
            // 校验钱包余额
            if (dto.getUserId() != 1847 && money.getMoney().compareTo(dto.getTotalFee()) < 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("余额不足"));
            }

            dto.setRelationType(SysBill.TypeShopDrawLabelFee);
        } else if (dto.getIeType() == 1) { // 退回label
            dto.setRelationType(SysBill.TypeShopDrawLabelRefund);
        } else {
            throw new RuntimeException("ieType is error");
        }

        // 钱包流水
        dto.setPayType(4);
        if (ObjectUtils.isEmpty(dto.getStatus())) {
            dto.setStatus(2);
        }

        // 已完成扣款
        if (dto.getStatus() == 2) {
            iSysMoneyService.change(dto.getUserType(), dto.getUserId(), dto.getTotalFee().negate());
        }

        // 生成流水：提现退还
        this.saveSysBill(dto);
    }

    /**
     * 处理商家 平台退货扣款
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean processTransactionRecords(List<SysBillImportVo> sysBillImportVoList) {
        List<String> platOrderNoList = sysBillImportVoList.stream().map(SysBillImportVo::getThirdPlatOrderNo)
                .collect(Collectors.toList());

        Assert.notEmpty(platOrderNoList, "platOrderNoList is empty");

        List<SysProdSale> sysProdSaleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery()
                .select(SysProdSale::getId, SysProdSale::getPlatOrderNo)
                .in(SysProdSale::getPlatOrderNo, platOrderNoList));
        Map<String, SysProdSale> platOrderNoMap = sysProdSaleList.stream().collect(Collectors.toMap(SysProdSale::getPlatOrderNo, a -> a));

        List<String> shopUidList = sysBillImportVoList.stream().map(SysBillImportVo::getShopUid)
                .collect(Collectors.toList());
        List<ShopUser> shopUserList = iShopUserService
                .list(Wrappers.<ShopUser>lambdaQuery().select(ShopUser::getId, ShopUser::getUid, ShopUser::getRealname)
                        .in(ShopUser::getUid, shopUidList));
        Map<String, ShopUser> shopUserMap = shopUserList.stream().collect(Collectors.toMap(ShopUser::getUid, a -> a));

        // 遍历交易记录列表
        for (SysBillImportVo sysBillImportVo : sysBillImportVoList) {
            Assert.notNull(sysBillImportVo.getShopUid(), "sysBillImportVo is null");
            Assert.notNull(shopUserMap, "shopUserMap is null");
            Assert.notNull(shopUserMap.get(sysBillImportVo.getShopUid()), "shopUserMap.get(sysBillImportVo.getShopUid()) is null");
            Assert.notNull(shopUserMap.get(sysBillImportVo.getShopUid()).getId(), "shopId is null");
            Integer shopId = shopUserMap.get(sysBillImportVo.getShopUid()).getId();
            // 扣钱
            iSysMoneyService.change(5, shopId, sysBillImportVo.getAmount().negate()); // 5 是商家，1 是平台
            // 设置 attach 字段的 newMoney 字段
            String attach = "platOrderNo=" + BaseUtils.covertString(sysBillImportVo.getThirdPlatOrderNo()) + "&";
            SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, shopId)
                    .eq(SysMoney::getType, 5));
            if (ObjectUtils.isEmpty(money) || ObjectUtils.isEmpty(money.getMoney())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("查询余额失败"));
            }
            attach = attach + "newMoney=" + money.getMoney() + "&"; // 记录变更后余额

            // 生成流水：平台退货扣款
            SysBill bill = new SysBill();
            bill.setStatus(2);
            bill.setUserId(shopId);
            bill.setUserType(5);
            bill.setIeType(-1);
            bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypeSaleCancel, 4));
            bill.setPayType(4);
            bill.setTotalFee(sysBillImportVo.getAmount());
            bill.setRelationType(SysBill.TypeSaleCancel);
            bill.setAttach(attach);
            if (!ObjectUtils.isEmpty(platOrderNoMap) && !ObjectUtils.isEmpty(platOrderNoMap.get(sysBillImportVo.getThirdPlatOrderNo()))) {
                bill.setRelationId(platOrderNoMap.get(sysBillImportVo.getThirdPlatOrderNo()).getId());
            }
            baseMapper.insert(bill);
        }
        return true;
    }

    /**
     * 处理商家 UPS 补充运费 或者 批量knet 扣款
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean processUpsRecords(List<SysBillImportVo> sysBillImportVoList) {
        List<String> uidList = sysBillImportVoList.stream().map(SysBillImportVo::getShopUid)
                .collect(Collectors.toList());

        Assert.notEmpty(uidList, "识别码不能为空");

        List<String> shopUidList = sysBillImportVoList.stream().map(SysBillImportVo::getShopUid)
                .collect(Collectors.toList());
        List<ShopUser> shopUserList = iShopUserService
                .list(Wrappers.<ShopUser>lambdaQuery().select(ShopUser::getId, ShopUser::getUid, ShopUser::getRealname)
                        .in(ShopUser::getUid, shopUidList));
        Map<String, ShopUser> shopUserMap = shopUserList.stream().collect(Collectors.toMap(ShopUser::getUid, a -> a));

        // 遍历交易记录列表
        for (SysBillImportVo sysBillImportVo : sysBillImportVoList) {
            Assert.notNull(sysBillImportVo.getShopUid(), "sysBillImportVo is null");
            Assert.notNull(shopUserMap, "shopUserMap is null");
            Assert.notNull(shopUserMap.get(sysBillImportVo.getShopUid()), "shopUserMap.get(sysBillImportVo.getShopUid()) is null");
            Assert.notNull(shopUserMap.get(sysBillImportVo.getShopUid()).getId(), "shopId is null");
            Integer shopId = shopUserMap.get(sysBillImportVo.getShopUid()).getId();
            // 扣钱
            iSysMoneyService.change(5, shopId, sysBillImportVo.getAmount().negate()); // 5 是商家，1 是平台
            // 设置 attach 字段的 newMoney 字段
            String attach = "note=" + sysBillImportVo.getRemark() + "&";
            SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, shopId)
                    .eq(SysMoney::getType, 5));
            if (ObjectUtils.isEmpty(money) || ObjectUtils.isEmpty(money.getMoney())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("查询余额失败"));
            }
            attach = attach + "newMoney=" + money.getMoney() + "&"; // 记录变更后余额

            // 生成流水：平台退货扣款
            SysBill bill = new SysBill();
            bill.setStatus(2);
            bill.setUserId(shopId);
            bill.setUserType(5);
            bill.setIeType(-1);
            bill.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypeShopDeduction, 4));
            bill.setPayType(4);
            bill.setTotalFee(sysBillImportVo.getAmount());
            bill.setRelationType(SysBill.TypeShopDeduction);
            bill.setAttach(attach);
            bill.setRemark(sysBillImportVo.getRemark());
//            bill.setRelationId();
            baseMapper.insert(bill);
        }
        return true;
    }


    /**
     * 处理修鞋的流水和扣款
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void processRepairRecords(List<RepairProcessDto> repairProcessDtoList) {
        Assert.notEmpty(repairProcessDtoList, "钱包错误：修鞋记录列表不能为空");
        List<SysBill> sysBillList = new ArrayList<>();
        for (RepairProcessDto dto : repairProcessDtoList) {
            Assert.notNull(dto.getShopId(), "钱包错误：商家ID不能为空");
            Assert.notNull(dto.getTotalFee(), "钱包错误：修鞋费用不能为空");
            Assert.notNull(dto.getRepairId(), "钱包错误：修鞋订单ID不能为空");
            Assert.notNull(dto.getOperationType(), "钱包错误：操作类型不能为空");
            Assert.isTrue(dto.getOperationType() == SysBill.TypeShopDrawRepairFee ||
                            dto.getOperationType() == SysBill.TypeShopDrawRepairRefund,
                    "钱包错误：无法识别的操作");

            // 获取钱包信息
            SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery()
                    .eq(SysMoney::getUserId, dto.getShopId())
                    .eq(SysMoney::getType, 5)
            );
            Assert.notNull(money, "钱包错误：商家钱包信息不存在");

            // 根据操作类型判断是扣款还是退款
            boolean isRefund = dto.getOperationType() == SysBill.TypeShopDrawRepairRefund;

            // 处理钱包变更
            BigDecimal changeAmount = isRefund ? dto.getTotalFee() : dto.getTotalFee().negate();
            iSysMoneyService.change(5, dto.getShopId(), changeAmount);

            // 重新计算余额
            BigDecimal newMoney = money.getMoney().add(changeAmount);

            // 创建流水记录
            SysBill sysBill = new SysBill()
                    .setOutTradeNo(BaseUtils.getOutTradeNo(dto.getOperationType(), 4))
                    .setTotalFee(dto.getTotalFee())
                    .setStatus(2)
                    .setIeType(isRefund ? 1 : -1)  // 退款为1，扣款为-1
                    .setRelationType(dto.getOperationType())
                    .setRelationId(dto.getRepairId())
                    .setPayType(4)
                    .setUserId(dto.getShopId())
                    .setUserType(5)
                    .setAttach("newMoney=" + newMoney + "&" + "repairNo=" + dto.getRepairNo())
                    .setImg(null)
                    .setNote("")
                    .setRemark(BaseUtils.covertString(dto.getRemark()));

            // 保存流水
            sysBillList.add(sysBill);
        }

        // 批量保存流水
        if (!ObjectUtils.isEmpty(sysBillList)) {
            Assert.isTrue(this.saveBatch(sysBillList), "钱包错误：流水处理失败，请重试");
        }
    }


}
