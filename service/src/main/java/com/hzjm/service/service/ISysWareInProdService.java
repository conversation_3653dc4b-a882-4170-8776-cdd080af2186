package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.service.entity.SysWareIn;
import com.hzjm.service.entity.SysWareInProd;
import com.hzjm.service.model.DTO.SysWareInProdPageDto;
import com.hzjm.service.model.VO.*;

import java.util.List;
import java.util.Map;

/**
 * 入库商品 服务类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface ISysWareInProdService extends IService<SysWareInProd> {

    SysWareInProd getByIdWithoutLogic(Integer id);

    SysWareInProdVo getDetail(Integer id);

    Boolean saveSysWareInProd(SysWareInProd dto);

    Boolean insertList(List<SysWareInProd> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    @Deprecated
    IPage<SysWareInProdListVo> searchList(SysWareInProdPageDto dto);

    List<SysWareInProd> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareInProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Map<String, List<SysWareInProdListVo>> listByIn(SysWareIn in);

    Map<String, SysWareInPackListVo> packGroup(Map<Integer, String> packLogNoMap);

    @Deprecated
    List<SysWareInListVo> searchGroup(SysWareInProdPageDto dto, String timezone);

    @ReadOnly
    IPage<SysWareInListVo> searchGroupNew(SysWareInProdPageDto dto, String timezone);

    @ReadOnly
    List<SysWareInProdListVo> searchListNew(SysWareInProdPageDto dto);

    @Deprecated
    SysWareBatchCountVo groupCount(SysWareInProdPageDto dto, String timezone);

    @ReadOnly
    SysWareBatchCountVo groupCountNew(SysWareInProdPageDto dto, String timezone);

    List<SysWareInProd> getDistinctSkuAndSpec();
}
