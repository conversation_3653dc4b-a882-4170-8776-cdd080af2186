package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysCharge;
import com.hzjm.service.model.DTO.SysChargePageDto;
import com.hzjm.service.model.VO.SysChargeListVo;
import com.hzjm.service.model.VO.SysChargeVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 充值记录 服务类
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface ISysChargeService extends IService<SysCharge> {

    SysCharge getByIdWithoutLogic(Integer id);

    SysChargeVo getDetail(Integer id);

    Boolean saveSysCharge(SysCharge dto);

    Boolean insertList(List<SysCharge> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysChargeListVo> searchList(SysChargePageDto dto);

    List<SysCharge> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysCharge> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
