package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysNotice;
import com.hzjm.service.entity.SysNoticeUser;
import com.hzjm.service.mapper.SysNoticeMapper;
import com.hzjm.service.model.DTO.SysNoticePageDto;
import com.hzjm.service.model.VO.SysNoticeListVo;
import com.hzjm.service.model.VO.SysNoticeVo;
import com.hzjm.service.service.ISysNoticeService;
import com.hzjm.service.service.ISysNoticeUserService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 公告 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Slf4j
@Service
public class SysNoticeServiceImpl extends ServiceImpl<SysNoticeMapper, SysNotice> implements ISysNoticeService {

    @Resource
    ISysNoticeUserService sysNoticeUserService;

    @Override
    public SysNotice getByIdWithoutLogic(Integer id) {
        SysNotice data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该公告"));
        }

        return data;
    }

    @Override
    public SysNoticeVo getDetail(Integer id) {
        SysNotice data = getByIdWithoutLogic(id);

        SysNoticeVo vo = new SysNoticeVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysNotice(SysNotice dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysNoticeListVo> searchList(SysNoticePageDto dto) {

        LambdaQueryWrapper<SysNotice> qw = Wrappers.<SysNotice>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysNotice::getGmtCreate)
                .like(!ObjectUtils.isEmpty(dto.getTitle()), SysNotice::getTitle, dto.getTitle())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysNotice::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysNotice::getGmtCreate, endTime);

        if(!ObjectUtils.isEmpty(dto.getChannel())) {
            qw.like(SysNotice::getPorts, dto.getChannel());
        }

        if(JwtContentHolder.getRoleType() != 1) {
            qw.eq(SysNotice::getStatus, 1);
        }

        IPage<SysNotice> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        if(ObjectUtils.isEmpty(pageResult)
                || ObjectUtils.isEmpty(pageResult.getRecords())){
            return new Page<>();
        }

        List<Integer> noticeIds = pageResult.getRecords().stream().map(SysNotice::getId).collect(Collectors.toList());
        LambdaQueryWrapper<SysNoticeUser> lambdaQueryWrapper = new LambdaQueryWrapper<SysNoticeUser>()
                .in(SysNoticeUser::getNoticeId, noticeIds);
        List<SysNoticeUser> noticeUserList = sysNoticeUserService.list(lambdaQueryWrapper);
        java.util.Map<Integer, Integer> userIdAndNoticeIds = noticeUserList.stream()
                .collect(Collectors.toMap(SysNoticeUser::getNoticeId, SysNoticeUser::getReadStatus,(val1,val2)->val1));
        noticeIds.clear();
        List<SysNoticeListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysNoticeListVo vo = new SysNoticeListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setNoticeType(data.getType());
                if(!ObjectUtils.isEmpty(userIdAndNoticeIds.get(data.getId()))
                        && 1 == userIdAndNoticeIds.get(data.getId())
                ){ // 已读
                    vo.setReadStatus(1);
                }else{
                    vo.setReadStatus(0);
                }

                voList.add(vo);
            });
        }

        IPage<SysNoticeListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysNotice> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysNotice> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysNotice> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
