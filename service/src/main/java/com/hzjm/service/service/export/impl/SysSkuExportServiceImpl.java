package com.hzjm.service.service.export.impl;

import com.alibaba.fastjson.JSONArray;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.SysSku;
import com.hzjm.service.model.DTO.SysSkuPageDto;
import com.hzjm.service.model.VO.SysSkuListVo;
import com.hzjm.service.service.ISysSkuService;
import com.hzjm.service.service.LanguageConfigService;
import com.hzjm.service.service.export.ISysSkuExportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.hzjm.service.constants.ServiceConstants.SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE;
import static com.hzjm.service.constants.ServiceConstants.SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE_EN;

/**
 * <AUTHOR>
 * @date 2025/1/17 14:55
 * @description: sku导出服务
 */
@Slf4j
@Service
public class SysSkuExportServiceImpl implements ISysSkuExportService {

    @Autowired
    private ISysSkuService iSysSkuService;

    @Override
    public HashMap<String, String> exportSysSku(SysSkuPageDto req, String fileName, String language) {
        HashMap<String, String> resultMap = new HashMap<>(2);
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        dataList.add(LanguageConfigService.i18nForMsg(SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE, language));
        List<SysSkuListVo> entityList = iSysSkuService.searchList(req).getRecords();
        int i = 1;
        for (SysSkuListVo data : entityList) {
            List<String> cowList = new ArrayList<>();
            cowList.add(BaseUtils.covertString(data.getProductType()));
            cowList.add(BaseUtils.covertString(data.getBrand()));
            cowList.add(BaseUtils.covertString(data.getRemarks()));
            cowList.add(BaseUtils.covertString(data.getPku()));
            cowList.add(BaseUtils.covertString(data.getSku()));
            cowList.add(BaseUtils.covertString(data.getSpec()));
            cowList.add(BaseUtils.covertString(data.getColor()));
            cowList.add(BaseUtils.covertString(data.getGender()));
            cowList.add(BaseUtils.covertString(data.getCreateBy()));
            cowList.add(DateTimeUtils.format(DateTimeUtils.sdfTime, data.getGmtCreate()));
            dataList.add(cowList);
            i = i + 1;
        }
        try {
            String fileUrl = ExcelReader.exportExcel(dataList, fileName);
            resultMap.put("fileUrl", fileUrl);
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }
        return resultMap;
    }

    @Override
    public Boolean importSku(MultipartFile file, String language) {
        List<List<String>> dataList = new ArrayList<>();
        dataList.add(SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE_EN);
        JSONArray array = null;
        try {
            array = ExcelReader.readExcel(file.getOriginalFilename(), file.getInputStream(), SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE_EN);
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("文件读取异常"));
        }
        if (ObjectUtils.isEmpty(array)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未读取到任意数据"));
        }
        List<SysSku> entityList = array.toJavaList(SysSku.class);
        return iSysSkuService.insertList(entityList);
    }
}
