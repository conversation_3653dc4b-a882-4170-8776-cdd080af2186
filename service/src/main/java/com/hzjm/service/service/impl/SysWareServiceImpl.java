package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysWareMapper;
import com.hzjm.service.model.DTO.SysWarePageDto;
import com.hzjm.service.model.DTO.SysWareShelvesPageDto;
import com.hzjm.service.model.DTO.SysWareUserPageDto;
import com.hzjm.service.model.VO.SysWareAllListVo;
import com.hzjm.service.model.VO.SysWareListVo;
import com.hzjm.service.model.VO.SysWareVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 仓库 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-15
 */
@Slf4j
@Service
public class SysWareServiceImpl extends ServiceImpl<SysWareMapper, SysWare> implements ISysWareService {

    @Autowired
    private ISysWareUserService iSysWareUserService;

    @Autowired
    private ISysWareUserPermissionService iSysWareUserPermissionService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysProdService iSysProdService;

    Map<String, Integer> wareOrderMap = new HashMap<>();

    /**
     * 仓库的排序顺序
     */
    @PostConstruct
    void setWareOrderMap() {
        Map<String, Integer> orderMap = new HashMap<>();
        orderMap.put("NJ-1", 0);
        orderMap.put("NJ-2", 1);
        orderMap.put("NJ-3", 2);
        orderMap.put("DE-1", 3);
        orderMap.put("DE-2", 4);
        orderMap.put("DE-3", 5);
        orderMap.put("DE-4", 6);
        orderMap.put("DE-5", 7);
        orderMap.put("PA-1", 8);
        orderMap.put("PA-2", 9);
        orderMap.put("PA-3", 10);
        orderMap.put("TX-1", 11);
        orderMap.put("STV", 12);
        orderMap.put("StockX Flex", 13);
        this.wareOrderMap = orderMap;
    }



    @Override
    public SysWare getByIdWithoutLogic(Integer id) {
        SysWare data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该仓库"));
        }

        return data;
    }

    @Override
    public SysWareVo getDetail(Integer id) {
        SysWare data = getByIdWithoutLogic(id);

        SysWareVo vo = new SysWareVo();
        BeanUtils.copyProperties(data, vo);

        // 人员信息
        SysWareUserPageDto dto2 = new SysWareUserPageDto();
        dto2.setWareId(id);
        vo.setUserList(iSysWareUserService.searchList(dto2).getRecords());

        // 货架信息
        SysWareShelvesPageDto dto1 = new SysWareShelvesPageDto();
        dto1.setWareId(id);
        vo.setShelvesList(iSysWareShelvesService.searchList(dto1).getRecords());

        return vo;
    }

    @Override
    public Boolean saveSysWare(SysWare dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;

            // 初始化人员
            if (!ObjectUtils.isEmpty(dto.getIdList1())) {
                iSysWareUserService.update(Wrappers.<SysWareUser>lambdaUpdate()
                        .set(SysWareUser::getWareId, dto.getId())
                        .in(SysWareUser::getId, dto.getIdList1()));
                iSysWareUserPermissionService.update(Wrappers.<SysWareUserPermission>lambdaUpdate()
                        .set(SysWareUserPermission::getWareId, dto.getId())
                        .in(SysWareUserPermission::getWareUserId, dto.getIdList1()));
            }

            // 初始化货柜
            if (!ObjectUtils.isEmpty(dto.getIdList2())) {
                iSysWareShelvesService.update(Wrappers.<SysWareShelves>lambdaUpdate()
                        .set(SysWareShelves::getWareId, dto.getId())
                        .in(SysWareShelves::getId, dto.getIdList2()));
            }
        } else if (isDelete) {
            // 判断在仓库存，非0不可删除
            if (iSysProdService.count(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getWareId, dto.getId())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("本仓库下有在仓商品，无法删除"));
            }

            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;

            // 重置货柜
            if (!ObjectUtils.isEmpty(dto.getIdList2())) {
                iSysWareShelvesService.update(Wrappers.<SysWareShelves>lambdaUpdate()
                        .set(SysWareShelves::getWareId, dto.getId())
                        .in(SysWareShelves::getId, dto.getIdList2()));
            }
        }

        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysWareListVo> searchList(SysWarePageDto dto) {

        LambdaQueryWrapper<SysWare> qw = Wrappers.<SysWare>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWare::getGmtCreate)
                .like(!ObjectUtils.isEmpty(dto.getName()), SysWare::getName, dto.getName())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWare::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWare::getGmtCreate, endTime);

        // 我的仓库
        if (JwtContentHolder.getRoleType() == 4) {
            List<SysWareUser> wareUserList = iSysWareUserService.list(Wrappers.<SysWareUser>lambdaQuery().in(SysWareUser::getUserId, JwtContentHolder.getUserId()));
            List<Integer> wareIdList = BaseUtils.initList();
            wareIdList.addAll(wareUserList.stream().map(SysWareUser::getWareId).collect(Collectors.toList()));
            qw.in(SysWare::getId, wareIdList);
        }

        List<SysUser> userList = iSysUserService.list(Wrappers.<SysUser>lambdaQuery()
                .like(!ObjectUtils.isEmpty(dto.getStaffNames()), SysUser::getNickname, dto.getStaffNames()));
        Map<Integer, SysUser> userMap = userList.stream().collect(Collectors.toMap(SysUser::getId, a -> a));

        if (!ObjectUtils.isEmpty(dto.getStaffNames())) {
            List<Integer> userIdList = BaseUtils.initList();
            userIdList.addAll(userList.stream().map(SysUser::getId).collect(Collectors.toList()));

            List<SysWareUser> wareUserList = iSysWareUserService.list(Wrappers.<SysWareUser>lambdaQuery().in(SysWareUser::getUserId, userIdList));
            List<Integer> wareIdList = BaseUtils.initList();
            wareIdList.addAll(wareUserList.stream().map(SysWareUser::getWareId).collect(Collectors.toList()));
            qw.in(SysWare::getId, wareIdList);
        }

        // 不查询 STV
        qw.notIn(SysWare::getId, Arrays.asList(20034, 10000));

        IPage<SysWare> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> wareIdList = BaseUtils.initList();
            wareIdList.addAll(pageResult.getRecords().stream().map(SysWare::getId).collect(Collectors.toList()));

            Map<Integer, StringBuffer> nameMap = new HashMap<>();
            List<SysWareUser> wareUserList = iSysWareUserService.list(Wrappers.<SysWareUser>lambdaQuery()
                    .in(SysWareUser::getWareId, wareIdList));
            wareUserList.forEach(wareUser -> {
                SysUser user = userMap.get(wareUser.getUserId());
                if (!ObjectUtils.isEmpty(user)) {
                    StringBuffer sb = nameMap.get(wareUser.getWareId());
                    if (ObjectUtils.isEmpty(sb)) {
                        sb = new StringBuffer("");
                        nameMap.put(wareUser.getWareId(), sb);
                    }
                    if (ObjectUtils.isEmpty(user.getNickname())) {
                        sb.append("、未命名");
                    } else {
                        sb.append("、" + user.getNickname());
                    }
                }
            });

            List<SysWareShelves> shelvesList = iSysWareShelvesService.list(Wrappers.<SysWareShelves>lambdaQuery()
                    .select(SysWareShelves::getWareId)
                    .in(SysWareShelves::getWareId, wareIdList));
            Map<Integer, List<SysWareShelves>> shelvesMap = shelvesList.stream().collect(Collectors.groupingBy(SysWareShelves::getWareId));

            pageResult.getRecords().forEach(data -> {
                SysWareListVo vo = new SysWareListVo();
                BeanUtils.copyProperties(data, vo);

                // 仓库人员
                StringBuffer sb = nameMap.get(data.getId());
                if (!ObjectUtils.isEmpty(sb)) {
                    vo.setStaffNames(sb.substring(1));
                }

                // 在仓库存
                vo.setProdNum(iSysProdService.count(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getWareId, data.getId())));

                // 货架数
                List<SysWareShelves> shelves = shelvesMap.get(data.getId());
                if (ObjectUtils.isEmpty(shelves)) {
                    vo.setShelvesNum(0);
                } else {
                    vo.setShelvesNum(shelves.size());
                }

                voList.add(vo);
            });
        }

        voList.sort(Comparator.comparingInt((SysWareListVo vo) ->
                this.wareOrderMap.getOrDefault(vo.getName(), Integer.MAX_VALUE)
        ).thenComparing(Comparator.comparing(SysWareListVo::getGmtCreate).reversed()));

        IPage<SysWareListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWare> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWare> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysWareAllListVo> listAll(SysWarePageDto dto) {
        LambdaQueryWrapper<SysWare> qw = Wrappers.<SysWare>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWare::getGmtCreate)
                .like(!ObjectUtils.isEmpty(dto.getName()), SysWare::getName, dto.getName())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWare::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWare::getGmtCreate, endTime);

        // 我的仓库
        if (JwtContentHolder.getRoleType() == 4) {
            List<SysWareUser> wareUserList = iSysWareUserService.list(Wrappers.<SysWareUser>lambdaQuery().in(SysWareUser::getUserId, JwtContentHolder.getUserId()));
            List<Integer> wareIdList = BaseUtils.initList();
            wareIdList.addAll(wareUserList.stream().map(SysWareUser::getWareId).collect(Collectors.toList()));
            qw.in(SysWare::getId, wareIdList);
        }

        // 不查询 STV
        qw.notIn(SysWare::getId,Arrays.asList(20034,10000));

        IPage<SysWare> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareAllListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            Map<Integer, Integer> prodNumMap = new HashMap<>();
            Integer shopId = JwtContentHolder.getShopId();
            if (!ObjectUtils.isEmpty(shopId)) {
                // 该仓库下的商品数量
                List<SysProd> prods = iSysProdService.list(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getShopId, shopId).select(SysProd::getId, SysProd::getWareId));
                if (!ObjectUtils.isEmpty(prods)) {
                    Map<Integer, List<SysProd>> map = prods.stream()
                            .filter(prod -> !ObjectUtils.isEmpty(prod.getWareId())) // 有 shop id 的数据 ware id 不能为 null
                            .collect(Collectors.groupingBy(SysProd::getWareId));
                    map.keySet().forEach(wareId -> {
                        prodNumMap.put(wareId, map.get(wareId).size());
                    });
                }
            }

            pageResult.getRecords().forEach(data -> {
                SysWareAllListVo vo = new SysWareAllListVo();
                BeanUtils.copyProperties(data, vo);

                Integer num = prodNumMap.get(data.getId());
                vo.setProdNum(ObjectUtils.isEmpty(num) ? 0 : num);

                voList.add(vo);
            });
        }

        voList.sort(Comparator.comparingInt((SysWareAllListVo vo) ->
                this.wareOrderMap.getOrDefault(vo.getName(), Integer.MAX_VALUE)
        ).thenComparing(Comparator.comparing(SysWareAllListVo::getProdNum).reversed()));

        return voList;
    }

    /**
     * 根据 wareId 找到对应 仓库的等级，为 0 则 为 普通仓库 ，非 0 则 是 premium 仓库
     *
     * @param wareId
     * @return
     */
    @Override
    public Boolean isPremiumWare(Integer wareId) {
        SysWare ware = baseMapper.selectById(wareId);
        if (ObjectUtils.isEmpty(ware)) {
            return false;
        }

        return !(ware.getLevel() == 0);
    }

    @Override
    public List<SysWare> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
