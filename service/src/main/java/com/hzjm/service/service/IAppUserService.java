package com.hzjm.service.service;

import com.hzjm.common.model.HttpPageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.AppUser;
import com.hzjm.service.model.DTO.AppUserPageDto;
import com.hzjm.service.model.VO.AppUserListVo;
import com.hzjm.service.model.VO.AppUserVo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.apache.ibatis.annotations.Param;

/**
 * 用户 服务类
 *
 * <AUTHOR>
 * @since 2021-10-21
 */
public interface IAppUserService extends IService<AppUser> {

    AppUser getByIdWithoutLogic(Integer id);

    AppUserVo getDetail(Integer id);

    Boolean saveAppUser(AppUser dto);

    Boolean insertList(List<AppUser> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    HttpPageResult<AppUserListVo> searchList(AppUserPageDto dto);

    List<AppUser> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    IPage<AppUser> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);

}
