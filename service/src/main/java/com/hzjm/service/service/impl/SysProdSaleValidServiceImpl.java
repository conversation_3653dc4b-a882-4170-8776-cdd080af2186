package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysProdSaleValidPageDto;
import com.hzjm.service.model.VO.SysProdSaleValidListVo;
import com.hzjm.service.model.VO.SysProdSaleValidVo;
import com.hzjm.service.entity.SysProdSaleValid;
import com.hzjm.service.mapper.SysProdSaleValidMapper;
import com.hzjm.service.service.ISysProdSaleValidService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 上架失败校验池 服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
@Slf4j
@Service
public class SysProdSaleValidServiceImpl extends ServiceImpl<SysProdSaleValidMapper, SysProdSaleValid> implements ISysProdSaleValidService {

    @Override
    public SysProdSaleValid getByIdWithoutLogic(Integer id) {
        SysProdSaleValid data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该上架失败校验池"));
        }

        return data;
    }

    @Override
    public SysProdSaleValidVo getDetail(Integer id) {
        SysProdSaleValid data = getByIdWithoutLogic(id);

        SysProdSaleValidVo vo = new SysProdSaleValidVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdSaleValid(SysProdSaleValid dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdSaleValidListVo> searchList(SysProdSaleValidPageDto dto) {

        LambdaQueryWrapper<SysProdSaleValid> qw = Wrappers.<SysProdSaleValid>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdSaleValid::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdSaleValid::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdSaleValid::getGmtCreate, endTime);

        IPage<SysProdSaleValid> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdSaleValidListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysProdSaleValidListVo vo = new SysProdSaleValidListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysProdSaleValidListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdSaleValid> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdSaleValid> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdSaleValid> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
