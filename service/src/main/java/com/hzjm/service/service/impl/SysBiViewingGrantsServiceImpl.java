package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysBiViewingGrants;
import com.hzjm.service.mapper.SysBiViewingGrantsMapper;
import com.hzjm.service.model.DTO.SysBiViewingGrantsPageDto;
import com.hzjm.service.model.VO.SysBiViewingGrantsListVo;
import com.hzjm.service.model.VO.SysBiViewingGrantsVo;
import com.hzjm.service.service.ISysBiViewingGrantsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Slf4j
@Service
public class SysBiViewingGrantsServiceImpl extends ServiceImpl<SysBiViewingGrantsMapper, SysBiViewingGrants> implements ISysBiViewingGrantsService {

    @Override
    public SysBiViewingGrants getByIdWithoutLogic(Integer id) {
        SysBiViewingGrants data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该");
        }

        return data;
    }

    @Override
    public SysBiViewingGrantsVo getDetail(Integer id) {
        SysBiViewingGrants data = getByIdWithoutLogic(id);

        SysBiViewingGrantsVo vo = new SysBiViewingGrantsVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysBiViewingGrants(SysBiViewingGrants dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysBiViewingGrantsListVo> searchList(SysBiViewingGrantsPageDto dto) {

        LambdaQueryWrapper<SysBiViewingGrants> qw = Wrappers.<SysBiViewingGrants>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysBiViewingGrants::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysBiViewingGrants::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysBiViewingGrants::getGmtCreate, endTime);

        IPage<SysBiViewingGrants> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysBiViewingGrantsListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysBiViewingGrantsListVo vo = new SysBiViewingGrantsListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysBiViewingGrantsListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }


    @Override
    public Boolean insertList(List<SysBiViewingGrants> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysBiViewingGrants> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysBiViewingGrants> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
