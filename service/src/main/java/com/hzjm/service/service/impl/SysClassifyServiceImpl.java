package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.hzjm.common.utils.DateTimeUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.entity.SysClassify;
import com.hzjm.service.mapper.SysClassifyMapper;
import com.hzjm.service.service.ISysClassifyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 平台类别 服务实现类
 *
 * <AUTHOR>
 * @since 2021-02-04
 */
@Slf4j
@Service
public class SysClassifyServiceImpl extends ServiceImpl<SysClassifyMapper, SysClassify> implements ISysClassifyService {

    @Override
    public SysClassify getByIdWithoutLogic(Integer id) {
        SysClassify data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public Boolean saveSysClassify(SysClassify dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getType())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("[type]为必填项"));
            }
            rs = baseMapper.insert(dto) > 0;

            // 赋予显示顺序
            dto.setSortNum(dto.getId().longValue());
            dto.updateById();
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public JSONObject getParentInfo(Integer id) {
        JSONObject data = new JSONObject();

        SysClassify entity = baseMapper.selectByIdWithoutLogic(id);
        String key = getKey(entity);
        getParent(entity, data, key);
        return data;
    }

    @Override
    public JSONObject getParentInfo(List<Integer> idList) {
        JSONObject parentInfo = new JSONObject();
        List<SysClassify> dataList = baseMapper.selectList(Wrappers.<SysClassify>lambdaQuery()
                .orderByDesc(SysClassify::getType, SysClassify::getLevel)
                .in(SysClassify::getId, idList));

        dataList.forEach(data -> {
            String key = getKey(data);
            String wholeName = parentInfo.getString(key + "WholeName");
            if (ObjectUtils.isEmpty(wholeName)) {
                wholeName = "";
            }
            parentInfo.put(key + "WholeName", (data.getLevel() == 1 ? "" : "-") + data.getClassifyName() + wholeName);
        });
        return parentInfo;
    }

    private String getKey(SysClassify data) {
        String key = "";
        switch (data.getType()) {
            // 根据type获取字段名
            case 1:
                key = "test";
                break;
        }
        return key;
    }

    private void getParent(SysClassify data, JSONObject parentInfo, String key) {
        if (ObjectUtils.isEmpty(data)) {
            return;
        }

        SysClassify parent = baseMapper.selectByIdWithoutLogic(data.getParentId());
        if (!ObjectUtils.isEmpty(parent)) {
            // 父id
            parentInfo.put(key + "Id" + parent.getLevel(), parent.getId());
        }

        // 全称
        String wholeName = parentInfo.getString(key + "WholeName");
        if (ObjectUtils.isEmpty(wholeName)) {
            wholeName = "";
        }
        parentInfo.put(key + "WholeName", (data.getLevel() == 1 ? "" : "-") + data.getClassifyName() + wholeName);
        if (data.getLevel() == 1) {
            return;
        } else {
            getParent(parent, parentInfo, key);
        }
    }

    @Override
    public Boolean insertList(List<SysClassify> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {
            data.setGmtCreate(date);
            data.setGmtModify(date);
            data.setStatus(0);
            if (ObjectUtils.isEmpty(data.getLevel())) {
                data.setLevel(1);
            }
        });
        return baseMapper.insertList(dataList);
    }

    @Override
    public Map<Integer, String> nameMap(Integer type) {
        List<SysClassify> classifyList = baseMapper.listWithoutLogic(Wrappers.<SysClassify>lambdaQuery()
                .select(SysClassify::getId, SysClassify::getClassifyName)
                .eq(!ObjectUtils.isEmpty(type), SysClassify::getType, type));
        return classifyList.stream().collect(Collectors.toMap(SysClassify::getId, SysClassify::getClassifyName));
    }
}
