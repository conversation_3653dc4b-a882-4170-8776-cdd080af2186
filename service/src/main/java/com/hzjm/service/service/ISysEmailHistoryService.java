package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysEmailHistory;
import com.hzjm.service.model.DTO.SysEmailHistoryPageDto;
import com.hzjm.service.model.VO.SysEmailHistoryListVo;
import com.hzjm.service.model.VO.SysEmailHistoryVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 邮件历史表 服务类
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
public interface ISysEmailHistoryService extends IService<SysEmailHistory> {

    SysEmailHistory getByIdWithoutLogic(Integer id);

    SysEmailHistoryVo getDetail(Integer id);

    Boolean saveSysEmailHistory(SysEmailHistory dto);

    Boolean insertList(List<SysEmailHistory> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysEmailHistoryListVo> searchList(SysEmailHistoryPageDto dto);

    List<SysEmailHistory> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysEmailHistory> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
