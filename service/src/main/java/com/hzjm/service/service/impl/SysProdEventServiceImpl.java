package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysProdEventPageDto;
import com.hzjm.service.model.VO.SysProdEventListVo;
import com.hzjm.service.model.VO.SysProdEventVo;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.mapper.SysProdEventMapper;
import com.hzjm.service.service.ISysProdEventService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 商品事件 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Slf4j
@Service
public class SysProdEventServiceImpl extends ServiceImpl<SysProdEventMapper, SysProdEvent> implements ISysProdEventService {

    @Override
    public SysProdEvent getByIdWithoutLogic(Integer id) {
        SysProdEvent data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商品事件"));
        }

        return data;
    }

    @Override
    public SysProdEventVo getDetail(Integer id) {
        SysProdEvent data = getByIdWithoutLogic(id);

        SysProdEventVo vo = new SysProdEventVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdEvent(SysProdEvent dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdEventListVo> searchList(SysProdEventPageDto dto) {

        LambdaQueryWrapper<SysProdEvent> qw = Wrappers.<SysProdEvent>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdEvent::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdEvent::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdEvent::getGmtCreate, endTime);

        IPage<SysProdEvent> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdEventListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysProdEventListVo vo = new SysProdEventListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysProdEventListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdEvent> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdEvent> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean relateSale(List<Integer> prodIdList) {
        return baseMapper.relateSale(prodIdList);
    }

    @Override
    public List<SysProdEvent> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
