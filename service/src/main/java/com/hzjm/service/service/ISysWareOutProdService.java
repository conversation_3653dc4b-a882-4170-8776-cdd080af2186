package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysWareOutProd;
import com.hzjm.service.model.DTO.SysWareOutProdPageDto;
import com.hzjm.service.model.VO.SysWareOutProdListVo;
import com.hzjm.service.model.VO.SysWareOutProdVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 出库商品 服务类
 *
 * <AUTHOR>
 * @since 2023-03-03
 */
public interface ISysWareOutProdService extends IService<SysWareOutProd> {

    SysWareOutProd getByIdWithoutLogic(Integer id);

    SysWareOutProdVo getDetail(Integer id);

    Boolean saveSysWareOutProd(SysWareOutProd dto);

    Boolean insertList(List<SysWareOutProd> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysWareOutProdListVo> searchList(SysWareOutProdPageDto dto);

    List<SysWareOutProd> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareOutProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
