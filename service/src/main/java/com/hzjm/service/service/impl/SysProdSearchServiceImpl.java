package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.mapper.SysProdMapper;
import com.hzjm.service.mapper.SysProdSearchMapper;
import com.hzjm.service.model.DTO.SysProdSaveDto;
import com.hzjm.service.model.DTO.SysProdSearchPageDto;
import com.hzjm.service.model.VO.ShopProdRankListVo;
import com.hzjm.service.model.VO.SysProdSearchListVo;
import com.hzjm.service.model.VO.SysProdSearchVo;
import com.hzjm.service.service.ISysProdSearchService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品筛选 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
@Slf4j
@Service
public class SysProdSearchServiceImpl extends ServiceImpl<SysProdSearchMapper, SysProdSearch> implements ISysProdSearchService {

    @Resource
    private SysProdMapper sysProdMapper;

    @Override
    public SysProdSearch getByIdWithoutLogic(Integer id) {
        SysProdSearch data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商品筛选"));
        }

        return data;
    }

    @Override
    public SysProdSearchVo getDetail(Integer id) {
        SysProdSearch data = getByIdWithoutLogic(id);

        SysProdSearchVo vo = new SysProdSearchVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdSearch(SysProdSearch dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdSearchListVo> searchList(SysProdSearchPageDto dto) {

        LambdaQueryWrapper<SysProdSearch> qw = Wrappers.<SysProdSearch>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdSearch::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdSearch::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdSearch::getGmtCreate, endTime);

        IPage<SysProdSearch> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdSearchListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysProdSearchListVo vo = new SysProdSearchListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysProdSearchListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdSearch> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            if(ObjectUtils.isEmpty(data.getGmtCreate())) {
                data.setGmtCreate(date);
            }
            if(ObjectUtils.isEmpty(data.getGmtModify())) {
                data.setGmtModify(date);
            }

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdSearch> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public BigDecimal sumCost(LambdaQueryWrapper<SysProdSearch> qw) {
        return baseMapper.sumCost(qw);
    }

    @Override
    public List<ShopProdRankListVo> shopProdRank(TableDataSearchDto dto, int type) {
        List<ShopProdRankListVo> voList = new ArrayList<>();

        Date beginDate = dto.getBeginDate();
        if (ObjectUtils.isEmpty(beginDate)) {
            beginDate = DateTimeUtils.getNow();
        }
        beginDate = DateTimeUtils.getDateStart(beginDate);
        Date endDate = dto.getEndDate();
        if (ObjectUtils.isEmpty(endDate)) {
            endDate = DateTimeUtils.getNow();
        }
        endDate = DateTimeUtils.getDateEnd(endDate);

        Integer wareId = dto.getWareId(); // 全仓库
        if(!ObjectUtils.isEmpty(wareId)) {
            dto.setWareIdList(new ArrayList<>(Arrays.asList(wareId)));
        }

        QueryWrapper qw = new QueryWrapper();
        qw.select("ifnull(count(0),0) status, shop_id, shop_uid, shop_name");
        switch (type) {
            case 1:
                qw.ge(!ObjectUtils.isEmpty(beginDate), "gmt_in", beginDate);
                qw.le(!ObjectUtils.isEmpty(endDate), "gmt_in", endDate);
                break;
            case 2:
                qw.in("odd_type", SysProdEvent.TypeSend, SysProdEvent.TypeTransport, SysProdEvent.TypeSale);
                qw.ge(!ObjectUtils.isEmpty(beginDate), "gmt_out", beginDate);
                qw.le(!ObjectUtils.isEmpty(endDate), "gmt_out", endDate);
                qw.eq("status", 6); // 已出库
                break;
        }
        qw.eq(!ObjectUtils.isEmpty(wareId), "ware_id", wareId);
        qw.in(!ObjectUtils.isEmpty(dto.getWareIdList()), "ware_id", dto.getWareIdList());
        qw.eq("del_flag", 0);
        qw.isNotNull("shop_id"); // 忽略无主件
        qw.groupBy("shop_id");
        List<SysProdSearch> shopProdList = list(qw);

        List<SysProdSearch> rankList = shopProdList.stream().sorted(Comparator.comparing(SysProdSearch::getStatus).reversed()).collect(Collectors.toList());

        for (SysProdSearch rank : rankList) {
            ShopProdRankListVo vo = new ShopProdRankListVo();

            if (ObjectUtils.isEmpty(rank.getShopId())) {
                vo.setShopName("无主件");
            } else {
                vo.setShopId(rank.getShopId());
                vo.setShopName(rank.getShopName());
                vo.setShopUid(rank.getShopUid());
            }
            vo.setNum(rank.getStatus());

            voList.add(vo);
        }

        if(voList.size() > 20) {
            voList = voList.subList(0,20);
        }

        return voList.stream().sorted(Comparator.comparing(ShopProdRankListVo::getNum)).collect(Collectors.toList());
    }

    @Override
    public List<SysProdSearch> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }
    @Override
    public Date getOldGmtInByoneId(String oneId){
        return baseMapper.selectOldGmtInByoneId(oneId);
    }

    @Override
    @Transactional
    public Boolean saveSysSearchProd(SysProd dto, SysProdSaveDto sysProdSaveDto) {
        if (ObjectUtils.isEmpty(dto) || ObjectUtils.isEmpty(dto.getId())){
            return false;
        }

        SysProd data =  sysProdMapper.selectByIdWithoutLogic(dto.getId());

        if (ObjectUtils.isEmpty(data) ||ObjectUtils.isEmpty(data.getOneId())){
            return false;
        }

        //修改关联物流单号时，同时使用物流单号修改同批次的关联物流单号
        if (!ObjectUtils.isEmpty(sysProdSaveDto)
                && !ObjectUtils.isEmpty(sysProdSaveDto.getInLogNo())
                && !ObjectUtils.isEmpty(sysProdSaveDto.getLogNoRelatedList())
        ) {
            this.update(Wrappers.<SysProdSearch>lambdaUpdate()
                    .set(!ObjectUtils.isEmpty(sysProdSaveDto.getLogNoRelatedList()), SysProdSearch::getInLogNoRelated, sysProdSaveDto.getLogNoRelatedList())
                    .eq(SysProdSearch::getInLogNo, sysProdSaveDto.getInLogNo())
                    .eq(SysProdSearch::getSearchType, 1));
        }

        // search 更新
        return this.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .set(SysProdSearch::getDelFlag, 0)
                .set(!ObjectUtils.isEmpty(sysProdSaveDto.getLogNoRelatedList()), SysProdSearch::getInLogNoRelated, sysProdSaveDto.getLogNoRelatedList())
                .set(!ObjectUtils.isEmpty(sysProdSaveDto.getCheckRemark()), SysProdSearch::getCheckRemark, sysProdSaveDto.getCheckRemark())
                .eq(SysProdSearch::getOneId, data.getOneId())
                .eq(SysProdSearch::getSearchType, 1));
    }

}
