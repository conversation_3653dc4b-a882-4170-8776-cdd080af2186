package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.RecapYearly;

/**
 * <AUTHOR>
 * @date 2024/12/17 18:03
 * @description: 商家年度统计报表-保存增强接口
 */
public interface IRecapYearlySaveService extends IService<RecapYearly> {


    /**
     * 保存商家年度统计数据
     *
     * @param recapYearly 商家年度统计数据
     * @return 保存结果
     */
    Boolean saveByCustom(RecapYearly recapYearly);
}
