package com.hzjm.service.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.RecapMonthly;
import com.hzjm.service.mapper.DashboardAdminMapper;
import com.hzjm.service.mapper.DashboardMapper;
import com.hzjm.service.mapper.RecapMonthlyMapper;
import com.hzjm.service.model.DTO.TaskRecapReqDto;
import com.hzjm.service.model.VO.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/11 14:28
 * @description: RecapMonthly服务实现类
 */
@Slf4j
@Service
public class RecapMonthlyServiceImpl extends ServiceImpl<RecapMonthlyMapper, RecapMonthly> implements IRecapMonthlyService {

    @Resource
    DashboardMapper dashboardMapper;
    @Resource
    RecapMonthlyMapper recapMonthlyMapper;
    @Resource
    DashboardAdminMapper dashboardAdminMapper;
    @Resource
    private org.thymeleaf.spring5.SpringTemplateEngine templateEngine;
    @Resource
    private IRecapMonthlySaveService iRecapMonthlySaveService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createRecapMonthly(TaskRecapReqDto recapReqDto) {
        log.info("生成商家月度统计，商家ids：{}，开始时间：{}，结束时间：{}", recapReqDto.getShopIds(), recapReqDto.getStartDate(), recapReqDto.getEndDate());
        if (CollUtil.isEmpty(recapReqDto.getShopIds())) {
            log.error("商家ids为空");
            return false;
        }
        if (BeanUtil.isEmpty(recapReqDto.getStartDate()) || BeanUtil.isEmpty(recapReqDto.getEndDate())) {
            log.error("开始时间或结束时间为空");
            return false;
        }
        List<RecapMonthly> recapMonthlyList = CollUtil.newArrayList();
        for (Long shopId : recapReqDto.getShopIds()) {
            //查询总GMV,查询订单总数
            CompletableFuture<RecapMonthlyOrderVo> recapMonthlyOrderVoFuture = CompletableFuture
                    .supplyAsync(() -> dashboardAdminMapper.queryRecapMonthlyOrder(shopId, recapReqDto.getStartDate(), recapReqDto.getEndDate()));
            //查询商品入库总量
            CompletableFuture<List<AdminHomeBiUserListVo>> listInboundTotalCompletableFuture = CompletableFuture.supplyAsync(
                    () -> dashboardAdminMapper.selectUserInbound(Collections.singletonList(Math.toIntExact(shopId)), 0, 1
                            , recapReqDto.getStartDate(), recapReqDto.getEndDate(), null, null, null));
            //查询reship数
            CompletableFuture<AdminHomeBiDataTrendVo> reshipOrdersCurrentCycleFuture = CompletableFuture
                    .supplyAsync(() -> dashboardAdminMapper.selectReshipOrders(recapReqDto.getStartDate(), recapReqDto.getEndDate(), shopId, null, null));
            //获取商家平台统计信息
            CompletableFuture<List<ShopUidPlListVo>> shopUidPlListVosFuture = CompletableFuture
                    .supplyAsync(() -> dashboardAdminMapper.queryShopIdPlatformCount(shopId, recapReqDto.getStartDate(), recapReqDto.getEndDate()));
            //获取商家订单排行榜
            CompletableFuture<List<ShopUserRankVo>> shopUserRankVosFuture = CompletableFuture
                    .supplyAsync(() -> dashboardMapper.queryShopUserRank(recapReqDto));
            //获取商家商品在列表中的数量
            CompletableFuture<Long> listedTotalFuture = CompletableFuture
                    .supplyAsync(() -> dashboardAdminMapper.queryItemListedTotal(shopId, null, null));
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    recapMonthlyOrderVoFuture,
                    listInboundTotalCompletableFuture,
                    reshipOrdersCurrentCycleFuture,
                    shopUidPlListVosFuture,
                    shopUserRankVosFuture,
                    listedTotalFuture
            );
            allFutures.join();
            try {
                RecapMonthlyOrderVo recapMonthlyOrderVo = recapMonthlyOrderVoFuture.join();
                log.info("商家平台月度统计信息：recapMonthlyOrderVo: {} ", recapMonthlyOrderVo.toString());
                List<AdminHomeBiUserListVo> listInboundTotal = listInboundTotalCompletableFuture.join();
                log.info("商家平台月度统计信息：listInboundTotal: {} ", listInboundTotal.toString());
                AdminHomeBiDataTrendVo reshipOrdersCurrentCycle = reshipOrdersCurrentCycleFuture.join();
                log.info("商家平台月度统计信息：reshipOrdersCurrentCycle: {} ", reshipOrdersCurrentCycle.toString());
                List<ShopUidPlListVo> shopUidPlListVos = shopUidPlListVosFuture.join();
                log.info("商家平台月度统计信息：shopUidPlListVos: {} ", shopUidPlListVos);
                List<ShopUserRankVo> shopUserRankVos = shopUserRankVosFuture.join();
                Long listedTotal = listedTotalFuture.join();
                log.info("商家平台月度统计信息：listedTotal: {} ", listedTotal);
                RecapMonthly recapMonthly = RecapMonthly.buildRecapMonthly(
                        recapReqDto
                        , shopId
                        , recapMonthlyOrderVo
                        , listInboundTotal
                        , reshipOrdersCurrentCycle
                        , shopUidPlListVos
                        , shopUserRankVos
                        , listedTotal);
                recapMonthlyList.add(recapMonthly);
            } catch (Exception e) {
                log.error("生成商家月度统计失败 商家shop ID :{}", shopId);
            }
        }
        return iRecapMonthlySaveService.saveBatchCustomList(recapMonthlyList);
    }

    @Override
    public List<RecapMonthlyVo> queryRecapMonthlyByCondition(TaskRecapReqDto recapReqDto) {
        log.info("查询商家月度统计，商家ids：{}，开始时间：{}，结束时间：{}", recapReqDto.getShopIds(), recapReqDto.getStartDate(), recapReqDto.getEndDate());
        QueryWrapper<RecapMonthly> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("shop_uid", recapReqDto.getShopIds())
                .eq("DATE(start_date)", DateUtil.format(recapReqDto.getStartDate(), "yyyy-MM-dd"))
                .eq("DATE(end_date)", DateUtil.format(recapReqDto.getEndDate(), "yyyy-MM-dd"));
        List<RecapMonthlyVo> recapMonthlyVos = list(queryWrapper).stream()
                .map(RecapMonthly::createVo)
                .collect(Collectors.toList());
        log.info("查询商家月度统计成功");
        return recapMonthlyVos;
    }

    @Override
    public List<Long> queryUsedShopIds(Date beginTime, Date endTime) {
        log.info("查询参与统计的商家，开始时间：{}，结束时间：{}", beginTime, endTime);
        List<Long> usedShopIds = dashboardAdminMapper.queryUsedShopIds(beginTime, endTime);
        log.info("查询参与统计的商家成功,商家数量：{}", usedShopIds.size());
        return usedShopIds;
    }

    @Override
    public String createEmailContent(RecapMonthlyVo recapMonthlyVo) {
        log.info("生成邮件内容,商户ID:{}", recapMonthlyVo.getShopUid());
        if (BeanUtil.isEmpty(recapMonthlyVo)) {
            log.error("recapMonthlyVo为空");
            return null;
        }
        Map<String, Object> variables = BeanUtil.beanToMap(recapMonthlyVo);
        String content = processTemplate(variables);
        log.info("生成邮件内容成功,商户ID:{}", recapMonthlyVo.getShopUid());
        return content;
    }

    @Override
    public List<RecapMonthly> queryRecapYearlyByCondition(TaskRecapReqDto recapReqDto) {
        if (BeanUtil.isEmpty(recapReqDto.getYear())) {
            log.error("年度为空");
            return Collections.emptyList();
        }
        if (recapReqDto.getShopUid() == null) {
            log.error("商家uid为空");
            return Collections.emptyList();
        }
        List<RecapMonthly> recapMonthlies = recapMonthlyMapper.queryRecapYearlyByCondition(recapReqDto);
        // 过滤空对象SUM 会导致查询出null 值对象
        if (CollUtil.isNotEmpty(recapMonthlies)) {
            recapMonthlies = recapMonthlies.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        return recapMonthlies;
    }

    @Override
    public RecapMonthly queryBestMonthRecapMonthly(TaskRecapReqDto recapReqDto) {
        log.info("查询最佳月份-月度统计数据，商家ids：{}，开始时间：{}，结束时间：{}", recapReqDto.getShopIds(), recapReqDto.getStartDate(), recapReqDto.getEndDate());
        QueryWrapper<RecapMonthly> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .eq("shop_uid", recapReqDto.getShopUid())
                .eq("YEAR(start_date)", recapReqDto.getYear())
                .eq("YEAR(end_date)", recapReqDto.getYear())
                .orderByDesc("total_gmv")
                .last("LIMIT 1");
        RecapMonthly bestMonthRecap = getOne(queryWrapper);
        log.info("查询最佳月份-月度统计数据");
        return bestMonthRecap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean initRecapMonthly(Integer year) {
        Map<DateTime, DateTime> initDateMap = getInitDateMap(year);
        log.info("2024-商家月度统计数据初始化（1-12月数据补全）  初始化日期映射: {}", initDateMap);
        initDateMap.forEach(
                (beginTime, endTime) -> {
                    //  获取参与统计的商家
                    List<Long> shopIds = queryUsedShopIds(beginTime, endTime);
                    // 生成商家月度统计数据
                    TaskRecapReqDto taskRecapReqDto = TaskRecapReqDto.builder()
                            .shopIds(shopIds)
                            .startDate(beginTime)
                            .endDate(endTime).build();
                    createRecapMonthly(taskRecapReqDto);
                });
        return true;
    }

    /**
     * 获取指定年份前12月起止日期
     *
     * @param year 年份
     * @return Map<DateTime, DateTime>
     */
    private Map<DateTime, DateTime> getInitDateMap(int year) {
        Map<DateTime, DateTime> initDateMap = new HashMap<>(12);
        for (int month = 1; month <= 12; month++) {
            DateTime beginTime = DateUtil.parse(year + "-" + month + "-01 00:00:00");
            DateTime endTime = DateUtil.endOfMonth(beginTime);
            initDateMap.put(beginTime, endTime);
        }
        return initDateMap;
    }


    /**
     * 处理邮件模板
     *
     * @param variables template variables
     * @return template content
     */
    private String processTemplate(Map<String, Object> variables) {
        Context context = new Context();
        context.setVariables(variables);
        return templateEngine.process(com.hzjm.service.constants.ServiceConstants.EMAIL_RECAP_MONTHLY, context);
    }
}
