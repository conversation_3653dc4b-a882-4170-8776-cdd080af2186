package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.entity.po.SysRepairOrderDetailPO;
import com.hzjm.service.entity.po.SysRepairOrderPO;
import com.hzjm.service.mapper.SysRepairOrderMapper;
import com.hzjm.service.model.DTO.RepairProcessDto;
import com.hzjm.service.model.DTO.RepairTaskStatus;
import com.hzjm.service.model.DTO.SysRepairOrderPageDto;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 维修单主表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
@Slf4j
@Service
public class SysRepairOrderServiceImpl extends ServiceImpl<SysRepairOrderMapper, SysRepairOrder>
        implements ISysRepairOrderService {
    @Resource
    private ISysProductRepairRelService sysProductRepairRelService;
    @Resource
    private ISysRepairProjectService sysRepairProjectService;
    @Resource
    private ISysWareShelvesProdService sysWareShelvesProdService;
    @Resource
    private ISysMoneyService iSysMoneyService;
    @Resource
    private ISysCodePoolService iSysCodePoolService;
    @Resource
    private ISysUserService iSysUserService;
    @Resource
    private ISysFileService sysFileService;
    @Resource
    private ISysBillService iSysBillService;
    @Resource
    private ISysWareInProdService sysWareInProdService;
    @Resource
    private ISysFileService iSysFileService;
    @Resource
    private ISysProdSearchService sysProdSearchService;
    @Resource
    private ISysProdService sysProdService;
    @Resource
    private ISysProdEventService sysProdEventService;


    @Override
    public SysRepairOrder getByIdWithoutLogic(Integer id) {
        SysRepairOrder data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该维修单主表");
        }

        return data;
    }

    @Override
    public SysRepairOrderVo getDetail(Integer id, String oneId, Integer shopId) {
        if (ObjectUtils.isEmpty(id) && ObjectUtils.isEmpty(oneId)) {
            throw new BaseException("ID 和 oneId 不能同时为空");
        }

        List<SysRepairOrderDetailPO> data = baseMapper.selectRepairOrderDetail(id, null, oneId, shopId);
        Assert.notEmpty(data, "查询失败，未找到该维修单详情");

        SysRepairOrderVo vo = new SysRepairOrderVo();
        BeanUtils.copyProperties(data.get(0), vo);
        vo.setId(data.get(0).getRepairOrderId());

        vo.setApprovalRejectImage(iSysFileService.getFileUrl(vo.getId(), SysConstants.SYS_FILE_TYPE_9));
        vo.setRepairImageUrls(iSysFileService.getFileUrl(vo.getId(), SysConstants.SYS_FILE_TYPE_10));
        vo.setRepairFailureImage(iSysFileService.getFileUrl(vo.getId(), SysConstants.SYS_FILE_TYPE_11));

        this.setRepairOrderDetail(vo, data);

        return vo;
    }

    /**
     * 根据批次号查询维修单列表
     */
    @Override
    public List<SysRepairOrderVo> getDetail(String repairBatchNo, Integer shopId) {
        Assert.notNull(repairBatchNo, "维修批次号不能为空");

        List<SysRepairOrderDetailPO> data = baseMapper.selectRepairOrderDetail(null, repairBatchNo, null, shopId);
        Assert.notEmpty(data, "查询失败，未找到该商品的维修详情");

        List<SysRepairOrderVo> voList = new ArrayList<>();
        for (SysRepairOrderDetailPO repairOrderDetailPO : data) {
            SysRepairOrderVo vo = new SysRepairOrderVo();
            BeanUtils.copyProperties(repairOrderDetailPO, vo);

            vo.setId(repairOrderDetailPO.getRepairOrderId());

            this.setRepairOrderDetail(vo, data);

            voList.add(vo);
        }
        return voList;
    }

    /**
     * 商家通过 oneId 查询维修项目详情
     * <p>
     * 没有生成维修单的商品缺少维修单的主键ID，通过这个接口查询维修项目详情
     */
    @Override
    public SysRepairOrderVo getDetailByOneId(String oneId) {
        if (ObjectUtils.isEmpty(oneId)) {
            throw new BaseException("oneId 不能为空");
        }

        List<SysRepairOrderDetailPO> data = baseMapper.selectShopRepairProjectDetail(oneId);
        Assert.notEmpty(data, "查询失败，未找到该商品的维修项目详情");

        SysRepairOrderVo vo = new SysRepairOrderVo();
        BeanUtils.copyProperties(data.get(0), vo);

        this.setRepairOrderDetail(vo, data);

        return vo;
    }

    /**
     * 设置维修单详情
     *
     * @param vo
     * @param data
     */
    private void setRepairOrderDetail(SysRepairOrderVo vo, List<SysRepairOrderDetailPO> data) {
        // 维修项目信息
        Map<String, List<SysRepairOrderDetailPO>> repairProjectMap = new HashMap<>(); // key 是维修项目ID
        // 相同的维修项目ID，合并到一起
        for (SysRepairOrderDetailPO detail : data) {
            String repairNo = detail.getRepairNo();
            if (repairNo != null) {
                repairProjectMap.computeIfAbsent(repairNo, k -> new ArrayList<>()).add(detail);
            }
        }
        if (!ObjectUtils.isEmpty(vo.getRepairNo())){
            vo.setRepairProjectList(repairProjectMap.get(vo.getRepairNo()).stream().map(e -> {
                SysRepairProjectVo projectVo = new SysRepairProjectVo();
                BeanUtils.copyProperties(e, projectVo);
                projectVo.setAmount(e.getAmount());
                projectVo.setId(e.getRepairProjectId());
                return projectVo;
            }).collect(Collectors.toList()));
        }else if (!ObjectUtils.isEmpty(vo.getOneId()) && !ObjectUtils.isEmpty(data)){
            vo.setRepairProjectList(data.stream().map(e -> {
                SysRepairProjectVo projectVo = new SysRepairProjectVo();
                BeanUtils.copyProperties(e, projectVo);
                projectVo.setAmount(e.getAmount());
                projectVo.setId(e.getRepairProjectId());
                return projectVo;
            }).collect(Collectors.toList()));
        }

        // 商品信息
        List<SysWareInProd> productList = sysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery()
                .eq(SysWareInProd::getInId, vo.getInId()));
        // 验货照片
        productList.forEach(inProd -> {
            inProd.setImgList(iSysFileService.getFileUrl(inProd.getId(), SysFile.TypeInProdCheck));
        });

        vo.setProductList(productList);

        if (!ObjectUtils.isEmpty(vo.getInId())) { // 存在入库的才会查询图片信息
            // QcImage
            if (!ObjectUtils.isEmpty(vo.getProductList())
                    && !ObjectUtils.isEmpty(vo.getProductList().get(0))
                    && !ObjectUtils.isEmpty(vo.getProductList().get(0).getId())) {
                vo.setQcImage(sysFileService.getFileUrl(vo.getProductList().get(0).getId(), SysConstants.SYS_FILE_TYPE_1));
            }
            // 维修单-审批拒绝图片
            vo.setApprovalRejectImage(sysFileService.getFileUrl(vo.getId(), SysConstants.SYS_FILE_TYPE_9));
            // 维修单-维修留底图片
            vo.setRepairImageUrls(sysFileService.getFileUrl(vo.getId(), SysConstants.SYS_FILE_TYPE_10));
            // 维修单-维修失败图片
            vo.setRepairFailureImage(sysFileService.getFileUrl(vo.getId(), SysConstants.SYS_FILE_TYPE_11));
        }

        // 预估维修时长 = 前方所有鞋子的维修项目时间加在一起
        List<SysRepairOrderDetailPO> upcomingShoeRepairTasksByTime = baseMapper
                .selectUpcomingShoeRepairTasksByTime(new Date());
        // 计算所有任务和项目的总小时数
        BigDecimal projectHours = BigDecimal.ZERO; // 自身项目总小时数
        if (vo.getRepairProjectList() != null) {
            projectHours = vo.getRepairProjectList().stream()
                    .map(SysRepairProjectVo::getEstimatedHours)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        // 鞋子维修任务总小时数
        BigDecimal totalAllHours =
                upcomingShoeRepairTasksByTime.stream()
                        .map(SysRepairOrderDetailPO::getEstimatedHours)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                        .add(projectHours);
        // 将总小时数转换为天数（24 小时为一天）
        BigDecimal totalAllDays = totalAllHours.divide(new BigDecimal("24"), MathContext.DECIMAL32);
        // 设置 totalAllDays 最大为 30 天
        totalAllDays = totalAllDays.min(new BigDecimal("30"));
        vo.setEstimatedRepairDays(totalAllDays);
        // 等待维修数
        vo.setWaitRepairCount((int) upcomingShoeRepairTasksByTime.stream()
                .map(SysRepairOrderDetailPO::getOneId)
                .distinct()
                .count());
    }

    @Override
    @TrimParam
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveSysRepairOrder(SysRepairOrder dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        List<SysProdEvent> sysProdEventList = new ArrayList<>();

        if (ObjectUtils.isEmpty(dto.getId())) {
            Assert.notNull(dto,  "新增维修单，参数不能为空");
            Assert.notNull(dto.getOneId(),  "新增维修单，one ID不能为空");

            List<SysRepairOrder> sysRepairOrderList = this.list(Wrappers.<SysRepairOrder>lambdaQuery()
                    .eq(SysRepairOrder::getOneId, dto.getOneId())
            );

            sysRepairOrderList.forEach(sysRepairOrder -> {
                if (!ObjectUtils.isEmpty(sysRepairOrder.getApprovalStatus()) &&
                        (ApprovalStatusEnum.APPROVAL_PENDING == sysRepairOrder.getApprovalStatus())) {
                    throw new BaseException("该商品已存在待审批的维修单");
                }

                if (!ObjectUtils.isEmpty(sysRepairOrder.getRepairStatus()) &&
                        (RepairStatusEnum.REPAIR_PENDING == sysRepairOrder.getRepairStatus()
                        || RepairStatusEnum.REPAIR_PROCESSING == sysRepairOrder.getRepairStatus()
                        )) {
                    throw new BaseException("该商品已存在维修中的维修单");
                }

                if (!ObjectUtils.isEmpty(sysRepairOrder.getRepairStatus()) &&
                        (RepairStatusEnum.REPAIR_SUCCESS == sysRepairOrder.getRepairStatus())) {
                    throw new BaseException("该商品已存在维修成功的维修单");
                }

                if (!ObjectUtils.isEmpty(sysRepairOrder.getRepairStatus()) &&
                        (RepairStatusEnum.REPAIR_FAILURE == sysRepairOrder.getRepairStatus())) {
                    throw new BaseException("该商品已存在维修失败的维修单");
                }

                if (!ObjectUtils.isEmpty(sysRepairOrder.getRepairStatus()) &&
                        (RepairStatusEnum.REPAIR_LOST_PARCEL == sysRepairOrder.getRepairStatus())) {
                    throw new BaseException("该商品已存在包裹丢失的维修单");
                }

            });
            // 通过商品ID查询货架号
            SysWareShelvesProd shelvesProd = sysWareShelvesProdService.getOne(
                    Wrappers.<SysWareShelvesProd>lambdaQuery().eq(SysWareShelvesProd::getProdId, dto.getProductId()),
                    false);
            // 货架号存在
            if (!ObjectUtils.isEmpty(shelvesProd) && !ObjectUtils.isEmpty(shelvesProd.getShelvesId())) {
                dto.setShelfId(shelvesProd.getShelvesId());
            }
            // 审批状态
            dto.setApprovalStatus(ApprovalStatusEnum.APPROVAL_PENDING);
            // 维修单号
            dto.setRepairNo(iSysCodePoolService.build(28, 1).get(0));
            rs = baseMapper.insert(dto) > 0;
            // 建立维修单与维修项目的关联关系
            sysProductRepairRelService.lambdaUpdate()
                    .eq(SysProductRepairRel::getProductId, dto.getProductId())
                    .set(SysProductRepairRel::getRepairOrderId, dto.getId())
                    .update();

            // 更新商品状态
            sysProdSearchService.lambdaUpdate()
                    .set(SysProdSearch::getStatus, 12)
                    .set(SysProdSearch::getOddNo, dto.getRepairNo())
                    .set(SysProdSearch::getGmtModify,new Date())
                    .eq(SysProdSearch::getOneId, dto.getOneId())
                    .eq(SysProdSearch::getDelFlag, 0)
                    .eq(SysProdSearch::getSearchType, 1)
                    .update();
            sysProdService.lambdaUpdate()
                    .eq(SysProd::getOneId, dto.getOneId())
                    .eq(SysProd::getDelFlag, 0)
                    .set(SysProd::getStatus, 12)
                    .set(SysProd::getOddNo, dto.getRepairNo())
                    .set(SysProd::getGmtModify,new Date())
                    .update();


            // 商品事件：新增维修单
            SysProdEvent event = new SysProdEvent()
                .setProdId(dto.getProductId())
                .setShopId(dto.getShopId())
                .setType(SysProdEvent.TypeRepair)
                .setDescription("新增维修单")
                .setRelationId(dto.getId());
            sysProdEventList.add(event);
        } else if (isDelete) {
            SysRepairOrder sysRepairOrder = this.getByIdWithoutLogic(dto.id);
            // 删除维修单与维修项目的关联关系, 清空维修单ID
            sysProductRepairRelService.lambdaUpdate()
                    .eq(SysProductRepairRel::getRepairOrderId, dto.getId())
                    .set(SysProductRepairRel::getRepairOrderId, null)
                    .update();
            rs = baseMapper.deleteById(dto.getId()) > 0;
            // 修改状态
            sysProdSearchService.lambdaUpdate()
                    .eq(SysProdSearch::getProdId, sysRepairOrder.getProductId())
                    .eq(SysProdSearch::getSearchType, 1)

                    .set(SysProdSearch::getRepairFlag, RepairFlagEnum.NOT_REPAIRABLE)
                    .set(SysProdSearch::getGmtModify, new Date())
                    .set(SysProdSearch::getStatus, 1)
                    .update();
            sysProdService.lambdaUpdate()
                    .eq(SysProd::getId, sysRepairOrder.getProductId())
                    .eq(SysProd::getDelFlag, 0)

                    .set(SysProd::getStatus, 1)
                    .set(SysProd::getGmtModify,new Date())
                    .update();
            // 商品事件：删除维修单
            SysProdEvent event = new SysProdEvent()
                .setProdId(sysRepairOrder.getProductId())
                .setShopId(sysRepairOrder.getShopId())
                .setType(SysProdEvent.TypeRepair)
                .setDescription("删除维修单")
                .setRelationId(sysRepairOrder.getId());
            sysProdEventList.add(event);
        } else {
            SysRepairOrder sysRepairOrder = this.getByIdWithoutLogic(dto.id);
            // 取消维修单修改状态
            if (!ObjectUtils.isEmpty(dto.getApprovalStatus()) && dto.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_CANCEL) {
                sysProdSearchService.lambdaUpdate()
                        .eq(SysProdSearch::getProdId, sysRepairOrder.getProductId())
                        .eq(SysProdSearch::getSearchType, 1)

                        .set(SysProdSearch::getGmtModify, new Date())
                        .set(SysProdSearch::getStatus, 1)
                        .update();
                sysProdService.lambdaUpdate()
                        .eq(SysProd::getId, sysRepairOrder.getProductId())
                        .eq(SysProd::getDelFlag, 0)

                        .set(SysProd::getStatus, 1)
                        .set(SysProd::getGmtModify,new Date())
                        .update();

                // 商品事件：取消维修单审批
                SysProdEvent event = new SysProdEvent()
                        .setProdId(sysRepairOrder.getProductId())
                        .setShopId(sysRepairOrder.getShopId())
                        .setType(SysProdEvent.TypeRepair)
                        .setDescription("取消维修单审批")
                        .setRelationId(sysRepairOrder.getId());
                sysProdEventList.add(event);
            }
            // 审批同意
            if (!ObjectUtils.isEmpty(dto.getApprovalStatus()) && dto.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_APPROVE) {
                dto.setGmtApprovalData(new Date());
                dto.setApprovalUserid(JwtContentHolder.getUserId());
                dto.setRepairStatus(RepairStatusEnum.REPAIR_PENDING);

                // 扣费
                Map<Integer, BigDecimal> feeMap = this.getRepairOrderFee(Collections.singletonList(dto.getId()));
                BigDecimal totalFee = feeMap.get(dto.getId());

                // 扣款 - 创建 RepairProcessDto 对象
                RepairProcessDto repairProcessDto = new RepairProcessDto();
                repairProcessDto.setTotalFee(totalFee);
                repairProcessDto.setShopId(sysRepairOrder.getShopId());
                repairProcessDto.setRepairId(dto.getId());
                repairProcessDto.setRepairNo(sysRepairOrder.getRepairNo());
                repairProcessDto.setRemark(sysRepairOrder.getRepairNo() + "审批通过");
                repairProcessDto.setOperationType(SysBill.TypeShopDrawRepairFee); // 使用扣款类型

                // 调用新的方法签名
                List<RepairProcessDto> repairProcessDtoList = new ArrayList<>();
                repairProcessDtoList.add(repairProcessDto);
                iSysBillService.processRepairRecords(repairProcessDtoList);
                // 商品事件：维修单审批通过
                SysProdEvent event = new SysProdEvent()
                    .setProdId(sysRepairOrder.getProductId())
                    .setShopId(sysRepairOrder.getShopId())
                    .setType(SysProdEvent.TypeRepair)
                    .setDescription("维修单审批通过")
                    .setRelationId(sysRepairOrder.getId());
                sysProdEventList.add(event);
            }

            // 审批拒绝
            if (!ObjectUtils.isEmpty(dto.getApprovalStatus()) && dto.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_REFUSE) {
                dto.setGmtApprovalData(new Date());
                dto.setApprovalUserid(JwtContentHolder.getUserId());
                // 保存审批拒绝图片
                if (!ObjectUtils.isEmpty(dto.getImageUrls())) {
                    sysFileService.resetFile(dto.getImageUrls(), dto.getId(), SysConstants.SYS_FILE_TYPE_9);
                }
                // 同步维修项目
                if (!ObjectUtils.isEmpty(dto.getRepairIdList())){
                    this.resetByRepairIdList(sysRepairOrder.getProductId(),sysRepairOrder.getOneId(),dto.getRepairIdList());
                }
                sysProdSearchService.lambdaUpdate()
                        .eq(SysProdSearch::getProdId, sysRepairOrder.getProductId())
                        .eq(SysProdSearch::getSearchType, 1)

                        .set(SysProdSearch::getGmtModify, new Date())
                        .set(SysProdSearch::getStatus, 1)
                        .update();
                sysProdService.lambdaUpdate()
                        .eq(SysProd::getId, sysRepairOrder.getProductId())
                        .eq(SysProd::getDelFlag, 0)

                        .set(SysProd::getStatus, 1)
                        .set(SysProd::getGmtModify,new Date())
                        .update();
                // 商品事件：维修单审批拒绝
                SysProdEvent event = new SysProdEvent()
                    .setProdId(sysRepairOrder.getProductId())
                    .setShopId(sysRepairOrder.getShopId())
                    .setType(SysProdEvent.TypeRepair)
                    .setDescription("维修单审批拒绝")
                    .setRelationId(sysRepairOrder.getId());
                sysProdEventList.add(event);
            }

            // 维修成功
            if (!ObjectUtils.isEmpty(dto.getRepairStatus()) && dto.getRepairStatus() == RepairStatusEnum.REPAIR_SUCCESS) {
                // 保存留底照片
                if (!ObjectUtils.isEmpty(dto.getRepairImageUrls())) {
                    sysFileService.resetFile(dto.getRepairImageUrls(), dto.getId(), SysConstants.SYS_FILE_TYPE_10);
                }

                // 更新鞋子的验货结果为合格,状态为合格, 维修标志为不可维修
                sysProdSearchService.lambdaUpdate()
                    .set(SysProdSearch::getCheckResult, 1)
                    .set(SysProdSearch::getStatus, 1)
                    .set(SysProdSearch::getGmtModify,new Date())
                    .set(SysProdSearch::getRepairFlag, RepairFlagEnum.NOT_REPAIRABLE)

                    .eq(SysProdSearch::getSearchType, 1)
                    .eq(SysProdSearch::getProdId, sysRepairOrder.getProductId())
                    .update();
                sysProdService.lambdaUpdate()
                        .eq(SysProd::getId, sysRepairOrder.getProductId())
                        .eq(SysProd::getDelFlag, 0)

                        .set(SysProd::getStatus, 1)
                        .set(SysProd::getGmtModify,new Date())
                        .update();

                // 商品事件：维修单维修成功
                SysProdEvent event = new SysProdEvent()
                    .setProdId(sysRepairOrder.getProductId())
                    .setShopId(sysRepairOrder.getShopId())
                    .setType(SysProdEvent.TypeRepair)
                    .setDescription("维修单维修成功")
                    .setRelationId(sysRepairOrder.getId());
                sysProdEventList.add(event);
            }

            // 维修失败 && 包裹丢失
            if (!ObjectUtils.isEmpty(dto.getRepairStatus())
                && (dto.getRepairStatus() == RepairStatusEnum.REPAIR_FAILURE || dto.getRepairStatus() == RepairStatusEnum.REPAIR_LOST_PARCEL)) {
                // 保存留底照片
                if (!ObjectUtils.isEmpty(dto.getRepairImageUrls())) {
                    sysFileService.resetFile(dto.getRepairImageUrls(), dto.getId(), SysConstants.SYS_FILE_TYPE_10);
                }
                // 保存维修失败图片
                if (!ObjectUtils.isEmpty(dto.getImageUrls())) {
                    sysFileService.resetFile(dto.getImageUrls(), dto.getId(), SysConstants.SYS_FILE_TYPE_11);
                }

                // 维修失败 && 包裹丢失, 不在进行二次维修
                sysProdSearchService.lambdaUpdate()
                    .eq(SysProdSearch::getProdId, sysRepairOrder.getProductId())
                    .eq(SysProdSearch::getSearchType, 1)

                    .set(SysProdSearch::getRepairFlag, RepairFlagEnum.NOT_REPAIRABLE)
                    .set(SysProdSearch::getGmtModify, new Date())
                    .set(SysProdSearch::getStatus, 1)
                    .update();
                sysProdService.lambdaUpdate()
                        .eq(SysProd::getId, sysRepairOrder.getProductId())
                        .eq(SysProd::getDelFlag, 0)

                        .set(SysProd::getStatus, 1)
                        .set(SysProd::getGmtModify,new Date())
                        .update();

                // 商品事件：维修单维修失败
                SysProdEvent event = new SysProdEvent()
                    .setProdId(sysRepairOrder.getProductId())
                    .setShopId(sysRepairOrder.getShopId())
                    .setType(SysProdEvent.TypeRepair)
                    .setDescription("维修单维修失败")
                    .setRelationId(sysRepairOrder.getId());
                sysProdEventList.add(event);
            }

            rs = baseMapper.updateById(dto) > 0;
        }
        if (!ObjectUtils.isEmpty(sysProdEventList)) {
           sysProdEventService.saveBatch(sysProdEventList);
        }
        return rs;
    }

    /**
     * 查询维修单费用，美元
     *
     * @param repairOrderIds 维修单ID集合
     * @return 维修单ID和费用的map
     */
    @Override
    public Map<Integer, BigDecimal> getRepairOrderFee(List<Integer> repairOrderIds) {
        Assert.notNull(repairOrderIds, "维修单ID集合不能为空");
        Map<Integer, BigDecimal> feeMap = new HashMap<>();

        // 查询维修单与维修项目的关联关系
        List<SysProductRepairRel> sysProductRepairRels = sysProductRepairRelService
                .list(Wrappers.<SysProductRepairRel>lambdaQuery()
                        .select(SysProductRepairRel::getRepairOrderId, SysProductRepairRel::getRepairProjectId)
                        .in(SysProductRepairRel::getRepairOrderId, repairOrderIds));
        Assert.notNull(sysProductRepairRels, "维修项目查询为空");

        // 获取维修项目ID集合
        List<Integer> repairProjectIds = sysProductRepairRels.stream()
                .filter(sysProductRepairRel -> !ObjectUtils.isEmpty(sysProductRepairRel.getRepairProjectId()))
                .map(SysProductRepairRel::getRepairProjectId)
                .collect(Collectors.toList());
        Assert.notNull(repairProjectIds, "维修项目ID集合为空");

        // 查询维修项目费用
        List<SysRepairProject> repairProjectList = sysRepairProjectService.list(Wrappers.<SysRepairProject>lambdaQuery()
                .select(SysRepairProject::getId, SysRepairProject::getAmount)
                .in(SysRepairProject::getId, repairProjectIds));
        Assert.notNull(repairProjectList, "维修项目费用查询为空");

        // 获取所有维修单ID，去重复
        Set<Integer> repairOrderIdSet = sysProductRepairRels.stream()
                .map(SysProductRepairRel::getRepairOrderId)
                .collect(Collectors.toSet());

        // 为每个维修单计算总费用
        for (Integer repairOrderId : repairOrderIdSet) {
            // 获取该维修单的所有维修项目ID
            List<Integer> projectIds = sysProductRepairRels.stream()
                    .filter(rel -> rel.getRepairOrderId().equals(repairOrderId))
                    .map(SysProductRepairRel::getRepairProjectId)
                    .collect(Collectors.toList());

            // 计算该维修单的总费用（累加所有维修项目费用）
            BigDecimal totalFee = repairProjectList.stream()
                    .filter(project -> projectIds.contains(project.getId()))
                    .map(SysRepairProject::getAmount)
                    .map(BigDecimal::valueOf)
                    .map(amount -> amount.divide(new BigDecimal("100"), 2, BigDecimal.ROUND_HALF_UP))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            feeMap.put(repairOrderId, totalFee);
        }

        return feeMap;
    }

    public LambdaQueryWrapper<SysRepairOrder> buildQueryWrapper(SysRepairOrderPageDto dto) {
        LambdaQueryWrapper<SysRepairOrder> qw = Wrappers.<SysRepairOrder>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysRepairOrder::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysRepairOrder::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysRepairOrder::getGmtCreate, endTime);
        return qw;
    }

    @Override
    @TrimParam
    @ReadOnly
    public IPage<SysRepairOrderListVo> searchList(SysRepairOrderPageDto dto, Integer shopId) {
        Assert.notNull(dto, "查询条件不能为空");

        List<SysRepairOrderPO> repairOrderList = baseMapper.selectRepairOrderList(dto, shopId);

        // 按批次号分组
        Map<String, List<SysRepairOrderPO>> groupMap = repairOrderList.stream()
                .collect(Collectors.groupingBy(SysRepairOrderPO::getRepairNo));

        // 转换为VO对象列表
        List<SysRepairOrderListVo> voList = new ArrayList<>();
        for (Map.Entry<String, List<SysRepairOrderPO>> entry : groupMap.entrySet()) {
            List<SysRepairOrderPO> poList = entry.getValue();
            if (!poList.isEmpty()) {
                SysRepairOrderPO firstPO = poList.get(0);

                // 基本信息
                SysRepairOrderListVo vo = new SysRepairOrderListVo();
                BeanUtils.copyProperties(firstPO, vo);

                // 设置名称等属性
                vo.setWareName(firstPO.getWareName());
                vo.setShelfName(firstPO.getShelfName());
                vo.setShopName(firstPO.getRealname());

                // 维修项目列表
                List<SysRepairProjectVo> projectList = poList.stream()
                        .filter(po -> !ObjectUtils.isEmpty(po.getProjectName()))
                        .map(po -> {
                            SysRepairProjectVo projectVo = new SysRepairProjectVo();
                            BeanUtils.copyProperties(po, projectVo);
                            return projectVo;
                        })
                        .distinct()
                        .collect(Collectors.toList());
                vo.setRepairProjectList(projectList);

                // 商品信息
                List<SysWareInProdVo> productList = poList.stream()
                        .filter(po -> !ObjectUtils.isEmpty(po.getOneId()))
                        .map(po -> {
                            SysWareInProdVo productVo = new SysWareInProdVo();
                            BeanUtils.copyProperties(po, productVo);
                            return productVo;
                        })
                        .distinct()
                        .collect(Collectors.toList());
                vo.setProductList(productList);

                voList.add(vo);
            }
        }

        // 设置返回结果
        IPage<SysRepairOrderListVo> result = new Page<>();
        if (dto.getCurrent() != null && dto.getSize() != null) {
            result.setCurrent(dto.getCurrent());
            result.setSize(dto.getSize());
        }
        // voList 重新排序,按照创建时间倒序
        voList.sort(Comparator.comparing(SysRepairOrderListVo::getGmtCreate).reversed());
        result.setRecords(voList);
        // 通过额外查询获取总记录数
        result.setTotal(baseMapper.selectRepairOrderListCount(dto,shopId));

        return result;
    }

    /**
     * 查询列表统计
     *
     * @param dto
     * @return
     */
    @Override
    @TrimParam
    @ReadOnly
    public SysRepairOrderCountVo searchListCount(SysRepairOrderPageDto dto,Integer shopId) {
        RepairTaskStatus taskStatus = dto.getTaskStatus();

        /**
         * 这里修改列表分页逻辑的情况下，需要同步修改 SysRepairOrderMapper.xml 中 taskStatus 的查询条件
         */

        // 去除分页和列表状态
        dto.setCurrent(null);
        dto.setSize(null);
        dto.setTaskStatus(null);
        // 查询列表
        List<SysRepairOrderListVo> orderList = this.searchList(dto,shopId).getRecords();
        // 维修订单 商家端
        if (JwtContentHolder.getRoleType() == 5) {// 商家
            // 待处理
            long pendingNum = orderList.stream().filter(e -> (e.getRepairStatus() == RepairStatusEnum.REPAIR_PENDING
                    || e.getRepairStatus() == RepairStatusEnum.REPAIR_PROCESSING)
                    || e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_PENDING).count();
            // 已完成
            long completedNum = orderList.stream().filter(e -> (e.getRepairStatus() == RepairStatusEnum.REPAIR_SUCCESS
                    || e.getRepairStatus() == RepairStatusEnum.REPAIR_FAILURE
                    || e.getRepairStatus() == RepairStatusEnum.REPAIR_LOST_PARCEL)

                    || e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_REFUSE
                    ).count();

            // 已取消
            long canceledNum = orderList.stream()
                    .filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_CANCEL).count();

            return new SysRepairOrderCountVo(
                    0,
                    0,
                    pendingNum,
                    completedNum,
                    canceledNum);
        } else {
            // 审核中心
            if (taskStatus == RepairTaskStatus.APPROVAL_ORDER_PENDING_TODAY
                    || taskStatus == RepairTaskStatus.APPROVAL_PENDING
                    || taskStatus == RepairTaskStatus.APPROVAL_COMPLETED
                    || taskStatus == RepairTaskStatus.APPROVAL_CANCEL) {
                // 管理员
                if (JwtContentHolder.getRoleType() == 1) {
                    // 美东 时间0点
                    Date dateStart = DateTimeUtils.getStartOfDayEastern();

                    // 今日待处理 ：状态为待处理，且创建时间大于当天0点
                    long pendingTodayNum = orderList.stream()
                            .filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_PENDING
                                    && DateTimeUtils.convertDateToEastern(e.getGmtCreate()).compareTo(dateStart) >= 0)
                            .count();
                    // 待处理 ：状态为待处理，且创建时间小于美东时间0点
                    long pendingNum = orderList.stream()
                            .filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_PENDING
                                    && DateTimeUtils.convertDateToEastern(e.getGmtCreate()).compareTo(dateStart) < 0)
                            .count();
                    // 已完成
                    long completedNum = orderList.stream()
                            .filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_APPROVE
                                    || e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_REFUSE)
                            .count();

                    // 已取消
                    long canceledNum = orderList.stream()
                            .filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_CANCEL).count();

                    return new SysRepairOrderCountVo(
                            baseMapper.selectRepairOrderListCount(dto,shopId),
                            pendingTodayNum,
                            pendingNum,
                            completedNum,
                            canceledNum);

                }
            }

            // 任务管理
            if (taskStatus == RepairTaskStatus.TASK_PENDING
                    || taskStatus == RepairTaskStatus.TASK_COMPLETED) {
                // 待处理
                long pendingNum = orderList.stream().filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_APPROVE
                        && ObjectUtils.isEmpty(e.getRepairBatchNo())).count();
                // 已完成
                long completedNum = orderList.stream().filter(e -> e.getApprovalStatus() == ApprovalStatusEnum.APPROVAL_APPROVE
                        && !ObjectUtils.isEmpty(e.getRepairBatchNo())).count();

                return new SysRepairOrderCountVo(
                        0,
                        0,
                        pendingNum,
                        completedNum,
                        0
                );

            }
        }

        return new SysRepairOrderCountVo(0, 0, 0, 0, 0);
    }

    @Override
    public Boolean insertList(List<SysRepairOrder> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysRepairOrder> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysRepairOrder> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    /**
     * 重置 商品 和 维修项目 的信息
     *
     * @param productId    商品ID
     * @param oneId        商品oneID
     * @param repairIdList 维修项目ID集合
     * @return
     */
    @Override
    public Boolean resetByRepairIdList(Integer productId, String oneId, List<Integer> repairIdList) {
        if (ObjectUtils.isEmpty(repairIdList)) {
            throw new BaseException("未选择维修项目");
        }

        if (ObjectUtils.isEmpty(productId) || ObjectUtils.isEmpty(oneId)) {
            throw new BaseException("商品ID或商品oneID不能为空");
        }
        // 删除之前和鞋子的关联关系,只是保留维修单和维修项目的关系
        sysProductRepairRelService.lambdaUpdate()
                .eq(SysProductRepairRel::getProductId, productId)
                .eq(SysProductRepairRel::getProductOneId, oneId)

                .set(SysProductRepairRel::getProductId, null)
                .set(SysProductRepairRel::getProductOneId, null)
                .update();

        // 根据维修项目ID集合进行批量保存
        List<SysProductRepairRel> sysProductRepairRelList = new ArrayList<>();
        for (Integer repairId : repairIdList) {
            SysProductRepairRel item = new SysProductRepairRel();
            item.setProductId(productId);
            item.setProductOneId(oneId);
            item.setRepairProjectId(repairId);
            sysProductRepairRelList.add(item);
        }

        return sysProductRepairRelService.saveBatch(sysProductRepairRelList);
    }

    @Override
    @TrimParam
    public Boolean updateStatus(String oneId, RepairStatusEnum repairStatus) {

        return this.lambdaUpdate()
            .eq(SysRepairOrder::getOneId, oneId)
            .set(SysRepairOrder::getRepairStatus, repairStatus)
            .update();
    }
}
