package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.service.entity.RecapHotSku;
import com.hzjm.service.mapper.RecapHotSkuMapper;
import com.hzjm.service.model.VO.PlatformOrderVo;
import com.hzjm.service.service.IRecapHotSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Year;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static cn.hutool.core.date.DateTime.now;

/**
 * <AUTHOR>
 * @date 2024/12/18 10:16
 * @description: 热门商品服务实现类
 */
@Slf4j
@Service
public class RecapHotSkuServiceImpl extends ServiceImpl<RecapHotSkuMapper, RecapHotSku> implements IRecapHotSkuService {

    @Transactional(rollbackFor = Exception.class)
    @Override
    @AcquireTaskLock(name = "batchRecapHotSkuList", timeout = 30)
    public boolean batchSaveSortSku(List<PlatformOrderVo> platformOrderVoList, Year year) {
        log.info("批量保存热门商品，保存条数：{}，年份：{}", platformOrderVoList.size(), year);
        if (platformOrderVoList.isEmpty() || year == null) {
            log.error("热门商品列表或年份为空");
            return true;
        }
        //设置utc时间
        LocalDate startLocalDate = year.atDay(1);
        LocalDate endLocalDate = year.atDay(startLocalDate.lengthOfYear());
        Date startDate = Date.from(startLocalDate.atStartOfDay(ZoneId.of("UTC")).toInstant());
        Date endDate = Date.from(endLocalDate.atStartOfDay(ZoneId.of("UTC")).toInstant());
        List<RecapHotSku> recapHotSkuList = new ArrayList<>(16);
        for (int i = 0; i < platformOrderVoList.size(); i++) {
            PlatformOrderVo vo = platformOrderVoList.get(i);
            RecapHotSku recapHotSku = RecapHotSku.builder()
                    .shopUid(vo.getShopUid())
                    .sku(vo.getSku())
                    .img(vo.getImg())
                    .startDate(startDate)
                    .endDate(endDate)
                    .gmtCreate(now())
                    .gmtModify(now())
                    .sort((long) (i + 1))
                    .build();
            recapHotSkuList.add(recapHotSku);
        }
        if (saveBatch(recapHotSkuList)) {
            log.info("批量保存热门商品成功，保存条数：{}，年份：{}", platformOrderVoList.size(), year);
            return true;
        }
        log.error("批量保存热门商品失败，保存条数：{}，年份：{}", platformOrderVoList.size(), year);
        return false;
    }
}
