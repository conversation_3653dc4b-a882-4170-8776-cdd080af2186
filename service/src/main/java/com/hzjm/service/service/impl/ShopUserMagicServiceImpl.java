package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;

import com.hzjm.common.utils.DateTimeUtils;

import java.util.ArrayList;

import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.ShopUserTouch;
import com.hzjm.service.model.VO.ShopUserTouchVo;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.ShopUserMagicPageDto;
import com.hzjm.service.model.VO.ShopUserMagicListVo;
import com.hzjm.service.model.VO.ShopUserMagicVo;
import com.hzjm.service.entity.ShopUserMagic;
import com.hzjm.service.mapper.ShopUserMagicMapper;
import com.hzjm.service.service.IShopUserMagicService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 商家的magic账号 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-01
 */
@Slf4j
@Service
public class ShopUserMagicServiceImpl extends ServiceImpl<ShopUserMagicMapper, ShopUserMagic> implements IShopUserMagicService {

    @Value("${magic.apiUrl}")
    public String apiUrl;
    @Value("${magic.fromId}")
    public String fromId;

    @Autowired
    private IShopUserService iShopUserService;

    @Override
    public ShopUserMagic getByIdWithoutLogic(Integer id) {
        ShopUserMagic data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商家的magic账号"));
        }

        return data;
    }

    @Override
    public ShopUserMagicVo getDetail(Integer id, Integer shopId) {
        ShopUserMagic data;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<ShopUserMagic>lambdaQuery().eq(ShopUserMagic::getShopId, shopId));
        }

        if (ObjectUtils.isEmpty(data)) {
            data = new ShopUserMagic();
            data.setShopId(shopId);
        }

        ShopUserMagicVo vo = new ShopUserMagicVo();
        BeanUtils.copyProperties(data, vo);

        ShopUser shop = iShopUserService.getById(data.getShopId());
        if (!ObjectUtils.isEmpty(shop)) {
            vo.setLinkUrl(apiUrl + "/authorize.html?form_id=" + fromId + "&user_id=" + shop.getId());
        }
        return vo;
    }

    @Override
    public Boolean saveShopUserMagic(ShopUserMagic dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<ShopUserMagicListVo> searchList(ShopUserMagicPageDto dto) {

        LambdaQueryWrapper<ShopUserMagic> qw = Wrappers.<ShopUserMagic>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopUserMagic::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUserMagic::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUserMagic::getGmtCreate, endTime);

        IPage<ShopUserMagic> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopUserMagicListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopUserMagicListVo vo = new ShopUserMagicListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<ShopUserMagicListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopUserMagic> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopUserMagic> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopUserMagic> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
