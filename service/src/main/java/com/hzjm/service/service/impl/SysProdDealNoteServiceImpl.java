package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.*;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.stream.Collectors;

import com.hzjm.common.utils.BaseUtils;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysProdDealNotePageDto;
import com.hzjm.service.model.VO.SysProdDealNoteListVo;
import com.hzjm.service.model.VO.SysProdDealNoteVo;
import com.hzjm.service.entity.SysProdDealNote;
import com.hzjm.service.mapper.SysProdDealNoteMapper;
import com.hzjm.service.service.ISysProdDealNoteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 留言 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Slf4j
@Service
public class SysProdDealNoteServiceImpl extends ServiceImpl<SysProdDealNoteMapper, SysProdDealNote> implements ISysProdDealNoteService {

    @Autowired
    private IShopUserService iShopUserService;

    @Override
    public SysProdDealNote getByIdWithoutLogic(Integer id) {
        SysProdDealNote data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该留言"));
        }

        return data;
    }

    @Override
    public SysProdDealNoteVo getDetail(Integer id) {
        SysProdDealNote data = getByIdWithoutLogic(id);

        SysProdDealNoteVo vo = new SysProdDealNoteVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdDealNote(SysProdDealNote dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysProdDealNoteListVo> searchList(SysProdDealNotePageDto dto) {

        LambdaQueryWrapper<SysProdDealNote> qw = Wrappers.<SysProdDealNote>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByAsc(SysProdDealNote::getGmtCreate)
                .eq(!ObjectUtils.isEmpty(dto.getType()), SysProdDealNote::getType, dto.getType())
                .eq(!ObjectUtils.isEmpty(dto.getRelationId()), SysProdDealNote::getRelationId, dto.getRelationId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdDealNote::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdDealNote::getGmtCreate, endTime);

        IPage<SysProdDealNote> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdDealNoteListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            Map<Integer, ShopUser> shopMap = new HashMap<>();
            List<Integer> shopIdList = BaseUtils.initList();
            shopIdList.addAll(pageResult.getRecords().stream().filter(a -> {
                return a.getUserType() == 5;
            }).map(SysProdDealNote::getUserId).collect(Collectors.toList()));

            if (!ObjectUtils.isEmpty(shopIdList)) {
                List<ShopUser> shopList = iShopUserService.list(Wrappers.<ShopUser>lambdaQuery().in(ShopUser::getId, shopIdList));
                shopMap.putAll(shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a)));
            }

            pageResult.getRecords().forEach(data -> {
                SysProdDealNoteListVo vo = new SysProdDealNoteListVo();
                BeanUtils.copyProperties(data, vo);

                if (data.getUserType() == 5) {
                    ShopUser shop = shopMap.get(data.getUserId());
                    if (!ObjectUtils.isEmpty(shop)) {
                        vo.setName(shop.getRealname());
                        vo.setHeadImg(shop.getHeadImg());
                        vo.setUid(shop.getUid());
                    }
                } else {
                    // 官方头像
                    vo.setHeadImg("");
                }
                voList.add(vo);
            });
        }

        IPage<SysProdDealNoteListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdDealNote> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdDealNote> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysProdDealNote> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
