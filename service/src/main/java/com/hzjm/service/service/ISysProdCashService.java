package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysProdCash;
import com.hzjm.service.model.DTO.SysProdCashPageDto;
import com.hzjm.service.model.VO.SysProdCashListVo;
import com.hzjm.service.model.VO.SysProdCashVo;

import java.util.List;

/**
 * 套现 服务类
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
public interface ISysProdCashService extends IService<SysProdCash> {

    SysProdCash getByIdWithoutLogic(Integer id);

    SysProdCashVo getDetail(Integer id, String oddNo);

    Boolean saveSysProdCash(SysProdCash dto);

    Boolean insertList(List<SysProdCash> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProdCashListVo> searchList(SysProdCashPageDto dto);

    List<SysProdCash> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdCash> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    /**
     * 导出套现审核列表
     *
     * @param dto      dto
     * @param language 语言
     * @return 文件上传地址
     */
    String exportCashOutList(SysProdCashPageDto dto, String language);

}
