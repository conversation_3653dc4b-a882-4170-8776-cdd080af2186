package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysProdSaleValid;
import com.hzjm.service.model.DTO.SysProdSaleValidPageDto;
import com.hzjm.service.model.VO.SysProdSaleValidListVo;
import com.hzjm.service.model.VO.SysProdSaleValidVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 上架失败校验池 服务类
 *
 * <AUTHOR>
 * @since 2023-09-06
 */
public interface ISysProdSaleValidService extends IService<SysProdSaleValid> {

    SysProdSaleValid getByIdWithoutLogic(Integer id);

    SysProdSaleValidVo getDetail(Integer id);

    Boolean saveSysProdSaleValid(SysProdSaleValid dto);

    Boolean insertList(List<SysProdSaleValid> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysProdSaleValidListVo> searchList(SysProdSaleValidPageDto dto);

    List<SysProdSaleValid> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdSaleValid> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
