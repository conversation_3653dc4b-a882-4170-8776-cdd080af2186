package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ShopUserPlat;
import com.hzjm.service.mapper.ShopUserPlatMapper;
import com.hzjm.service.model.DTO.ShopUserPlatPageDto;
import com.hzjm.service.model.VO.ShopUserPlatListVo;
import com.hzjm.service.model.VO.ShopUserPlatVo;
import com.hzjm.service.service.IShopUserPlatService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 商户寄售权限 服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Slf4j
@Service
public class ShopUserPlatServiceImpl extends ServiceImpl<ShopUserPlatMapper, ShopUserPlat> implements IShopUserPlatService {

    @Override
    public ShopUserPlat getByIdWithoutLogic(Integer id) {
        ShopUserPlat data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商户寄售权限"));
        }

        return data;
    }

    @Override
    public ShopUserPlatVo getDetail(Integer id) {
        ShopUserPlat data = getByIdWithoutLogic(id);

        ShopUserPlatVo vo = new ShopUserPlatVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveShopUserPlat(ShopUserPlat dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<ShopUserPlatListVo> searchList(ShopUserPlatPageDto dto) {

        LambdaQueryWrapper<ShopUserPlat> qw = Wrappers.<ShopUserPlat>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopUserPlat::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUserPlat::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUserPlat::getGmtCreate, endTime);

        IPage<ShopUserPlat> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopUserPlatListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopUserPlatListVo vo = new ShopUserPlatListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<ShopUserPlatListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopUserPlat> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {
            data.setGmtCreate(date);
            data.setGmtModify(date);
            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopUserPlat> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean reset(Integer shopId, List<Integer> platIdList) {
        if (ObjectUtils.isEmpty(platIdList)) {
            platIdList = new ArrayList<>();
        }

        List<Integer> unIncludeIdList = BaseUtils.initList();
        unIncludeIdList.addAll(platIdList);
        List<ShopUserPlat> existList = list(Wrappers.<ShopUserPlat>lambdaQuery().eq(ShopUserPlat::getShopId, shopId));
        unIncludeIdList.retainAll(existList.stream().map(ShopUserPlat::getPlatId).collect(Collectors.toList()));

        remove(Wrappers.<ShopUserPlat>lambdaQuery()
                .notIn(!ObjectUtils.isEmpty(unIncludeIdList), ShopUserPlat::getPlatId, unIncludeIdList) // 保留已存在的
                .eq(ShopUserPlat::getShopId, shopId));

        platIdList.removeAll(unIncludeIdList);
        if (!ObjectUtils.isEmpty(platIdList)) {
            List<ShopUserPlat> dataList = new ArrayList<>();
            platIdList.forEach(platId -> {
                ShopUserPlat data = new ShopUserPlat();
                data.setShopId(shopId);
                data.setPlatId(platId);
                // 查找是否存在对应的 ShopUserPlat
                Optional<ShopUserPlat> existingPlat = existList.stream().findFirst();
                if (existingPlat.isPresent()) {
                    // 使用已有的 ocFee
                    data.setOcFee(existingPlat.get().getOcFee());
                } else {
                    // 使用默认的 ocFee
                    data.setOcFee(new BigDecimal(SysConstants.KnetFee));
                }
                data.setServiceRate(getDefaultServiceRate(platId));
                data.setShippingFee(getDefaultShippingFee(platId));
                data.setDrawRate(getDefaultDrawRate(platId));
                dataList.add(data);
            });
            saveBatch(dataList);
        }

        return true;
    }

    private BigDecimal getDefaultServiceRate(Integer platId) {
        BigDecimal fee = new BigDecimal(0);
        switch (platId) {
            case SysConstants.platformStockX:
                fee = new BigDecimal(SysConstants.StockXServiceRate);
                break;
            case SysConstants.platformStockXFlex:
                fee = new BigDecimal(SysConstants.StockXFlexServiceRate);
                break;
            case SysConstants.platformStockXDirect:
                fee = new BigDecimal(SysConstants.StockXDirectServiceRate);
                break;
            case SysConstants.platformGoat:
            case SysConstants.platformGoatIS:
            case SysConstants.platformGoatSTVDefect:
                fee = new BigDecimal(SysConstants.GoatServiceRate);
                break;
            case SysConstants.platformKicksCrew:
                fee = new BigDecimal(SysConstants.KicksCrewServiceRate);
                break;
            case SysConstants.platformEbay:
                fee = new BigDecimal(SysConstants.EbayServiceRate);
                break;
            case SysConstants.platformPoizon:
                fee = new BigDecimal(SysConstants.PoizonServiceRate);
                break;
            case SysConstants.platformTts:
                fee = new BigDecimal(SysConstants.TtsServiceRate);
                break;
            case SysConstants.PLATFORM_UPS:
                fee = new BigDecimal(SysConstants.UPS_SERVICE_RATE);
                break;
            default:
                break;
        }

        return fee;
    }

    private Integer getDefaultShippingFee(Integer platId) {
        int fee = 0;
        switch (platId) {
            case SysConstants.platformStockX:
            case SysConstants.platformStockXFlex:
                fee = SysConstants.StockXShippingFee * 100;
                break;
            case SysConstants.platformStockXDirect:
                fee = SysConstants.StockXDirectShippingFee * 100;
                break;
            case SysConstants.platformGoat:
            case SysConstants.platformGoatIS:
            case SysConstants.platformGoatSTVDefect:
                fee = SysConstants.GoatShippingFee * 100;
                break;
            case SysConstants.platformKicksCrew:
                fee = SysConstants.KicksCrewShippingFee * 100;
                break;
            case SysConstants.platformEbay:
                fee = SysConstants.EbayShippingFee * 100;
                break;
            case SysConstants.platformPoizon:
                fee = SysConstants.PoizonShippingFee * 100;
                break;
            case SysConstants.platformTts:
                fee = SysConstants.TtsShippingFee * 100;
                break;
            default:
                break;
        }

        return fee;
    }

    private BigDecimal getDefaultDrawRate(Integer platId) {
        BigDecimal fee = new BigDecimal(0);
        switch (platId) {
            case SysConstants.platformStockX:
            case SysConstants.platformStockXFlex:
            case SysConstants.platformStockXDirect:
                fee = new BigDecimal(SysConstants.StockXDrawRate);
                break;
            case SysConstants.platformGoat:
            case SysConstants.platformGoatIS:
            case SysConstants.platformGoatSTVDefect:
                fee = new BigDecimal(SysConstants.GoatDrawRate);
                break;
            case SysConstants.platformKicksCrew:
                fee = new BigDecimal(SysConstants.KicksCrewDrawRate);
                break;
            case SysConstants.platformEbay:
                fee = new BigDecimal(SysConstants.EbayDrawRate);
                break;
            case SysConstants.platformPoizon:
                fee = new BigDecimal(SysConstants.PoizonDrawRate);
                break;
            case SysConstants.platformTts:
                fee = new BigDecimal(SysConstants.TtsDrawRate);
                break;
            default:
                break;
        }

        return fee;
    }

    @Override
    public List<Integer> getIdList(Integer shopId) {
        List<ShopUserPlat> list = list(Wrappers.<ShopUserPlat>lambdaQuery().eq(ShopUserPlat::getShopId, shopId).select(ShopUserPlat::getPlatId));
        return list.stream().map(ShopUserPlat::getPlatId).collect(Collectors.toList());
    }

    @Override
    public List<ShopUserPlat> getPlatList(Integer shopId) {
        List<ShopUserPlat> list = list(Wrappers.<ShopUserPlat>lambdaQuery().eq(ShopUserPlat::getShopId, shopId));
        return list;
    }

    /**
     * @param shopId
     * @param platId
     * @return
     */
    @Override
    public boolean hasRights(Integer shopId, Integer platId) {
        if (ObjectUtils.isEmpty(shopId) || ObjectUtils.isEmpty(platId)) {
            log.error(LanguageConfigService.i18nForMsg("No available shopId or platId to judge user has rights."));
            return false;
        }

        LambdaQueryWrapper<ShopUserPlat> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(ShopUserPlat::getShopId, shopId)
                .eq(ShopUserPlat::getPlatId, platId)
                .last("LIMIT 1");

        ShopUserPlat shopUserPlat = getBaseMapper().selectOne(queryWrapper);

        return !ObjectUtils.isEmpty(shopUserPlat);
    }

    @Override
    public ShopUserPlat getShopPlat(Integer shopId, Integer thirdPlatId) {
        ShopUserPlat plat = getOne(Wrappers.<ShopUserPlat>lambdaQuery().eq(ShopUserPlat::getShopId, shopId).eq(ShopUserPlat::getPlatId, thirdPlatId));
        if (ObjectUtils.isEmpty(plat)) {
            plat = new ShopUserPlat();
            plat.setDrawRate(SysConstants.zero); // 默认为0
            plat.setServiceRate(SysConstants.zero); // 默认为0
        }
        return plat;
    }

    @Override
    public List<ShopUserPlat> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
