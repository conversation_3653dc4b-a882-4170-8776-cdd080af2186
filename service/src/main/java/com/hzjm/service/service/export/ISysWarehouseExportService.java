package com.hzjm.service.service.export;

import com.hzjm.service.model.DTO.SysInventoryExportDto;
import com.hzjm.service.model.DTO.SysProdPageDto;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/2 13:51
 * @description: 仓库管理-导出服务类
 */
public interface ISysWarehouseExportService {
    /**
     * 管理端-创建-库存管理-导出库存-任务
     *
     * @param dto      导出请求
     * @param timeZone 时区
     * @param language
     * @return 是否成功
     */
    Boolean createExportSysInventoryTask(SysProdPageDto dto, String timeZone, String language);

    /**
     * 管理端-导出-库存管理-导出库存（重写版本-迁移旧方法）
     *
     * @param req      原始请求
     * @param fileName 文件名
     * @return 导出文件地址
     */
    HashMap<String, String> exportSysInventory(SysInventoryExportDto req, String fileName);

}
