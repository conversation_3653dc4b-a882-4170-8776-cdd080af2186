package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysApplyKnet;

import java.util.List;

/**
 * 申请加入knet 服务类
 *
 * <AUTHOR>
 * @since 2024-07-22
 */
public interface ISysApplyKnetService extends IService<SysApplyKnet> {


    Boolean saveSysApplyKnet(SysApplyKnet dto);

    Boolean insertList(List<SysApplyKnet> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    int hardUpdate(Integer id);

    IPage<SysApplyKnet> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<SysApplyKnet> listWithoutLogic(LambdaQueryWrapper qw);
}
