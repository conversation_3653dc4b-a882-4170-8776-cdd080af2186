package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.hzjm.service.entity.SysEmailHistory;
import com.hzjm.service.mapper.ShopUserMapper;
import com.hzjm.service.service.BaseSendEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class BaseSendEmailServiceImpl implements BaseSendEmailService {

    @Resource
    private ShopUserMapper shopUserMapper;

    @Resource
    private AsyncImpl asyncImpl;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    // 上一次电子邮件的发送时间
    String EMAIL_SEND_KEY = "EMAIL_SEND_KEY";

    // 邮件发送间隔时间，毫秒单位
    private static final int SLEEP_TIME = 30000;

    //当前系统时间
    long currentTime = System.currentTimeMillis();

    /**
     * 发送邮件，限制发送邮件的速度，每一封邮件发送的间隔不能小于 SLEEP_TIME
     */
    @Override
    @Async
    public void sendEmail(String email,String bccEmail, String content, String subject, SysEmailHistory sysEmailHistory) {
        String lastSendTimeStr = redisTemplate.opsForValue().get(EMAIL_SEND_KEY);
        if (StringUtils.isEmpty(lastSendTimeStr) || (currentTime - Long.parseLong(lastSendTimeStr)) >= SLEEP_TIME) {
            asyncImpl.sendMailAndHis(email,bccEmail, content, subject, sysEmailHistory);
            redisTemplate.opsForValue().set(EMAIL_SEND_KEY, String.valueOf(currentTime), 1, TimeUnit.MINUTES);
        } else {
            try {
                Thread.sleep(SLEEP_TIME);
                asyncImpl.sendMailAndHis(email,bccEmail, content, subject, sysEmailHistory);
                redisTemplate.opsForValue().set(EMAIL_SEND_KEY, String.valueOf(currentTime), 1, TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                log.info("SendEmailServiceImpl sendEmail sleep is error={},message={}", e, e.getMessage());
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public List<String> getUserEamilAll(String limitSql){
        return shopUserMapper.getUserEamilAll(limitSql);
    }

    @Override
    public Integer getUserEamilAllCount(){
        return shopUserMapper.getUserEamilAllCount();
    }

}
