package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysUploadRecord;
import com.hzjm.service.entity.SysUploadRecordDetail;
import com.hzjm.service.model.DTO.SysUploadRecordPageDto;
import com.hzjm.service.model.VO.SysUploadRecordVo;

import java.util.List;

/**
 * 导入记录流水表 服务类
 *
 * <AUTHOR>
 * @since 2024-11-15
 */
public interface ISysUploadRecordService extends IService<SysUploadRecord> {

    SysUploadRecord getByIdWithoutLogic(Integer id);

    SysUploadRecordVo getDetail(Integer id);

    List<SysUploadRecordDetail> selectSysUploadRecordDetail(Integer id);

    Boolean saveSysUploadRecord(SysUploadRecord dto);

    Boolean insertList(List<SysUploadRecord> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysUploadRecordVo> searchList(SysUploadRecordPageDto dto);

    List<SysUploadRecord> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysUploadRecord> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
