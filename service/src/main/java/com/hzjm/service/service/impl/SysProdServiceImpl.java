package com.hzjm.service.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.*;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.*;
import com.hzjm.service.model.DTO.*;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.model.enums.SysUploadRecordUploadStatusEnum;
import com.hzjm.service.model.enums.SysUploadRecordUploadTypeEnum;
import com.hzjm.service.model.enums.SysUploadRecordUserTypeEnum;
import com.hzjm.service.model.touch.TouchProdAddRequest;
import com.hzjm.service.service.*;
import com.hzjm.service.touch.TouchUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品信息 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
@Slf4j
@Service
public class SysProdServiceImpl extends ServiceImpl<SysProdMapper, SysProd> implements ISysProdService {

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Resource
    private ISysRepairOrderService sysReairOrderService;

    @Resource
    ISysFileService sysFileService;

    @Resource
    private SysUploadRecordMapper sysUploadRecordMapper;

    @Autowired
    private ISysWareOutBatchProdService iSysWareOutBatchProdService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysWareInProdService iSysWareInProdService;

    @Autowired
    private ISysWareInService iSysWareInService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private IShopUserService iShopUserService;

    @Autowired
    private IShopPackService iShopPackService;

    @Autowired
    private ISysAuditService iSysAuditService;

    @Autowired
    private ISysProdEventService iSysProdEventService;

    @Autowired
    private ISysProdSwitchItemService iSysProdSwitchItemService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysProdTransferService iSysProdTransferService;

    @Autowired
    private ISysProdCashService iSysProdCashService;

    @Autowired
    private ISysProdSaleService iSysProdSaleService;

    @Autowired
    private ISysFileService iSysFileService;

    @Autowired
    private ISysCodePoolService iSysCodePoolService;

    @Autowired
    private ISysMoneyService iSysMoneyService;

    @Autowired
    private ISysBillService iSysBillService;

    @Autowired
    private ISysWareOutService iSysWareOutService;

    @Autowired
    private ISysWareOutProdService iSysWareOutProdService;

    @Autowired
    private ISysThirdPlatService iSysThirdPlatService;

    @Autowired
    private IShopUserTouchService iShopUserTouchService;

    @Autowired
    private IShopUserPlatService iShopUserPlatService;

    @Autowired
    private ISysSkuService iSysSkuService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysParamSetService iSysParamSetService;

    @Autowired
    private TouchUtils touchUtils;

    @Autowired
    private AsyncImpl async;

    @Autowired
    private ExtKnetProductListingMapper extKnetProductListingMapper;

    @Autowired
    private SysProdSearchMapper sysProdSearchMapper;

    @Resource
    SysProdTransportMapper sysProdTransportMapper;

    @Resource
    ISysUploadRecordService iSysUploadRecordService;

    @Resource
    SysUploadRecordDetailMapper sysUploadRecordDetailMapper;

    private final PlatformTransactionManager transactionManager;

    public SysProdServiceImpl(PlatformTransactionManager transactionManager) {
        this.transactionManager = transactionManager;
    }

    @Override
    public SysProd getByIdWithoutLogic(Integer id) {
        SysProd data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商品信息"));
        }

        return data;
    }

    @Override
    @ReadOnly
    public SysProdVo getDetail(Integer id, String oneId) {
        SysProd data = null;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getOneId, oneId));
            if (ObjectUtils.isEmpty(data)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商品信息"));
            }
        }

        SysProdVo vo = new SysProdVo();
        // 商品信息
        BeanUtils.copyProperties(data, vo);

        // 入库信息
        SysWareInProd inProd = iSysWareInProdService.getOne(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getProdId, data.getId()));
        if (!ObjectUtils.isEmpty(inProd)) {
            // 验货照片
            inProd.setImgList(iSysFileService.getFileUrl(inProd.getId(), SysFile.TypeInProdCheck));

            // 在仓时长
            Date now = DateTimeUtils.getNow();
            inProd.setWareDays(DateTimeUtils.timeDiff(ObjectUtils.isEmpty(inProd.getGmtPay()) ? now : inProd.getGmtPay(), inProd.getGmtCreate(), 4));

            // 所在仓库
            SysWare ware = iSysWareService.getById(inProd.getWareId());
            if (!ObjectUtils.isEmpty(ware)) {
                inProd.setWareName(ware.getName());

                // 在仓费用
                BigDecimal wareFee = inProd.getWareDays() - ware.getFreeDays() <= 0 ? SysConstants.zero : new BigDecimal(inProd.getWareDays() - ware.getFreeDays()).multiply(ware.getPrice());
                inProd.setWareFee(wareFee);
            }

            // 所在仓位
            //SysWareShelvesProd shelvesProd = iSysWareShelvesProdService.getOne(Wrappers.<SysWareShelvesProd>lambdaQuery().eq(SysWareShelvesProd::getProdId, data.getId()));
            List<SysWareShelvesProd> list = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery()
                    .eq(SysWareShelvesProd::getProdId, data.getId())
                    .eq(SysWareShelvesProd::getDelFlag, 0)
            );
            SysWareShelvesProd shelvesProd = null;
            if (!list.isEmpty() && list.size() > 0) {
                shelvesProd = list.get(0);
            }
            if (!ObjectUtils.isEmpty(shelvesProd)) {
                SysWareShelves shelves = iSysWareShelvesService.getById(shelvesProd.getShelvesId());
                if (!ObjectUtils.isEmpty(shelves)) {
                    inProd.setShelvesName(shelves.getName());

                    inProd.setShelvesUserName(shelvesProd.getUserName());
                    inProd.setGmtShelvesCreate(shelvesProd.getGmtCreate());
                }
            }
            vo.setInProdInfo(inProd);
            vo.setPackInfo(iShopPackService.getById(inProd.getPackId()));

            SysWareIn in = iSysWareInService.getById(inProd.getInId());
            if (!ObjectUtils.isEmpty(in)) {
                inProd.setBatchNo(in.getBatchNo());
                inProd.setType(in.getType());

                // 关联 oneidRelated
                vo.setOneidRelated(in.getOneidRelated());
                // 面单照片
                vo.setImageList(sysFileService.getFileUrl(in.getId(), SysConstants.SYS_FILE_TYPE_8));
            }

        }

        // 最新的归属者
        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .orderByDesc(SysProdDeal::getGmtModify)
                .eq(SysProdDeal::getStatus, 3)
                .eq(SysProdDeal::getProdId, data.getId()));

        Integer shopId = JwtContentHolder.getShopId();
        if (!ObjectUtils.isEmpty(shopId)) {
            Integer finalShopId = shopId;
            dealList = dealList.stream().filter(a -> {
                if (!ObjectUtils.isEmpty(a.getShopId())) {
                    return a.getShopId().intValue() == finalShopId;
                }
                return true;
            }).sorted(Comparator.comparing(SysProdDeal::getGmtModify).reversed()).collect(Collectors.toList());
        }

        // 我的最新记录
        String oddNo = "";
        SysProdDeal deal = null;
        if (ObjectUtils.isEmpty(shopId)) {
            oddNo = data.getOddNo();
            shopId = data.getShopId();
            if (ObjectUtils.isEmpty(shopId) && !ObjectUtils.isEmpty(dealList)) {
                shopId = dealList.get(0).getShopId();
            }
        } else if (!ObjectUtils.isEmpty(shopId) && !ObjectUtils.isEmpty(dealList)) {
            if (ObjectUtils.isEmpty(data.getShopId()) || data.getShopId() != shopId.intValue()) {
                // 对我来说，商品已出库
                vo.setStatus(6);

                deal = dealList.get(0);
                switch (deal.getType()) {
                    case SysProdEvent.TypeSale:
                        SysProdSale sale = iSysProdSaleService.getById(deal.getSaleId());
                        oddNo = sale.getOddNo();
                        break;
                    default:
                        SysAudit audit = iSysAuditService.getOne(Wrappers.<SysAudit>lambdaQuery()
                                .eq(SysAudit::getType, deal.getType()).eq(SysAudit::getRelationId, deal.getRelationId()));
                        oddNo = audit.getOddNo();
                }
            }
        }

        vo.setShopInfo(iShopUserService.getById(shopId));

        // 当前的出库单
        SysWareOut out = iSysWareOutService.getOne(Wrappers.<SysWareOut>lambdaQuery().eq(SysWareOut::getOddNo, oddNo));
        if (!ObjectUtils.isEmpty(out)) {
            // 出库单的费用合计
            if (out.getType() == SysProdEvent.TypeSale) {
                SysWareOutProd outProd = iSysWareOutProdService.getOne(Wrappers.<SysWareOutProd>lambdaQuery()
                        .orderByDesc(SysWareOutProd::getGmtCreate).last("limit 1")
                        .eq(SysWareOutProd::getProdId, data.getId())
                        .eq(SysWareOutProd::getOutId, out.getId()));

                if (!ObjectUtils.isEmpty(outProd)) {
                    deal = iSysProdDealService.getById(outProd.getDealId());
                    out.setSoldPrice(deal.getSoldPrice());
                    out.setPlatOrderNo(deal.getPlatOrderNo());

                    SysProdSale sale = iSysProdSaleService.getById(deal.getSaleId());
                    if (!ObjectUtils.isEmpty(sale)) {
                        out.setPlatName(sale.getPlatName());
                    }
                }
            } else {
                SysProdTransportVo transportVo = iSysProdTransportService.getDetail(null, data.getOddNo());
                out.setTotalFee(transportVo.getTotalFee());
            }
            vo.setOutInfo(out);
        } else if (!ObjectUtils.isEmpty(deal)) {
            out = new SysWareOut();
            out.setOddNo(oddNo);
            out.setGmtCreate(deal.getGmtModify());
            out.setType(deal.getType());
            vo.setOutInfo(out);
        }

        // 生命旅程
        fillCircle(vo);

        return vo;
    }

    /**
     * 填充生命周期
     *
     * @param vo
     */
    private void fillCircle(SysProdVo vo) {
        List<SysProdEvent> eventList = iSysProdEventService.list(Wrappers.<SysProdEvent>lambdaQuery()
                .and(JwtContentHolder.getRoleType() == 5, a -> a.eq(SysProdEvent::getShopId, JwtContentHolder.getShopId()).or().isNull(SysProdEvent::getShopId))
                .orderByAsc(SysProdEvent::getGmtCreate)
                .eq(SysProdEvent::getProdId, vo.getId()).in(SysProdEvent::getType, SysProdEvent.TypeCircleList));
        JSONArray events = new JSONArray();
        for (SysProdEvent event : eventList) {
            switch (event.getType()) {
                // 预报单：packInfo
                case SysProdEvent.TypePreReport:
                    ShopPack packInfo = vo.getPackInfo();
                    if (!ObjectUtils.isEmpty(packInfo)) {
                        JSONObject pack = new JSONObject();
                        pack.put("type", event.getType());
                        pack.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, packInfo.getGmtCreate()));
                        pack.put("packId", packInfo.getId());
                        pack.put("logNo", packInfo.getLogNo());
                        pack.put("brand", vo.getBrand());
                        pack.put("remarks", vo.getRemarks());
                        pack.put("sku", vo.getSku());
                        pack.put("spec", vo.getSpec());

                        ShopUser shop = iShopUserService.getById(packInfo.getShopId());
                        if (!ObjectUtils.isEmpty(shop)) {
                            pack.put("operator", shop.getUid());
                        }
                        events.add(pack);
                    }
                    break;
                case SysProdEvent.TypeShelvesOn:
                    break;
                // 入库信息：inProdInfo
                case SysProdEvent.TypeIn:
                    SysWareInProd inProdInfo = vo.getInProdInfo();
                    if (!ObjectUtils.isEmpty(inProdInfo)) {
                        JSONObject in = new JSONObject();
                        in.put("type", event.getType());
                        in.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, inProdInfo.getGmtCreate()));

                        in.put("brand", vo.getBrand());
                        in.put("spec", vo.getSpec());
                        in.put("remarks", vo.getRemarks());
                        in.put("sku", vo.getSku());
                        in.put("pku", vo.getPku());

                        packInfo = vo.getPackInfo();
                        if (!ObjectUtils.isEmpty(packInfo)) {
                            in.put("logNo", packInfo.getLogNo());
                        }

                        in.put("inId", inProdInfo.getId());
                        in.put("batchNo", inProdInfo.getBatchNo());
                        in.put("oneId", inProdInfo.getOneId());
                        in.put("checkResult", inProdInfo.getCheckResult());
                        in.put("imgList", inProdInfo.getImgList());
                        in.put("wareName", inProdInfo.getWareName());
                        in.put("shelvesName", inProdInfo.getShelvesName());
                        SysUser checker = iSysUserService.getById(inProdInfo.getCheckId());
                        if (!ObjectUtils.isEmpty(checker)) {
                            in.put("operator", checker.getNickname());
                        }
                        events.add(in);
                    }
                    break;
                // 入库信息：inProdInfo
                case SysProdEvent.TypeOwnChange:
                    inProdInfo = vo.getInProdInfo();

                    JSONObject ownChange = new JSONObject();
                    ownChange.put("type", event.getType());
                    ownChange.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, event.getGmtCreate()));

                    ownChange.put("brand", vo.getBrand());
                    ownChange.put("spec", vo.getSpec());
                    ownChange.put("remarks", vo.getRemarks());
                    ownChange.put("sku", vo.getSku());
                    ownChange.put("pku", vo.getPku());

                    packInfo = vo.getPackInfo();
                    if (!ObjectUtils.isEmpty(packInfo)) {
                        ownChange.put("logNo", packInfo.getLogNo());
                    }

                    List<SysProdEvent> ends = iSysProdEventService.list(Wrappers.<SysProdEvent>lambdaQuery()
                            .orderByDesc(SysProdEvent::getGmtCreate)
//                            .and(JwtContentHolder.getRoleType() == 5, a -> a.eq(SysProdEvent::getShopId, JwtContentHolder.getShopId()).or().isNull(SysProdEvent::getShopId))
                            .eq(SysProdEvent::getProdId, vo.getId())
                            .le(SysProdEvent::getGmtCreate, event.getGmtCreate())
                            .likeLeft(SysProdEvent::getType, SysProdEvent.TypeEnd));
                    if (!ObjectUtils.isEmpty(ends)) {
                        SysProdEvent end = ends.get(0);
                        int endType = end.getType() / 100;

                        ownChange.put("eventType", endType);
                        SysAudit audit = iSysAuditService.getOne(Wrappers.<SysAudit>lambdaQuery()
                                .eq(SysAudit::getType, endType).eq(SysAudit::getRelationId, end.getRelationId()));
                        if (!ObjectUtils.isEmpty(audit)) {
                            ownChange.put("batchNo", audit.getOddNo());
                            SysUser auditor = iSysUserService.getById(audit.getOperatorId());
                            if (!ObjectUtils.isEmpty(auditor)) {
                                ownChange.put("operator", auditor.getNickname());
                            }
                            SysProdDeal deal = iSysProdDealService.getOne(Wrappers.<SysProdDeal>lambdaQuery()
                                    .eq(SysProdDeal::getProdId, vo.getId())
                                    .eq(SysProdDeal::getType, endType).eq(SysProdDeal::getRelationId, end.getRelationId()));
                            if (!ObjectUtils.isEmpty(deal)) {
                                SysWare ware = iSysWareService.getById(deal.getWareId());
                                if (!ObjectUtils.isEmpty(ware)) {
                                    ownChange.put("wareName", ware.getName());
                                }
                            }
                        }
                    }

                    if (!ObjectUtils.isEmpty(inProdInfo)) {
                        ownChange.put("oneId", inProdInfo.getOneId());
                        ownChange.put("checkResult", inProdInfo.getCheckResult());
                        ownChange.put("imgList", inProdInfo.getImgList());

                        if (!ObjectUtils.isEmpty(ends)) {
//                            if (ends.get(0).getShopId().intValue() != JwtContentHolder.getShopId()) {
                            inProdInfo.setType(ownChange.getInteger("eventType"));
                            inProdInfo.setGmtCreate(event.getGmtCreate());
                            inProdInfo.setBatchNo(ownChange.getString("batchNo"));
                            inProdInfo.setWareName(ownChange.getString("wareName"));
//                            }
                        }
                    }

                    events.add(ownChange);
                    break;
                // 寄售信息：寄售转自营
                case SysProdEvent.TypeSaleSelf:
                    SysProdDeal selfDeal = iSysProdDealService.getByIdWithoutLogic(event.getRelationId());

                    JSONObject self = new JSONObject();
                    self.put("type", event.getType());
                    self.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, selfDeal.getGmtModify()));
                    self.put("outType", SysProdEvent.TypeSale);

                    SysThirdPlat plat = iSysThirdPlatService.getById(selfDeal.getThirdPlatId());
                    if (!ObjectUtils.isEmpty(plat)) {
                        self.put("platName", plat.getName());
                    }
                    self.put("salePrice", selfDeal.getSalePrice());
//                    if(!ObjectUtils.isEmpty(selfDeal.getPlatSoldPrice())) {
//                        selfDeal.setSalePrice(selfDeal.getPlatSoldPrice());
//                    }
                    if (ObjectUtils.isEmpty(selfDeal.getSoldPrice())) {
                        ShopUser shop = iShopUserService.getById(selfDeal.getShopId());
                        ShopUserPlat shopPlat = iShopUserPlatService.getShopPlat(selfDeal.getShopId(), selfDeal.getThirdPlatId());
                        BigDecimal serviceFee = selfDeal.getSalePrice().multiply(
                                        shopPlat.getServiceRate().add(shopPlat.getDrawRate())
                                ).divide(SysConstants.hundred, 2, RoundingMode.HALF_EVEN)
                                .add(plat.getOtherFee()).add(shop.getOcFee());
                        self.put("serviceFee", serviceFee);

                        // 到手价 = 寄价格*(1-第三方手续费-第三方提现费)*100%-第三方其他费用-0C服务费
                        self.put("soldPrice", selfDeal.getSalePrice().subtract(serviceFee));
                    } else {
                        self.put("soldPrice", selfDeal.getSoldPrice());
                        self.put("serviceFee", selfDeal.getSalePrice().subtract(selfDeal.getSoldPrice()));
                    }
                    self.put("saleDays", DateTimeUtils.timeDiff(selfDeal.getStatus() == 3 ? selfDeal.getGmtModify() : DateTimeUtils.getNow(), selfDeal.getGmtCreate(), 4));
                    self.put("saleStatus", selfDeal.getStatus());
                    events.add(self);
                    break;
                // 出库信息
                case SysProdEvent.TypeOutApply:
                    SysWareOut out = iSysWareOutService.getById(event.getRelationId());
                    if (!ObjectUtils.isEmpty(out)) {
                        JSONObject o = new JSONObject();
                        o.put("type", event.getType());
                        o.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, out.getGmtCreate()));
                        o.put("outId", out.getId());
                        o.put("oddNo", out.getOddNo());
                        o.put("outType", out.getType());

                        SysWareOutProd outProd = iSysWareOutProdService.getOne(Wrappers.<SysWareOutProd>lambdaQuery().eq(SysWareOutProd::getOutId, out.getId()).eq(SysWareOutProd::getProdId, vo.getId()));
                        if (!ObjectUtils.isEmpty(outProd) && !ObjectUtils.isEmpty(outProd.getCheckId())) {
                            SysUser user = iSysUserService.getById(outProd.getCheckId());
                            if (!ObjectUtils.isEmpty(user)) {
                                o.put("operator", user.getNickname());
                            }
                        }

                        if (out.getType() == SysProdEvent.TypeSale) {
//                            o.put("type", SysProdEvent.TypeSaleSelf); // 此处改变类型，意为变更页面渲染方式
                            // 寄售信息
                            if (!ObjectUtils.isEmpty(outProd) && !ObjectUtils.isEmpty(outProd.getDealId())) {
                                SysProdDeal deal = iSysProdDealService.getByIdWithoutLogic(outProd.getDealId());
                                SysThirdPlat plat1 = iSysThirdPlatService.getById(deal.getThirdPlatId());
                                if (!ObjectUtils.isEmpty(plat1)) {
                                    o.put("platName", plat1.getName());
                                }
                                o.put("salePrice", deal.getSalePrice());
//                                if(!ObjectUtils.isEmpty(deal.getPlatSoldPrice())) {
//                                    deal.setSalePrice(deal.getPlatSoldPrice());
//                                }
                                if (ObjectUtils.isEmpty(deal.getSoldPrice())) {
                                    ShopUser shop = iShopUserService.getById(deal.getShopId());
                                    ShopUserPlat shopPlat = iShopUserPlatService.getShopPlat(deal.getShopId(), deal.getThirdPlatId());
                                    BigDecimal serviceFee = deal.getSalePrice().multiply(
                                                    shopPlat.getServiceRate().add(shopPlat.getDrawRate())
                                            ).divide(SysConstants.hundred, 2, RoundingMode.HALF_EVEN)
                                            .add(plat1.getOtherFee()).add(shop.getOcFee());
                                    o.put("serviceFee", serviceFee);

                                    // 到手价 = 寄价格*(1-第三方手续费-第三方提现费)*100%-第三方其他费用-0C服务费
                                    o.put("soldPrice", deal.getSalePrice().subtract(serviceFee));
                                } else {
                                    o.put("soldPrice", deal.getSoldPrice());
                                    o.put("serviceFee", deal.getSalePrice().subtract(deal.getSoldPrice()));
                                }
                                o.put("saleDays", DateTimeUtils.timeDiff(deal.getStatus() == 3 ? deal.getGmtModify() : DateTimeUtils.getNow(), deal.getGmtCreate(), 4));
                                o.put("saleStatus", deal.getStatus());
                            }
                        } else {
                            // 出库信息
                            SysProdTransportVo transport = iSysProdTransportService.getDetail(out.getRelationId(), null);
                            List<SysProdDealListVo> prods = transport.getProdList().stream().filter(a -> a.getProdId() == vo.getId().intValue()).collect(Collectors.toList());
                            SysProdDealListVo prodInfo = prods.get(0);
                            o.put("wareDays", prodInfo.getWareDays());
                            o.put("wareFee", prodInfo.getWareFee());
                            if (prodInfo.getSaleStatus() == 3) {
                                o.put("outTime", DateTimeUtils.format(DateTimeUtils.sdfTime, prodInfo.getGmtSale()));
                            }
                            o.put("deliveryFee", transport.getDeliveryFee());
                            o.put("otherFee", transport.getPlatFee().multiply(new BigDecimal(prods.size())).subtract(transport.getFreeFee()));
                        }
                        events.add(o);
                    }
                    break;
                // 操作审批
                default:
                    JSONObject o = new JSONObject();
                    o.put("type", event.getType());
                    o.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, event.getGmtCreate()));

                    if (event.getType() % 100 == 12) {
                        int type = event.getType() / 100;
                        switch (type) {
                            case SysProdEvent.TypeSale:
                                // 寄售结果
                                SysProdDeal deal = iSysProdDealService.getById(event.getRelationId());
                                if (!ObjectUtils.isEmpty(deal)) {
                                    if (deal.getStatus() == 2) {
                                        o.put("flowStatus", 1);
                                    } else if (deal.getStatus() == 3) {
                                        o.put("flowStatus", 6);
                                    }
                                }
                                break;
                            default:
                                SysAudit audit = iSysAuditService.getOne(Wrappers.<SysAudit>lambdaQuery()
                                        .eq(SysAudit::getType, type)
                                        .eq(SysAudit::getRelationId, event.getRelationId()));
                                if (!ObjectUtils.isEmpty(audit)) {
                                    o.put("oddNo", audit.getOddNo());
                                    if (audit.getStatus() == 2 || audit.getStatus() == 4 || audit.getStatus() == 6 || audit.getStatus() == 7) {
                                        o.put("gmtEnd", DateTimeUtils.format(DateTimeUtils.sdfTime, audit.getGmtModify()));

                                        // 可根据审批记录判断流程状态
                                        if (audit.getStatus() == 2) {
                                            o.put("flowStatus", 1); // 1-已取消，2-寄卖中，3-出库中，4-套现中，5-修复中，6-已完成
                                        } else if (audit.getStatus() == 4 || audit.getStatus() == 6) {
                                            o.put("flowStatus", 6); // 1-已取消，2-寄卖中，3-出库中，4-套现中，5-修复中，6-已完成
                                        }
                                    }

                                    SysUser auditor = iSysUserService.getById(audit.getOperatorId());
                                    if (!ObjectUtils.isEmpty(auditor)) {
                                        o.put("operator", auditor.getNickname());
                                    }
                                }
                        }
                    }

                    if (event.getType() == SysProdEvent.TypeSale) {
                        o.put("flowStatus", 2);
                        SysProdDeal deal = iSysProdDealService.getByIdWithoutLogic(event.getRelationId());
                        if (!ObjectUtils.isEmpty(deal)) {
                            ShopUser shop = iShopUserService.getById(deal.getShopId());
                            if (!ObjectUtils.isEmpty(shop)) {
                                o.put("operator", shop.getUid());
                            }
                        }
                    } else {
                        SysAudit audit = iSysAuditService.getOne(Wrappers.<SysAudit>lambdaQuery().eq(SysAudit::getType, event.getType()).eq(SysAudit::getRelationId, event.getRelationId()));
                        if (!ObjectUtils.isEmpty(audit)) {
                            o.put("oddNo", audit.getOddNo());
                            o.put("flowStatus", audit.getType() == SysProdEvent.TypeCash ? 4 : 3);
                            ShopUser shop = iShopUserService.getById(audit.getShopId());
                            if (!ObjectUtils.isEmpty(shop)) {
                                o.put("operator", shop.getUid());
                            }
                        }
                    }

                    events.add(o);
            }
        }

        if (CollUtil.isNotEmpty(eventList)) {
            SysProdEvent prodEvent = eventList.stream()
                    .filter(event -> event.getType() == SysProdEvent.TypeShelvesOn)
                    .findFirst()
                    .orElse(null);
            if (BeanUtil.isNotEmpty(prodEvent)) {
                List<SysWareShelvesProd> shelvesProds = iSysWareShelvesProdService.qyeryByWareShelvesProdByProdId(prodEvent.getProdId());
                log.info("上架事件shelvesProds:{}", JSON.toJSONString(shelvesProds));
                shelvesProds.forEach(shelvesProd -> {
                    SysWareShelves shelves = iSysWareShelvesService.getById(shelvesProd.getShelvesId());
                    log.info("上架事件shelves:{}", JSON.toJSONString(shelves));
                    if (BeanUtil.isNotEmpty(shelves)) {
                        JSONObject pack = new JSONObject();
                        pack.put("type", SysProdEvent.TypeShelvesOn);
                        pack.put("gmtCreate", DateTimeUtils.format(DateTimeUtils.sdfTime, shelvesProd.getGmtCreate()));
                        pack.put("shelvesName", shelves.getName());
                        //上架操作人
                        pack.put("operator", shelvesProd.getUserName());
                        events.add(pack);
                    }
                });
            }
        }
        vo.setEventList(events);
    }

    @Override
    public Boolean saveSysProd(SysProd dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            if (ObjectUtils.isEmpty(dto.getOneId())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("录入商品失败，缺少OneId"));
            }
            if (count(Wrappers.<SysProd>lambdaQuery().eq(SysProd::getOneId, dto.getOneId())) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("录入商品失败，oneId已被使用"));
            }

            if (ObjectUtils.isEmpty(dto.getSku())) {
                dto.setSku("");
            }

            if (ObjectUtils.isEmpty(dto.getShopId())) {
//                dto.setShopId(1); // 默认为平台自营
            }

            dto.setGmtIn(DateTimeUtils.getNow()); // 新增的时间即为入库时间

            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            SysProd data = getByIdWithoutLogic(dto.getId());

            if (data.getStatus() != 1) {
                throw new BaseException(LanguageConfigService.i18nForMsg("非空闲状态不可删除"));
            }

            // 记录在event表中，删除商品
            SysProdEvent event = new SysProdEvent();
            event.setProdId(data.getId());
            event.setType(SysProdEvent.TypeDelete);
            event.setGmtCreate(DateTimeUtils.getNow());
            event.setGmtModify(DateTimeUtils.getNow());
            event.setDescription(JwtContentHolder.getUserId()+"进行删除商品,用户角色是："+JwtContentHolder.getRoleType());
            iSysProdEventService.save(event);

            // 清理历史痕迹
            iSysWareInProdService.remove(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getProdId, data.getId()));
            iSysProdEventService.remove(Wrappers.<SysProdEvent>lambdaQuery().eq(SysProdEvent::getProdId, data.getId()));
            iSysWareShelvesProdService.remove(Wrappers.<SysWareShelvesProd>lambdaQuery().eq(SysWareShelvesProd::getProdId, data.getId()));
            iSysProdSearchService.remove(Wrappers.<SysProdSearch>lambdaQuery().eq(SysProdSearch::getProdId, data.getId()));

            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            SysProd data = getById(dto.getId());

            if (!ObjectUtils.isEmpty(dto.getShopId()) && !ObjectUtils.isEmpty(data.getShopId())
                    && data.getShopId().intValue() != dto.getShopId()) {
                if (JwtContentHolder.getRoleType() != 1) {
//                    throw new BaseException(LanguageConfigService.i18nForMsg("仅平台管理员有该权限"));
                    throw new BaseException(LanguageConfigService.i18nForMsg("No permission"));
                }
                // 修改为无主件
                if (dto.getShopId() == -1) {
                    dto.setShopId(null);
                    baseMapper.clearShop(data.getId());
                    iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                            .setSql(" shop_id = null, shop_uid = null, shop_name = null ")
                            .eq(SysProdSearch::getSearchType, 1)
                            .eq(SysProdSearch::getProdId, data.getId()));
                }
            }

            if (!ObjectUtils.isEmpty(dto.getStatus()) && data.getStatus().intValue() != dto.getStatus()) {
                // 非空闲状态，不可操作
                if (data.getStatus() != 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("非空闲状态不可操作"));
                }
            }

            if (!ObjectUtils.isEmpty(dto.getSupply())) {
                // 修改货源后推送给touch
                if (data.getStatus() == 2) {
                    touchUtils.updateRemark(dto.getSupply(), new ArrayList<>(Arrays.asList(data.getOneId())));
                }

                // 同步更新操作记录中的商品信息
                iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                        .eq(SysProdDeal::getProdId, data.getId())
                        .eq(SysProdDeal::getStatus, 1)
                        .set(!ObjectUtils.isEmpty(dto.getSupply()), SysProdDeal::getSupply, dto.getSupply()));
            }

            if (!ObjectUtils.isEmpty(dto.getCostPrice())) {
                // 修改成本后推送给touch
                if (data.getStatus() == 2) {
                    touchUtils.updateCost(dto.getCostPrice(), new ArrayList<>(Arrays.asList(data.getOneId())));
                }

                // 同步更新操作记录中的商品信息
                iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                        .eq(SysProdDeal::getProdId, data.getId())
                        .eq(SysProdDeal::getStatus, 1)
                        .set(!ObjectUtils.isEmpty(dto.getCostPrice()), SysProdDeal::getCostPrice, dto.getCostPrice()));
            }

            ShopUser shop = iShopUserService.getById(dto.getShopId());
            if (ObjectUtils.isEmpty(shop)) {
                shop = new ShopUser();
            }

            // search同步更新
            iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                    .set(SysProdSearch::getDelFlag, 0)
                    .set(!ObjectUtils.isEmpty(dto.getStatus()), SysProdSearch::getStatus, dto.getStatus())
                    .set(!ObjectUtils.isEmpty(dto.getSupply()), SysProdSearch::getSupply, dto.getSupply())
                    .set(!ObjectUtils.isEmpty(dto.getCostPrice()), SysProdSearch::getCostPrice, dto.getCostPrice())
                    .set(!ObjectUtils.isEmpty(shop.getId()), SysProdSearch::getShopId, shop.getId())
                    .set(!ObjectUtils.isEmpty(shop.getUid()), SysProdSearch::getShopUid, shop.getUid())
                    .set(!ObjectUtils.isEmpty(shop.getRealname()), SysProdSearch::getShopName, shop.getRealname())
                    .eq(SysProdSearch::getProdId, data.getId())
                    .eq(SysProdSearch::getSearchType, 1));

            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    /**
     * 初始化请求参数
     */
    void initSysProdPageDtoParameters(SysProdPageDto dto) {
        // 查询参数初始化
        Date endTime = dto.dealEndTime();
        Date outEndTime = dto.dealOutEndTime();

        // 处理出库时间 , 新增outTime对象  示例参数：outTime : ["2024-03-06T12:30:11.992Z", "2024-04-25T12:30:11.992Z"]
        if (!ObjectUtils.isEmpty(dto.getOutTime()) && dto.getOutTime().size() >= 2) {
            dto.setOutBeginTime(DateTimeUtils.localDateTimeToDate(dto.getOutTime().get(0)));
            outEndTime = DateTimeUtils.localDateTimeToDate(dto.getOutTime().get(1));
        }

        if (!ObjectUtils.isEmpty(endTime))
            dto.setEndTime(endTime);

        if (!ObjectUtils.isEmpty(outEndTime))
            dto.setOutEndTimeDate(outEndTime);

        if (!ObjectUtils.isEmpty(dto.getSku()))
            dto.setSku(dto.getSku().trim());

        // 存在分页数量的时候查询分页数量
        if (dto.getSize() != null && dto.getSize() > 0
                && dto.getCurrent() != null && dto.getCurrent() > 0) {

            dto.setCurrSize((dto.getCurrent() - 1) * dto.getSize()); // 从哪条开始
            dto.setLimitSize(dto.getSize());
        }

        // 所在货架
        if (ObjectUtils.isEmpty(dto.getShelvesIdList()) && !ObjectUtils.isEmpty(dto.getShelvesId())) {
            dto.setShelvesIdList(new ArrayList<>(Arrays.asList(dto.getShelvesId())));
        }

        // 入库物流单号
        if (ObjectUtils.isEmpty(dto.getLogNoList()) && !ObjectUtils.isEmpty(dto.getLogNo())) {
            dto.setLogNoList(new ArrayList<>(Arrays.asList(dto.getLogNo())));
        }

        // 入库单号
        if (ObjectUtils.isEmpty(dto.getInBatchNoList()) && !ObjectUtils.isEmpty(dto.getInBatchNo())) {
            dto.setInBatchNoList(new ArrayList<>(Arrays.asList(dto.getInBatchNo())));
        }

        // 身份判断
        dto.setShopId(JwtContentHolder.getShopId());

        // 商家
        dto.setShopIdPowerList(JwtContentHolder.getShopIdList());
        dto.setWareId(JwtContentHolder.getWareId());
        //当前登录信息
        dto.setRoleType(JwtContentHolder.getRoleType());
        dto.setShopIdLogin(JwtContentHolder.getShopId());
        dto.setWareIdLogin(JwtContentHolder.getWareId());

        // skuList 查询进行处理
        if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getSkuList())) {
            List<String> searechList = new ArrayList<>();
            for (String s : dto.getSkuList()) {
                searechList.addAll(BaseUtils.getSkuList(s.trim()));
            }
            dto.setSkuList(searechList);
        }

        // searchSkuList 查询进行处理
        if (!ObjectUtils.isEmpty(dto) && !ObjectUtils.isEmpty(dto.getSku())) {
            dto.setSearchSkuList(BaseUtils.getSkuList(dto.getSku()));
        }

        if (!ObjectUtils.isEmpty(dto.getShopUidList())
                && dto.getShopUidList().stream().anyMatch(s -> s.equalsIgnoreCase("Unknown"))
        ){
            dto.setShopIDEmpty(true);
        }

        // 显示顺序
        if (ObjectUtils.isEmpty(dto.getSortField())
                && !ObjectUtils.isEmpty(dto.getStatusList()) && dto.getStatusList().size() == 1 && dto.getStatusList().contains(6)) {
            dto.setSortField("gmtOut");
        }
        if (!ObjectUtils.isEmpty(dto.getSortField())) {
            if (ObjectUtils.isEmpty(dto.getSortOrder())) {
                dto.setSortOrder("descend");
            }

            //  排序方式，1-总库存倒序，2-总库存升序，3-合格库存倒序，4-合格库存升序，5-破损库存倒序，6-破损库存升序
            switch (dto.getSortField()) {
                case "num":
                    dto.setSortType(1);
                    break;
                case "intactNum":
                    dto.setSortType(3);
                    break;
                case "brokenNum":
                    dto.setSortType(5);
                    break;
                case "gmtIn":
                    dto.setSortType(7);
                    break;
                case "sku":
                    dto.setSortType(9);
                    break;
                case "spec":
                    dto.setSortType(11);
                    break;
                case "wareDays":
                    dto.setSortType(7);
                    break;
                case "gmtOut":
                    dto.setSortType(13);
                    break;
                default:
                    dto.setSortType(0);
            }
            if (dto.getSortOrder().equals("ascend")) {
                dto.setSortType(dto.getSortType() + 1);
            }
        } else {
            dto.setSortType(0);
        }

        if (!ObjectUtils.isEmpty(dto.getSortType()) && dto.getSortType() <= 6) {
            dto.setGroupSortType(dto.getSortType());
        }

    }

    @Override
    @ReadOnly
    public IPage<SysProdListVo> searchList(SysProdPageDto dto) {
        // 初始化请求参数
        this.initSysProdPageDtoParameters(dto);

        IPage<SysProdListVo> pageResult = new Page();
        // 手动维护分页
        pageResult.setRecords(sysProdSearchMapper.selectSysProdSearchVOList(dto));
        pageResult.setCurrent(dto.getCurrent() != null ? dto.getCurrent() : 0);
        pageResult.setTotal(sysProdSearchMapper.selectSysProdSearchVOListNum(dto));

        if (!ObjectUtils.isEmpty(dto.getSize())) {
            pageResult.setSize(dto.getSize());
        }

        return pageResult;
    }

    /**
     *
     */
    @Override
    public IPage<SysProdListVo> searchListGroupBySkuAndSize(SysProdPageDto dto) {
        // 初始化请求参数
        this.initSysProdPageDtoParameters(dto);

        IPage<SysProdListVo> pageResult = new Page();
        pageResult.setRecords(sysProdSearchMapper.selectSysProdSearchVOListGroupBySkuAndSize(dto));
        pageResult.setCurrent(dto.getCurrent() != null ? dto.getCurrent() : 0);
        return pageResult;
    }

    /**
     * 统计数量
     */
    @Override
    @ReadOnly
    public Long searchListCount(SysProdPageDto dto) {
        // 初始化请求参数
        this.initSysProdPageDtoParameters(dto);

        return sysProdSearchMapper.selectSysProdSearchVOListNum(dto);
    }

    /**
     * @param dto
     * @return
     */
    @Override
    public List<String> updateCostPrice(List<UpdateSysProdCostPriceDTO> dto) {
        List<String> failedOneIds = new ArrayList<>();

        if (ObjectUtils.isEmpty(dto)) {
            throw new BaseException("Update cost price failed, parameter is empty");
        }

        Map<String, UpdateSysProdCostPriceDTO> mapByOneId = dto.stream().collect(Collectors.toMap(UpdateSysProdCostPriceDTO::getOneId,
                Function.identity(),
                (e, r) -> r)
        );

        List<SysProd> matchedProds = list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getOneId, mapByOneId.keySet()));
        List<SysProdSearch> matchedSearchs = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                .in(SysProdSearch::getOneId, mapByOneId.keySet())
                .eq(SysProdSearch::getSearchType, 1)
        );

        if (ObjectUtils.isEmpty(matchedProds) || matchedSearchs.size() != matchedProds.size()) {
            throw new BaseException("Update cost price failed, no matched sys prod data");
        }

        Map<String, SysProdSearch> mapByOneIdSearch = matchedSearchs.stream().collect(Collectors.toMap(SysProdSearch::getOneId,
                Function.identity(),
                (e, r) -> r)
        );

        matchedProds.forEach(prod -> {
            try {
                SysProdSearch matchedSysProdSearch = mapByOneIdSearch.get(prod.getOneId());
                if (ObjectUtils.isEmpty(matchedSysProdSearch)) {
                    log.info("Update cost price failed, no matched sys prod search data, oneId = {}", prod.getOneId());
                    failedOneIds.add(prod.getOneId());
                    return;
                }

                String newCostPrice = mapByOneId.get(prod.getOneId()).getCostPrice();
                BigDecimal bigDecimal = new BigDecimal(newCostPrice);

                prod.setCostPrice(bigDecimal);
                matchedSysProdSearch.setCostPrice(bigDecimal);

                updateById(prod);
                sysProdSearchMapper.updateById(matchedSysProdSearch);

            } catch (Exception e) {
                log.error("Update cost price failed, oneId = {}, costPrice = {}", prod.getOneId(), mapByOneId.get(prod.getOneId()).getCostPrice());
                failedOneIds.add(prod.getOneId());
            }
        });

        return failedOneIds;
    }

    @Override
    @ReadOnly
    public IPage<SysProdGroupListVo> searchGroup(SysProdPageDto dto, Integer groupType) {
        // 分组查询校验分页参数。 top: 无需分页的查询使用 searchList 方法
        if (ObjectUtils.isEmpty(dto.getSize()) || ObjectUtils.isEmpty(dto.getCurrent()) || ObjectUtils.isEmpty(groupType)) {
            log.error("sysProdService_searchGroup : dto getSize is null or  groupType is null . dto = {} , groupType = {} ", JSON.toJSONString(dto), groupType);
            return new Page<SysProdGroupListVo>();
        }

        // 初始化请求参数
        this.initSysProdPageDtoParameters(dto);

        // 获取外层数据   groupType = 1 ： 按照品名分类 ； groupType = 2 按照货架分类
        IPage<SysProdGroupListVo> pageResult = new Page();
        // 为了提高查询速度，查询内部做了数据范围限制，这里手动维护分页
        if (groupType == 1) {
            pageResult.setRecords(sysProdSearchMapper.selectSysProdSearchVOListGroupBySku(dto));
            pageResult.setTotal(sysProdSearchMapper.selectSysProdSearchVOListGroupBySkuCount(dto));
            pageResult.setCurrent(dto.getCurrent() != null ? dto.getCurrent() : 0);
        } else if (groupType == 2) {
            pageResult.setRecords(sysProdSearchMapper.selectSysProdSearchVOListGroupByWare(dto));
            pageResult.setTotal(sysProdSearchMapper.selectSysProdSearchVOListGroupByWareCount(dto));
            pageResult.setCurrent(dto.getCurrent() != null ? dto.getCurrent() : 0);
        } else {
            log.error("sysProdService_searchGroup : groupType There is no definition。 groupType = {} ", groupType);
            return new Page<>();
        }
        if (!ObjectUtils.isEmpty(dto.getSize())) {
            pageResult.setSize(dto.getSize());
        }
//
//        //  根据外层结果进行二级查询
//        List<SysProdGroupListVo> sysProdGroupListVos = pageResult.getRecords();
//        if (groupType == 1) {
//            // 根据sku查询商品列表
//            List<String> skuList = sysProdGroupListVos.stream().map(SysProdGroupListVo::getSku).collect(Collectors.toList());
//            dto.setSkuList(skuList);
//        } else if (groupType == 2) {
//            // 根据货架名字 + 仓库名字 查询商品列表
//            List<String> wareNameAndShelvesName = sysProdGroupListVos.stream()
//                    .map(v -> v.getWareName() + v.getShelvesName())
//                    .collect(Collectors.toList());
//            dto.setWareNameAndShelvesName(wareNameAndShelvesName);
//        }
//
//        // 商品列表 和 列表的统计参数
//        List<SysProdListVo> sysProdListVos = this.getProductGroupBySku(dto);
//        Map<String, List<SysProdListVo>> skuToProdMap = new HashMap<>();
//        if (groupType == 1)
//            skuToProdMap = sysProdListVos.stream()
//                    .collect(Collectors.groupingBy(SysProdListVo::getSku));
//        if (groupType == 2)
//            skuToProdMap = sysProdListVos.stream()
//                    .collect(Collectors.groupingBy(v -> v.getWareName() + v.getShelvesName()));
//
//        Map<String, List<SysProdListVo>> finalSkuToProdMap = skuToProdMap;
//        sysProdGroupListVos.forEach(e -> {
//            List<SysProdListVo> prodListVos = new ArrayList<>();
//            if (groupType == 1) {
//                prodListVos = finalSkuToProdMap.get(e.getSku());
//                // 按照 sku分组时，对尺码进行排序
//                prodListVos.sort(Comparator.comparingDouble(
//                        sysProdListVo -> {
//                            String s = BaseUtils.convertGoatSizeValue(sysProdListVo.getSpec());
//                            if (ObjectUtils.isEmpty(s) || s.trim().equals("")) {
//                                s = "0.00";
//                            }
//                            return Double.parseDouble(s);
//                        }
//                ));
//            }
//
//            if (groupType == 2)
//                prodListVos = finalSkuToProdMap.get(e.getWareName() + e.getShelvesName());
//
//            if (ObjectUtils.isEmpty(prodListVos)) {
//                return;
//            }
//
//            e.setProdList(prodListVos); // 商品列表
//        });
//
//        pageResult.setRecords(sysProdGroupListVos);

        return pageResult;
    }

    /**
     * 仓库 - 按品名分类 - 查询商品详情列表
     */
    @Override
    @ReadOnly
    public List<SysProdListVo> getProductGroupBySku(SysProdPageDto dto) {
        this.initSysProdPageDtoParameters(dto);
        return sysProdSearchMapper.selectGroupBySkuForProduct(dto);
    }

    /**
     * 仓库 - 按品名分类 - 查询商品详情列表统计
     */
    @Override
    @ReadOnly
    public List<SysProdListVo> getProductGroupBySkuCount(SysProdPageDto dto) {
        this.initSysProdPageDtoParameters(dto);
        return sysProdSearchMapper.selectGroupBySkuForProductCount(dto);
    }

    @Override
    @ReadOnly
    public SysProdCountV2Vo getCountV2(SysProdPageDto dto) {
        this.initSysProdPageDtoParameters(dto);
        dto.setStatusList(null);
        return sysProdSearchMapper.selectSearchCount(dto);
    }

    /**
     * 对 list 进行重新排序
     */
    void sortSysProdGroupListVos(List<SysProdGroupListVo> sysProdGroupListVos, Integer sortType) {
        // 排序方式，1-总库存倒序，2-总库存升序，3-合格库存倒序，4-合格库存升序，5-破损库存倒序，6-破损库存升序
        List<SysProdGroupListVo> voList;
        switch (sortType) {
            case 1:
                sysProdGroupListVos = sysProdGroupListVos.stream().sorted(Comparator.comparing(SysProdGroupListVo::getNum).reversed()).collect(Collectors.toList());
                break;
            case 2:
                sysProdGroupListVos = sysProdGroupListVos.stream().sorted(Comparator.comparing(SysProdGroupListVo::getNum)).collect(Collectors.toList());
                break;
            case 3:
                sysProdGroupListVos = sysProdGroupListVos.stream().sorted(Comparator.comparing(SysProdGroupListVo::getIntactNum).reversed()).collect(Collectors.toList());
                break;
            case 4:
                sysProdGroupListVos = sysProdGroupListVos.stream().sorted(Comparator.comparing(SysProdGroupListVo::getIntactNum)).collect(Collectors.toList());
                break;
            case 5:
                sysProdGroupListVos = sysProdGroupListVos.stream().sorted(Comparator.comparing(SysProdGroupListVo::getBrokenNum).reversed()).collect(Collectors.toList());
                break;
            case 6:
                sysProdGroupListVos = sysProdGroupListVos.stream().sorted(Comparator.comparing(SysProdGroupListVo::getBrokenNum)).collect(Collectors.toList());
                break;
            default:
                sysProdGroupListVos = sysProdGroupListVos;
        }
    }

    /*@Override
    public IPage<SysProdListVo> searchList(SysProdPageDto dto) {

        // 筛选并查询数据
        LambdaQueryWrapper<SysProdSearch> qw = this.buildQw(dto);

        IPage<SysProdSearch> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = iSysProdSearchService.page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(iSysProdSearchService.list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            Date now = DateTimeUtils.getNow();

            List<Integer> prodIdList = BaseUtils.initList();
            prodIdList.addAll(pageResult.getRecords().stream().map(SysProdSearch::getProdId).collect(Collectors.toList()));
            List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
            Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
            prodList.clear();

            List<SysWare> wareList = iSysWareService.list();
            Map<Integer, SysWare> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, a -> a));
            wareList.clear();

            // 货架
            List<SysWareShelves> shelvesList = iSysWareShelvesService.list();
            Map<Integer, String> shelvesMap = shelvesList.stream().collect(Collectors.toMap(SysWareShelves::getId, SysWareShelves::getName));

            // 操作记录
            List<Integer> dealIdList = BaseUtils.initList();
            dealIdList.addAll(pageResult.getRecords().stream().filter(a -> !ObjectUtils.isEmpty(a.getDealId())).map(SysProdSearch::getDealId).collect(Collectors.toList()));
            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery().in(SysProdDeal::getId, dealIdList));
            Map<Integer, SysProdDeal> dealMap = dealList.stream().collect(Collectors.toMap(SysProdDeal::getId, a -> a));

            // 寄售信息
            List<Integer> saleIdList = BaseUtils.initList();
            saleIdList.addAll(dealList.stream().filter(a -> !ObjectUtils.isEmpty(a.getSaleId())).map(SysProdDeal::getSaleId).collect(Collectors.toList()));
            dealList.clear();
            List<SysProdSale> saleList = iSysProdSaleService.list(Wrappers.<SysProdSale>lambdaQuery().in(SysProdSale::getId, saleIdList));
            Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
            saleList.clear();

            // 包裹查验时间
            List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
            Map<Integer, Integer> prodPackMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, SysWareInProd::getPackId));
            List<Integer> packIdList = BaseUtils.initList();
            packIdList.addAll(inProdList.stream().map(SysWareInProd::getPackId).collect(Collectors.toList()));
            inProdList.clear();
            List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery().in(ShopPack::getId, packIdList));
            Map<Integer, Date> gmtWareMap = packList.stream().collect(Collectors.toMap(ShopPack::getId, ShopPack::getGmtWare));
            packList.clear();

            // 预报类型 入库表
//            List<SysProdSearch> sysProdSearchList = sysWareInMapper.selectBatch();
//            Map<String, List<String>> sysProdSearchLis = sysProdSearchList.stream()
//                    .collect(Collectors.groupingBy(SysProdSearch::getOneId,
//                            Collectors.mapping(SysProdSearch::getInBatchNo, Collectors.toList())));
//            sysProdSearchList.clear();
//
//            List<SysWareIn> sysWareInList =  sysWareInMapper.selectType();
//            Map<String, Integer> sysWareInMap = sysWareInList.stream().collect(Collectors.toMap(SysWareIn::getBatchNo, SysWareIn::getType));
//            sysWareInList.clear();
//
//            // 预报类型 预报表
//            List<ShopPack> shopPackList =  shopPackMapper.selectType();
//            Map<String, Integer> shopPackMap = shopPackList.stream().collect(Collectors.toMap(ShopPack::getLogNo, ShopPack::getType, (oldValue, newValue) -> newValue));
//            shopPackList.clear();

            pageResult.getRecords().forEach(data -> {
                SysProdListVo vo = new SysProdListVo();
                BeanUtils.copyProperties(data, vo);

                vo.setSearchId(data.getId());
                vo.setLogNo(data.getInLogNo());

                // 商品基本信息
                SysProd prod = prodMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    // fix: 修改后使用WarehouseSearchId字段记录原始主键ID，用于库存管理的列表导出功能
                    vo.setWarehouseSearchId(vo.getId());

                    vo.setId(prod.getId());
                    vo.setImg(prod.getImg());
                    vo.setSpec(prod.getSpec());
                }

                // 流程类型
                vo.setEventType(data.getOddType());

                SysProdDeal deal = dealMap.get(data.getDealId());
                if (!ObjectUtils.isEmpty(deal)) {
                    // 出库价格
                    vo.setOutPrice(deal.getOutPrice());
                    switch (deal.getType()) {
                        case SysProdEvent.TypeSale:
                            vo.setOutPrice(deal.getSoldPrice());
                            break;
                        case SysProdEvent.TypeCash:
                            vo.setOutPrice(deal.getSalePrice());
                            break;
                    }

                    // 销售信息
                    SysProdSale sale = saleMap.get(deal.getSaleId());
                    if (!ObjectUtils.isEmpty(sale)) {
                        vo.setOddNo(sale.getOddNo()); // 前端若取值更换为outNo，则可删除此行代码
                        vo.setPlatName(sale.getPlatName());
                        vo.setOutLogNo(sale.getLogNo());
                        vo.setGmtOrder(sale.getGmtCreate());
                    }
                }

                // 仓库名
                SysWare ware = wareMap.get(data.getWareId());
                if (!ObjectUtils.isEmpty(ware)) {
                    vo.setWareName(ware.getName());
                }

                // 货架号
                vo.setShelvesName(shelvesMap.get(data.getShelvesId()));

                // 在仓信息
                vo.setWareDays(DateTimeUtils.timeDiff(ObjectUtils.isEmpty(data.getGmtPay()) ? now : data.getGmtPay(), data.getGmtCreate(), 4));

                // 包裹查验时间
                vo.setGmtWare(Optional.ofNullable(prodPackMap.get(data.getProdId())).map(packId -> gmtWareMap.get(packId)).orElse(null));

                // 预报类型
//                if (ObjectUtils.isEmpty(data.getInLogNo())) { // 物流单号为null时，通过入库表查询结果
//                    for (String e : sysProdSearchLis.get(data.getOneId())) {
//                        if (!ObjectUtils.isEmpty(e)) {
//                            vo.setType(sysWareInMap.get(e));
//                            break;
//                        }
//                    }
//
//                } else {
//                    vo.setType(shopPackMap.get(data.getInLogNo()));
//                }



                // 更新：使用OneID查询search表中是否有多条，存在多条时处理成： 显示最新的数据，但是入库时间显示最老的
//                if (!ObjectUtils.isEmpty(vo.getOneId())){
//                    vo.setGmtIn(this.getOldGmtInByOneId(vo.getOneId()));
//                }

                voList.add(vo);
            });
        }

        IPage<SysProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }*/

    //通过OneId ,查询鞋子最老的入库日期
    private Date getOldGmtInByOneId(String oneId) {

        if (ObjectUtils.isEmpty(oneId)) {
            return null;
        }

        return iSysProdSearchService.getOldGmtInByoneId(oneId);
    }

    private LambdaQueryWrapper<SysProdSearch> buildQw(SysProdPageDto dto) {
        Date endTime = dto.dealEndTime();
        if (ObjectUtils.isEmpty(endTime)) {
            endTime = dto.getEndTime();
        }
        Date outEndTime = dto.dealOutEndTime();

        // 处理出库时间 , 新增outTime对象  示例参数：outTime : ["2024-03-06T12:30:11.992Z", "2024-04-25T12:30:11.992Z"]
        if (!ObjectUtils.isEmpty(dto.getOutTime()) && dto.getOutTime().size() >= 2) {
            dto.setOutBeginTime(DateTimeUtils.localDateTimeToDate(dto.getOutTime().get(0)));
            outEndTime = DateTimeUtils.localDateTimeToDate(dto.getOutTime().get(1));
        }

        LambdaQueryWrapper<SysProdSearch> search = Wrappers.<SysProdSearch>lambdaQuery()
                .eq(SysProdSearch::getDelFlag, 0)
//                .eq(!ObjectUtils.isEmpty(dto.getSearchType()), SysProdSearch::getSearchType, dto.getSearchType())
                .eq(!ObjectUtils.isEmpty(dto.getShopId()), SysProdSearch::getShopId, dto.getShopId())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysProdSearch::getStatus, dto.getStatus())
                .in(!ObjectUtils.isEmpty(dto.getStatusList()), SysProdSearch::getStatus, dto.getStatusList())
                .in(!ObjectUtils.isEmpty(dto.getStatusList2()), SysProdSearch::getStatus, dto.getStatusList2())
                .eq(!ObjectUtils.isEmpty(dto.getOneIdEq()), SysProdSearch::getOneId, dto.getOneIdEq())
                .eq(!ObjectUtils.isEmpty(dto.getSkuEq()), SysProdSearch::getSku, dto.getSkuEq())
                .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProdSearch::getOneId, dto.getOneId())
                .like(!ObjectUtils.isEmpty(dto.getSupply()), SysProdSearch::getSupply, dto.getSupply())
                .like(!ObjectUtils.isEmpty(dto.getPku()), SysProdSearch::getPku, dto.getPku())
                .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysProdSearch::getSku, dto.getSkuList())
                .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProdSearch::getSpec, dto.getSpec())
                .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysProdSearch::getSpec, dto.getSpecSearchList())
                .like(!ObjectUtils.isEmpty(dto.getBrand()), SysProdSearch::getBrand, dto.getBrand())
                .eq(!ObjectUtils.isEmpty(dto.getCostPrice()), SysProdSearch::getCostPrice, dto.getCostPrice())
                .ge(!ObjectUtils.isEmpty(dto.getCostPriceMin()), SysProdSearch::getCostPrice, dto.getCostPriceMin())
                .le(!ObjectUtils.isEmpty(dto.getCostPriceMax()), SysProdSearch::getCostPrice, dto.getCostPriceMax())

                .like(!ObjectUtils.isEmpty(dto.getShopName()), SysProdSearch::getShopName, dto.getShopName())

                .eq(!ObjectUtils.isEmpty(dto.getTransferStatus()), SysProdSearch::getTransferStatus, dto.getTransferStatus())

                .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysProdSearch::getWareId, dto.getWareIdList())
                .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysProdSearch::getWareId, dto.getWareId())
                .eq(!ObjectUtils.isEmpty(dto.getCheckResult()), SysProdSearch::getCheckResult, dto.getCheckResult())
                .in(!ObjectUtils.isEmpty(dto.getCheckResultList()), SysProdSearch::getCheckResult, dto.getCheckResultList())

                .eq(!ObjectUtils.isEmpty(dto.getType()), SysProdSearch::getOddType, dto.getType())
                .eq(!ObjectUtils.isEmpty(dto.getThirdPlatId()), SysProdSearch::getThirdPlatId, dto.getThirdPlatId())
                .like(!ObjectUtils.isEmpty(dto.getPlatName()), SysProdSearch::getThirdPlatName, dto.getPlatName())
                .like(!ObjectUtils.isEmpty(dto.getPlatOrderNo()), SysProdSearch::getPlatOrderNo, dto.getPlatOrderNo())

                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdSearch::getGmtIn, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdSearch::getGmtIn, endTime)
                .ge(!ObjectUtils.isEmpty(dto.getOutBeginTime()), SysProdSearch::getGmtOut, dto.getOutBeginTime())
                .lt(!ObjectUtils.isEmpty(outEndTime), SysProdSearch::getGmtOut, outEndTime)
                .in(!ObjectUtils.isEmpty(dto.getOneIdList()), SysProdSearch::getOneId, dto.getOneIdList());

        if (!ObjectUtils.isEmpty(dto.getSku())) {
            dto.setSku(dto.getSku().trim());
            search.and((e) -> {
                e.like(!ObjectUtils.isEmpty(dto.getSku()), SysProdSearch::getRemarks, dto.getSku())
                        .or().like(!ObjectUtils.isEmpty(dto.getSku()), SysProdSearch::getSku, dto.getSku())
                        .or().like(!ObjectUtils.isEmpty(dto.getSku()), SysProdSearch::getOneId, dto.getSku())
                        .or().like(!ObjectUtils.isEmpty(dto.getSku()), SysProdSearch::getInLogNo, dto.getSku());
            });
        }
        // oneId批量搜索
//        if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
//            StringBuffer sb = new StringBuffer();
//            dto.getOneIdList().forEach(oneId -> {
//                sb.append("(one_id like '%" + oneId + "%') or ");
//            });
//            search.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
//        }

        // pku/sku
        if (!ObjectUtils.isEmpty(dto.getKuSearch())) {
            search.and(a -> a.like(SysProdSearch::getSku, dto.getKuSearch()).or().like(SysProdSearch::getPku, dto.getKuSearch()));
        }

        // 识别码
        if (!ObjectUtils.isEmpty(dto.getShopUid())) {
            if (dto.getShopUid().equals("无主件")
                    || dto.getShopUid().equals("Unknown")
                    || dto.getShopUid().equals("unknown"))
                search.isNull(SysProdSearch::getShopId);
            else
                search.like(SysProdSearch::getShopUid, dto.getShopUid());
        }

        // 所在货架
        if (ObjectUtils.isEmpty(dto.getShelvesIdList()) && !ObjectUtils.isEmpty(dto.getShelvesId())) {
            dto.setShelvesIdList(new ArrayList<>(Arrays.asList(dto.getShelvesId())));
        }
        if (!ObjectUtils.isEmpty(dto.getShelvesIdList())) {
            // -2代表未上架
            search.and(a -> a.in(SysProdSearch::getShelvesId, dto.getShelvesIdList())
                    .or(dto.getShelvesIdList().contains(-2), o -> o.isNull(SysProdSearch::getShelvesId)));
        }

        // 入库物流单号
        if (ObjectUtils.isEmpty(dto.getLogNoList()) && !ObjectUtils.isEmpty(dto.getLogNo())) {
            dto.setLogNoList(new ArrayList<>(Arrays.asList(dto.getLogNo())));
        }
        if (!ObjectUtils.isEmpty(dto.getLogNoList())) {
            StringBuffer sb = new StringBuffer();
            dto.getLogNoList().forEach(logNo -> {
                sb.append("(in_log_no like '%" + logNo + "%') or ");
            });
            search.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
        }

        // 入库单号
        if (ObjectUtils.isEmpty(dto.getInBatchNoList()) && !ObjectUtils.isEmpty(dto.getInBatchNo())) {
            dto.setInBatchNoList(new ArrayList<>(Arrays.asList(dto.getInBatchNo())));
        }
        if (!ObjectUtils.isEmpty(dto.getInBatchNoList())) {
            StringBuffer sb = new StringBuffer();
            dto.getInBatchNoList().forEach(batchNo -> {
                sb.append("(in_batch_no like '%" + batchNo + "%') or ");
            });
            search.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
        }

        // 管理员商家权限
        List<Integer> shopIdPowerList = JwtContentHolder.getShopIdList();
        if (!ObjectUtils.isEmpty(shopIdPowerList)) {
            search.in(SysProdSearch::getShopId, shopIdPowerList);
        }

        // 身份判断
        Integer shopId = JwtContentHolder.getShopId();
        Integer wareId = JwtContentHolder.getWareId();
        switch (JwtContentHolder.getRoleType()) {
            case 1:
                // 超管
                search.eq(SysProdSearch::getSearchType, 1);
                break;
            case 4:
                // 仓库
                search.eq(SysProdSearch::getWareId, wareId).ne(SysProdSearch::getStatus, 6).eq(SysProdSearch::getSearchType, 1);
                break;
            case 5:
                // 商家
                break;
        }

        if (!ObjectUtils.isEmpty(shopId)) {
            // 商家
            search.eq(SysProdSearch::getShopId, shopId);

            if (!ObjectUtils.isEmpty(dto.getStatusList()) && !dto.getStatusList().contains(6)) {
                // 在仓
                search.eq(SysProdSearch::getSearchType, 1);
                if (!ObjectUtils.isEmpty(dto.getIdList())) {
                    search.in(SysProdSearch::getProdId, dto.getIdList());
                }
            } else {
                // 已出库
                if (!ObjectUtils.isEmpty(dto.getIdList())) {
                    search.in(SysProdSearch::getDealId, dto.getIdList());
                }
            }
            // 全部
            if (!ObjectUtils.isEmpty(dto.getSearchIdList())) {
                // WarehouseSearchIdArray存在时，则使用WarehouseSearchIdArray查询
                if (ObjectUtils.isEmpty(dto.getWarehouseSearchIdArray())) {
                    search.in(SysProdSearch::getId, dto.getSearchIdList());
                } else {
                    search.in(SysProdSearch::getId, dto.getWarehouseSearchIdArray());
                }
            }
        } else {
            // 超管/仓库
            if (!ObjectUtils.isEmpty(dto.getIdList())) {
                search.in(SysProdSearch::getProdId, dto.getIdList());
            }

            if (!ObjectUtils.isEmpty(dto.getSearchIdList())) {
                search.in(SysProdSearch::getProdId, dto.getSearchIdList());
            }
        }


        // 显示顺序
        if (ObjectUtils.isEmpty(dto.getSortField())
                && !ObjectUtils.isEmpty(dto.getStatusList()) && dto.getStatusList().size() == 1 && dto.getStatusList().contains(6)) {
            dto.setSortField("gmtOut");
        }
        if (!ObjectUtils.isEmpty(dto.getSortField())) {
            if (ObjectUtils.isEmpty(dto.getSortOrder())) {
                dto.setSortOrder("descend");
            }

            //  排序方式，1-总库存倒序，2-总库存升序，3-合格库存倒序，4-合格库存升序，5-破损库存倒序，6-破损库存升序
            switch (dto.getSortField()) {
                case "num":
                    dto.setSortType(1);
                    break;
                case "intactNum":
                    dto.setSortType(3);
                    break;
                case "brokenNum":
                    dto.setSortType(5);
                    break;
                case "gmtIn":
                    dto.setSortType(7);
                    break;
                case "sku":
                    dto.setSortType(9);
                    break;
                case "spec":
                    dto.setSortType(11);
                    break;
                case "wareDays":
                    dto.setSortType(7);
                    break;
                case "gmtOut":
                    dto.setSortType(13);
                    break;
                default:
                    dto.setSortType(0);
            }
            if (dto.getSortOrder().equals("ascend")) {
                dto.setSortType(dto.getSortType() + 1);
            }
        } else {
            dto.setSortType(0);
        }
        switch (dto.getSortType()) {
            // 入库时间排序
            case 7:
                search.orderByDesc(SysProdSearch::getGmtIn);
                break;
            case 8:
                search.orderByAsc(SysProdSearch::getGmtIn);
                break;
            // sku
            case 9:
                search.orderByDesc(SysProdSearch::getSku);
                break;
            case 10:
                search.orderByAsc(SysProdSearch::getSku);
                break;
            // spec
            case 11:
                search.orderByDesc(SysProdSearch::getSpec);
                break;
            case 12:
                search.orderByAsc(SysProdSearch::getSpec);
                break;
            // 出库时间排序
            case 13:
                search.orderByDesc(SysProdSearch::getGmtOut);
                break;
            case 14:
                search.orderByAsc(SysProdSearch::getGmtOut);
                break;
        }
        search.orderByDesc(SysProdSearch::getGmtIn).orderByAsc(SysProdSearch::getId);

        return search;
    }

    @Override
    public Boolean insertList(List<SysProd> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProd> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public IPage<SysProdListVo> offList(PageBaseSearchDto dto) {

        LambdaQueryWrapper<SysProd> qw = Wrappers.<SysProd>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProd::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProd::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProd::getGmtCreate, endTime);

        Integer wareId = JwtContentHolder.getWareId();
        // 1.在指定仓库
        qw.eq(SysProd::getWareId, wareId);
        /*List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().eq(SysWareInProd::getWareId, wareId));
        if (!ObjectUtils.isEmpty(inProdList)) {
            qw.in(SysProd::getId, inProdList.stream().map(SysWareInProd::getProdId).collect(Collectors.toList()));
        } else {
            qw.in(SysProd::getId, 0);
        }*/

        // 2.未在货架上
        List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery().eq(SysWareShelvesProd::getWareId, wareId));
        if (!ObjectUtils.isEmpty(shelvesProdList)) {
            qw.notIn(SysProd::getId, shelvesProdList.stream().map(SysWareShelvesProd::getProdId).collect(Collectors.toList()));
        }
        shelvesProdList.clear();

        // 3.未在出库批次中
        List<SysWareOutBatchProd> batchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery().eq(SysWareOutBatchProd::getBatchStatus, 1));
        if (!ObjectUtils.isEmpty(batchProdList)) {
            qw.notIn(SysProd::getId, batchProdList.stream().map(SysWareOutBatchProd::getProdId).collect(Collectors.toList()));
        }
        batchProdList.clear();

        IPage<SysProd> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        Map<String, Integer> checkResultMap = new HashMap<>();

        if (!ObjectUtils.isEmpty(pageResult.getRecords()) && !ObjectUtils.isEmpty(pageResult.getRecords().stream().map(SysProd::getOneId).collect(Collectors.toList()))) {
            List<SysProdSearch> searchList = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                    .select(SysProdSearch::getOneId, SysProdSearch::getCheckResult)
                    .in(SysProdSearch::getSearchType, 1)
                    .in(SysProdSearch::getDelFlag, 0)
                    .in(SysProdSearch::getOneId, pageResult.getRecords().stream().map(SysProd::getOneId).collect(Collectors.toList()))
            );

            checkResultMap = searchList.stream().collect(Collectors.toMap(SysProdSearch::getOneId, SysProdSearch::getCheckResult
                    , (k1, k2) -> k2
            ));
        }

        // 获取处于锁定中的商品
        List<String> lockList = this.querySysProdLock(pageResult.getRecords().stream().map(SysProd::getOneId).collect(Collectors.toList()));

        List<SysProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            for (SysProd data : pageResult.getRecords()) {

                SysProdListVo vo = new SysProdListVo();
                BeanUtils.copyProperties(data, vo);
                vo.setCheckResult(checkResultMap.get(data.getOneId()));

                if (data.getStatus() != 1 && lockList.contains(data.getOneId())) {
                    vo.setLocked(SysConstants.SYS_PRODUCT_LOCKED);
                }

                voList.add(vo);
            }
        }

        IPage<SysProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public IPage<SysProdListVo> onList(SysWareShelvesProdPageDto dto) {

        LambdaQueryWrapper<SysProd> qw = Wrappers.<SysProd>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProd::getGmtCreate).ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProd::getGmtCreate, dto.getBeginTime()).lt(!ObjectUtils.isEmpty(endTime), SysProd::getGmtCreate, endTime);

        Integer wareId = JwtContentHolder.getWareId();

        // 1.在货架上
        List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery().eq(!ObjectUtils.isEmpty(dto.getShelvesId()), SysWareShelvesProd::getShelvesId, dto.getShelvesId()).eq(SysWareShelvesProd::getWareId, wareId));
        Map<Integer, Date> onTimeMap = shelvesProdList.stream().collect(Collectors.toMap(SysWareShelvesProd::getProdId, SysWareShelvesProd::getGmtCreate
                // 当出现多个相同的prodID时，取最新的一条
                , (k1, k2) -> k2
        ));
        if (!ObjectUtils.isEmpty(shelvesProdList)) {
            qw.in(SysProd::getId, shelvesProdList.stream().map(SysWareShelvesProd::getProdId).collect(Collectors.toList()));
        } else {
            qw.in(SysProd::getId, 0);
        }
        shelvesProdList.clear();

        IPage<SysProd> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }


        Map<String, Integer> checkResultMap = new HashMap<>();

        if (!ObjectUtils.isEmpty(pageResult.getRecords()) && !ObjectUtils.isEmpty(pageResult.getRecords().stream().map(SysProd::getOneId).collect(Collectors.toList()))) {
            List<SysProdSearch> searchList = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                    .select(SysProdSearch::getOneId, SysProdSearch::getCheckResult)
                    .in(SysProdSearch::getSearchType, 1)
                    .in(SysProdSearch::getDelFlag, 0)
                    .in(SysProdSearch::getOneId, pageResult.getRecords().stream().map(SysProd::getOneId).collect(Collectors.toList()))
            );

            checkResultMap = searchList.stream().collect(Collectors.toMap(SysProdSearch::getOneId, SysProdSearch::getCheckResult
                    , (k1, k2) -> k2
            ));
        }

        List<SysProdListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<SysWareInProd> inProdList = iSysWareInProdService.list(
                    Wrappers.<SysWareInProd>lambdaQuery()
                            .in(SysWareInProd::getProdId, pageResult.getRecords().stream().map(SysProd::getId).collect(Collectors.toList()))
            );
            Map<Integer, SysWareInProd> inMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));

            // 获取处于锁定中的商品
            List<String> lockList = this.querySysProdLock(pageResult.getRecords().stream().map(SysProd::getOneId).collect(Collectors.toList()));

            Date now = DateTimeUtils.getNow();
            for (SysProd data : pageResult.getRecords()) {
                SysProdListVo vo = new SysProdListVo();
                BeanUtils.copyProperties(data, vo);

                SysWareInProd in = inMap.get(data.getId());
                if (!ObjectUtils.isEmpty(in)) {
                    vo.setWareDays(DateTimeUtils.timeDiff(ObjectUtils.isEmpty(in.getGmtPay()) ? now : in.getGmtPay(), in.getGmtCreate(), 4));
                }

                vo.setGmtOn(onTimeMap.get(data.getId()));

                vo.setCheckResult(checkResultMap.get(data.getOneId()));

                if (data.getStatus() != 1 && lockList.contains(data.getOneId())) {
                    vo.setLocked(SysConstants.SYS_PRODUCT_LOCKED);
                }

                voList.add(vo);
            }
        }

        IPage<SysProdListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    @ReadOnly
    public SysProdCountVo getCount(SysProdPageDto dto) {
        dto.setCurrent(0);
        dto.setSize(0);
        dto.setStatusList(null);

        // 商品数量统计
        SysProdCountVo vo = new SysProdCountVo();

        // 在仓库存
        SysProdPageDto dto1 = new SysProdPageDto();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setStatusList(new ArrayList<>(Arrays.asList(1, 2, 3, 4, 7, 8, 9, 10,12)));
        vo.setWareNum(searchListCount(dto1).intValue());

        // 已出库
        if (ObjectUtils.isEmpty(JwtContentHolder.getShopId())) {
            SysProdPageDto dto2 = new SysProdPageDto();
            BeanUtils.copyProperties(dto, dto2);
            dto2.setSearchType(1);
            dto2.setStatusList(new ArrayList<>(Arrays.asList(6)));
            vo.setOutNum(searchListCount(dto2).intValue());
        } else {
            SysProdPageDto dto2 = new SysProdPageDto();
            BeanUtils.copyProperties(dto, dto2);
            dto2.setStatusList(new ArrayList<>(Arrays.asList(6)));
            dto2.setSearchType(null);
            vo.setOutNum(searchListCount(dto2).intValue());
        }

        // 平台寄售：待售商品
        SysProdDealPageDto dto3 = new SysProdDealPageDto();
        BeanUtils.copyProperties(dto, dto3);
        dto3.setStatus(1);
        vo.setSaleNum((int) iSysProdDealService.saleList(dto3).getTotal());

        // 平台寄售：已售商品
        SysProdDealPageDto dto4 = new SysProdDealPageDto();
        BeanUtils.copyProperties(dto, dto4);
        dto4.setStatus(2);
        vo.setSoldNum((int) iSysProdDealService.saleList(dto4).getTotal());

        // 平台寄售：已结算商品
        SysProdDealPageDto dto5 = new SysProdDealPageDto();
        BeanUtils.copyProperties(dto, dto5);
        dto5.setStatus(3);
        vo.setSettledNum((int) iSysProdDealService.saleList(dto5).getTotal());

        // 平台寄售：已结算商品
        SysProdDealPageDto dto6 = new SysProdDealPageDto();
        BeanUtils.copyProperties(dto, dto6);
        dto6.setStatus(4);
        vo.setCloseNum((int) iSysProdDealService.saleList(dto6).getTotal());

        // 在仓总成本
        SysProdPageDto dto8 = new SysProdPageDto();
        BeanUtils.copyProperties(dto, dto8);
        dto8.setStatusList(new ArrayList<>(Arrays.asList(1, 2, 3, 4, 7, 8, 9, 10,12)));
        vo.setWareCost(sumCost(dto8));

        // 已出库总成本
        if (ObjectUtils.isEmpty(JwtContentHolder.getShopId())) {
            SysProdPageDto dto9 = new SysProdPageDto();
            BeanUtils.copyProperties(dto, dto9);
            dto9.setSearchType(1);
            dto9.setStatusList(new ArrayList<>(Arrays.asList(6)));
            vo.setOutCost(sumCost(dto9));
        } else {
            SysProdPageDto dto9 = new SysProdPageDto();
            BeanUtils.copyProperties(dto, dto9);
            dto9.setShopId(JwtContentHolder.getShopId());
            dto9.setStatusList(new ArrayList<>(Arrays.asList(6)));
            dto9.setSearchType(null);
//            dto9.setOutBeginTime(dto.getBeginTime());
//            dto9.setOutEndTime(dto.getEndTime());
//            dto9.setBeginTime(null);
//            dto9.setEndTime(null);
            vo.setOutCost(sumCost(dto9));
        }

        // 全部总成本
        if (ObjectUtils.isEmpty(JwtContentHolder.getShopId())) {
            SysProdPageDto dto10 = new SysProdPageDto();
            BeanUtils.copyProperties(dto, dto10);
            dto10.setSearchType(1);
            vo.setAllCost(sumCost(dto10));
        } else {
            SysProdPageDto dto10 = new SysProdPageDto();
            BeanUtils.copyProperties(dto, dto10);
            dto10.setShopId(JwtContentHolder.getShopId());
            dto10.setSearchType(null);
//            dto10.setOutBeginTime(dto.getBeginTime());
//            dto10.setOutEndTime(dto.getEndTime());
//            dto10.setBeginTime(null);
//            dto10.setEndTime(null);
            vo.setAllCost(sumCost(dto10));
        }


        return vo;
    }

    /*@Deprecated
    public List<SysProdGroupListVo> searchGroup_Deprecated(SysProdPageDto dto, Integer groupType) {
        List<SysProdListVo> dataList = searchList(dto).getRecords();
        Map<String, List<SysProdListVo>> keyMap = new HashMap<>();
        switch (groupType) {
            case 1:
                keyMap.putAll(dataList.stream().collect(Collectors.groupingBy(SysProdListVo::getSku)));
                break;
            case 2:
                dataList.forEach(data -> {
                    if (ObjectUtils.isEmpty(data.getShelvesName())) {
                        data.setShelvesName("未上架");
                    }
                    if (ObjectUtils.isEmpty(data.getWareName())) {
                        data.setWareName("已出库");
                    }
                });
                keyMap.putAll(dataList.stream().collect(Collectors.groupingBy(a -> {
                    return a.getWareName() + "##" + a.getShelvesName();
                })));
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("不支持的分组方式"));
        }

        List<SysProdGroupListVo> groupList = new ArrayList<>();
        keyMap.keySet().forEach(key -> {
            SysProdGroupListVo vo = new SysProdGroupListVo();

            List<SysProdListVo> prodList = keyMap.get(key);
            SysProdListVo sample = prodList.get(0);
            BeanUtils.copyProperties(sample, vo);
            vo.setProdList(prodList.stream().sorted(Comparator.comparing(a -> {
                return Optional.ofNullable(a.getSku()).orElse("") + "##" + Optional.ofNullable(a.getSpec())
                        .map(spec -> BaseUtils.dealSizeStr(spec).toString())
                        .orElse("");
            })).collect(Collectors.toList()));

            int num = 0;
            int intactNum = 0;
            int brokenNum = 0;
            BigDecimal sumCost = SysConstants.zero;

            for (SysProdListVo prod : prodList) {
                num++;

                Integer checkResult = prod.getCheckResult();
                if (!ObjectUtils.isEmpty(checkResult)) {
                    if (checkResult == 1) {
                        intactNum++;
                    } else {
                        brokenNum++;
                    }
                }

                if (!ObjectUtils.isEmpty(prod.getCostPrice())) {
                    sumCost = sumCost.add(prod.getCostPrice());
                }
            }
            vo.setNum(num);
            vo.setIntactNum(intactNum);
            vo.setBrokenNum(brokenNum);
            vo.setSumCost(sumCost);
            groupList.add(vo);
        });

        // 排序方式，1-总库存倒序，2-总库存升序，3-合格库存倒序，4-合格库存升序，5-破损库存倒序，6-破损库存升序
        List<SysProdGroupListVo> voList;
        switch (dto.getSortType()) {
            case 1:
                voList = groupList.stream().sorted(Comparator.comparing(SysProdGroupListVo::getNum).reversed()).collect(Collectors.toList());
                break;
            case 2:
                voList = groupList.stream().sorted(Comparator.comparing(SysProdGroupListVo::getNum)).collect(Collectors.toList());
                break;
            case 3:
                voList = groupList.stream().sorted(Comparator.comparing(SysProdGroupListVo::getIntactNum).reversed()).collect(Collectors.toList());
                break;
            case 4:
                voList = groupList.stream().sorted(Comparator.comparing(SysProdGroupListVo::getIntactNum)).collect(Collectors.toList());
                break;
            case 5:
                voList = groupList.stream().sorted(Comparator.comparing(SysProdGroupListVo::getBrokenNum).reversed()).collect(Collectors.toList());
                break;
            case 6:
                voList = groupList.stream().sorted(Comparator.comparing(SysProdGroupListVo::getBrokenNum)).collect(Collectors.toList());
                break;
            default:
                voList = groupList;
        }

        voList.forEach(vo -> {
            vo.setAvgCost(vo.getSumCost().divide(new BigDecimal(vo.getNum()), 2, RoundingMode.HALF_EVEN));
        });

        return voList;
    }*/


    @Override
    public void batchDealCheck(SysProdDealDto dto) {
        if (ObjectUtils.isEmpty(dto)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("必填参数为null"));
        }
        List<SysProd> prodList = new ArrayList<>();
        List<Integer> prodIdList = new ArrayList<>();
        Map<Integer, SysProdSaleDto> saleProdMap = new HashMap<>();

        if (ObjectUtils.isEmpty(dto.getType())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("操作类型不明"));
        }

        if (!ObjectUtils.isEmpty(dto.getSaleProdList())) {
            List<SysProdSaleDto> saleProdList = dto.getSaleProdList();
            if (ObjectUtils.isEmpty(saleProdList)) {
                throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意商品"));
            }
            if (dto.getType() == SysProdEvent.TypeSale
                    && saleProdList.stream().anyMatch(a -> ObjectUtils.isEmpty(a.getProdId()) || ObjectUtils.isEmpty(a.getSalePrice()) || ObjectUtils.isEmpty(a.getThirdPlatId()))
            ) {
                throw new BaseException(LanguageConfigService.i18nForMsg("请选择上架平台并填写寄售价格"));
            }

            dto.setProdIdList(saleProdList.stream().map(SysProdSaleDto::getProdId).collect(Collectors.toList()));
            saleProdMap.putAll(saleProdList.stream().collect(Collectors.toMap(SysProdSaleDto::getProdId, a -> a)));
        }
        prodIdList = dto.getProdIdList();
        if (ObjectUtils.isEmpty(prodIdList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意商品"));
        }
//        if (prodIdList.size() > 150) {
//            throw new BaseException(LanguageConfigService.i18nForMsg("单次操作最多150双"));
//        }
        prodList = list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
        prodList.stream().filter(a -> {
            return a.getStatus() != 1;
        }).collect(Collectors.toList()).forEach(item -> {
            throw new BaseException(LanguageConfigService.i18nForMsg("操作失败，商品[") + item.getOneId() + LanguageConfigService.i18nForMsg("]非空闲状态"));
        });
        if (prodList.stream().collect(Collectors.groupingBy(SysProd::getShopId)).keySet().size() != 1) {
            throw new BaseException(LanguageConfigService.i18nForMsg("操作失败，所有商品需归属同一个商家"));
        }
        if ((dto.getType() == SysProdEvent.TypeSend || dto.getType() == SysProdEvent.TypeTransport || dto.getType() == SysProdEvent.TypeSale)
                && iSysProdSwitchItemService.count(Wrappers.<SysProdSwitchItem>lambdaQuery().ne(SysProdSwitchItem::getStatus, 4).in(SysProdSwitchItem::getProdId, prodIdList)) > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("操作失败，存在商品正在转仓中"));
        }

        // 商家进行代发申请的时候判断客户余额
        if (dto.getType() == SysProdEvent.TypeSend && JwtContentHolder.getRoleType() == 5) {
            if (!this.checkBalance(dto.getProdIdList().size())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("操作失败，余额不足"));
            }
        }
        Integer shopId = prodList.get(0).getShopId();
        switch (dto.getType()) {
            // 转运
            case SysProdEvent.TypeTransport:
                // 转运：需要所有商品在同一个仓库才可批量操作。
                if (prodList.stream().collect(Collectors.groupingBy(SysProd::getWareId)).size() != 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("操作失败，所有商品需在同一个仓库"));
                }
                break;
            // 代发
            case SysProdEvent.TypeSend:
                // 代发：需要所有商品在同一个仓库才可批量操作。
                if (prodList.stream().collect(Collectors.groupingBy(SysProd::getWareId)).size() != 1) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("操作失败，所有商品需在同一个仓库"));
                }
                break;
            // 平台内转移
            case SysProdEvent.TypeTransfer:
                if (ObjectUtils.isEmpty(dto.getNewShopId())) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("新归属人不明"));
                }
                break;
            // 套现
            case SysProdEvent.TypeCash:
                break;
            // 寄卖
            case SysProdEvent.TypeSale:
                // 验证寄售许可
                if (iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery().eq(SysProdDeal::getType, SysProdEvent.TypeSale)
                        .eq(SysProdDeal::getStatus, 1).in(SysProdDeal::getProdId, prodIdList)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("存在商品已寄售"));
                }
                List<Integer> platIdList = dto.getSaleProdList().stream().map(SysProdSaleDto::getThirdPlatId).distinct().collect(Collectors.toList());
                if (ObjectUtils.isEmpty(platIdList)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("未选择上架平台"));
                }
                if (iShopUserPlatService.count(Wrappers.<ShopUserPlat>lambdaQuery()
                        .eq(ShopUserPlat::getShopId, shopId)
                        .in(ShopUserPlat::getPlatId, platIdList)) != platIdList.size()) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该商家未拥有对应的寄售权限"));
                }
                if (iSysThirdPlatService.count(Wrappers.<SysThirdPlat>lambdaQuery()
                        .in(SysThirdPlat::getId, platIdList)
                        .like(SysThirdPlat::getName, "touch")) > 0) {
                    if (iShopUserTouchService.count(Wrappers.<ShopUserTouch>lambdaQuery().eq(ShopUserTouch::getShopId, shopId).isNotNull(ShopUserTouch::getTouchUserId)) == 0) {
                        throw new BaseException(LanguageConfigService.i18nForMsg("请先关联touch账号"));
                    }
                }
                if (iSysWareInProdService.count(Wrappers.<SysWareInProd>lambdaQuery()
                        .ne(SysWareInProd::getCheckResult, 1)
                        .in(SysWareInProd::getProdId, prodIdList)) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("瑕疵商品无法申请寄售"));
                }
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("暂不支持此功能"));
        }
    }

    /**
     * 【注意事项】调用 batchDeal 新增前，需要
     * 先调用 batchDealCheck(dto) 对参数进行检查
     */
    @Override
    public String batchDeal(SysProdDealDto dto) {
        List<Integer> prodIdList = dto.getProdIdList();
        List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
        Map<Integer, SysProdSaleDto> saleProdMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(dto.getSaleProdList())) {
            saleProdMap = dto.getSaleProdList().stream().collect(Collectors.toMap(SysProdSaleDto::getProdId, a -> a));
        }

        Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));

        int status;
        int type = dto.getType();
        Integer shopId = prodList.get(0).getShopId();
        String oddNo = iSysCodePoolService.build(type, 1).get(0);
        String description = "";
        Integer relationId = null;

        Date gmtDeal = BaseUtils.getGmtDeal();

        PlatDefaultPriceVo defaultPrice = iSysParamSetService.defaultPrice(type);
        switch (dto.getType()) {
            // 转运
            case SysProdEvent.TypeTransport:
                description = "申请转运";
                status = 10;
                // 生成转运申请
                SysProdTransport transport = new SysProdTransport();
                BeanUtils.copyProperties(dto, transport);

                transport.setWareId(prodList.get(0).getWareId());
                transport.setShopId(shopId);
                transport.setStatus(1);
                transport.setOddNo(oddNo);
                transport.setProdNum(prodList.size());
                transport.setGmtDeal(gmtDeal);
                transport.setDeliveryFee(defaultPrice.getDeliveryFee());
                transport.setPlatFee(defaultPrice.getPlatFee());
                transport.setFreeFee(defaultPrice.getFreeFee());
                transport.insert();

                relationId = transport.getId();

                break;
            // 代发
            case SysProdEvent.TypeSend:
                description = "申请代发";
                status = 9;

                // 生成转运申请
                SysProdTransport send = new SysProdTransport();
                BeanUtils.copyProperties(dto, send);

                send.setWareId(prodList.get(0).getWareId());
                send.setShopId(shopId);
                send.setStatus(1);
                send.setOddNo(oddNo);
                send.setProdNum(prodList.size());
                send.setGmtDeal(gmtDeal);
                send.setDeliveryFee(defaultPrice.getDeliveryFee());
                send.setPlatFee(defaultPrice.getPlatFee());
                send.setFreeFee(defaultPrice.getFreeFee());
                send.insert();

                relationId = send.getId();

                break;
            // 平台内转移
            case SysProdEvent.TypeTransfer:
                description = "申请平台内转移";
                status = 7;

                // 生成转仓申请
                SysProdTransfer transfer = new SysProdTransfer();
                BeanUtils.copyProperties(dto, transfer);

                transfer.setShopId(shopId);
                transfer.setStatus(1);
                transfer.setOddNo(oddNo);
                transfer.setGmtDeal(gmtDeal);
                transfer.setDeliveryFee(defaultPrice.getDeliveryFee());
                transfer.setPlatFee(defaultPrice.getPlatFee());
                transfer.setFreeFee(defaultPrice.getFreeFee());
                transfer.insert();
                relationId = transfer.getId();

                break;
            // 套现
            case SysProdEvent.TypeCash:
                description = "申请套现";
                status = 4;

                // 生成套现申请
                SysProdCash cash = new SysProdCash();
                BeanUtils.copyProperties(dto, cash);

                cash.setShopId(shopId);
                cash.setStatus(1);
                cash.setOddNo(oddNo);
                cash.setGmtDeal(gmtDeal);
                cash.setFreeFee(defaultPrice.getFreeFee());
                cash.insert();
                relationId = cash.getId();

                break;
            // 寄卖
            case SysProdEvent.TypeSale:
                description = "申请寄卖";
                status = 2;
                Boolean touch = false; // 是否为touch平台

                List<Integer> platIdList = dto.getSaleProdList().stream().map(SysProdSaleDto::getThirdPlatId).distinct().collect(Collectors.toList());

                if (iSysThirdPlatService.count(Wrappers.<SysThirdPlat>lambdaQuery()
                        .in(SysThirdPlat::getId, platIdList)
                        .like(SysThirdPlat::getName, "touch")) > 0) {
                    touch = true;
                }

                if (touch) {
                    List<TouchProdAddRequest> request = new ArrayList<>();
                    List<SysWare> wareList = iSysWareService.list();
                    Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
                    for (Integer prodId : prodIdList) {
                        // 添加关联商品
                        TouchProdAddRequest item = new TouchProdAddRequest();
                        SysProd prod = prodMap.get(prodId);
                        if (!ObjectUtils.isEmpty(prod)) {
                            item.setErpProductId(prod.getOneId());
                            item.setErpUserId(BaseUtils.covertString(prod.getShopId()));
                            item.setSku(prod.getSku());
                            item.setSize(prod.getSpec());
                            item.setCost(prod.getCostPrice()); // 会正式推到三方平台，避免价格过低，暂时关闭此项
                            item.setRemark(prod.getSupply());
                            if (!ObjectUtils.isEmpty(prod.getWareId())) {
                                item.setWarehouseId(prod.getWareId() + "");
                                item.setWarehouseName(wareMap.get(item.getWarehouseId()));
                            }

                            request.add(item);
                        }
                    }
                    async.createProduct(request);
                    /*
//                    try {
                    JSONObject createRs = touchUtils.createProduct(request);
                    if (createRs.containsKey("erpProductIdList")) {
                        List<String> oneIdList = createRs.getJSONArray("erpProductIdList").toJavaList(String.class);
                        if (!ObjectUtils.isEmpty(oneIdList)) {
                            List<SysProd> removeList = list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getOneId, oneIdList));
                            if (!ObjectUtils.isEmpty(removeList)) {
                                prodIdList.removeAll(removeList.stream().map(SysProd::getId).collect(Collectors.toList()));
                            }
                            removeList.clear();
                        }
                    }
//                    } catch (BaseException e) {
//                        log.info("touch请求失败：" + e.getMsg());
//                    }*/
                }

                oddNo = null;
                relationId = 0; // 缺省值
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("暂不支持此功能"));
        }
        prodList.clear();

        List<SysProdEvent> eventList = new ArrayList<>();
        List<SysProdDeal> dealList = new ArrayList<>();
        for (Integer prodId : prodIdList) {
            // 添加关联商品
            SysProdDeal deal = new SysProdDeal();
            deal.setProdId(prodId);
            deal.setType(type);
            deal.setRelationId(relationId);

            SysProd prod = prodMap.get(prodId);
            if (!ObjectUtils.isEmpty(prod)) {
                deal.setGmtIn(prod.getGmtIn());
                deal.setShopId(prod.getShopId());
                deal.setWareId(prod.getWareId());
                deal.setPku(prod.getPku());
                deal.setSku(prod.getSku());
                deal.setCostPrice(prod.getCostPrice());
                deal.setSupply(prod.getSupply());
            }

            SysProdSaleDto saleInfo = saleProdMap.get(prodId);
            if (!ObjectUtils.isEmpty(saleInfo)) {
                deal.setCostPrice(saleInfo.getCostPrice());
                deal.setSalePrice(saleInfo.getSalePrice());
                deal.setQuotePrice(saleInfo.getQuotePrice());
                deal.setThirdPlatId(saleInfo.getThirdPlatId());
            }

            dealList.add(deal);

            // 商品事件：代发、转运、套现、寄卖、平台内转移
            SysProdEvent event = new SysProdEvent();
            event.setProdId(prodId);
            event.setShopId(deal.getShopId());
            event.setDescription(description);
            event.setType(type);
            event.setRelationId(relationId);
            if (event.getProdId() > 0) eventList.add(event);
        }

        Map<BigDecimal, List<SysProdDeal>> costMap = dealList.stream()
                .filter(a -> !ObjectUtils.isEmpty(a.getCostPrice()))
                .collect(Collectors.groupingBy(SysProdDeal::getCostPrice));
        costMap.keySet().forEach(costPrice -> {
            // 批量修改成本价
            update(Wrappers.<SysProd>lambdaUpdate()
                    .set(SysProd::getCostPrice, costPrice)
                    .in(SysProd::getId, costMap.get(costPrice).stream().map(SysProdDeal::getProdId).collect(Collectors.toList())));
        });

        iSysProdDealService.insertList(dealList);
        iSysProdEventService.insertList(eventList);

        // 变更商品状态
        update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).set(SysProd::getOddNo, oddNo).set(SysProd::getStatus, status));

        // 生成审核记录
        if (type != SysProdEvent.TypeSale) {
            SysAudit audit = new SysAudit();
            audit.setStatus(1);
            audit.setOddNo(oddNo);
            audit.setShopId(shopId);
            audit.setRelationId(relationId);
            audit.setType(type);
            iSysAuditService.saveSysAudit(audit);

            // search同步更新
            iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                    .set(SysProdSearch::getDelFlag, 0)
                    .set(SysProdSearch::getOddType, type)
                    .set(SysProdSearch::getOddNo, oddNo)
                    .set(SysProdSearch::getStatus, status)
                    .in(SysProdSearch::getProdId, prodIdList)
                    .eq(SysProdSearch::getSearchType, 1));
        } else {
            // 批量绑定事件关联id
            iSysProdEventService.relateSale(prodIdList);
            oddNo = "";

            // search同步更新
            iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                    .set(SysProdSearch::getDelFlag, 0)
                    .set(SysProdSearch::getStatus, status)
                    .in(SysProdSearch::getProdId, prodIdList)
                    .eq(SysProdSearch::getSearchType, 1));
        }

        return oddNo;
    }

    @Override
    public Boolean release(List<Integer> idList, Integer type) {
        if (ObjectUtils.isEmpty(idList)) {
            log.info("SysProdServiceImpl.release(): idList is empty!");

            return false;
        }

        if (ObjectUtils.isEmpty(type)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("无法识别单号类型"));
        }

        switch (type) {
            case SysProdEvent.TypeSend:
                if (!iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate().in(SysProdTransport::getStatus, 1, 3)
                        .in(SysProdTransport::getId, idList).set(SysProdTransport::getStatus, 6))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("申请单状态发生变化"));
                }
                break;
            case SysProdEvent.TypeTransport:
                if (!iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate().in(SysProdTransport::getStatus, 1, 3)
                        .in(SysProdTransport::getId, idList).set(SysProdTransport::getStatus, 6))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("申请单状态发生变化"));
                }
                break;
            case SysProdEvent.TypeCash:
                if (!iSysProdCashService.update(Wrappers.<SysProdCash>lambdaUpdate().in(SysProdCash::getStatus, 1, 3)
                        .in(SysProdCash::getId, idList).set(SysProdCash::getStatus, 5))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("申请单状态发生变化"));
                }
                break;
            case SysProdEvent.TypeSale:
                if (iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery().in(SysProdDeal::getProdId, idList)
                        .eq(SysProdDeal::getType, 6).eq(SysProdDeal::getStatus, 1)) == 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("寄售状态发生变化"));
                }

                // 下架后推送给touch
                List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery()
                        .in(SysProd::getId, idList)
                        .select(SysProd::getId, SysProd::getOneId, SysProd::getStatus));
                touchUtils.offProduct(prodList.stream().map(SysProd::getOneId).collect(Collectors.toList()));
                break;
            case SysProdEvent.TypeTransfer:
                if (!iSysProdTransferService.update(Wrappers.<SysProdTransfer>lambdaUpdate().in(SysProdTransfer::getStatus, 1, 3)
                        .in(SysProdTransfer::getId, idList).set(SysProdTransfer::getStatus, 5))) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("申请单状态发生变化"));
                }
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("无法识别单号类型"));
        }

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .in(type != SysProdEvent.TypeSale, SysProdDeal::getRelationId, idList)
                .in(type == SysProdEvent.TypeSale, SysProdDeal::getProdId, idList)
                .eq(SysProdDeal::getStatus, 1).eq(SysProdDeal::getType, type));
        List<Integer> prodIdList = BaseUtils.initList();
        if (!ObjectUtils.isEmpty(dealList)) {
            prodIdList.addAll(dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList()));
        }

        // 商品事件：撤销操作
        List<SysProdEvent> eventList = new ArrayList<>();
        dealList.forEach(deal -> {
            SysProdEvent event = new SysProdEvent();
            event.setProdId(deal.getProdId());
            event.setShopId(deal.getShopId());
            event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, type)));
            event.setDescription("操作撤销");
            event.setRelationId(deal.getType() == SysProdEvent.TypeSale ? deal.getId() : deal.getRelationId());
            if (event.getProdId() > 0) eventList.add(event);
        });
        iSysProdEventService.insertList(eventList);

        // 更新平台审核状态：撤销
        if (type != SysProdEvent.TypeSale) {
            iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate()
                    .set(SysAudit::getGmtModify, DateTimeUtils.getNow())
                    .set(SysAudit::getStatus, 7)
                    .in(SysAudit::getRelationId, idList));
        }

        // 流程结束：操作取消
        iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                .in(type != SysProdEvent.TypeSale, SysProdDeal::getRelationId, idList)
                .in(type == SysProdEvent.TypeSale, SysProdDeal::getProdId, idList)
                .eq(SysProdDeal::getType, type).eq(SysProdDeal::getStatus, 1)
                .set(SysProdDeal::getGmtModify, DateTimeUtils.getNow())
                .set(SysProdDeal::getStatus, 2));

        update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList)
                .setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

        // search同步更新
        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                .in(SysProdSearch::getProdId, prodIdList)
                .eq(SysProdSearch::getSearchType, 1));

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean pay(Integer id, Integer type) {
        if (ObjectUtils.isEmpty(type)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("无法识别单号类型"));
        }

        Calendar c = Calendar.getInstance();
        c.setTime(DateTimeUtils.getNow());
        Date now = c.getTime();
        // 支付金额
        BigDecimal payAmount = null;
        Integer shopId = null;
        Integer newShopId = null;
        JSONObject json = new JSONObject();
        List<SysBill> billList = new ArrayList<>();
        switch (type) {
            case SysProdEvent.TypeSend:
                SysProdTransportVo send = iSysProdTransportService.getDetail(id, null);
                if (send.getStatus() != 3) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该状态下不可支付"));
                }
                if (JwtContentHolder.getRoleType() == 5 && now.compareTo(send.getGmtPayValid()) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("支付超时，撤销申请"));
                }

                c.setTime(send.getGmtPayValid());
                c.add(Calendar.DATE, -1);
                json.put("gmtAudit", c.getTime());
                json.put("totalFee", send.getTotalFee());
                json.put("wareFee", send.getWareFee());
                json.put("oddNo", send.getOddNo());

                json.put("receiveAddress", send.getReceiveAddress());
                json.put("receivePhone", send.getReceivePhone());
                json.put("receiveName", send.getReceiveName());
                json.put("labelImg", send.getLabelImg());

                payAmount = send.getTotalFee();
                shopId = send.getShopId();
                iSysProdDealService.saveWareInfo(id, send.getProdList());

                buildPlatBill1(billList, send);
                break;
            case SysProdEvent.TypeTransport:
                SysProdTransportVo transport = iSysProdTransportService.getDetail(id, null);
                if (transport.getStatus() != 3) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该状态下不可支付"));
                }
                if (JwtContentHolder.getRoleType() == 5 && now.compareTo(transport.getGmtPayValid()) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("支付超时，撤销申请"));
                }

                c.setTime(transport.getGmtPayValid());
                c.add(Calendar.DATE, -1);
                json.put("gmtAudit", c.getTime());

                json.put("totalFee", transport.getTotalFee());
                json.put("wareFee", transport.getWareFee());
                json.put("oddNo", transport.getOddNo());

                json.put("receiveAddress", transport.getReceiveAddress());
                json.put("receivePhone", transport.getReceivePhone());
                json.put("receiveName", transport.getReceiveName());
                json.put("labelImg", transport.getLabelImg());

                payAmount = transport.getTotalFee();
                shopId = transport.getShopId();
                iSysProdDealService.saveWareInfo(id, transport.getProdList());

                buildPlatBill1(billList, transport);
                break;
            case SysProdEvent.TypeCash:
                SysProdCashVo cash = iSysProdCashService.getDetail(id, null);
                if (cash.getStatus() != 3) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该状态下不可支付"));
                }
                if (JwtContentHolder.getRoleType() == 5 && now.compareTo(cash.getGmtPayValid()) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("支付超时，撤销申请"));
                }
                Assert.notNull(cash.getNewShopId(), LanguageConfigService.i18nForMsg("查询不到鞋子的归属者"));

                c.setTime(cash.getGmtPayValid());
                c.add(Calendar.DATE, -1);
                json.put("gmtAudit", c.getTime());

                json.put("totalFee", cash.getTotalFee());
                json.put("wareFee", cash.getWareFee());
                json.put("oddNo", cash.getOddNo());

                shopId = cash.getShopId();
                newShopId = cash.getNewShopId();
                payAmount = cash.getTotalFee().negate(); // 扣除平台费用后加钱
                iSysProdDealService.saveWareInfo(id, cash.getProdList());

                buildPlatBill2(billList, cash);
                break;
            case SysProdEvent.TypeTransfer:
                SysProdTransferVo transfer = iSysProdTransferService.getDetail(id, null);
                if (transfer.getStatus() != 3) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("该状态下不可支付"));
                }
                if (JwtContentHolder.getRoleType() == 5 && now.compareTo(transfer.getGmtPayValid()) > 0) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("支付超时，撤销申请"));
                }

                json.put("totalFee", transfer.getTotalFee());
                json.put("wareFee", transfer.getWareFee());
                json.put("newShopId", transfer.getNewShopId());
                json.put("oddNo", transfer.getOddNo());

                payAmount = transfer.getTotalFee();
                shopId = transfer.getShopId();
                iSysProdDealService.saveWareInfo(id, transfer.getProdList());

                buildPlatBill3(billList, transfer);
                break;
            default:
                throw new BaseException(LanguageConfigService.i18nForMsg("无法识别单号类型"));
        }

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery().eq(SysProdDeal::getStatus, 1).eq(SysProdDeal::getType, type).eq(SysProdDeal::getRelationId, id));
        List<Integer> prodIdList = dealList.stream().map(SysProdDeal::getProdId).collect(Collectors.toList());
        List<SysProdEvent> eventList = new ArrayList<>();

        if (!ObjectUtils.isEmpty(payAmount)) {
            // 找不到 对应的 prodId List
            if (dealList.isEmpty()) {
                throw new BaseException(LanguageConfigService.i18nForMsg("找不到 对应的处于处理中状态的Sys Prod Deal数据：relation id: ") + id + ", type: " + type);
            }

            // 扣款
            SysMoney money = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getType, 5).eq(SysMoney::getUserId, shopId));
            // 套现不校验钱包余额 type != TypeCash
            if (money.getMoney().compareTo(payAmount) < 0 && type != SysProdEvent.TypeCash) {
                throw new BaseException(LanguageConfigService.i18nForMsg("钱包余额不足"));
            }

            // 套现对新的商家进行扣款 newShopId
            if (type == SysProdEvent.TypeCash) {
                if (!iSysMoneyService.change(5, newShopId, payAmount)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("套现扣款失败"));
                }
            }

            if (!iSysMoneyService.change(5, shopId, payAmount.negate())) {
                throw new BaseException(LanguageConfigService.i18nForMsg("扣款失败"));
            }


            // 生成流水：流程单支付
            SysBill bill = new SysBill();
            bill.setStatus(2);
            bill.setUserId(money.getUserId());
            bill.setUserType(money.getType());
            bill.setIeType(type == SysProdEvent.TypeCash ? 1 : -1);
            bill.setOutTradeNo(BaseUtils.getOutTradeNo(type, 4));
            bill.setPayType(4);
            bill.setTotalFee(payAmount.abs());
            bill.setRelationType(type);
            bill.setRelationId(id);
            bill.setAttach("oddNo=" + json.getString("oddNo") + "&");
            billList.add(bill);

            // 商品事件：支付
            for (Integer prodId : prodIdList) {
                SysProdEvent event = new SysProdEvent();
                event.setProdId(prodId);
                event.setShopId(shopId);
                event.setDescription("申请单支付 $" + payAmount);
                event.setRelationId(id);
                if (event.getProdId() > 0) eventList.add(event);
            }
        }

        // 生成出库单
        if (type == SysProdEvent.TypeSend || type == SysProdEvent.TypeTransport) {
            SysWareOut outDto = new SysWareOut();
            outDto.setOddNo(json.getString("oddNo"));
            outDto.setRelationId(id);
            outDto.setType(type);

            Map<Integer, Integer> dealIdMap = dealList.stream().collect(Collectors.toMap(SysProdDeal::getProdId, SysProdDeal::getId));

            Map<Integer, Integer> prodDealMap = new HashMap<>();
            prodIdList.forEach(prodId -> {
                if (prodId > 0) {
                    prodDealMap.put(prodId, dealIdMap.get(prodId));
                }
            });
            outDto.setProdDealMap(prodDealMap);

            SysProd prod = getById(prodIdList.get(prodIdList.size() - 1));
            outDto.setGmtCreate(json.getDate("gmtAudit"));
            outDto.setWareId(prod.getWareId());
            outDto.setShopId(prod.getShopId());

            outDto.setReceiveName(json.getString("receiveName"));
            outDto.setReceivePhone(json.getString("receivePhone"));
            outDto.setReceiveAddress(json.getString("receiveAddress"));
            outDto.setLabelImg(json.getString("labelImg"));
            iSysWareOutService.saveSysWareOut(outDto);
        }

        // 更新入库商品信息
        List<SysProdSearch> searchList = new ArrayList<>();
        Map<Integer, String> packLogMap = new HashMap<>();
        Map<Integer, SysWareInProd> inProdMap = new HashMap<>();
        Map<Integer, Integer> prodShelvesMap = new HashMap<>();
        if (type == SysProdEvent.TypeCash || type == SysProdEvent.TypeTransfer) {
            // 更新平台审核状态：已完成
            iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate().set(SysAudit::getGmtModify, DateTimeUtils.getNow()).set(SysAudit::getStatus, 4).eq(SysAudit::getOddNo, json.getString("oddNo")));

            List<SysWareInProd> inProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery().in(SysWareInProd::getProdId, prodIdList));
            inProdMap.putAll(inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a)));
            List<Integer> packIdList = BaseUtils.initList();
            packIdList.addAll(inProdList.stream().map(SysWareInProd::getPackId).collect(Collectors.toList()));
            inProdList.clear();
            List<ShopPack> packList = iShopPackService.list(Wrappers.<ShopPack>lambdaQuery().in(ShopPack::getId, packIdList));
            packLogMap.putAll(packList.stream().collect(Collectors.toMap(ShopPack::getId, ShopPack::getLogNo)));
            packList.clear();

            List<SysWareShelvesProd> shelvesProds = iSysWareShelvesProdService.list(Wrappers.<SysWareShelvesProd>lambdaQuery().in(SysWareShelvesProd::getProdId, prodIdList));
            prodShelvesMap.putAll(shelvesProds.stream().collect(Collectors.toMap(SysWareShelvesProd::getProdId, SysWareShelvesProd::getShelvesId
                    // 当出现多个相同的prodID时，取最新的一条
                    , (k1, k2) -> k2
            )));
            shelvesProds.clear();
        } else {
            iSysWareInProdService.update(Wrappers.<SysWareInProd>lambdaUpdate().set(SysWareInProd::getGmtPay, now).in(SysWareInProd::getProdId, prodIdList));

            // 更新平台审核状态：已支付待出库
            iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate().set(SysAudit::getGmtModify, DateTimeUtils.getNow()).set(SysAudit::getStatus, 5).eq(SysAudit::getOddNo, json.getString("oddNo")));
        }

        List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery().in(SysProd::getId, prodIdList));
        Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
        List<String> oneIdList = prodList.stream().filter(a -> a.getStatus() == 2).map(SysProd::getOneId).collect(Collectors.toList());
        prodList.clear();

        // 变更单号状态
        switch (type) {
            case SysProdEvent.TypeTransport:
                SysProdTransport transport = new SysProdTransport();
                transport.setId(id);
                transport.setGmtPay(now);
                transport.setWareFee(json.getBigDecimal("wareFee"));
                transport.setTotalFee(json.getBigDecimal("totalFee"));
                transport.setStatus(4);
                transport.updateById();
                break;
            case SysProdEvent.TypeSend:
                SysProdTransport send = new SysProdTransport();
                send.setId(id);
                send.setGmtPay(now);
                send.setWareFee(json.getBigDecimal("wareFee"));
                send.setTotalFee(json.getBigDecimal("totalFee"));
                send.setStatus(4);
                send.updateById();
                break;
            case SysProdEvent.TypeCash:
                SysProdCash cash = new SysProdCash();
                cash.setId(id);
                cash.setGmtPay(now);
                cash.setWareFee(json.getBigDecimal("wareFee"));
                cash.setTotalFee(json.getBigDecimal("totalFee"));
                cash.setStatus(4);
                cash.updateById();

                ShopUser shop = iShopUserService.getById(shopId);
                String supply = shop.getUid() + "-" + json.getString("oddNo"); // 货源规则：识别码+平台出库单号

                // 商品事件：套现
                for (Integer prodId : prodIdList) {
                    SysProdEvent event = new SysProdEvent();
                    event.setProdId(prodId);
                    event.setShopId(shopId);
                    event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, type)));
                    event.setDescription("套现");
                    event.setRelationId(id);
                    if (event.getProdId() > 0) eventList.add(event);

                    SysProdEvent event2 = new SysProdEvent();
                    event2.setProdId(prodId);
                    event2.setShopId(1);
                    event2.setType(SysProdEvent.TypeOwnChange);
                    event2.setDescription("归属者变更");
                    event2.setRelationId(id);
                    if (event2.getProdId() > 0) eventList.add(event2);
                }

                // 转移归属者，流程结束
                update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null, shop_id = " + newShopId));

                // 流程结束：套现 完成
                dealList.forEach(deal -> {
                    deal.setGmtModify(now);
                    deal.setGmtOut(now);
                    deal.setStatus(3);

                    SysProd prod = prodMap.get(deal.getProdId());
                    if (!ObjectUtils.isEmpty(prod)) {
                        deal.setSupply(prod.getSupply());
                        deal.setCostPrice(prod.getCostPrice());
                        deal.setGmtIn(prod.getGmtIn());
                    }

                    // search同步更新
                    iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                            .set(SysProdSearch::getDelFlag, 0)
                            .set(SysProdSearch::getSearchType, 2)
                            .set(SysProdSearch::getDealId, deal.getId())
                            .set(SysProdSearch::getOutNo, json.getString("oddNo"))
                            .set(SysProdSearch::getGmtOut, now)
                            .set(SysProdSearch::getStatus, 6)
                            .eq(SysProdSearch::getProdId, deal.getProdId())
                            .eq(SysProdSearch::getSearchType, 1));

                    deal.updateById();
                });

                // 变更商品的成本价&货源&入库时间
                update(Wrappers.<SysProd>lambdaUpdate()
                        .set(SysProd::getGmtIn, now)
                        .set(SysProd::getSupply, supply)
                        .in(SysProd::getId, prodIdList));

                // 修改货源后推送给touch
                touchUtils.updateRemark(supply, oneIdList);

                Map<BigDecimal, List<SysProdDeal>> costMap = dealList.stream().filter(a -> {
                    return !ObjectUtils.isEmpty(a.getSalePrice());
                }).collect(Collectors.groupingBy(SysProdDeal::getSalePrice));
                Map<Integer, BigDecimal> prodCostMap = new HashMap<>();
                costMap.keySet().forEach(costPrice -> {
                    List<Integer> prodIdList2 = new ArrayList<>();

                    costMap.get(costPrice).forEach(prod -> {
                        prodIdList2.add(prod.getProdId());
                        prodCostMap.put(prod.getProdId(), costPrice);
                    });

                    update(Wrappers.<SysProd>lambdaUpdate()
                            .set(SysProd::getCostPrice, costPrice)
                            .in(SysProd::getId, prodIdList2));


                    // 修改成本后推送给touch
                    touchUtils.updateCost(costPrice, oneIdList);
                });

                // search新增记录
                ShopUser newShop = iShopUserService.getById(newShopId);
                prodIdList.forEach(prodId -> {
                    SysProd prod = prodMap.get(prodId);

                    SysProdSearch search = new SysProdSearch();
                    search.setSearchType(1);
                    search.setProdId(prod.getId());
                    search.setOneId(prod.getOneId());
                    search.setCostPrice(prodCostMap.get(prodId));
                    search.setSupply(supply);
                    search.setRemarks(prod.getRemarks());
                    search.setSku(prod.getSku());
                    search.setPku(prod.getPku());
                    search.setSpec(BaseUtils.dealSizeStr(prod.getSpec()));
                    search.setBrand(prod.getBrand());
                    search.setStatus(1);

                    if (!ObjectUtils.isEmpty(newShop)) {
                        search.setShopId(newShop.getId());
                        search.setShopUid(newShop.getUid());
                        search.setShopName(newShop.getRealname());
                    }

                    search.setWareId(prod.getWareId());
                    search.setShelvesId(prodShelvesMap.get(prodId));
                    search.setInBatchNo(json.getString("oddNo"));
                    SysWareInProd inProd = inProdMap.get(prodId);
                    if (!ObjectUtils.isEmpty(inProd)) {
                        search.setCheckResult(inProd.getCheckResult());
                        search.setInLogNo(packLogMap.get(inProd.getPackId()));
                    }
                    search.setGmtIn(now);
                    searchList.add(search);
                });
                break;
            case SysProdEvent.TypeTransfer:
                SysProdTransfer transfer = new SysProdTransfer();
                transfer.setId(id);
                transfer.setGmtPay(now);
                transfer.setWareFee(json.getBigDecimal("wareFee"));
                transfer.setTotalFee(json.getBigDecimal("totalFee"));
                transfer.setStatus(4);
                transfer.updateById();

                // 商品事件：转移归属者
                for (Integer prodId : prodIdList) {
                    SysProdEvent event = new SysProdEvent();
                    event.setProdId(prodId);
                    event.setShopId(shopId);
                    event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, type)));
                    event.setDescription("转移归属者");
                    event.setRelationId(id);
                    if (event.getProdId() > 0) eventList.add(event);

                    SysProdEvent event2 = new SysProdEvent();
                    event2.setProdId(prodId);
                    event2.setShopId(json.getInteger("newShopId"));
                    event2.setType(SysProdEvent.TypeOwnChange);
                    event2.setDescription("归属者变更");
                    event2.setRelationId(id);
                    if (event2.getProdId() > 0) eventList.add(event2);
                }

                // 转移归属者，流程结束
                update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null, shop_id = " + json.getInteger("newShopId")));

                // 流程结束：仓库内转移 完成
                dealList.forEach(deal -> {
                    deal.setGmtModify(now);
                    deal.setGmtOut(now);
                    deal.setStatus(3);

                    SysProd prod = prodMap.get(deal.getProdId());
                    if (!ObjectUtils.isEmpty(prod)) {
                        deal.setSupply(prod.getSupply());
                        deal.setCostPrice(prod.getCostPrice());
                        deal.setGmtIn(prod.getGmtIn());
                    }

                    // search同步更新
                    iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                            .set(SysProdSearch::getDelFlag, 0)
                            .set(SysProdSearch::getSearchType, 2)
                            .set(SysProdSearch::getDealId, deal.getId())
                            .set(SysProdSearch::getOutNo, json.getString("oddNo"))
                            .set(SysProdSearch::getGmtOut, now)
                            .set(SysProdSearch::getStatus, 6)
                            .in(SysProdSearch::getProdId, prodIdList)
                            .eq(SysProdSearch::getSearchType, 1));

                    deal.updateById();
                });

                shop = iShopUserService.getById(shopId);
                supply = shop.getUid() + "-" + json.getString("oddNo"); // 货源规则：识别码+平台出库单号

                // 变更商品的货源&入库时间
                update(Wrappers.<SysProd>lambdaUpdate()
                        .set(SysProd::getGmtIn, now)
                        .set(SysProd::getSupply, supply)
                        .in(SysProd::getId, prodIdList));

                // 修改货源后推送给touch
                touchUtils.updateRemark(shop.getUid(), oneIdList);

                // search新增记录
                newShop = iShopUserService.getById(json.getInteger("newShopId"));
                prodMap.keySet().forEach(prodId -> {
                    SysProd prod = prodMap.get(prodId);

                    SysProdSearch search = new SysProdSearch();
                    search.setGmtCreate(prod.getGmtCreate()); // Set GmtCreate as original create date for in warehouse time caculation
                    search.setSearchType(1);
                    search.setProdId(prod.getId());
                    search.setOneId(prod.getOneId());
                    search.setCostPrice(prod.getCostPrice());
                    search.setSupply(supply);
                    search.setRemarks(prod.getRemarks());
                    search.setSku(prod.getSku());
                    search.setSpec(BaseUtils.dealSizeStr(prod.getSpec()));
                    search.setBrand(prod.getBrand());
                    search.setStatus(1);

                    if (!ObjectUtils.isEmpty(newShop)) {
                        search.setShopId(newShop.getId());
                        search.setShopUid(newShop.getUid());
                        search.setShopName(newShop.getRealname());
                    }

                    search.setWareId(prod.getWareId());
                    search.setShelvesId(prodShelvesMap.get(prodId));
                    search.setInBatchNo(json.getString("oddNo"));
                    SysWareInProd inProd = inProdMap.get(prodId);
                    if (!ObjectUtils.isEmpty(inProd)) {
                        search.setCheckResult(inProd.getCheckResult());
                        search.setInLogNo(packLogMap.get(inProd.getPackId()));
                    }
                    search.setGmtIn(prod.getGmtIn()); // Set GmtIn as original GmtIn date for in warehouse time caculation
                    searchList.add(search);
                });
                break;
        }

        billList.forEach(bill -> {
            iSysBillService.saveSysBill(bill);
        });
        iSysProdEventService.insertList(eventList);
        iSysProdSearchService.insertList(searchList);
        return true;
    }

    private void buildPlatBill3(List<SysBill> billList, SysProdTransferVo transfer) {
        SysBill base = new SysBill();
        base.setStatus(2);
        base.setUserId(0);
        base.setUserType(1);
        base.setIeType(1);
        base.setPayType(4);
        base.setRelationId(transfer.getId());

        SysBill ware = new SysBill();
        BeanUtils.copyProperties(base, ware);
        ware.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatWare, 4));
        ware.setTotalFee(transfer.getWareFee());
        ware.setRelationType(SysBill.TypePlatWare);
        billList.add(ware);

        SysBill delivery = new SysBill();
        BeanUtils.copyProperties(base, delivery);
        delivery.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatDelivery, 4));
        delivery.setTotalFee(transfer.getDeliveryFee());
        delivery.setRelationType(SysBill.TypePlatDelivery);
        billList.add(delivery);

        SysBill serve = new SysBill();
        BeanUtils.copyProperties(base, serve);
        serve.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatServe, 4));
        serve.setTotalFee(transfer.getPlatFee().multiply(new BigDecimal(iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery().eq(SysProdDeal::getRelationId, transfer.getId()).eq(SysProdDeal::getType, SysProdEvent.TypeTransfer)))));
        serve.setRelationType(SysBill.TypePlatServe);
        billList.add(serve);

        SysBill free = new SysBill();
        BeanUtils.copyProperties(base, free);
        free.setIeType(-1);
        free.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatFree, 4));
        free.setTotalFee(transfer.getFreeFee());
        free.setRelationType(SysBill.TypePlatFree);
        billList.add(free);
    }

    private void buildPlatBill2(List<SysBill> billList, SysProdCashVo cash) {
        SysBill base = new SysBill();
        base.setStatus(2);
        base.setUserId(0);
        base.setUserType(1);
        base.setPayType(4);
        base.setRelationId(cash.getId());

        SysBill ware = new SysBill();
        BeanUtils.copyProperties(base, ware);
        ware.setIeType(1);
        ware.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatWare, 4));
        ware.setTotalFee(cash.getWareFee());
        ware.setRelationType(SysBill.TypePlatWare);
        billList.add(ware);

        SysBill buy = new SysBill();
        BeanUtils.copyProperties(base, buy);
        buy.setUserId(cash.getNewShopId());
        buy.setIeType(-1);
        buy.setUserType(5);
        buy.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatCash, 4));
        buy.setTotalFee(cash.getSaleTotalFee());
        buy.setRelationType(SysBill.TypePlatCash);
        buy.setAttach("oddNo=" + cash.getOddNo() + "&");
        billList.add(buy);
    }

    private void buildPlatBill1(List<SysBill> billList, SysProdTransportVo transport) {
        SysBill base = new SysBill();
        base.setStatus(2);
        base.setUserId(0);
        base.setUserType(1);
        base.setIeType(1);
        base.setPayType(4);
        base.setRelationId(transport.getId());

        SysBill ware = new SysBill();
        BeanUtils.copyProperties(base, ware);
        ware.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatWare, 4));
        ware.setTotalFee(transport.getWareFee());
        ware.setRelationType(SysBill.TypePlatWare);
        billList.add(ware);

        SysBill delivery = new SysBill();
        BeanUtils.copyProperties(base, delivery);
        delivery.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatDelivery, 4));
        delivery.setTotalFee(transport.getDeliveryFee());
        delivery.setRelationType(SysBill.TypePlatDelivery);
        billList.add(delivery);

        SysBill serve = new SysBill();
        BeanUtils.copyProperties(base, serve);
        serve.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatServe, 4));
        serve.setTotalFee(transport.getPlatFee().multiply(new BigDecimal(iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery().eq(SysProdDeal::getRelationId, transport.getId()).eq(SysProdDeal::getType, transport.getType())))));
        serve.setRelationType(SysBill.TypePlatServe);
        billList.add(serve);

        SysBill free = new SysBill();
        BeanUtils.copyProperties(base, free);
        free.setIeType(-1);
        free.setOutTradeNo(BaseUtils.getOutTradeNo(SysBill.TypePlatFree, 4));
        free.setTotalFee(transport.getFreeFee());
        free.setRelationType(SysBill.TypePlatFree);
        billList.add(free);
    }


    @Override
    public Boolean endEvent(Integer id, Integer type) {
        List<Integer> prodIdList = iSysProdDealService.getProdIdList(type, id);
        List<SysProdEvent> eventList = new ArrayList<>();

        Integer saleProdId = id.intValue();
        Integer shopId = null;
        Boolean rs = false;
        switch (type) {
            case SysProdEvent.TypeSend:
                SysProdTransport send = iSysProdTransportService.getById(id);
                shopId = send.getShopId();
                rs = iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate().eq(SysProdTransport::getId, id).set(SysProdTransport::getStatus, 6));
                break;
            case SysProdEvent.TypeTransport:
                SysProdTransport transport = iSysProdTransportService.getById(id);
                shopId = transport.getShopId();
                rs = iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate().eq(SysProdTransport::getId, id).set(SysProdTransport::getStatus, 6));
                break;
            case SysProdEvent.TypeTransfer:
                SysProdTransfer transfer = iSysProdTransferService.getById(id);
                shopId = transfer.getShopId();
                rs = iSysProdTransferService.update(Wrappers.<SysProdTransfer>lambdaUpdate().eq(SysProdTransfer::getId, id).set(SysProdTransfer::getStatus, 5));
                break;
            case SysProdEvent.TypeSale:
                throw new BaseException(LanguageConfigService.i18nForMsg("寄售不走此通道"));
//                SysProd prod = getById(id);
//                shopId = prod.getShopId();
//                id = 0;
//                rs = true;
//                break;
            case SysProdEvent.TypeCash:
                SysProdCash cash = iSysProdCashService.getById(id);
                shopId = cash.getShopId();
                rs = iSysProdCashService.update(Wrappers.<SysProdCash>lambdaUpdate().eq(SysProdCash::getId, id).set(SysProdCash::getStatus, 5));
                break;
        }

        if (!rs) {
            throw new BaseException(LanguageConfigService.i18nForMsg("撤回失败，状态不同步"));
        }

        // 商品事件：流程终止
        for (Integer prodId : prodIdList) {
            SysProdEvent event = new SysProdEvent();
            event.setProdId(prodId);
            event.setShopId(shopId);
            event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, type)));
            event.setDescription("流程终止");
            event.setRelationId(id);
            if (event.getProdId() > 0) eventList.add(event);
        }

        // 流程结束：流程终止
        if (type == SysProdEvent.TypeSale) {
            iSysProdDealService.remove(Wrappers.<SysProdDeal>lambdaQuery().eq(SysProdDeal::getProdId, saleProdId).eq(SysProdDeal::getType, type).eq(SysProdDeal::getStatus, 1));
        } else {
            iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate().eq(SysProdDeal::getRelationId, id).eq(SysProdDeal::getType, type).eq(SysProdDeal::getStatus, 1).set(SysProdDeal::getGmtModify, DateTimeUtils.getNow()).set(SysProdDeal::getStatus, 2));

            // 更新平台审核状态：中止
            iSysAuditService.update(Wrappers.<SysAudit>lambdaUpdate().set(SysAudit::getGmtModify, DateTimeUtils.getNow()).set(SysAudit::getStatus, 7).eq(SysAudit::getRelationId, id));
        }

        iSysProdEventService.insertList(eventList);

        // 终止后释放商品
        update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList)
                .setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

        // search同步更新
        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                .in(SysProdSearch::getProdId, prodIdList)
                .eq(SysProdSearch::getSearchType, 1));

        // 清理出库信息
        iSysWareOutProdService.remove(Wrappers.<SysWareOutProd>lambdaQuery()
                .in(SysWareOutProd::getProdId, prodIdList));
        iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .in(SysWareOutBatchProd::getProdId, prodIdList));
        iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                .in(SysWareOut::getRelationId, id)
                .eq(SysWareOut::getType, type));

        return true;
    }

    @Override
    public List<SysSkuRankListVo> rankList(int sortType, Integer shopId) {
        List<SysSkuRankListVo> voList = new ArrayList<>();
        int rankNum = 10; // 只展示前10的sku

        QueryWrapper<SysProd> prodQw = new QueryWrapper();
        prodQw.select("count(0) id, sku");
        prodQw.eq("del_flag", 0);
        prodQw.isNotNull("shop_id");
        prodQw.eq(!ObjectUtils.isEmpty(shopId), "shop_id", shopId);
        prodQw.groupBy("sku");
        List<SysProd> prodList = list(prodQw);

        List<SysSku> skuList = iSysSkuService.list(Wrappers.<SysSku>lambdaQuery().orderByAsc(SysSku::getHotRankNum));
        Map<String, SysSku> skuMap = skuList.stream().collect(Collectors.toMap(SysSku::getSku, a -> a));

        if (!ObjectUtils.isEmpty(prodList)) {
            prodList.stream().sorted(Comparator.comparing(SysProd::getId).reversed()).collect(Collectors.toList()).forEach(prod -> {
                if (voList.size() >= rankNum) {
                    return;
                }
                SysSkuRankListVo vo = new SysSkuRankListVo();
                vo.setSku(prod.getSku());
                vo.setStockNum(prod.getId());
                voList.add(vo);
            });
        }

        // 补空
        for (String s : skuMap.keySet()) {
            if (voList.size() >= rankNum) {
                break;
            }
            SysSkuRankListVo vo = new SysSkuRankListVo();
            SysSku sku = skuMap.get(s);
            vo.setId(sku.getId());
            vo.setSku(sku.getSku());
            vo.setHotRankNum(sku.getHotRankNum());
            vo.setImg(sku.getImg());
            vo.setSaleNum(0);
            vo.setStockNum(0);
            voList.add(vo);
        }

        List<String> skuStrList = voList.stream().map(SysSkuRankListVo::getSku).collect(Collectors.toList());

        QueryWrapper dealQw = new QueryWrapper();
        dealQw.select("count(0) type, sku");
        dealQw.eq("type", SysProdEvent.TypeSale);
        dealQw.in("`status`", 1, 3);
        dealQw.groupBy("sku");
        dealQw.eq(!ObjectUtils.isEmpty(shopId), "shop_id", shopId);
        dealQw.in("sku", skuStrList);
        List<SysProdDeal> dealList = iSysProdDealService.list(dealQw);
        Map<String, Integer> saleMap = dealList.stream().collect(Collectors.toMap(SysProdDeal::getSku, SysProdDeal::getType));

        voList.forEach(vo -> {
            SysSku sku = skuMap.get(vo.getSku());
            if (!ObjectUtils.isEmpty(sku)) {
                vo.setImg(sku.getImg());
                vo.setHotRankNum(sku.getHotRankNum());
                vo.setId(sku.getId());
                skuMap.remove(vo.getSku());
            }

            Integer saleNum = saleMap.get(vo.getSku());
            vo.setSaleNum(ObjectUtils.isEmpty(saleNum) ? 0 : saleNum);
        });

        return voList;
    }

    @Override
    public BigDecimal sumCost(SysProdPageDto dto) {
        LambdaQueryWrapper<SysProdSearch> search = buildQw(dto);
        return iSysProdSearchService.sumCost(search);
    }

    @Override
    public List<SysProd> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Override
    public void buildSearch() {
        iSysProdSearchService.remove(Wrappers.<SysProdSearch>lambdaQuery());

        List<SysProdSearch> dataList = new ArrayList<>();

        List<SysProd> prodList = list();
        Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
        prodList.clear();

        List<SysWareShelvesProd> shelvesProdList = iSysWareShelvesProdService.list();
        Map<Integer, Integer> prodShelvesMap = shelvesProdList.stream().collect(Collectors.toMap(SysWareShelvesProd::getProdId, SysWareShelvesProd::getShelvesId
                // 当出现多个相同的prodID时，取最新的一条
                , (k1, k2) -> k2
        ));
        shelvesProdList.clear();

        List<SysWareInProd> inProdList = iSysWareInProdService.list();
        Map<Integer, SysWareInProd> inProdMap = inProdList.stream().collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));
        inProdList.clear();

        List<SysWareIn> inList = iSysWareInService.list();
        Map<Integer, SysWareIn> inMap = inList.stream().collect(Collectors.toMap(SysWareIn::getId, a -> a));
        inList.clear();

        List<ShopUser> shopList = iShopUserService.list();
        Map<Integer, ShopUser> shopMap = shopList.stream().collect(Collectors.toMap(ShopUser::getId, a -> a));
        shopList.clear();

        List<ShopPack> packList = iShopPackService.list();
        Map<Integer, ShopPack> packMap = packList.stream().collect(Collectors.toMap(ShopPack::getId, a -> a));
        packList.clear();

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery().ne(SysProdDeal::getStatus, 2));
        Map<Integer, List<SysProdDeal>> dealGroup = dealList.stream().collect(Collectors.groupingBy(SysProdDeal::getProdId));
        dealList.clear();

        List<SysProdSale> saleList = iSysProdSaleService.list();
        Map<Integer, SysProdSale> saleMap = saleList.stream().collect(Collectors.toMap(SysProdSale::getId, a -> a));
        saleList.clear();

        List<SysWareOutBatchProd> outBatchProdList = iSysWareOutBatchProdService.list(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .eq(SysWareOutBatchProd::getStatus, 4));
        Map<Integer, SysWareOutBatchProd> outBatchProdMap = outBatchProdList.stream().collect(Collectors.toMap(SysWareOutBatchProd::getProdId, a -> a));
        outBatchProdList.clear();

        List<SysWareOut> outList = iSysWareOutService.list();
        Map<Integer, SysWareOut> outMap = outList.stream().collect(Collectors.toMap(SysWareOut::getId, a -> a));
        outList.clear();

        List<SysWareOutProd> outProdList = iSysWareOutProdService.list();
        Map<Integer, SysWareOutProd> outProdMap = outProdList.stream().collect(Collectors.toMap(SysWareOutProd::getDealId, a -> a));
        outProdList.clear();

        Map<Integer, Map<Integer, SysAudit>> auditTree = new HashMap<>();
        List<SysAudit> auditList = iSysAuditService.list();
        auditList.forEach(audit -> {
            Map<Integer, SysAudit> auditMap = auditTree.get(audit.getType());
            if (ObjectUtils.isEmpty(auditMap)) {
                auditMap = new HashMap<>();
                auditTree.put(audit.getType(), auditMap);
            }
            auditMap.put(audit.getRelationId(), audit);
        });
        auditList.clear();

        prodMap.keySet().forEach(prodId -> {
            SysProdSearch data = new SysProdSearch();
            SysProd prod = prodMap.get(prodId);

            // 通用
            data.setProdId(prodId);
            data.setOneId(prod.getOneId());
            data.setSku(prod.getSku());
            data.setPku(prod.getPku());
            data.setSpec(BaseUtils.dealSizeStr(prod.getSpec()));
            data.setBrand(prod.getBrand());
            data.setRemarks(prod.getRemarks());

            // 1.历史商家
            shopHistory(dataList, data, prod, prodShelvesMap, inProdMap, inMap, shopMap, packMap, dealGroup, saleMap, auditTree);

            // 2.当前商家
            shopNow(dataList, data, prod, prodShelvesMap, inProdMap, inMap, shopMap, packMap, dealGroup, saleMap, outBatchProdMap, outProdMap, outMap, auditTree);
        });
        iSysProdSearchService.insertList(dataList);
    }

    @Override
    public List<String> fixProd(List<String> oneIdList) {
        if (ObjectUtils.isEmpty(oneIdList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("empty list of Erp product"));
        }

        List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery()
                .in(SysProd::getOneId, oneIdList));

        List<String> failList = prodList.stream().filter(a -> a.getStatus() != 1).map(SysProd::getOneId).collect(Collectors.toList());
        Map<Integer, SysProd> prodMap = prodList.stream().filter(a -> a.getStatus() == 1).collect(Collectors.toMap(SysProd::getId, a -> a));
        List<Integer> prodIdList = prodList.stream().filter(a -> a.getStatus() == 1).map(SysProd::getId).collect(Collectors.toList());
        prodList.clear();

        SysThirdPlat plat = iSysThirdPlatService.getOne(Wrappers.<SysThirdPlat>lambdaQuery().like(SysThirdPlat::getName, "touch").last("limit 1"));
        if (ObjectUtils.isEmpty(plat)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("The platform is not active on ERP System"));
        }

        int type = SysProdEvent.TypeSale;
        int relationId = 0;
        List<SysProdEvent> eventList = new ArrayList<>();
        List<SysProdDeal> dealList = new ArrayList<>();
        for (Integer prodId : prodMap.keySet()) {
            // 添加关联商品
            SysProdDeal deal = new SysProdDeal();
            deal.setProdId(prodId);
            deal.setType(type);
            deal.setRelationId(relationId);

            SysProd prod = prodMap.get(prodId);
            if (!ObjectUtils.isEmpty(prod)) {
                deal.setGmtIn(prod.getGmtIn());
                deal.setShopId(prod.getShopId());
                deal.setWareId(prod.getWareId());
                deal.setPku(prod.getPku());
                deal.setSku(prod.getSku());
                deal.setCostPrice(prod.getCostPrice());
                deal.setSupply(prod.getSupply());
            }

            deal.setThirdPlatId(plat.getId());
            deal.setSalePrice(SysConstants.zero);
            deal.setQuotePrice(SysConstants.zero);

            dealList.add(deal);

            // 商品事件：代发、转运、套现、寄卖、平台内转移
            SysProdEvent event = new SysProdEvent();
            event.setProdId(prodId);
            event.setShopId(deal.getShopId());
            event.setDescription("寄卖上架");
            event.setType(type);
            event.setRelationId(relationId);
            if (event.getProdId() > 0) eventList.add(event);
        }

        iSysProdDealService.insertList(dealList);
        iSysProdEventService.insertList(eventList);

        if (!ObjectUtils.isEmpty(prodIdList)) {
            // 变更商品状态
            update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList).set(SysProd::getStatus, 2));

            // 批量绑定事件关联id
            iSysProdEventService.relateSale(prodIdList);

            // search同步更新
            iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                    .set(SysProdSearch::getDelFlag, 0)
                    .set(SysProdSearch::getStatus, 2)
                    .in(SysProdSearch::getProdId, prodIdList)
                    .eq(SysProdSearch::getSearchType, 1));
        }

        return failList;
    }

    @Override
    public Boolean fixProd2(List<String> oneIdList) {
        if (ObjectUtils.isEmpty(oneIdList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("empty list of Erp product"));
        }

        List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery()
                .in(SysProd::getOneId, oneIdList));

        List<Integer> prodIdList = prodList.stream().map(SysProd::getId).collect(Collectors.toList());

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .in(SysProdDeal::getProdId, prodIdList)
                .eq(SysProdDeal::getStatus, 1).eq(SysProdDeal::getType, SysProdEvent.TypeSale));

        // 商品事件：撤销操作
        List<SysProdEvent> eventList = new ArrayList<>();
        dealList.forEach(deal -> {
            SysProdEvent event = new SysProdEvent();
            event.setProdId(deal.getProdId());
            event.setShopId(deal.getShopId());
            event.setType(Integer.valueOf(String.format("%d" + SysProdEvent.TypeEnd, SysProdEvent.TypeSale)));
            event.setDescription("操作撤销");
            event.setRelationId(deal.getType() == SysProdEvent.TypeSale ? deal.getId() : deal.getRelationId());
            if (event.getProdId() > 0) eventList.add(event);
        });
        iSysProdEventService.insertList(eventList);

        // 流程结束：操作取消
        iSysProdDealService.update(Wrappers.<SysProdDeal>lambdaUpdate()
                .in(SysProdDeal::getProdId, prodIdList)
                .eq(SysProdDeal::getType, SysProdEvent.TypeSale).eq(SysProdDeal::getStatus, 1)
                .set(SysProdDeal::getGmtModify, DateTimeUtils.getNow())
                .set(SysProdDeal::getStatus, 2));

        update(Wrappers.<SysProd>lambdaUpdate().in(SysProd::getId, prodIdList)
                .setSql("gmt_modify = '" + DateTimeUtils.getNowByStr() + "', `status` = 1, odd_no = null"));

        // search同步更新
        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .setSql(" `status` = 1, odd_no = null, odd_type = null ")
                .in(SysProdSearch::getProdId, prodIdList)
                .eq(SysProdSearch::getSearchType, 1));

        // 清理出库信息
        iSysWareOutProdService.remove(Wrappers.<SysWareOutProd>lambdaQuery()
                .in(SysWareOutProd::getProdId, prodIdList));
        iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                .in(SysWareOutBatchProd::getProdId, prodIdList));

        List<Integer> saleIdList = dealList.stream()
                .filter(a -> !ObjectUtils.isEmpty(a.getSaleId()))
                .map(SysProdDeal::getSaleId).distinct().collect(Collectors.toList());
        if (!ObjectUtils.isEmpty(saleIdList)) {
            saleIdList.forEach(saleId -> {
                iSysWareOutService.resetProdNum(null, saleId, SysProdEvent.TypeSale);
            });

            List<SysProdDeal> prods = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .ne(SysProdDeal::getStatus, 2).in(SysProdDeal::getSaleId, saleIdList));
            if (!ObjectUtils.isEmpty(prods)) {
                Map<Integer, List<SysProdDeal>> saleMap = prods.stream().collect(Collectors.groupingBy(SysProdDeal::getSaleId));
                saleMap.keySet().forEach(saleId -> {
                    saleIdList.remove(saleId);
                });
            }
            // 已清空的寄售单
            if (!ObjectUtils.isEmpty(saleIdList)) {
                iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                        .in(SysWareOut::getRelationId, saleIdList)
                        .eq(SysWareOut::getType, SysProdEvent.TypeSale));

                iSysProdSaleService.remove(Wrappers.<SysProdSale>lambdaQuery()
                        .in(SysProdSale::getId, saleIdList));
            }
        }


        return true;
    }

    @Override
    public Boolean fixProd3(List<String> oneIdList) {
        if (ObjectUtils.isEmpty(oneIdList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("empty list of Erp product"));
        }

        List<SysProd> prodList = list(Wrappers.<SysProd>lambdaQuery()
                .in(SysProd::getOneId, oneIdList));

        List<Integer> prodIdList = prodList.stream().map(SysProd::getId).collect(Collectors.toList());

        List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                .in(SysProdDeal::getProdId, prodIdList));
        if (!ObjectUtils.isEmpty(dealList)) {
            // 审核记录
            dealList.forEach(deal -> {
                switch (deal.getType()) {
                    case SysProdEvent.TypeSend:
                        SysProdTransport send = iSysProdTransportService.getById(deal.getRelationId());
                        if (!ObjectUtils.isEmpty(send)) {
                            if (send.getProdNum() == 1) {
                                // 移除出库单
                                send.deleteById();

                                // 出库记录
                                iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                                        .eq(SysWareOut::getType, deal.getType())
                                        .eq(SysWareOut::getRelationId, deal.getRelationId()));
                            } else {
                                iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                        .setSql(" prod_num = prod_num - 1 ")
                                        .eq(SysProdTransport::getId, deal.getRelationId()));
                            }
                        }
                        break;
                    case SysProdEvent.TypeTransport:
                        SysProdTransport transport = iSysProdTransportService.getById(deal.getRelationId());
                        if (!ObjectUtils.isEmpty(transport)) {
                            if (transport.getProdNum() == 1) {
                                // 移除出库单
                                transport.deleteById();

                                // 出库记录
                                iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                                        .eq(SysWareOut::getType, deal.getType())
                                        .eq(SysWareOut::getRelationId, deal.getRelationId()));
                            } else {
                                iSysProdTransportService.update(Wrappers.<SysProdTransport>lambdaUpdate()
                                        .setSql(" prod_num = prod_num - 1 ")
                                        .eq(SysProdTransport::getId, deal.getRelationId()));
                            }
                        }
                        break;
                    case SysProdEvent.TypeCash:
                        SysProdCash cash = iSysProdCashService.getById(deal.getRelationId());
                        if (!ObjectUtils.isEmpty(cash)) {
                            if (iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery()
                                    .eq(SysProdDeal::getRelationId, deal.getRelationId())
                                    .eq(SysProdDeal::getType, SysProdEvent.TypeCash)) == 1) {
                                // 移除套现记录
                                cash.deleteById();

                                // 出库记录
                                iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                                        .eq(SysWareOut::getType, deal.getType())
                                        .eq(SysWareOut::getRelationId, deal.getRelationId()));
                            }
                        }
                        break;
                    case SysProdEvent.TypeSale:
                        SysProdSale sale = iSysProdSaleService.getById(deal.getRelationId());
                        if (!ObjectUtils.isEmpty(sale)) {
                            if (iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery()
                                    .eq(SysProdDeal::getRelationId, deal.getRelationId())
                                    .eq(SysProdDeal::getType, SysProdEvent.TypeSale)) == 1) {
                                // 移除寄卖记录
                                sale.deleteById();

                                // 出库记录
                                iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                                        .eq(SysWareOut::getType, deal.getType())
                                        .eq(SysWareOut::getRelationId, deal.getRelationId()));
                            }
                        }
                        break;
                    case SysProdEvent.TypeTransfer:
                        SysProdTransfer transfer = iSysProdTransferService.getById(deal.getRelationId());
                        if (!ObjectUtils.isEmpty(transfer)) {
                            if (iSysProdDealService.count(Wrappers.<SysProdDeal>lambdaQuery()
                                    .eq(SysProdDeal::getRelationId, deal.getRelationId())
                                    .eq(SysProdDeal::getType, SysProdEvent.TypeTransfer)) == 1) {
                                // 移除平台内转移记录
                                transfer.deleteById();

                                // 出库记录
                                iSysWareOutService.remove(Wrappers.<SysWareOut>lambdaQuery()
                                        .eq(SysWareOut::getType, deal.getType())
                                        .eq(SysWareOut::getRelationId, deal.getRelationId()));
                            }
                        }
                        break;
                }

                iSysWareOutProdService.remove(Wrappers.<SysWareOutProd>lambdaQuery()
                        .eq(SysWareOutProd::getDealId, deal.getId()));
                iSysWareOutBatchProdService.remove(Wrappers.<SysWareOutBatchProd>lambdaQuery()
                        .eq(SysWareOutBatchProd::getProdId, deal.getProdId()));

                iSysWareOutService.resetProdNum(null, deal.getRelationId(), deal.getType());
            });

            iSysProdDealService.removeByIds(dealList.stream().map(SysProdDeal::getId).collect(Collectors.toList()));
        }

        removeByIds(prodIdList);

        iSysProdSearchService.remove(Wrappers.<SysProdSearch>lambdaQuery().in(SysProdSearch::getProdId, prodIdList));

        return true;
    }

    private void shopNow(List<SysProdSearch> dataList, SysProdSearch copy, SysProd prod,
                         Map<Integer, Integer> prodShelvesMap,
                         Map<Integer, SysWareInProd> inProdMap,
                         Map<Integer, SysWareIn> inMap,
                         Map<Integer, ShopUser> shopMap,
                         Map<Integer, ShopPack> packMap,
                         Map<Integer, List<SysProdDeal>> dealGroup,
                         Map<Integer, SysProdSale> saleMap,
                         Map<Integer, SysWareOutBatchProd> outBatchProdMap,
                         Map<Integer, SysWareOutProd> outProdMap,
                         Map<Integer, SysWareOut> outMap,
                         Map<Integer, Map<Integer, SysAudit>> auditTree) {
        SysProdSearch data = JSONObject.parseObject(JSONObject.toJSONString(copy), SysProdSearch.class);
        data.setSearchType(1);

        Integer prodId = prod.getId();
        Integer shelvesId = prodShelvesMap.get(prodId);
        SysWareInProd inProd = inProdMap.get(prodId);
        List<SysProdDeal> dealList = dealGroup.get(prodId);
        SysWareOutBatchProd batchProd = outBatchProdMap.get(prodId);

        data.setSupply(prod.getSupply());
        data.setCostPrice(prod.getCostPrice());
        data.setStatus(prod.getStatus());
        data.setWareId(prod.getWareId());
        data.setShopId(prod.getShopId());

        data.setShelvesId(shelvesId);

        if (!ObjectUtils.isEmpty(inProd)) {
            data.setCheckResult(inProd.getCheckResult());
            data.setGmtIn(inProd.getGmtCreate());

            SysWareIn in = inMap.get(inProd.getInId());
            if (!ObjectUtils.isEmpty(in)) {
                data.setInBatchNo(in.getBatchNo());
            }

            ShopPack pack = packMap.get(inProd.getPackId());
            if (!ObjectUtils.isEmpty(pack)) {
                data.setInLogNo(pack.getLogNo());
            }
        }

        data.setOddNo(prod.getOddNo());
        if (!ObjectUtils.isEmpty(dealList)) {
            List<SysProdDeal> finishList = dealList.stream().filter(a -> {
                return a.getStatus() == 3 && (a.getType() == SysProdEvent.TypeCash || a.getType() == SysProdEvent.TypeTransfer);
            }).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(finishList)) {
                SysProdDeal deal = finishList.get(finishList.size() - 1);

                SysAudit audit = Optional.ofNullable(auditTree.get(deal.getType())).map(auditMap -> auditMap.get(deal.getRelationId())).orElse(null);
                if (!ObjectUtils.isEmpty(audit)) {
                    data.setInBatchNo(audit.getOddNo());
                    data.setGmtIn(audit.getGmtModify());
                }
            }

            SysProdDeal deal = dealList.get(dealList.size() - 1);

            if (ObjectUtils.isEmpty(prod.getShopId())) {
                data.setDealId(deal.getId());
                data.setShopId(deal.getShopId());
            }

            data.setOddType(deal.getType());
            data.setThirdPlatId(deal.getThirdPlatId());
            data.setPlatOrderNo(deal.getPlatOrderNo());
            SysProdSale sale = saleMap.get(deal.getSaleId());
            if (!ObjectUtils.isEmpty(sale)) {
                data.setThirdPlatName(sale.getPlatName());
            }
        }

        if (!ObjectUtils.isEmpty(batchProd)) {
            data.setGmtOut(batchProd.getGmtOut());

            SysWareOut out = outMap.get(batchProd.getOutId());
            if (!ObjectUtils.isEmpty(out)) {
                data.setOutNo(out.getOddNo());
                data.setWareId(out.getWareId());
            }

            SysWareOutProd outProd = outProdMap.get(data.getDealId());
            if (!ObjectUtils.isEmpty(outProd)) {
                data.setShelvesId(outProd.getShelvesId());
                data.setGmtPay(outProd.getGmtCreate());
            }
        }

        ShopUser shop = shopMap.get(data.getShopId());
        if (!ObjectUtils.isEmpty(shop)) {
            data.setShopUid(shop.getUid());
            data.setShopName(shop.getRealname());
        }

        data.setGmtCreate(data.getGmtIn());

        dataList.add(data);
    }

    private void shopHistory(List<SysProdSearch> dataList, SysProdSearch copy, SysProd prod,
                             Map<Integer, Integer> prodShelvesMap,
                             Map<Integer, SysWareInProd> inProdMap,
                             Map<Integer, SysWareIn> inMap,
                             Map<Integer, ShopUser> shopMap,
                             Map<Integer, ShopPack> packMap,
                             Map<Integer, List<SysProdDeal>> dealGroup,
                             Map<Integer, SysProdSale> saleMap,
                             Map<Integer, Map<Integer, SysAudit>> auditTree) {

        Integer prodId = prod.getId();
        SysWareInProd inProd = inProdMap.get(prodId);
        Integer shelvesId = prodShelvesMap.get(prodId);
        List<SysProdDeal> dealList = dealGroup.get(prodId);
        if (!ObjectUtils.isEmpty(dealList)) {
            dealList = dealList.stream().filter(a -> a.getStatus() == 3).collect(Collectors.toList());
        }

        if (ObjectUtils.isEmpty(dealList)) {
            return;
        }

        if (ObjectUtils.isEmpty(prod.getShopId())) {
            dealList.remove(dealList.size() - 1);
        }

        for (int i = 0; i < dealList.size(); i++) {
            SysProdDeal deal = dealList.get(i);

            SysProdSearch data = JSONObject.parseObject(JSONObject.toJSONString(copy), SysProdSearch.class);
            data.setSearchType(2);

            data.setDealId(deal.getId());
            data.setShopId(deal.getShopId());
            data.setSupply(deal.getSupply());
            data.setCostPrice(deal.getCostPrice());
            data.setStatus(6);
            data.setWareId(deal.getWareId());

            data.setShelvesId(shelvesId);
            data.setGmtPay(deal.getGmtModify());

            if (!ObjectUtils.isEmpty(inProd)) {
                data.setCheckResult(inProd.getCheckResult());
                data.setGmtIn(inProd.getGmtCreate());

                SysWareIn in = inMap.get(inProd.getInId());
                if (!ObjectUtils.isEmpty(in)) {
                    data.setInBatchNo(in.getBatchNo());
                }

                ShopPack pack = packMap.get(inProd.getPackId());
                if (!ObjectUtils.isEmpty(pack)) {
                    data.setInLogNo(pack.getLogNo());
                }
            }

            ShopUser shop = shopMap.get(deal.getShopId());
            if (!ObjectUtils.isEmpty(shop)) {
                data.setShopUid(shop.getUid());
                data.setShopName(shop.getRealname());
            }

            SysAudit audit = Optional.ofNullable(auditTree.get(deal.getType())).map(auditMap -> auditMap.get(deal.getRelationId())).orElse(null);
            if (!ObjectUtils.isEmpty(audit)) {
                data.setOddNo(audit.getOddNo());
            }

            if (i != 0) {
                SysProdDeal lastDeal = dealList.get(i - 1);

                SysAudit lastAudit = Optional.ofNullable(auditTree.get(lastDeal.getType())).map(auditMap -> auditMap.get(lastDeal.getRelationId())).orElse(null);
                if (!ObjectUtils.isEmpty(lastAudit)) {
                    data.setInBatchNo(lastAudit.getOddNo());
                    data.setGmtIn(lastAudit.getGmtModify());
                }
            }

            data.setOddType(deal.getType());
            data.setThirdPlatId(deal.getThirdPlatId());
            data.setPlatOrderNo(deal.getPlatOrderNo());
            SysProdSale sale = saleMap.get(deal.getSaleId());
            if (!ObjectUtils.isEmpty(sale)) {
                data.setThirdPlatName(sale.getPlatName());
            }

            data.setOutNo(data.getOddNo());
            data.setGmtOut(deal.getGmtModify());

            data.setGmtCreate(data.getGmtIn());

            dataList.add(data);
        }
    }

    /**
     * @return
     */
    @Override
    public List<SysProd> getDistinctSkuAndSpec() {
        return baseMapper.selectDistinctSkuAndSpec();
    }

    @Override
    public int updateKnetProductListingForCost(ExtKnetProductListing extKnetProductListing) {
        return extKnetProductListingMapper.updateCost(extKnetProductListing);
    }

    /**
     * 是否寄存在
     *
     * @param oneId
     * @return
     */
    @Override
    public boolean consignedInPremiumWare(String oneId) {
        SysProd sysProd = getBaseMapper().selectOne(Wrappers.<SysProd>lambdaQuery()
                .eq(SysProd::getOneId, oneId)
                .last("LIMIT 1")
        );

        if (ObjectUtils.isEmpty(sysProd)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("No available SysProd matched one id: " + oneId));
        }
        if (ObjectUtils.isEmpty(sysProd.getWareId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("No available Ware Id matched one id: " + oneId));
        }

        // 仓库是否是高级等级
        return iSysWareService.isPremiumWare(sysProd.getWareId());
    }

    /**
     * @param oneId
     * @return
     */
    @Override
    public boolean releaseSysProd(String oneId) {
        if (ObjectUtils.isEmpty(oneId)) {
            throw new BaseException("empty oneId input to release sysProd.");
        }

        // 清理对应的数据
        SysProd matchedProd = getBaseMapper().selectOne(Wrappers.<SysProd>lambdaQuery()
                .eq(SysProd::getOneId, oneId)
                .eq(SysProd::getStatus, 2)
                .last("LIMIT 1"));

        SysProdSearch matchedSearch = sysProdSearchMapper.selectOne(Wrappers.<SysProdSearch>lambdaQuery()
                .eq(SysProdSearch::getOneId, oneId)
                .eq(SysProdSearch::getStatus, 2)
                .eq(SysProdSearch::getSearchType, 1)
                .last("LIMIT 1"));

        if (matchedProd != null && matchedSearch != null) {
            matchedProd.setStatus(1);
            matchedSearch.setStatus(1);
            getBaseMapper().updateById(matchedProd);
            sysProdSearchMapper.updateById(matchedSearch);
            return true;
        }

        throw new BaseException("No matched SysProd to release by given oneId: " + oneId);
    }

    /**
     * 查询商品是否处于锁定状态
     */
    @Override
    public List<String> querySysProdLock(List<String> oneIdList) {
        if (ObjectUtils.isEmpty(oneIdList)) return null;

        // 获取处于订单中处于锁定的商品
        List<String> list = baseMapper.selectOrderLocked(oneIdList);
        // 获取处于库存中处于锁定的商品
        list.addAll(baseMapper.selectSysProdLocked(oneIdList));
        //去除重复项
        list = list.stream().distinct().collect(Collectors.toList());

        return list;

    }


    /**
     * 批量进行代发
     */
    @Override
    public String batchDealFile(MultipartFile xlsxFile
            , MultipartFile archiveFile
            , Integer userId
            , SysUploadRecordUserTypeEnum userType) throws IOException {
        log.info("SysProdServiceImpl batchDealFile start");
        Assert.notNull(xlsxFile, LanguageConfigService.i18nForMsg("xlsxFile is null"));
        Assert.notNull(archiveFile, LanguageConfigService.i18nForMsg("archiveFile is null"));
        Assert.notNull(userId, LanguageConfigService.i18nForMsg("userId is null"));
        Assert.notNull(userType, LanguageConfigService.i18nForMsg("userType is null"));

        if (!Objects.requireNonNull(xlsxFile.getOriginalFilename()).endsWith(".xlsx") &&
                !xlsxFile.getOriginalFilename().endsWith(".xls")) {
            throw new BaseException(LanguageConfigService.i18nForMsg("文件类型错误，只支持 .xls 和 .xlsx 格式"));
        }
        if (!Objects.requireNonNull(archiveFile.getOriginalFilename()).endsWith(".zip")) {
            throw new BaseException(LanguageConfigService.i18nForMsg("zip 文件类型错误"));
        }
        // 初始化参数
        List<SysProdFileBatchDealDTO> entityList = new ArrayList<>();
        Map<String, List<SysProdFileBatchDealDTO>> entityListMap = new HashMap<>();
        // 文件解析
        List<String> headers = Arrays.asList(
                "sku", "size", "oneId", "label"
        );
        JSONArray array = ExcelReader.readExcel(xlsxFile.getOriginalFilename(), xlsxFile.getInputStream(), headers);
        Assert.notNull(array, LanguageConfigService.i18nForMsg("excel 文件不存在"));
        Assert.isTrue(array.size() > 0, LanguageConfigService.i18nForMsg("excel 文件数据条数为0"));

        entityList = array.toJavaList(SysProdFileBatchDealDTO.class);

        // label 分组
        entityListMap = entityList.stream().filter(e -> !ObjectUtils.isEmpty(e.getLabel()))
                .collect(Collectors.groupingBy(SysProdFileBatchDealDTO::getLabel));
        Assert.notNull(entityListMap, LanguageConfigService.i18nForMsg("excel 文件异常，无法正常分组数据"));

        SysUploadRecordVo recordVo = new SysUploadRecordVo();
        recordVo.setUploadStatus(SysUploadRecordUploadStatusEnum.SUCCESS);
        recordVo.setUploadType(SysUploadRecordUploadTypeEnum.SHOP_BATCH_DELIVERY);
        recordVo.setTotalCount(0);
        recordVo.setFailCount(0);
        recordVo.setSuccessCount(0);

        List<SysUploadRecordDetail> sysUploadRecordDetailList = new ArrayList<>();
        // 按照label进行分组代发
        for (Map.Entry<String, List<SysProdFileBatchDealDTO>> entry : entityListMap.entrySet()) {
            // 调用代发方法
            SysUploadRecordVo sysUploadRecordVo = this.dataDealSave(entry.getKey(), entry.getValue(), archiveFile);
            // 对结果进行整合
            if (!ObjectUtils.isEmpty(sysUploadRecordVo)) {
                if (ObjectUtils.isEmpty(sysUploadRecordVo.getTotalCount())) {
                    sysUploadRecordVo.setTotalCount(0);
                }
                if (ObjectUtils.isEmpty(sysUploadRecordVo.getFailCount())) {
                    sysUploadRecordVo.setFailCount(0);
                }
                if (ObjectUtils.isEmpty(sysUploadRecordVo.getSuccessCount())) {
                    sysUploadRecordVo.setSuccessCount(0);
                }
                recordVo.setTotalCount(sysUploadRecordVo.getTotalCount() + recordVo.getTotalCount());
                recordVo.setFailCount(sysUploadRecordVo.getFailCount() + recordVo.getFailCount());
                recordVo.setSuccessCount(sysUploadRecordVo.getSuccessCount() + recordVo.getSuccessCount());
                // 存在一次失败，则修改
                if (sysUploadRecordVo.getUploadStatus().equals(SysUploadRecordUploadStatusEnum.FAIL)) {
                    recordVo.setUploadStatus(SysUploadRecordUploadStatusEnum.FAIL);
                }
                sysUploadRecordDetailList.addAll(sysUploadRecordVo.getSysUploadRecordDetails());
            }
        }
        recordVo.setSysUploadRecordDetails(sysUploadRecordDetailList);

        // 保存导入流水
        String uploadBatchNo = iSysCodePoolService.build(26, 1).get(0);
        if (!ObjectUtils.isEmpty(recordVo)
                && !ObjectUtils.isEmpty(recordVo.getSysUploadRecordDetails())) {
            // 保存错误记录
            SysUploadRecord sysUploadRecord = new SysUploadRecord();
            BeanUtils.copyProperties(recordVo, sysUploadRecord);
            sysUploadRecord.setUserType(SysUploadRecordUserTypeEnum.SHOP);
            sysUploadRecord.setUserId(JwtContentHolder.getUserId());
            sysUploadRecord.setBatchNo(uploadBatchNo);
            iSysUploadRecordService.save(sysUploadRecord);
            recordVo.getSysUploadRecordDetails().forEach(detail -> {
                detail.setUploadId(sysUploadRecord.getId());
                detail.setDelFlag(0);
                detail.setGmtCreate(sysUploadRecord.getGmtCreate());
            });
            sysUploadRecordDetailMapper.insertList(recordVo.getSysUploadRecordDetails());

        }

        log.info("SysProdServiceImpl batchDealFile end");
        return "";
    }


    /**
     * 批量发货
     */
    public SysUploadRecordVo dataDealSave(String labelName, List<SysProdFileBatchDealDTO> sysProdFileBatchDealDTOS, MultipartFile archiveFile) {
        Assert.notNull(sysProdFileBatchDealDTOS, LanguageConfigService.i18nForMsg("sysProdFileBatchDealDTOS is null"));
        Assert.isTrue(sysProdFileBatchDealDTOS.size() > 0, LanguageConfigService.i18nForMsg("sysProdFileBatchDealDTOS size is 0"));
        // 数据校验 & 初始化
        SysUploadRecordVo recordVo = this.checkReshippingData(sysProdFileBatchDealDTOS, archiveFile);
        Assert.notNull(recordVo, LanguageConfigService.i18nForMsg("excel 文件异常，无法正常校验数据"));

        if (recordVo.getUploadStatus().equals(SysUploadRecordUploadStatusEnum.SUCCESS)) {
            Assert.notNull(sysProdFileBatchDealDTOS.get(0).getLabelUrl(), LanguageConfigService.i18nForMsg("sysProdFileBatchDealDTOS LabelUrl is null"));

            String labelUrl = sysProdFileBatchDealDTOS.get(0).getLabelUrl();
            // 手动获取事务
            DefaultTransactionDefinition def = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(def);
            try {
                List<Integer> prodIdList = sysProdFileBatchDealDTOS.stream()
                        .map(SysProdFileBatchDealDTO::getProdId).collect(Collectors.toList());
                // 按批次发货
                SysProdDealDto insertDto = new SysProdDealDto()
                        .setLabelImg(labelUrl)
                        .setProdIdList(prodIdList)
                        .setType(3);
                this.batchDeal(insertDto);
                for (SysProdFileBatchDealDTO item : sysProdFileBatchDealDTOS) {

                    String outBatch = sysUploadRecordMapper.selectOutBatchNoByProdId(prodIdList);

                    // 新增成功后回填 出库批次号和 oneId
                    for (SysUploadRecordDetail detail : recordVo.getSysUploadRecordDetails()) {
                        if (!ObjectUtils.isEmpty(detail.getOneId())
                                && !ObjectUtils.isEmpty(detail.getOneId())
                                && detail.getOneId().equals(item.getOneId())) {
                            detail.setOutBatchNo(outBatch);
                            break;
                        }
                        if (!ObjectUtils.isEmpty(detail.getSku())
                                && !ObjectUtils.isEmpty(detail.getSize())
                                && !ObjectUtils.isEmpty(item.getSku())
                                && !ObjectUtils.isEmpty(item.getSize())
                                && detail.getSku().equals(item.getSku())
                                && detail.getSize().equals(item.getSize())) {
                            detail.setOutBatchNo(outBatch);
                        }
                    }

//                    if (!ObjectUtils.isEmpty(outBatch) && !ObjectUtils.isEmpty(item.getOneId())) {
//                        sysUploadRecordDetailMapper.updateOutBatchNoForOneId(outBatch, item.getOneId(),item.getLabel());
//                    }
//                    if (!ObjectUtils.isEmpty(outBatch)
//                            && !ObjectUtils.isEmpty(item.getSku())
//                            && !ObjectUtils.isEmpty(item.getSize())) {
//                        sysUploadRecordDetailMapper.updateOutBatchNoForSkuSize(outBatch, item.getOneId(), item.getSku(), item.getSize(),item.getLabel());
//                    }
                }
                // 提交事务
                transactionManager.commit(status);
            } catch (Exception e) {
                // 回滚事务
                e.printStackTrace();
                log.error("文件读取异常", e);
                transactionManager.rollback(status);
            }
        }

        return recordVo;
    }

    /**
     * 对批量发货的数据进行校验
     */
    public SysUploadRecordVo checkReshippingData(List<SysProdFileBatchDealDTO> prodFileBatchDealDTOList
            , MultipartFile archiveFile) {
        List<String> oneIds = prodFileBatchDealDTOList.stream()
                .filter(t -> !ObjectUtils.isEmpty(t.getOneId()))
                .map(t -> t.getOneId().trim())
                .collect(Collectors.toList());
        Map<String, Long> oneIdCountMap = oneIds.stream()
                .map(String::trim)
                .collect(Collectors.groupingBy(s -> s, Collectors.counting()));

        List<String> skuAndSizeList = prodFileBatchDealDTOList.stream()
                .filter(t -> !ObjectUtils.isEmpty(t.getSku()) && !ObjectUtils.isEmpty(t.getSize()))
                .map(t -> t.getSku().trim() + t.getSize().trim())
                .collect(Collectors.toList());
        Map<String, Long> skuAndSizeMap = skuAndSizeList.stream()
                .map(String::trim)
                .collect(Collectors.groupingBy(s -> s, Collectors.counting()));

        SysUploadRecordVo sysUploadRecordVo = new SysUploadRecordVo();
        List<SysUploadRecordDetail> sysUploadRecordDetailList = new ArrayList<>();
        // 开始校验前对prodFileBatchDealDTOList的Id属性进行初始化
        int index = 0;
        for (SysProdFileBatchDealDTO item : prodFileBatchDealDTOList) {
            if (ObjectUtils.isEmpty(item.getMarkId())) {
                item.setMarkId(++index);
            }
        }
        // 记录查询过的Sku+Size
        Map<String, List<String>> skuSizeStrMap = new HashMap<>();
        // 预查询： SKU + size 可以使用 仓库
        List<Integer> wareIdList = new ArrayList<>();
        boolean isFirstQuery = true;
        if (!ObjectUtils.isEmpty(skuAndSizeMap)) {
            for (String key : skuAndSizeMap.keySet()) {
                List<Integer> wareIdBySkuSize = baseMapper.selectIdByOrSkuSize(key
                        , oneIds
                        , JwtContentHolder.getUserId()
                        , null
                ).stream().map(SysProd::getWareId).collect(Collectors.toList());

                // 商品没有可以使用的仓库进行出库
                if (wareIdBySkuSize.size() == 0) {
                    wareIdList = new ArrayList<>();
                    log.info("SKU + size 无法使用仓库 ,key = {}", key);
                    break;
                }

                if (isFirstQuery) {
                    wareIdList.addAll(wareIdBySkuSize);
                } else {
                    wareIdList.retainAll(wareIdBySkuSize);
                }
                isFirstQuery = false;
            }
        }
        for (int i = 0; i < prodFileBatchDealDTOList.size(); i++) {
            SysProdFileBatchDealDTO sysProdFileBatchDealDTO = prodFileBatchDealDTOList.get(i);
            //判断sysProdFileBatchDealDTO所有属性值都是空白，则跳过本次循环
            if (ObjectUtils.isEmpty(sysProdFileBatchDealDTO)) {
                continue;
            }

            SysUploadRecordDetail sysUploadRecordDetail = new SysUploadRecordDetail();
            sysUploadRecordDetail.setMarkId(sysProdFileBatchDealDTO.getMarkId());
            sysUploadRecordDetail.setOutBatchNo("");
            sysUploadRecordDetail.setSku(sysProdFileBatchDealDTO.getSku() != null ? sysProdFileBatchDealDTO.getSku() : "");
            sysUploadRecordDetail.setSize(sysProdFileBatchDealDTO.getSize() != null ? sysProdFileBatchDealDTO.getSize() : "");
            sysUploadRecordDetail.setOneId(sysProdFileBatchDealDTO.getOneId() != null ? sysProdFileBatchDealDTO.getOneId() : "");
            sysUploadRecordDetail.setLabelName(sysProdFileBatchDealDTO.getLabel() != null ? sysProdFileBatchDealDTO.getLabel() : "");

            // 对 excel 每列数据进行非空校验
            if (ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getOneId())
                    && ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSku())
                    && ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSize())
            ) {
                this.initFailReason(sysUploadRecordDetail, "Sku, Size, OneId 不能同时为空", sysUploadRecordDetailList);
                continue;
            }

            // oneId 为空的时候，sku 和 size 必须有值,存在时
            if (ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getOneId())
                    && (ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSku()) || ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSize()))) {
                this.initFailReason(sysUploadRecordDetail, "OneId 为空时，Sku 和 Size 不能同时为空", sysUploadRecordDetailList);
                continue;
            }

            // sku + size 的商品这里找到相应的OneId
            String skuSize = "";
            List<String> oneIdList = new ArrayList<>();
            if (!ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSku())
                    && !ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSize())) {
                // SKU + size 所属label下没有符合仓库
                if (ObjectUtils.isEmpty(wareIdList)) {
                    this.initFailReason(sysUploadRecordDetail, "SKU + size 所属label下没有符合仓库", sysUploadRecordDetailList);
                }
                skuSize = sysProdFileBatchDealDTO.getSku().trim() + sysProdFileBatchDealDTO.getSize().trim();
                if (!skuSizeStrMap.containsKey(skuSize)) {
                    List<SysProd> sysProds = baseMapper.selectIdByOrSkuSize(skuSize
                            , oneIds
                            , JwtContentHolder.getUserId()
                            , wareIdList
                    );
                    // 商品按照仓库分组
                    Map<Integer, List<String>> wareIdMap = sysProds.stream().collect(Collectors.groupingBy(SysProd::getWareId, Collectors.mapping(SysProd::getOneId, Collectors.toList())));
                    // 判断这些仓库下的商品数量是否符合
                    for (Map.Entry<Integer, List<String>> entry : wareIdMap.entrySet()) {
                        List<String> wareGroupByOneIdList = entry.getValue();
                        // 同一个仓库的库存数量必须大于等于发货的商品数量
                        if (wareGroupByOneIdList.size() >= skuAndSizeMap.get(skuSize)) {
                            oneIdList = new ArrayList<>(wareGroupByOneIdList);
//                            oneIdList.addAll(wareGroupByOneIdList);
                        }
                    }
                    skuSizeStrMap.put(skuSize, oneIdList);
                }

                log.info("SysProdServiceImpl checkReshippingData oneIdList ={}", JSON.toJSONString(skuSizeStrMap));

                if (skuSizeStrMap.get(skuSize) == null) {
                    this.initFailReason(sysUploadRecordDetail, "Sku 和 Size 存在但不能匹配到 oneId", sysUploadRecordDetailList);
                    continue;
                } else if (!ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSku())
                        && !ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSize())
                        && skuAndSizeMap.get(skuSize) > skuSizeStrMap.get(skuSize).size()) {
                    this.initFailReason(sysUploadRecordDetail, "Sku 和 Size 库存不足", sysUploadRecordDetailList);
                    continue;
                } else {
                    // 查询 sku+size 类型的 oneId ,赋值给oneIds列表，后续新增代发时，只使用oneIds 列表
                    if (!ObjectUtils.isEmpty(oneIdList)) {
                        oneIds.addAll(oneIdList);
                    }
                }
            }

            // label 必填，不能为空
            if (ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getLabel())) {
                this.initFailReason(sysUploadRecordDetail, "label 不能为空", sysUploadRecordDetailList);
                continue;
            } else {
                // label 在 archiveFile 压缩包中必须存在
                if (!sysProdFileBatchDealDTO.getLabel().endsWith(".pdf")) {
                    sysProdFileBatchDealDTO.setLabel(sysProdFileBatchDealDTO.getLabel() + ".pdf");
                }
            }

            boolean isFileExists = false;
            try {
                isFileExists = FileUtils.isPdfExistsInZip(archiveFile, sysProdFileBatchDealDTO.getLabel());
            } catch (Exception e) {
                this.initFailReason(sysUploadRecordDetail, "label 文件异常", sysUploadRecordDetailList);
                continue;
            }
            if (!isFileExists) {
                this.initFailReason(sysUploadRecordDetail, "label 在 archiveFile 中不存在", sysUploadRecordDetailList);
                continue;
            }

            // oneId 在excel列表中不能重复
            if (!ObjectUtils.isEmpty(oneIdCountMap)
                    && !ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getOneId())
                    && !ObjectUtils.isEmpty(oneIdCountMap.get(sysProdFileBatchDealDTO.getOneId()))
                    && oneIdCountMap.get(sysProdFileBatchDealDTO.getOneId()) > 1) {
                this.initFailReason(sysUploadRecordDetail, "oneId 在excel列表中不能重复", sysUploadRecordDetailList);
                continue;
            }

            /*  sku  size 发货允许多双
            // sku+size 不能列表重复
            if (!ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSku())
                    && !ObjectUtils.isEmpty(sysProdFileBatchDealDTO.getSize())) {
                if (skuAndSizeMap.get(sysProdFileBatchDealDTO.getSku().trim() + sysProdFileBatchDealDTO.getSize().trim()) > 1) {
                    this.initFailReason(sysUploadRecordDetail, "sku size excel 列表重复", sysUploadRecordDetailList);
                    continue;
                }
            }*/

            // 账户余额不足
            if (!this.checkBalance(prodFileBatchDealDTOList.size())) {
                this.initFailReason(sysUploadRecordDetail, "账户余额不足", sysUploadRecordDetailList);
                continue;
            }

            // 商品不在同一个仓库
            if (ObjectUtils.isEmpty(skuSize)) {
                int wareCount = this.list(Wrappers.<SysProd>lambdaQuery().select(SysProd::getWareId).in(SysProd::getOneId, oneIds).groupBy(SysProd::getWareId)).size();
                if (wareCount > 1) {
                    this.initFailReason(sysUploadRecordDetail, "商品不在同一仓库", sysUploadRecordDetailList);
                    continue;
                }
            }

            if (ObjectUtils.isEmpty(sysUploadRecordDetail.getUploadStatus())) {
                // 成功
                this.initFailReason(sysUploadRecordDetail, null, sysUploadRecordDetailList);
            }
        }

        // 通过后进行批量代发的逻辑检查
        int failCount = (int) sysUploadRecordDetailList
                .stream()
                .filter(a -> !ObjectUtils.isEmpty(a.getUploadStatus()) && a.getUploadStatus().equals(SysUploadRecordUploadStatusEnum.FAIL))
                .count();
        if (failCount == 0) {
            List<SysProd> prodIdList = this.list(Wrappers.<SysProd>lambdaQuery()
                    .select(SysProd::getId, SysProd::getOneId, SysProd::getSku, SysProd::getSpec)
                    .in(SysProd::getOneId, oneIds)
                    .eq(SysProd::getStatus, 1)
                    .eq(SysProd::getShopId, JwtContentHolder.getUserId())
                    .eq(SysProd::getDelFlag, 0)
                    .orderByAsc(SysProd::getId));
            Map<Integer, String> oneIdsMap = prodIdList.stream().collect(Collectors.toMap(SysProd::getId, SysProd::getOneId, (key1, key2) -> key2));

            // 按照新增的校验方法把 oneIds 列表检查一遍
            for (SysProdFileBatchDealDTO prod : prodFileBatchDealDTOList) {
                SysUploadRecordDetail sysUploadRecordDetail = new SysUploadRecordDetail();
                sysUploadRecordDetail.setMarkId(prod.getMarkId());
                sysUploadRecordDetail.setOutBatchNo("");
                sysUploadRecordDetail.setSku(prod.getSku());
                sysUploadRecordDetail.setSize(prod.getSize());
                sysUploadRecordDetail.setOneId(prod.getOneId());
                sysUploadRecordDetail.setLabelName(prod.getLabel());
                sysUploadRecordDetail.setLabelUrl(prod.getLabelUrl());

                Integer prodId = null;
                if (!ObjectUtils.isEmpty(prod.getOneId())) {
                    // oneId 进行匹配
                    prodId = prodIdList.stream()
                            .filter(p -> !ObjectUtils.isEmpty(p.getOneId()) && p.getOneId().trim().equals(prod.getOneId().trim()))
                            .map(SysProd::getId)
                            .findFirst()
                            .orElse(null);
                } else {
                    String sku = Optional.ofNullable(prod.getSku()).map(String::trim).orElse("");
                    String size = Optional.ofNullable(prod.getSize()).map(String::trim).orElse("");
                    // sku+size 进行匹配
                    prodId = prodIdList.stream()
                            .filter(p -> !ObjectUtils.isEmpty(p.getSku())
                                    && p.getSku().equals(sku)
                                    && !ObjectUtils.isEmpty(p.getSpec())
                                    && p.getSpec().equals(size)
                            )
                            .map(SysProd::getId)
                            .findFirst()
                            .orElse(null);
                }

                if (ObjectUtils.isEmpty(prodId)) {
                    this.initFailReason(sysUploadRecordDetail, "没有匹配到商品", sysUploadRecordDetailList);
                    continue;
                }
                prod.setProdId(prodId);
                // 通过sku+size查询出来的货品没有oneId，这里补充一下
                if (ObjectUtils.isEmpty(prod.getOneId())) {
                    prod.setOneId(oneIdsMap.get(prodId));
                    // 补充 sysUploadRecordDetailList 的 OneId
                    sysUploadRecordDetail.setOneId(prod.getOneId());
                    Optional<SysUploadRecordDetail> existingDetail = sysUploadRecordDetailList.stream()
                            .filter(detail -> !ObjectUtils.isEmpty(detail.getMarkId())
                                    && Objects.equals(detail.getMarkId(), sysUploadRecordDetail.getMarkId())
                            )
                            .findFirst();
                    existingDetail.ifPresent(uploadRecordDetail -> sysUploadRecordDetailList.set(sysUploadRecordDetailList.indexOf(uploadRecordDetail), sysUploadRecordDetail));
                }
                // 获取过 prodId 后清掉 prodIdList 的元素
                Iterator<SysProd> iterator = prodIdList.iterator();
                while (iterator.hasNext()) {
                    SysProd p = iterator.next();
                    if (Objects.equals(p.getId(), prodId)) {
                        iterator.remove();
                    }
                }
                // 清除 oneIdsMap 中元素
                oneIdsMap.remove(prodId);

                // 检查数据
                SysProdDealDto dto = new SysProdDealDto()
                        .setProdIdList(Collections.singletonList(prod.getProdId()))
                        .setType(3);
                try {
                    // 批量上传的方法校验
                    this.batchDealCheck(dto);
                } catch (BaseException e) {
                    this.initFailReason(sysUploadRecordDetail, e.getMsg(), sysUploadRecordDetailList);
                    continue;
                }

                // 上传pdf文件
                if (!prod.getLabel().endsWith(".pdf")) { // 确保 labelName 以.pdf结尾
                    prod.setLabel(prod.getLabel() + ".pdf");
                }
                InputStream labelFile = null;
                try {
                    labelFile = FileUtils.getPdfInputStreamFromZip(archiveFile, prod.getLabel());
                    // 文件上传 S3
                    prod.setLabelUrl(AwsS3Utils.uploadFile(labelFile, prod.getLabel()));
                    if (ObjectUtils.isEmpty(prod.getLabelUrl())) {
                        this.initFailReason(sysUploadRecordDetail, "label文件返回为null，上传失败", sysUploadRecordDetailList);
                        continue;
                    }
                } catch (IOException e) {
                    this.initFailReason(sysUploadRecordDetail, "label文件，上传失败", sysUploadRecordDetailList);
                    continue;
                }
                // 校验通过，因为这里是第二遍，所以成功的条数不需要再增加了
                if (ObjectUtils.isEmpty(sysUploadRecordDetail.getUploadStatus())) {
                    sysUploadRecordDetail.setUploadStatus(SysUploadRecordUploadStatusEnum.SUCCESS);
                }
            }
        }


        if (sysUploadRecordDetailList.size() > prodFileBatchDealDTOList.size()) {
            log.info("SysProdServiceImpl checkReshippingData error sysUploadRecordDetailList = {} , prodFileBatchDealDTOList ={}", JSON.toJSONString(sysUploadRecordDetailList), JSON.toJSON(prodFileBatchDealDTOList));
        }

        failCount = (int) sysUploadRecordDetailList
                .stream()
                .filter(a -> !ObjectUtils.isEmpty(a.getUploadStatus()) && a.getUploadStatus().equals(SysUploadRecordUploadStatusEnum.FAIL))
                .count();

        sysUploadRecordVo.setFailCount(failCount);
        sysUploadRecordVo.setTotalCount(prodFileBatchDealDTOList.size());
        sysUploadRecordVo.setSuccessCount((prodFileBatchDealDTOList.size() - failCount));
        sysUploadRecordVo.setUploadStatus(failCount == 0 ? SysUploadRecordUploadStatusEnum.SUCCESS : SysUploadRecordUploadStatusEnum.FAIL);
        sysUploadRecordVo.setUploadType(SysUploadRecordUploadTypeEnum.SHOP_BATCH_DELIVERY);
        sysUploadRecordVo.setSysUploadRecordDetails(sysUploadRecordDetailList);

        return sysUploadRecordVo;
    }

    /**
     * 导入时对校验不通过的数据进行集中处理
     */
    public void initFailReason(SysUploadRecordDetail sysUploadRecordDetail, String failReason, List<SysUploadRecordDetail> sysUploadRecordDetailList) {

        if (!ObjectUtils.isEmpty(failReason)) {
            sysUploadRecordDetail.setFailReason(LanguageConfigService.i18nForMsg(failReason));
            sysUploadRecordDetail.setUploadStatus(SysUploadRecordUploadStatusEnum.FAIL);
        } else {
            sysUploadRecordDetail.setUploadStatus(SysUploadRecordUploadStatusEnum.SUCCESS);
        }

        if (ObjectUtils.isEmpty(sysUploadRecordDetail.getOneId())) { // sku+size 的维度允许重复，这里使用ID来标记
            log.info("SysProdServiceImpl initFailReason sysUploadRecordDetail oneId is null={}", JSON.toJSONString(sysUploadRecordDetail));
            Optional<SysUploadRecordDetail> existingDetail = sysUploadRecordDetailList.stream()
                    .filter(detail -> !ObjectUtils.isEmpty(detail.getMarkId())
                            && Objects.equals(detail.getMarkId(), sysUploadRecordDetail.getMarkId())
                    )
                    .findFirst();

            if (existingDetail.isPresent()) {
                int index = sysUploadRecordDetailList.indexOf(existingDetail.get());
                sysUploadRecordDetailList.set(index, sysUploadRecordDetail);
            } else {
                sysUploadRecordDetailList.add(sysUploadRecordDetail);

            }
        } else { // oneId 不允许重复
            Optional<SysUploadRecordDetail> existingDetail = sysUploadRecordDetailList.stream()
                    .filter(detail -> !ObjectUtils.isEmpty(detail.getOneId()) && detail.getOneId().equals(sysUploadRecordDetail.getOneId()))
                    .findFirst();
            if (existingDetail.isPresent()) {
                int index = sysUploadRecordDetailList.indexOf(existingDetail.get());
                sysUploadRecordDetailList.set(index, sysUploadRecordDetail);
            } else {
                sysUploadRecordDetailList.add(sysUploadRecordDetail);

            }
        }

    }

    /**
     * 判断商家余额是足够支付
     *
     * @return false 不足，true 足够
     */
    public boolean checkBalance(Integer prodSize) {
        // 获取默认价格
        PlatDefaultPriceVo platDefaultPriceVo = iSysParamSetService.defaultPrice(null);
        SysMoney sysMoney = iSysMoneyService.getOne(Wrappers.<SysMoney>lambdaQuery().eq(SysMoney::getUserId, JwtContentHolder.getShopId()), false);

        if (!ObjectUtils.isEmpty(sysMoney)
                && !ObjectUtils.isEmpty(sysMoney.getMoney())
                && !ObjectUtils.isEmpty(prodSize)
                && !ObjectUtils.isEmpty(platDefaultPriceVo)) {

            BigDecimal totalFee = platDefaultPriceVo.getPlatFee().multiply(new BigDecimal(prodSize));

            // 获取商家尚未支付的代发金额，累积金额进行校验
            BigDecimal fee = sysProdTransportMapper.selectFeeByShopId(JwtContentHolder.getShopId());
            if (!ObjectUtils.isEmpty(fee)) {
                log.info("SysProdServiceImpl checkBalance fee > 0 ={}", fee);
                totalFee = totalFee.add(fee);
            }

            log.info("sys_prod checkBalance if money shopId = {} pordIdList={} ,totalFee= {} ,platDefaultPriceVo={} , sysMoney={} "
                    , JwtContentHolder.getShopId()
                    , prodSize
                    , totalFee
                    , JSON.toJSONString(platDefaultPriceVo)
                    , JSON.toJSONString(sysMoney)
            );

            if (sysMoney.getMoney().compareTo(totalFee) < 0) {
                return false;
            } else {
                return true;
            }
        }
        log.info("SysProdServiceImpl checkBalance requst is null");
        return false;
    }

    @Override
    public IPage<SysProdListVo> getProductGroupBySkuPage(SysProdPageDto dto) {
        // 初始化请求参数
        this.initSysProdPageDtoParameters(dto);
        // 创建分页对象
        Page<SysProdListVo> page = new Page<>(dto.getCurrent(), dto.getSize());

        // 使用SQL分页查询
        return sysProdSearchMapper.selectGroupBySkuForProductPage(page, dto);
    }

    /**
     * 通过 OneId 更换鞋子的主人
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeOwner(List<SysProdSearch> sysProdSearchList) {
        Assert.notNull(sysProdSearchList, "sysProdSearchList不能为空");
        Assert.isTrue(sysProdSearchList.size() <= 2000, "sysProdSearchList不能大于2000");

        List<String> shopUidList = sysProdSearchList.stream().map(SysProdSearch::getShopUid)
                .collect(Collectors.toList());
        List<ShopUser> shopUserList = iShopUserService
                .list(Wrappers.<ShopUser>lambdaQuery().select(ShopUser::getId, ShopUser::getUid, ShopUser::getRealname)
                        .in(ShopUser::getUid, shopUidList));
        Map<String, ShopUser> shopUserMap = shopUserList.stream().collect(Collectors.toMap(ShopUser::getUid, a -> a));

        List<SysProd> prodList = this.list(Wrappers.<SysProd>lambdaQuery()
                .select(SysProd::getId, SysProd::getOneId)
                .in(SysProd::getOneId,
                        sysProdSearchList.stream().map(SysProdSearch::getOneId).collect(Collectors.toList())));
        Map<String, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getOneId, a -> a));
        List<SysProd> updateProdList = new ArrayList<>();
        sysProdSearchList.forEach(search -> {
            Assert.notNull(prodMap.get(search.getOneId()), "商品不存在");
            Assert.notNull(prodMap.get(search.getOneId()).getId(), "商品ID不存在");
            Assert.notNull(shopUserMap.get(search.getShopUid()), "新商家不存在");
            Assert.notNull(shopUserMap.get(search.getShopUid()).getId(), "新商家ID不存在");

            // 维护一下 sysProdSearchList 的 prodId
            search.setProdId(prodMap.get(search.getOneId()).getId());

            SysProd prod = new SysProd();
            prod.setId(prodMap.get(search.getOneId()).getId());
            prod.setShopId(shopUserMap.get(search.getShopUid()).getId());
            updateProdList.add(prod);
        });
        this.updateBatchById(updateProdList, 2000);

        List<SysProdSearch> newSearchList = iSysProdSearchService.list(Wrappers.<SysProdSearch>lambdaQuery()
                .select(SysProdSearch::getId, SysProdSearch::getOneId)
                .in(SysProdSearch::getProdId,
                        sysProdSearchList.stream().map(SysProdSearch::getProdId).collect(Collectors.toList()))
                .in(SysProdSearch::getSearchType, 1)
        );

        Map<String, SysProdSearch> newSearchMap = newSearchList.stream()
                .collect(Collectors.toMap(SysProdSearch::getOneId, a -> a));
        List<SysProdSearch> updateSearchList = new ArrayList<>();
        sysProdSearchList.forEach(search -> {
            Assert.notNull(newSearchMap.get(search.getOneId()), "search 不存在");
            Assert.notNull(newSearchMap.get(search.getOneId()).getId(), "search ID不存在");
            Assert.notNull(shopUserMap.get(search.getShopUid()), "新商家不存在");
            Assert.notNull(shopUserMap.get(search.getShopUid()).getId(), "新商家ID不存在");
            Assert.notNull(shopUserMap.get(search.getShopUid()).getUid(), "新商家UID不存在");
            Assert.notNull(shopUserMap.get(search.getShopUid()).getRealname(), "新商家姓名不存在");

            SysProdSearch newSearch = new SysProdSearch();
            newSearch.setId(newSearchMap.get(search.getOneId()).getId());
            newSearch.setShopId(shopUserMap.get(search.getShopUid()).getId());
            newSearch.setShopUid(shopUserMap.get(search.getShopUid()).getUid());
            newSearch.setShopName(shopUserMap.get(search.getShopUid()).getRealname());
            updateSearchList.add(newSearch);
        });
        iSysProdSearchService.updateBatchById(updateSearchList, 2000);

//        List<SysProdDeal> prodDealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
//                .select(SysProdDeal::getId)
//                .in(SysProdDeal::getProdId,
//                        sysProdSearchList.stream().map(SysProdSearch::getProdId).collect(Collectors.toList())));
//        if (!ObjectUtils.isEmpty(prodDealList)) {
//            Map<Integer, SysProdDeal> prodDealMap = prodDealList.stream()
//                    .collect(Collectors.toMap(SysProdDeal::getProdId, a -> a));
//            List<SysProdDeal> updateDealList = new ArrayList<>();
//            sysProdSearchList.stream()
//                    .filter(search -> !ObjectUtils.isEmpty(prodDealMap.get(search.getProdId())))
//                    .forEach(search -> {
//                Assert.notNull(prodDealMap.get(search.getProdId()).getId(), "prodDeal ID不存在");
//                Assert.notNull(shopUserMap.get(search.getShopUid()), "新商家不存在");
//                Assert.notNull(shopUserMap.get(search.getShopUid()).getId(), "新商家ID不存在");
//                SysProdDeal newDeal = new SysProdDeal();
//                newDeal.setId(prodDealMap.get(search.getProdId()).getId());
//                newDeal.setShopId(shopUserMap.get(search.getShopUid()).getId());
//                updateDealList.add(newDeal);
//            });
//            iSysProdDealService.updateBatchById(updateDealList, 2000);
//        }


        List<SysWareInProd> wareInProdList = iSysWareInProdService.list(Wrappers.<SysWareInProd>lambdaQuery()
                .select(SysWareInProd::getId, SysWareInProd::getProdId, SysWareInProd::getInId)
                .in(SysWareInProd::getProdId,
                        sysProdSearchList.stream().map(SysProdSearch::getProdId).collect(Collectors.toList())));
        if (!ObjectUtils.isEmpty(wareInProdList)) {
            Map<Integer, SysWareInProd> wareInProdMap = wareInProdList.stream()
                    .collect(Collectors.toMap(SysWareInProd::getProdId, a -> a));
            List<SysWareIn> updateWareInList = new ArrayList<>();
            List<ShopPack> updateShopPackList = new ArrayList<>();
            sysProdSearchList.stream()
                    .filter(search -> !ObjectUtils.isEmpty(wareInProdMap.get(search.getProdId())))
                    .forEach(search -> {
                Assert.notNull(wareInProdMap.get(search.getProdId()).getInId(), "wareInProd ID不存在");
                Assert.notNull(shopUserMap.get(search.getShopUid()), "新商家不存在");
                Assert.notNull(shopUserMap.get(search.getShopUid()).getId(), "新商家ID不存在");
                SysWareIn newWareIn = new SysWareIn();
                newWareIn.setId(wareInProdMap.get(search.getProdId()).getInId());
                newWareIn.setShopId(shopUserMap.get(search.getShopUid()).getId());
                ShopPack newShopPack = new ShopPack();
                newShopPack.setId(wareInProdMap.get(search.getProdId()).getInId());
                newShopPack.setShopId(shopUserMap.get(search.getShopUid()).getId());
                updateWareInList.add(newWareIn);
                updateShopPackList.add(newShopPack);
            });

            iSysWareInService.updateBatchById(updateWareInList, 2000);
            iShopPackService.updateBatchById(updateShopPackList, 2000);
        }
    }

    /**
     * 修改维修状态
     */
    @Override
    public void updateRepairFlag(SysProdUpdateRepairFlagDto dto) {
        Assert.notNull(dto.getProdId(), "商品ID不能为空");
        // search 表标记
        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .eq(!ObjectUtils.isEmpty(dto.getProdId()), SysProdSearch::getProdId, dto.getProdId())
                .eq(SysProdSearch::getSearchType, 1)
                .set(SysProdSearch::getRepairFlag, dto.getRepairFlag().getCode()));
        // 维修项目
        if (!ObjectUtils.isEmpty(dto.getRepairFlag()) && dto.getRepairFlag() == RepairFlagEnum.CAN_REPAIR) { // 这里对维修标记进行一次判断
            if (ObjectUtils.isEmpty(dto.getRepairIdList())) {
                throw new BaseException("当前商品已标记为可维修，但未配置任何维修项目。请检查并添加相应的维修项目。");
            }
            String oneId = this.getById(dto.getProdId()).getOneId();
            // 重置关联关系
            sysReairOrderService.resetByRepairIdList(dto.getProdId(), oneId, dto.getRepairIdList());
        }

    }

    /**
     * 获取所有在售的商品种类分类
     *
     * @return
     */
    @Override
    public List<String> getCrossListingSkuCategories() {
        return getBaseMapper().selectDistinctSkuOnListing();
    }

}
