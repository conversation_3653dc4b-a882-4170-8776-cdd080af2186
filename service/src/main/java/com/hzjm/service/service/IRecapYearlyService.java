package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.RecapYearly;
import com.hzjm.service.model.DTO.TaskRecapReqDto;
import com.hzjm.service.model.VO.PlatformOrderVo;
import com.hzjm.service.model.VO.RecapYearlyVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/17 18:03
 * @description: 商家年度统计报表
 */
public interface IRecapYearlyService extends IService<RecapYearly> {
    /**
     * 查询商家平台年度统计数据-前多少名的热门商品
     *
     * @param shopUid 商家uid
     * @param top     前多少名
     * @param year    年份
     * @return List<PlatformOrderVo>
     */
    List<PlatformOrderVo> queryShopProductTop(Long shopUid, Integer top, Integer year);


    /**
     * 生成商家年度统计数据
     * 仅支持商家uid单条生成
     *
     * @param recapReqDto 调用参数
     * @return 生成结果
     */
    Boolean createRecapYearly(TaskRecapReqDto recapReqDto);


    /**
     * 根据条件查询商家年度统计数据
     *
     * @param recapReqDto 请求参数
     * @return list
     */
    List<RecapYearlyVo> queryRecapYearlyByCondition(TaskRecapReqDto recapReqDto);


    /**
     * 生成邮件内容
     *
     * @param recapYearlyVo recapMonthlyVo
     * @return 邮件内容
     */
    String createEmailContent(RecapYearlyVo recapYearlyVo);
}
