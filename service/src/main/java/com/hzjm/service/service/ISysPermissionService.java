package com.hzjm.service.service;

import com.hzjm.common.model.HttpPageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysPermission;
import com.hzjm.service.model.DTO.SysPermissionPageDto;
import com.hzjm.service.model.VO.SysPermissionListVo;
import com.hzjm.service.model.VO.SysPermissionVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 后管权限 服务类
 *
 * <AUTHOR>
 * @since 2021-10-28
 */
public interface ISysPermissionService extends IService<SysPermission> {

    SysPermission getByIdWithoutLogic(Integer id);

    SysPermissionVo getDetail(Integer id);

    Boolean saveSysPermission(SysPermission dto);

    Boolean insertList(List<SysPermission> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    HttpPageResult<SysPermissionListVo> searchList(SysPermissionPageDto dto);

    List<SysPermission> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysPermission> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<SysPermission> getPermission(Integer userId, Integer type);

}
