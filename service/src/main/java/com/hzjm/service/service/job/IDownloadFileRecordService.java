package com.hzjm.service.service.job;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.service.entity.DownloadFileRecord;
import com.hzjm.service.model.DTO.FileDownloadDelReqDto;
import com.hzjm.service.model.DTO.FileDownloadReqDto;
import com.hzjm.service.model.enums.SysTaskStatus;

/**
 * <AUTHOR>
 * @date 2024/12/26 15:25
 * @description: 文件下载记录服务
 */
public interface IDownloadFileRecordService {

    /**
     * 创建下载记录
     *
     * @param downloadFileRecord 下载记录
     * @return 创建结果
     */
    int create(DownloadFileRecord downloadFileRecord);

    /**
     * 查询下载列表
     *
     * @param reqDto 查询条件
     * @return 任务列表
     */
    IPage<DownloadFileRecord> queryFileRecords(FileDownloadReqDto reqDto);

    /**
     * 更新下载记录状态,返回文件名
     *
     * @param taskId 任务ID
     * @return 文件名
     */
    String setPendingAndReturnFileName(Integer taskId);

    /**
     * 更新下载记录状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @return 更新结果
     */
    boolean setDoneByTaskId(Integer taskId, SysTaskStatus status);

    /**
     * 更新下载记录
     *
     * @param downloadFileRecord 下载记录
     * @return 更新结果
     */
    int updateDownloadFileRecord(DownloadFileRecord downloadFileRecord);

    /**
     * (逻辑)删除下载记录
     *
     * @param reqDto 任务IDs,用户ID
     */
    void deleteDownloadFileRecord(FileDownloadDelReqDto reqDto);
}
