package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysWithdraw;
import com.hzjm.service.model.DTO.SysWithdrawPageDto;
import com.hzjm.service.model.VO.SysWithdrawListVo;
import com.hzjm.service.model.VO.SysWithdrawVo;
import com.hzjm.service.model.VO.WithdrawOutTradeNoVO;

import java.util.List;

/**
 * 提现 服务类
 *
 * <AUTHOR>
 * @since 2023-03-28
 */
public interface ISysWithdrawService extends IService<SysWithdraw> {

    SysWithdraw getByIdWithoutLogic(Integer id);

    SysWithdrawVo getDetail(Integer id);

    List<String> queryAllDrawAccount();

    List<String> queryWithdrawOutTradeNo(WithdrawOutTradeNoVO dto);

    List<String> queryWithdrawOutTradeNoByAlibaba(WithdrawOutTradeNoVO dto);

    Boolean saveSysWithdraw(SysWithdraw dto);

    Boolean insertList(List<SysWithdraw> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWithdrawListVo> searchList(SysWithdrawPageDto dto);

    List<SysWithdraw> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWithdraw> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
