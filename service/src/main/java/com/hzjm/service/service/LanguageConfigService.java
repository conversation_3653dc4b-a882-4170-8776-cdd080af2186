package com.hzjm.service.service;

import cn.hutool.core.collection.CollUtil;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.service.entity.Language;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.util.StringUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class LanguageConfigService {

    private static LanguageInitService languageInitService;

    /**
     * 注入LanguageInitService
     */
    @Resource
    public void setMyService(LanguageInitService languageInitService) {
        LanguageConfigService.languageInitService = languageInitService;
    }


    public static String getLanguage(Integer userId, Integer userType) {
        if (ObjectUtils.isEmpty(userId)) {
            return null;
        }
        if (ObjectUtils.isEmpty(userType)) {
            return null;
        }
        return languageInitService.getOrSetLanguage(userId, userType);
    }

    // 获取国际化文本信息
    public static String i18nForMsg(String content) {

        Integer userId = JwtContentHolder.getUserId();
        //Integer userId = null;
        Integer userType = JwtContentHolder.getRoleType();

        if (!StringUtil.isBlank(content)) {

            String userLanguage = LanguageConfigService.getLanguage(userId, userType);
            if (userType != null && userType != 1 && userType != 5) {
                log.info("LanguageConfigService userType is not 1,5 userId ={} ,userType={},content={}", userId, userType, content);
            }
            if (StringUtil.isBlank(userLanguage)) { // 没有获取到用户设置的语言
                log.info("LanguageConfigService userLanguage is null userId ={} ,userType={},content={}", userId, userType, content);
                return LanguageConfigService.i18nForMsg(content, SysConstants.LANGUAGE_EN_US);
            }

            // 从 Redis 获取语言映射
            Map<String, Language> languageMap = languageInitService.getLanguageMap();
            if (!languageMap.containsKey(content)) { // 没有获取到国际化
                log.info("LanguageConfigService languageMap is null userId ={} ,userType={},content={}", userId, userType, content);
                return LanguageConfigService.i18nForMsg(content, SysConstants.LANGUAGE_EN_US);
            }

            return LanguageConfigService.i18nForMsg(content, userLanguage);

        }
        return content;
    }

    public static String i18nForMsg(String content, String userLanguage) {
        // 从 Redis 获取单个翻译条目
        Language i18n = languageInitService.getLanguageItem(content);

        if (!ObjectUtils.isEmpty(i18n) && !ObjectUtils.isEmpty(i18n.getEnUs()) && SysConstants.LANGUAGE_EN_US.equals(userLanguage)) { // 英文
            return i18n.getEnUs();
        }

        if (!ObjectUtils.isEmpty(i18n) && !ObjectUtils.isEmpty(i18n.getZhCn()) && SysConstants.LANGUAGE_ZH_CN.equals(userLanguage)) { // 中文
            return i18n.getZhCn();
        }

        return content;
    }

    public static List<String> i18nForMsg(List<String> contentList) {
        if (!ObjectUtils.isEmpty(contentList)) {
            contentList.replaceAll(LanguageConfigService::i18nForMsg);
        }
        return contentList;
    }

    public static List<String> i18nForMsg(List<String> contentList, String language) {
        if (!ObjectUtils.isEmpty(contentList)) {
            contentList.replaceAll(content -> i18nForMsg(content, language));
        }
        return contentList;
    }

    /**
     * Excel表头国际化转换
     *
     * @param headers  中文Excel表头
     * @param language 语言
     * @return 输出国际化表头
     */
    public static List<List<String>> generateExcelHeaders(List<String> headers, String language) {
        List<List<String>> result = new ArrayList<>();
        if (CollUtil.isEmpty(headers)) {
            throw new RuntimeException("Excel表头不能为空");
        }
        List<String> internationalHeaders = i18nForMsg(headers, language);
        for (String name : internationalHeaders) {
            List<String> singleHeaderList = new ArrayList<>();
            singleHeaderList.add(name);
            result.add(singleHeaderList);
        }
        return result;
    }
}
