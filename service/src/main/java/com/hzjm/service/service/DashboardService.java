package com.hzjm.service.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.annotation.TrimParam;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.service.model.DTO.AdminHomeBiUserCashoutHistoryDTO;
import com.hzjm.service.model.DTO.req.SourcingOpportunitiesDetailReq;
import com.hzjm.service.model.DTO.req.SourcingOpportunitiesReq;
import com.hzjm.service.model.VO.*;
import com.hzjm.service.model.enums.SourcePlatformService;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 商品筛选 服务实现类
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
public interface DashboardService {

    ShopInOutDashboard inOutNumTable(TableDataSearchDto dto);

    ShopInOutDashboard inOutCostTable(TableDataSearchDto dto);

    List<ShopUserListVo> queryShopUserTop10(TableDataSearchDto dto);

    List<PlatformOrderVo> queryProductTop20(TableDataSearchDto dto);

    PlatformOrderVo querySysProdDay(TableDataSearchDto dto);

    PlatformOrderVo querySysProdCostSum(TableDataSearchDto dto);

    @ReadOnly
    @TrimParam
    List<ShopHomeOrderPlatformVo> queryShopHomeOrderTotalGmvbyPlatform(Integer shopId
            , Date beginDate
            , Date endDate);

    @ReadOnly
    @TrimParam
    List<ShopHomeOrderPlatformVo> queryShopHomeOrderTotalGmv(Integer shopId
            , Date beginDate
            , Date endDate);

    SalesDashboardDataVo querySaleNumTable(TableDataSearchDto dto);

    List<SalesDayDataVo> queryDaySaleNumTable(TableDataSearchDto dto);

    List<SalesDayDataVo> queryProfitGroupByDay(TableDataSearchDto dto);

    Map<String, List<SalesDayDataVo>> queryProfitPlatformGroupByDay(TableDataSearchDto dto);

    AdminHomeBiDefaultVo queryAdminHomeBiDefaultVo(Set<Integer> shopIds);

    AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToActiceUsers(Date beginDate, Set<Integer> shopIds);

    long queryAdminHomeBiDefaultVoToActiceUsersYeaterDay(Date beginDate, Set<Integer> shopIds);

    AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToPercentage(Set<Integer> shopIds);

    AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToDefectPercentage(Set<Integer> shopIds);

    AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToOldProductPercentage(Set<Integer> shopIds);

    AdminHomeBiDefaultVo queryAdminHomeBiDefaultVoToLowestAskPercentage(Set<Integer> shopIds);

    AdminHomeBiOverviewVo queryAdminHomeBiOverviewVo(Date beginDate, Date endDate, Long userId, Set<Integer> shopIds);

    List<AdminHomeBiDataTrendVo> queryAdminHomeBiDataTrendVo(Date beginDate, Date endDate, Long userId, Set<Integer> shopIds);

    AdminHomeBiDataComparisonVo queryAdminHomeBiDataComparison(Date currentCycleBeginDate
            , Date currentCycleEndDate
            , Date comparedWithBeginDate
            , Date comparedWithEndDate
            , List<String> comparedWithType
            , Long userId
            , Set<Integer> shopIds
    );

    List<AdminHomeBiOrderPlatformVo> queryAdminHomeBiOrderPlatformVo(Date beginDate
            , Date endDate
            , SourcePlatformService platform
            , Set<Integer> shopIds
    );

    AdminHomeBiUserActiveListingsVo queryUserActiveListings(Long userId, Set<Integer> shopIds);

    List<AdminHomeBiUserPlatFeeVo> queryUserPlat(Long userId, Set<Integer> shopIds);

    IPage<AdminHomeBiUserCashoutHistoryVo> quereyAdminHomeBiUserCashoutHistoryVo(AdminHomeBiUserCashoutHistoryDTO dto, Set<Integer> shopIds);

    AdminHomeBiUserInfoVo queryUserInfo(Long userId, Set<Integer> shopIds);

    List<AdminHomeBiDataTrendVo> queryPlatformDistribution(Long userId, Date beginDate, Date endDate, Set<Integer> shopIds);

    IPage<AdminHomeBiUserListVo> queryAdminHomeBiUserListVo(Date beginDate
            , Date endDate
            , String userName
            , String userId
            , Integer current
            , Integer size
            , String sortField
            , String sortOrder
            , String customerManager
            , Set<Integer> shopIds
            , String referrerName
    );

    FinancialOverviewVo financialOverview(Set<Integer> shopIds);

    FinancialStatisticsVo financialStatistics(Date beginDate, Date endDate, Set<Integer> shopIds);


    /**
     * 查询自营数据
     *
     * @return AdminHomeSelfOperatedDataVo
     */
    List<AdminHomeSelfOperatedDataVo> querySelfOperatedData(TableDataSearchDto dto);

    /**
     * 查询商家排行榜 缓存
     *
     * @param dto dto
     * @return List
     */
    List<HotSkuRankVo> queryProductTop20ByCache(TableDataSearchDto dto);

    /**
     * TikTok 商机列表
     *
     * @param request 查询条件
     * @return 销售机会列表
     */
    IPage<SourcingOpportunitiesVo> queryTiktokSourcingOpportunities(SourcingOpportunitiesReq request);

    /**
     * TikTok 商机列表详情
     *
     * @param req 查询条件
     * @return 销售机会列表详情
     */
    List<SourcingOpportunitiesDetailVo> queryTiktokSourcingOpportunitiesDetails(SourcingOpportunitiesDetailReq req);
}
