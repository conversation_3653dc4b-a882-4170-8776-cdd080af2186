package com.hzjm.service.service.job;

import com.hzjm.service.entity.SysTask;
import com.hzjm.service.model.enums.SysTaskStatus;

/**
 * <AUTHOR>
 * @date 2024/12/26 15:25
 * @description: SysTaskService服务
 */
public interface ISysTaskSaveService {

    /**
     * 创建任务
     *
     * @param sysTask 任务
     * @return 任务id
     */
    Integer createTask(SysTask sysTask);

    /**
     * 更新任务状态为处理中
     *
     * @param taskId 任务id
     * @return 操作结果
     */
    boolean updateTaskStatus(Integer taskId, SysTaskStatus status);
}
