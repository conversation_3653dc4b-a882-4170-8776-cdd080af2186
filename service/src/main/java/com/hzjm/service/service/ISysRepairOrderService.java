package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.RepairStatusEnum;
import com.hzjm.service.entity.SysRepairOrder;
import com.hzjm.service.model.DTO.SysRepairOrderPageDto;
import com.hzjm.service.model.VO.SysRepairOrderCountVo;
import com.hzjm.service.model.VO.SysRepairOrderListVo;
import com.hzjm.service.model.VO.SysRepairOrderVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 维修单主表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
public interface ISysRepairOrderService extends IService<SysRepairOrder> {

    SysRepairOrder getByIdWithoutLogic(Integer id);

    /**
     * 根据ID查询维修单详情
     * @param id 维修单ID
     * @param oneId 商品oneId
     * @param shopId 商店ID
     * @return 维修单详情
     */
    SysRepairOrderVo getDetail(Integer id,String oneId,Integer shopId);

    List<SysRepairOrderVo> getDetail(String repairBatchNo, Integer shopId);

    SysRepairOrderVo getDetailByOneId(String oneId);

    Boolean saveSysRepairOrder(SysRepairOrder dto);

    SysRepairOrderCountVo searchListCount(SysRepairOrderPageDto dto,Integer shopId);

    Boolean insertList(List<SysRepairOrder> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysRepairOrderListVo> searchList(SysRepairOrderPageDto dto,Integer shopId);

    List<SysRepairOrder> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysRepairOrder> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean resetByRepairIdList(Integer productId, String oneId, List<Integer> repairIdList);

    Boolean updateStatus(String oneId, RepairStatusEnum repairStatus);

    Map<Integer, BigDecimal> getRepairOrderFee(List<Integer> repairOrderIds);
}
