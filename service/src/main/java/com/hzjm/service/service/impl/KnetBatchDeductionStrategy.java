package com.hzjm.service.service.impl;

import com.hzjm.service.model.DTO.KnetBatchRecordDto;
import com.hzjm.service.model.VO.SysBillImportVo;
import com.hzjm.service.service.ISysBillService;
import com.hzjm.service.strategy.IKnetBatchOperationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量扣款
 */
@Slf4j
@Service
public class KnetBatchDeductionStrategy implements IKnetBatchOperationStrategy {

    @Autowired
    private ISysBillService iSysBillService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean execute(List<KnetBatchRecordDto> records) {
        log.info("KnetBatchDeductionStrategy execute start, record count: {}", records.size());
        List<SysBillImportVo> sysBillImportVoList = new ArrayList<>();

        for (KnetBatchRecordDto record : records) {
            SysBillImportVo sysBillImportVo = new SysBillImportVo();
            sysBillImportVo.setShopUid(record.getShopUid());
            sysBillImportVo.setAmount(record.getAmount());
            sysBillImportVo.setRemark(record.getRemark());
            sysBillImportVoList.add(sysBillImportVo);
        }
        iSysBillService.processUpsRecords(sysBillImportVoList);
        return true;
    }

    @Override
    public String getStrategyType() {
        return "DEDUCTION";
    }
}
