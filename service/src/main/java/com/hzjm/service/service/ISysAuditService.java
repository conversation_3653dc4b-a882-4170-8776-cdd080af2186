package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.Date;
import java.util.List;
import com.hzjm.service.entity.SysAudit;
import com.hzjm.service.model.DTO.SysAuditPageDto;
import com.hzjm.service.model.VO.SysAuditListVo;
import com.hzjm.service.model.VO.SysAuditVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 平台审核 服务类
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
public interface ISysAuditService extends IService<SysAudit> {

    SysAudit getByIdWithoutLogic(Integer id);

    SysAuditVo getDetail(Integer id);

    Boolean saveSysAudit(SysAudit dto);

    Boolean insertList(List<SysAudit> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysAuditListVo> searchList(SysAuditPageDto dto);

    List<SysAudit> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysAudit> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
