package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Map;

import com.hzjm.service.entity.SysProdDeal;
import com.hzjm.service.model.DTO.SysProdDealPageDto;
import com.hzjm.service.model.DTO.SysProdPageDto;
import com.hzjm.service.model.DTO.SysProdSaleDealDto;
import com.hzjm.service.model.VO.*;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品处理绑定关系 服务类
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface ISysProdDealService extends IService<SysProdDeal> {

    SysProdDeal getByIdWithoutLogic(Integer id);

    SysProdDealVo getDetail(Integer id);

    Boolean saveSysProdDeal(SysProdDeal dto);

    Boolean insertList(List<SysProdDeal> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProdDealListVo> searchList(SysProdDealPageDto dto);

    List<SysProdDeal> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdDeal> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<Integer> getProdIdList(int type, Integer relationId);

    List<SysProdDealListVo> dealList(Integer relationId, Integer type, SysProdDealPageDto dto);

    /**
     * 根据事件类型，对事件的商品清单各自进行组装
     * @param relationIdList 事件id集合
     * @param typeList       事件类型集合
     * @param prodIdList     待查询商品
     */
    Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealGroup(List<Integer> relationIdList, List<Integer> typeList, List<Integer> prodIdList);

    IPage<SysProdListVo> saleList(SysProdDealPageDto dto);

    List<SysProdGroupListVo> saleGroup(SysProdDealPageDto dto);

    IPage<SysProdListVo> outList(SysProdPageDto dto);

    List<SysProdGroupListVo> outGroup(SysProdPageDto dto);

    Boolean saveWareInfo(Integer relationId, List<SysProdDealListVo> prodList);

    Boolean dealSale(SysProdSaleDealDto dto);

    Boolean saleOut(List<Integer> prodIdList, Integer outId);

    Boolean dealOut(SysProdSaleDealDto dto);

    List<ShopEventListVo> eventList();

    Boolean cancel(List<Integer> prodIdList, Integer type);
}
