package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysProdTransfer;
import com.hzjm.service.model.DTO.SysProdTransferPageDto;
import com.hzjm.service.model.VO.SysProdTransferListVo;
import com.hzjm.service.model.VO.SysProdTransferVo;

import java.util.List;

/**
 * 平台内转移 服务类
 *
 * <AUTHOR>
 * @since 2023-02-27
 */
public interface ISysProdTransferService extends IService<SysProdTransfer> {

    SysProdTransfer getByIdWithoutLogic(Integer id);

    SysProdTransferVo getDetail(Integer id, String oddNo);

    Boolean saveSysProdTransfer(SysProdTransfer dto);

    Boolean insertList(List<SysProdTransfer> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProdTransferListVo> searchList(SysProdTransferPageDto dto);

    List<SysProdTransfer> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdTransfer> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    /**
     * 导出平台内转移列表
     *
     * @param dto      dto
     * @param language 语言
     * @return url
     */
    String exportProdTransferList(SysProdTransferPageDto dto, String language);
}
