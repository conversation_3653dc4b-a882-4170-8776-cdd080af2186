package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.ShopLabelAddress;
import com.hzjm.service.mapper.ShopLabelAddressMapper;
import com.hzjm.service.model.DTO.ShopLabelAddressPageDto;
import com.hzjm.service.model.VO.ShopLabelAddressListVo;
import com.hzjm.service.model.VO.ShopLabelAddressVo;
import com.hzjm.service.service.IShopLabelAddressService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * label 收件信息 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Service
public class ShopLabelAddressServiceImpl extends ServiceImpl<ShopLabelAddressMapper, ShopLabelAddress> implements IShopLabelAddressService {

    @Override
    public ShopLabelAddress getByIdWithoutLogic(Integer id) {
        ShopLabelAddress data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该label 收件信息"));
        }

        return data;
    }

    @Override
    public ShopLabelAddressVo getDetail(Integer id) {
        ShopLabelAddress data = getByIdWithoutLogic(id);

        ShopLabelAddressVo vo = new ShopLabelAddressVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveShopLabelAddress(ShopLabelAddress dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<ShopLabelAddressListVo> searchList(ShopLabelAddressPageDto dto) {

        LambdaQueryWrapper<ShopLabelAddress> qw = Wrappers.<ShopLabelAddress>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopLabelAddress::getGmtCreate)
                .eq(ShopLabelAddress::getShopId, JwtContentHolder.getUserId())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopLabelAddress::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopLabelAddress::getGmtCreate, endTime);

        IPage<ShopLabelAddress> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopLabelAddressListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopLabelAddressListVo vo = new ShopLabelAddressListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<ShopLabelAddressListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopLabelAddress> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopLabelAddress> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopLabelAddress> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
