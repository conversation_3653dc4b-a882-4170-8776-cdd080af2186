package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.service.entity.SysWareOutBatchUser;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysWareOutBatchUserPageDto;
import com.hzjm.service.model.VO.SysWareOutBatchUserListVo;
import com.hzjm.service.model.VO.SysWareOutBatchUserVo;
import com.hzjm.service.entity.SysWareOutBatchUser;
import com.hzjm.service.mapper.SysWareOutBatchUserMapper;
import com.hzjm.service.service.ISysWareOutBatchUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 出库批次任务情况 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-15
 */
@Slf4j
@Service
public class SysWareOutBatchUserServiceImpl extends ServiceImpl<SysWareOutBatchUserMapper, SysWareOutBatchUser> implements ISysWareOutBatchUserService {

    @Override
    public SysWareOutBatchUser getByIdWithoutLogic(Integer id) {
        SysWareOutBatchUser data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该出库批次任务情况"));
        }

        return data;
    }

    @Override
    public SysWareOutBatchUserVo getDetail(Integer id) {
        SysWareOutBatchUser data = getByIdWithoutLogic(id);

        SysWareOutBatchUserVo vo = new SysWareOutBatchUserVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysWareOutBatchUser(SysWareOutBatchUser dto) {
        if(ObjectUtils.isEmpty(dto.getBatchId()) || ObjectUtils.isEmpty(dto.getUserId())) {
            throw new BaseException(LanguageConfigService.i18nForMsg("任务情况不明"));
        }

        remove(Wrappers.<SysWareOutBatchUser>lambdaQuery().eq(SysWareOutBatchUser::getBatchId, dto.getBatchId()));
        dto.setCreateById(JwtContentHolder.getUserId());
        return baseMapper.insert(dto) > 0;
    }

    @Override
    public IPage<SysWareOutBatchUserListVo> searchList(SysWareOutBatchUserPageDto dto) {

        LambdaQueryWrapper<SysWareOutBatchUser> qw = Wrappers.<SysWareOutBatchUser>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysWareOutBatchUser::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysWareOutBatchUser::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysWareOutBatchUser::getGmtCreate, endTime);

        IPage<SysWareOutBatchUser> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysWareOutBatchUserListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysWareOutBatchUserListVo vo = new SysWareOutBatchUserListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysWareOutBatchUserListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysWareOutBatchUser> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysWareOutBatchUser> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysWareOutBatchUser> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
