package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysUpdatePriceEvents;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/18 16:41
 * @description: SysUpdatePriceEvents服务接口
 */
public interface ISysUpdatePriceEventsService extends IService<SysUpdatePriceEvents> {
    /**
     * 查询需要更新价格的事件
     *
     * @param minutes 时间间隔（分钟）
     * @param total   最大返回条数
     * @return 需要更新价格的事件列表
     */
    List<SysUpdatePriceEvents> findNeedToUpdatePriceEvents(Integer minutes, Integer total);

    /**
     * 成功-更新价格事件
     *
     * @param priceEvents 价格事件列表
     */
    void successUpdatePriceTasks(List<SysUpdatePriceEvents> priceEvents);

    /**
     * 失败-更新价格事件
     *
     * @param priceEvents 价格事件列表
     */
    void failUpdatePriceTasks(List<SysUpdatePriceEvents> priceEvents);

    /**
     * 清除-更新价格事件
     *
     * @param priceEvents 价格事件列表
     */
    void cleanUpdatePriceTasks(List<SysUpdatePriceEvents> priceEvents);
}
