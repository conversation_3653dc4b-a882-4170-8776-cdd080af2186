package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysSku;
import com.hzjm.service.model.DTO.SysSkuPageDto;
import com.hzjm.service.model.VO.SysSkuListVo;
import com.hzjm.service.model.VO.SysSkuRankListVo;
import com.hzjm.service.model.VO.SysSkuVo;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * sku池 服务类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface ISysSkuService extends IService<SysSku> {

    SysSku getByIdWithoutLogic(Integer id);

    SysSkuVo getDetail(Integer id);

    List<String> skuConvertSkuList(String sku);

    SysSku getDetail(String sku);

    Boolean saveSysSku(SysSku dto);

    Boolean insertList(List<SysSku> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysSkuListVo> searchList(SysSkuPageDto dto);

    List<SysSku> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysSku> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    /**
     * @param sortType 1-销量排名，2-热度排名，3-库存排名
     * @param shopId
     */
    List<SysSkuRankListVo> rankList(int sortType, Integer shopId);

    List<SysSku> getDistinctSkuAndSpec();

    List<SysSku> selectBySkuWithoutSpacesAndSpec(String sku, String spec);

    List<SysSku> selectSkuAfter(LocalDateTime time);

    /**
     * 批量更新额外数据
     *
     * @return 布尔值
     */
    Boolean batchUpdateExtraData(List<String> skuList);

    /**
     * 查询符合需求的sku 取第一个
     *
     * @param skus sku列表
     * @return map
     */
    Map<String, SysSku> selectFirstSkuForEachIndexed(List<String> skus);

    /**
     * 查询 符合需求的sku 取第一个
     *
     * @return
     */
    List<SysSku> queryDataGroupSkuAndSpec();

    /**
     * 获取商品名称,品牌
     *
     * @param sku         商品sku列表
     * @param ignoreSpace 是否包含空格（sku) 不包含(sku_indexed）
     * @return 商品名称列表
     */
    Map<String, String> getSkuCacheMap(String sku, Boolean ignoreSpace);
}
