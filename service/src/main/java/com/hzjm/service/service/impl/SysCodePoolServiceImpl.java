package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.AcquireTaskLock;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.SysCodePool;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.mapper.SysCodePoolMapper;
import com.hzjm.service.model.DTO.SysCodePoolPageDto;
import com.hzjm.service.model.VO.SysCodePoolListVo;
import com.hzjm.service.model.VO.SysCodePoolVo;
import com.hzjm.service.service.ISysCodePoolService;
import com.hzjm.service.service.ISysProdService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 编号池（每天0点清除） 服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-20
 */
@Slf4j
@Service
public class SysCodePoolServiceImpl extends ServiceImpl<SysCodePoolMapper, SysCodePool> implements ISysCodePoolService {

    @Autowired
    private ISysProdService iSysProdService;

    @Override
    public SysCodePool getByIdWithoutLogic(Integer id) {
        SysCodePool data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该编号池（每天0点清除）"));
        }

        return data;
    }

    @Override
    public SysCodePoolVo getDetail(Integer id) {
        SysCodePool data = getByIdWithoutLogic(id);

        SysCodePoolVo vo = new SysCodePoolVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysCodePool(SysCodePool dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysCodePoolListVo> searchList(SysCodePoolPageDto dto) {

        LambdaQueryWrapper<SysCodePool> qw = Wrappers.<SysCodePool>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysCodePool::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysCodePool::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysCodePool::getGmtCreate, endTime);

        IPage<SysCodePool> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysCodePoolListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysCodePoolListVo vo = new SysCodePoolListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysCodePoolListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysCodePool> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysCodePool> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysCodePool> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

    @Override
    @AcquireTaskLock(name = "buildCode", key = "#type", timeout = 10)
    synchronized public List<String> build(Integer type, Integer num) {
        if (ObjectUtils.isEmpty(type)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("编号类型不可知"));
        }

        if (ObjectUtils.isEmpty(num) || num <= 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("生成数量不可知"));
        }

        int retries = 0;
        int MAX_RETRY = 5;
        while (retries < MAX_RETRY) {
            try {
                return buildInner(type, num);
            } catch (Exception e) {
                retries++;
            }
        }

        throw new BaseException(LanguageConfigService.i18nForMsg("网络繁忙，请重试"));
    }

    private List<String> buildInner(Integer type, Integer num) {
        type = type == 4 || type == 3 ? 3 : type; // 与代发共用同一批编号

        String dateStr = DateTimeUtils.format(DateTimeUtils.sdfCode, DateTimeUtils.getNow());
        int count = count(Wrappers.<SysCodePool>lambdaQuery().eq(SysCodePool::getDateStr, dateStr).eq(SysCodePool::getType, type));

        List<String> codeList = new ArrayList<>();
        List<SysCodePool> poolList = new ArrayList<>();

        // 逢100，多生成1个code，但是不返回

        Random r = new Random();
        for (int i = 0; codeList.size() < num; i++) {
            int charNum = count / 100;
            if (count != 0 && count % 100 == 0) {
//                num++; // 00不采用
            }

            count++;

            StringBuffer code = new StringBuffer("");
            // -1-仓库出库批次，1-oneId，2-批次编号，3~7-审批流程
            switch (type) {
                case -1:
                    // -1.仓库出库批次：OP+yyyyMMdd+(a~z)+(01~99)
                    code.append("OP");
                    break;
                case 1:
                    // 1.ONE id：OC+yyyyMMdd+(a~z)+(01~99)
                    code.append("OC");
                    break;
                case 2:
                    // 2.批次编号：JK+yyyyMMdd+(a~z)+(01~99)
                    code.append("JK");
                    break;
                case SysProdEvent.TypeSend:
                    // 3.代发：CK+yyyyMMdd+(a~z)+(01~99)
                    code.append("CK");
                    break;
                case SysProdEvent.TypeTransport:
                    // 4.转运：CK+yyyyMMdd+(a~z)+(01~99)
                    code.append("CK");
                    break;
                case SysProdEvent.TypeCash:
                    // 5.套现审核：TX+yyyyMMdd+(a~z)+(01~99)
                    code.append("TX");
                    break;
                case SysProdEvent.TypeSale:
                    // 6.寄卖审核：JM+yyyyMMdd+(a~z)+(01~99)
                    code.append("JM");
                    break;
                case SysProdEvent.TypeTransfer:
                    // 7.平台内转移：ZY+yyyyMMdd+(a~z)+(01~99)
                    code.append("ZY");
                    break;
                case SysProdEvent.TypeSwitch:
                    // 16.平台转仓：ZC+yyyyMMdd+(a~z)+(01~99)
                    code.append("ZC");
                    break;
                case SysProdEvent.TypePlatOut:
                    // 14.平台出库：OT+yyyyMMdd+(a~z)+(01~99)
                    code.append("OT");
                    break;
                case 21:
                    // 21.入库批次编号[shopping]
                    code.append("SP");
                    break;
                case 22:
                    // 22.入库批次编号[dropoff]
                    code.append("DO");
                    break;
                case 23:
                    // 23. Cross Listing 打包的 Boxing ID
                    code.append("BOXED");
                    break;
                case 24:
                    // 24. 平台退回商品
                    code.append("RT");
                    break;
                case 25: //stockX 预报
                    code.append("FX");
                    break;
                case 26: // 批量发货
                    code.append("BR");
                    break;
                case 27: // ups 批次号
                    code.append("LG");
                    break;
                case 28: // 维修单
                    code.append("WX");
                    break;
                case 29: // 维系批次
                    code.append("RP");
                    break;
            }

            if (type == 1) {
                code.append(BaseUtils.getRandomStr(12));
            } else {
                /*
                // 日期
                code.append(dateStr);

                // a~z
                int asc = 97 + charNum;
                if (asc > 122) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("当日生成次数已达上限"));
                }
                code.append((char) asc);
                // 序号
                code.append(String.format("%02d", count % 100));
                */
                code.append(String.format("%08d", r.nextInt(99999999)));
            }

            SysCodePool pool = new SysCodePool();
            pool.setCode(code.toString());
            pool.setType(type);
            pool.setDateStr(dateStr);
            poolList.add(pool);

            if (count % 100 != 0) {
                codeList.add(code.toString());
            }
        }

        if (count(Wrappers.<SysCodePool>lambdaQuery().in(SysCodePool::getCode, poolList.stream().map(SysCodePool::getCode).collect(Collectors.toList()))) > 0) {
            throw new BaseException(LanguageConfigService.i18nForMsg("网络繁忙，请重试"));
        }

        if (type == 1) {
            if (iSysProdService.count(Wrappers.<SysProd>lambdaQuery().in(SysProd::getOneId, codeList)) > 0) {
                throw new BaseException(LanguageConfigService.i18nForMsg("网络繁忙，请重试"));
            }
        }

        insertList(poolList);

        return codeList;
    }

}
