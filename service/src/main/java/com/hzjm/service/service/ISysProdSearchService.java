package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.model.TableDataSearchDto;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdSearch;
import com.hzjm.service.model.DTO.SysProdSaveDto;
import com.hzjm.service.model.DTO.SysProdSearchPageDto;
import com.hzjm.service.model.VO.ShopProdRankListVo;
import com.hzjm.service.model.VO.SysProdSearchListVo;
import com.hzjm.service.model.VO.SysProdSearchVo;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品筛选 服务类
 *
 * <AUTHOR>
 * @since 2023-07-28
 */
public interface ISysProdSearchService extends IService<SysProdSearch> {

    SysProdSearch getByIdWithoutLogic(Integer id);

    SysProdSearchVo getDetail(Integer id);

    Boolean saveSysProdSearch(SysProdSearch dto);

    Boolean insertList(List<SysProdSearch> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProdSearchListVo> searchList(SysProdSearchPageDto dto);

    List<SysProdSearch> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdSearch> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    BigDecimal sumCost(LambdaQueryWrapper<SysProdSearch> qw);

    /**
     * @param type 1-入库商品，2-出库商品
     */
    List<ShopProdRankListVo> shopProdRank(TableDataSearchDto dto, int type);

    Date getOldGmtInByoneId(String oneId);

    @Transactional
    Boolean saveSysSearchProd(SysProd dto, SysProdSaveDto sysProdSaveDto);
}
