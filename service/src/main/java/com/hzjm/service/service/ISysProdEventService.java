package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysProdEvent;
import com.hzjm.service.model.DTO.SysProdEventPageDto;
import com.hzjm.service.model.VO.SysProdEventListVo;
import com.hzjm.service.model.VO.SysProdEventVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品事件 服务类
 *
 * <AUTHOR>
 * @since 2023-02-21
 */
public interface ISysProdEventService extends IService<SysProdEvent> {

    SysProdEvent getByIdWithoutLogic(Integer id);

    SysProdEventVo getDetail(Integer id);

    Boolean saveSysProdEvent(SysProdEvent dto);

    Boolean insertList(List<SysProdEvent> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysProdEventListVo> searchList(SysProdEventPageDto dto);

    List<SysProdEvent> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdEvent> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    Boolean relateSale(List<Integer> prodIdList);
}
