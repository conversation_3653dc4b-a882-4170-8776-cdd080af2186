package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysProdSwitch;
import com.hzjm.service.model.DTO.SysProdSwitchPageDto;
import com.hzjm.service.model.VO.SysProdSwitchListVo;
import com.hzjm.service.model.VO.SysProdSwitchVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 转仓 服务类
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
public interface ISysProdSwitchService extends IService<SysProdSwitch> {

    SysProdSwitch getByIdWithoutLogic(Integer id);

    SysProdSwitchVo getDetail(Integer id);

    Boolean saveSysProdSwitch(SysProdSwitch dto);

    Boolean insertList(List<SysProdSwitch> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysProdSwitchListVo> searchList(SysProdSwitchPageDto dto);

    List<SysProdSwitch> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdSwitch> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
