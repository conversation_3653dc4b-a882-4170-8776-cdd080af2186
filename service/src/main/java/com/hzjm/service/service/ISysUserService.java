package com.hzjm.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.common.model.HttpPageResult;
import com.hzjm.service.entity.SysUser;
import com.hzjm.service.model.DTO.SysUserPageDto;
import com.hzjm.service.model.VO.SysWareUserDto;

import java.util.List;

/**
 * 后管用户 服务类
 *
 * <AUTHOR>
 * @since 2020-11-13
 */
public interface ISysUserService extends IService<SysUser> {

    SysUser getByIdWithoutLogic(Integer id);

    Boolean saveSysUser(SysUser dto);

    List<SysUser> listAll();

    HttpPageResult<SysUser> searchList(SysUserPageDto dto);

    boolean isAdmin(Integer userId);

    boolean isAdminer(Integer userId);

    /**
     * 查询当前仓库下拥有权限的用户列表
     *
     * @return 用户列表
     */
    List<SysWareUserDto> querySysWareUserList();
}
