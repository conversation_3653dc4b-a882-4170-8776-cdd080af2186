package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.entity.ScheduleJob;
import com.hzjm.service.mapper.ScheduleJobMapper;
import com.hzjm.service.service.IScheduleJobService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 定时任务 服务实现类
 *
 * <AUTHOR>
 * @since 2021-04-15
 */
@Slf4j
@Service
public class ScheduleJobServiceImpl extends ServiceImpl<ScheduleJobMapper, ScheduleJob> implements IScheduleJobService {

    @Override
    public ScheduleJob getByIdWithoutLogic(Long id) {
        ScheduleJob data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该记录"));
        }

        return data;
    }

    @Override
    public ScheduleJob getDetail(Long id) {
        ScheduleJob data = getByIdWithoutLogic(id);

        return data;
    }

    @Override
    public Boolean saveScheduleJob(ScheduleJob dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getJobId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getJobId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

}
