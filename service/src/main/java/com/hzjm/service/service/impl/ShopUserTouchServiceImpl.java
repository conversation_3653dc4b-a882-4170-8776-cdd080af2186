package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.ShopUserTouchPageDto;
import com.hzjm.service.model.VO.ShopUserTouchListVo;
import com.hzjm.service.model.VO.ShopUserTouchVo;
import com.hzjm.service.entity.ShopUserTouch;
import com.hzjm.service.mapper.ShopUserTouchMapper;
import com.hzjm.service.service.IShopUserTouchService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 商家的touch账号 服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-16
 */
@Slf4j
@Service
public class ShopUserTouchServiceImpl extends ServiceImpl<ShopUserTouchMapper, ShopUserTouch> implements IShopUserTouchService {

    @Value("${touch.apiUrl}")
    public String apiUrl;

    @Autowired
    private IShopUserService iShopUserService;

    @Override
    public ShopUserTouch getByIdWithoutLogic(Integer id) {
        ShopUserTouch data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该商家的touch账号"));
        }

        return data;
    }

    @Override
    public ShopUserTouchVo getDetail(Integer id, Integer shopId) {
        ShopUserTouch data;
        if (!ObjectUtils.isEmpty(id)) {
            data = getByIdWithoutLogic(id);
        } else {
            data = getOne(Wrappers.<ShopUserTouch>lambdaQuery().eq(ShopUserTouch::getShopId, shopId));
        }

        if (ObjectUtils.isEmpty(data)) {
            data = new ShopUserTouch();
            data.setShopId(shopId);
        }

        ShopUserTouchVo vo = new ShopUserTouchVo();
        BeanUtils.copyProperties(data, vo);

        ShopUser shop = iShopUserService.getById(data.getShopId());
        if (!ObjectUtils.isEmpty(shop)) {
            vo.setLinkUrl(apiUrl + "/linkOcAccount?erpUserId=" + shop.getId() + "&erpUserTag=" + shop.getUid());
        }
        return vo;
    }

    @Override
    public Boolean saveShopUserTouch(ShopUserTouch dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<ShopUserTouchListVo> searchList(ShopUserTouchPageDto dto) {

        LambdaQueryWrapper<ShopUserTouch> qw = Wrappers.<ShopUserTouch>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopUserTouch::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopUserTouch::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopUserTouch::getGmtCreate, endTime);

        IPage<ShopUserTouch> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<ShopUserTouchListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ShopUserTouchListVo vo = new ShopUserTouchListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<ShopUserTouchListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopUserTouch> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopUserTouch> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<ShopUserTouch> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
