package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysOperatedMetrics;
import com.hzjm.service.model.DTO.SysOperatedMetricsPageDto;
import com.hzjm.service.model.VO.SysOperatedMetricsGroupByTeamVo;
import com.hzjm.service.model.VO.SysOperatedMetricsListVo;
import com.hzjm.service.model.VO.SysOperatedMetricsVo;

import java.util.List;

/**
 * 自营数据表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-09
 */
public interface ISysOperatedMetricsService extends IService<SysOperatedMetrics> {

    SysOperatedMetrics getByIdWithoutLogic(Integer id);

    SysOperatedMetricsVo getDetail(Integer id);

    Boolean saveSysOperatedMetrics(SysOperatedMetrics dto);

    Boolean insertList(List<SysOperatedMetrics> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysOperatedMetricsListVo> searchList(SysOperatedMetricsPageDto dto);

    List<SysOperatedMetrics> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysOperatedMetrics> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    List<SysOperatedMetricsGroupByTeamVo> queryMetricsList();

    Boolean refreshMetrics();
}
