package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysWareOutBatch;
import com.hzjm.service.entity.SysWareOutBatchProd;
import com.hzjm.service.model.DTO.SysWareOutBatchPageDto;
import com.hzjm.service.model.VO.SysWareOutBatchListVo;
import com.hzjm.service.model.VO.SysWareOutBatchVo;

import java.util.List;

/**
 * 出库批次 服务类
 *
 * <AUTHOR>
 * @since 2023-05-24
 */
public interface ISysWareOutBatchService extends IService<SysWareOutBatch> {

    SysWareOutBatch getByIdWithoutLogic(Integer id);

    SysWareOutBatchVo getDetail(Integer id);

    Boolean saveSysWareOutBatch(SysWareOutBatch dto);

    Boolean insertList(List<SysWareOutBatch> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysWareOutBatchListVo> searchList(SysWareOutBatchPageDto dto);

    List<SysWareOutBatch> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysWareOutBatch> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

    /**
     * @param type         操作类型，1-拣货，2-打包
     * @param batchId
     * @param relationType 1-批次id，2-出库单id
     */
    Boolean scanByOneId(String oneId, Integer type, Integer batchId, Integer relationType);

    Boolean lose(Integer id, Integer prodId, Integer outId);

    Boolean dealLose(Integer prodId, Integer type, Integer dealType);

    /**
     * @param status 4-部分出库，5-完全出库
     */
    void out(List<SysWareOutBatchProd> batchProdList,  int status);

    Integer sumProdNum(SysWareOutBatchPageDto dto);

}
