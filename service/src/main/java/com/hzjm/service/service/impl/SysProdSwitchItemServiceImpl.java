package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysProdSwitchItemMapper;
import com.hzjm.service.model.DTO.SysProdSwitchItemPageDto;
import com.hzjm.service.model.DTO.SysWareShelvesDealDto;
import com.hzjm.service.model.VO.SysProdSwitchItemCountVo;
import com.hzjm.service.model.VO.SysProdSwitchItemListVo;
import com.hzjm.service.model.VO.SysProdSwitchItemVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 转仓明细 服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-18
 */
@Slf4j
@Service
public class SysProdSwitchItemServiceImpl extends ServiceImpl<SysProdSwitchItemMapper, SysProdSwitchItem> implements ISysProdSwitchItemService {

    @Autowired
    private ISysProdSwitchService iSysProdSwitchService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISysWareService iSysWareService;

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdSearchService iSysProdSearchService;

    @Autowired
    private ISysWareShelvesService iSysWareShelvesService;

    @Autowired
    private ISysWareShelvesProdService iSysWareShelvesProdService;

    @Override
    public SysProdSwitchItem getByIdWithoutLogic(Integer id) {
        SysProdSwitchItem data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该转仓明细"));
        }

        return data;
    }

    @Override
    public SysProdSwitchItemVo getDetail(Integer id) {
        SysProdSwitchItem data = getByIdWithoutLogic(id);

        SysProdSwitchItemVo vo = new SysProdSwitchItemVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysProdSwitchItem(SysProdSwitchItem dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    @ReadOnly
    public IPage<SysProdSwitchItemListVo> searchList(SysProdSwitchItemPageDto dto) {

        LambdaQueryWrapper<SysProdSwitchItem> qw = Wrappers.<SysProdSwitchItem>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(SysProdSwitchItem::getGmtCreate)
                .in(!ObjectUtils.isEmpty(dto.getSkuList()), SysProdSwitchItem::getSku, dto.getSkuList())
                .in(!ObjectUtils.isEmpty(dto.getSpecList()), SysProdSwitchItem::getSpec, dto.getSpecSearchList())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), SysProdSwitchItem::getStatus, dto.getStatus())
                .eq(!ObjectUtils.isEmpty(dto.getOldWareId()), SysProdSwitchItem::getOldWareId, dto.getOldWareId())
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysProdSwitchItem::getId, dto.getIdList())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysProdSwitchItem::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysProdSwitchItem::getGmtCreate, endTime);

        Integer wareId = JwtContentHolder.getWareId();
        if (!ObjectUtils.isEmpty(wareId) && !ObjectUtils.isEmpty(dto.getStatus())) {
            switch (dto.getStatus()) {
                case 3:
                    dto.setNewWareId(wareId);
                    break;
                default:
                    qw.eq(SysProdSwitchItem::getOldWareId, wareId);
            }
        }

        if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
            StringBuffer sb = new StringBuffer();
            dto.getOneIdList().forEach(oneId -> {
                sb.append("(one_id like '%" + oneId + "%') or ");
            });
            qw.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
        }

        if (!ObjectUtils.isEmpty(dto.getNewWareId())) {
            List<SysProdSwitch> switchList = iSysProdSwitchService.list(Wrappers.<SysProdSwitch>lambdaQuery().eq(SysProdSwitch::getNewWareId, dto.getNewWareId()));
            List<Integer> switchIdList = BaseUtils.initList();
            switchIdList.addAll(switchList.stream().map(SysProdSwitch::getId).collect(Collectors.toList()));
            qw.in(SysProdSwitchItem::getSwitchId, switchIdList);
            switchList.clear();
        }

        IPage<SysProdSwitchItem> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysProdSwitchItemListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<SysUser> userList = iSysUserService.list();
            Map<Integer, String> userMap = userList.stream().filter(a -> !ObjectUtils.isEmpty(a.getNickname())).collect(Collectors.toMap(SysUser::getId, SysUser::getNickname));
            userList.clear();

            List<SysWare> wareList = iSysWareService.list();
            Map<Integer, String> wareMap = wareList.stream().collect(Collectors.toMap(SysWare::getId, SysWare::getName));
            wareList.clear();

            List<SysProdSwitch> switchList = iSysProdSwitchService.list(Wrappers.<SysProdSwitch>lambdaQuery().in(SysProdSwitch::getId, pageResult.getRecords().stream().map(SysProdSwitchItem::getSwitchId).collect(Collectors.toList())));
            Map<Integer, Integer> switchMap = switchList.stream().collect(Collectors.toMap(SysProdSwitch::getId, SysProdSwitch::getNewWareId));
            switchList.clear();

            List<SysProd> prodList = iSysProdService.list(Wrappers.<SysProd>lambdaQuery()
                    .in(SysProd::getId, pageResult.getRecords().stream().map(SysProdSwitchItem::getProdId).collect(Collectors.toList())));
            Map<Integer, SysProd> prodMap = prodList.stream().collect(Collectors.toMap(SysProd::getId, a -> a));
            prodList.clear();

            List<SysWareShelves> shelvesList = iSysWareShelvesService.list();
            Map<Integer, String> shelvesMap = shelvesList.stream().collect(Collectors.toMap(SysWareShelves::getId, SysWareShelves::getName));

            pageResult.getRecords().forEach(data -> {
                SysProdSwitchItemListVo vo = new SysProdSwitchItemListVo();
                BeanUtils.copyProperties(data, vo);

                switch (data.getStatus()) {
                    case 2:
                        vo.setOperator(userMap.get(data.getOperateScanId()));
                        break;
                    case 3:
                        vo.setOperator(userMap.get(data.getOperateOutId()));
                        break;
                    case 4:
                        vo.setOperator(userMap.get(data.getOperateInId()));
                        break;
                }

                SysProd prod = prodMap.get(data.getProdId());
                if (!ObjectUtils.isEmpty(prod)) {
                    vo.setRemarks(prod.getRemarks());
                    vo.setImg(prod.getImg());
                    vo.setSpec(prod.getSpec());
                }

                vo.setShelvesName(shelvesMap.get(data.getOldShelvesId()));
                vo.setOldWareName(wareMap.get(data.getOldWareId()));
                vo.setNewWareName(Optional.ofNullable(switchMap.get(data.getSwitchId())).map(newWareId -> wareMap.get(newWareId)).orElse(""));

                voList.add(vo);
            });
        }

        IPage<SysProdSwitchItemListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysProdSwitchItem> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysProdSwitchItem> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public Boolean release(List<Integer> prodIdList) {
        if (ObjectUtils.isEmpty(prodIdList)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("未选中任意商品"));
        }
        // 移除转仓商品
        remove(Wrappers.<SysProdSwitchItem>lambdaQuery()
                .eq(SysProdSwitchItem::getSearchType, 1)
                .in(SysProdSwitchItem::getStatus, 1, 2)
                .in(SysProdSwitchItem::getProdId, prodIdList));

        // 同步状态
        iSysProdSearchService.update(Wrappers.<SysProdSearch>lambdaUpdate()
                .set(SysProdSearch::getTransferStatus, 1)
                .eq(SysProdSearch::getSearchType, 1)
                .in(SysProdSearch::getProdId, prodIdList));

        return true;
    }

    @Override
    @ReadOnly
    public SysProdSwitchItemCountVo getCount(SysProdSwitchItemPageDto dto) {

        dto.setCurrent(0);
        dto.setSize(0);
        dto.setStatus(null);

        // 数量统计
        SysProdSwitchItemCountVo vo = new SysProdSwitchItemCountVo();

        SysProdSwitchItemPageDto dto1 = new SysProdSwitchItemPageDto();
        BeanUtils.copyProperties(dto, dto1);
        dto1.setStatus(1);
        vo.setNum1((int) searchList(dto1).getTotal());

        SysProdSwitchItemPageDto dto2 = new SysProdSwitchItemPageDto();
        BeanUtils.copyProperties(dto, dto2);
        dto2.setStatus(2);
        vo.setNum2((int) searchList(dto2).getTotal());

        SysProdSwitchItemPageDto dto3 = new SysProdSwitchItemPageDto();
        BeanUtils.copyProperties(dto, dto3);
        dto3.setStatus(3);
        vo.setNum3((int) searchList(dto3).getTotal());

        SysProdSwitchItemPageDto dto4 = new SysProdSwitchItemPageDto();
        BeanUtils.copyProperties(dto, dto4);
        dto4.setStatus(4);
        vo.setNum4((int) searchList(dto4).getTotal());

        return vo;
    }

    @Override
    public Boolean scanByOneId(String oneId, Integer status) {
        SysProdSwitchItem item = getOne(Wrappers.<SysProdSwitchItem>lambdaQuery().eq(SysProdSwitchItem::getSearchType, 1).eq(SysProdSwitchItem::getOneId, oneId));
        if (ObjectUtils.isEmpty(item)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("无效oneId"));
        }

        if (item.getStatus().intValue() != status - 1) {
            throw new BaseException(LanguageConfigService.i18nForMsg("商品状态不同步"));
        }

        Date now = DateTimeUtils.getNow();

        UpdateWrapper qw = new UpdateWrapper();
        qw.eq("search_type", 1);
        qw.eq("del_flag", 0);
        qw.eq("prod_id", item.getProdId());

        switch (status) {
            case 2:
                item.setGmtScan(now);
                item.setOperateScanId(JwtContentHolder.getUserId());

                // 商品下架
                SysWareShelvesDealDto off = new SysWareShelvesDealDto();
                off.setType(2);
                off.setProdIdList(new ArrayList<>(Arrays.asList(item.getProdId())));
                iSysWareShelvesProdService.batchDeal(off);
                break;
            case 3:
                item.setGmtOut(now);
                item.setOperateOutId(JwtContentHolder.getUserId());

                // 同步状态：商品出库
                qw.setSql("ware_id = null");
                iSysProdSearchService.update(qw);
                break;
            case 4:
                SysProdSwitch odd = iSysProdSwitchService.getById(item.getSwitchId());
                if (ObjectUtils.isEmpty(odd)) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("转仓已失效"));
                }
                if (odd.getNewWareId().intValue() != JwtContentHolder.getWareId()) {
                    throw new BaseException(LanguageConfigService.i18nForMsg("扫描失败，目标仓库错误"));
                }

                item.setGmtIn(now);
                item.setOperateInId(JwtContentHolder.getUserId());
                // 同步状态：商品变更仓库
                qw.setSql("transfer_status = 1, ware_id = " + odd.getNewWareId());
                iSysProdSearchService.update(qw);

                iSysProdService.update(Wrappers.<SysProd>lambdaUpdate()
                        .eq(SysProd::getId, item.getProdId())
                        .isNotNull(SysProd::getWareId)
                        .set(SysProd::getWareId, odd.getNewWareId()));

                break;
        }

        item.setStatus(status);
        return updateById(item);
    }

    @Override
    public List<SysProdSwitchItem> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
