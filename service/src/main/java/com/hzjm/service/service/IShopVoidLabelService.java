package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopVoidLabel;
import com.hzjm.service.model.DTO.ShopVoidLabelPageDto;
import com.hzjm.service.model.VO.ShopVoidLabelListVo;
import com.hzjm.service.model.VO.ShopVoidLabelVo;

import java.util.List;

/**
 * 已撤销的label记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface IShopVoidLabelService extends IService<ShopVoidLabel> {

    ShopVoidLabel getByIdWithoutLogic(Integer id);

    ShopVoidLabelVo getDetail(Integer id);

    Boolean saveShopVoidLabel(ShopVoidLabel dto);

    Boolean insertList(List<ShopVoidLabel> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopVoidLabelListVo> searchList(ShopVoidLabelPageDto dto);

    List<ShopVoidLabel> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopVoidLabel> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
