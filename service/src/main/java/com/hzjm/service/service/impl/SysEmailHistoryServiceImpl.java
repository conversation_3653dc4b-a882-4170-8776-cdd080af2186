package com.hzjm.service.service.impl;

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.SysEmailHistoryPageDto;
import com.hzjm.service.model.VO.SysEmailHistoryListVo;
import com.hzjm.service.model.VO.SysEmailHistoryVo;
import com.hzjm.service.entity.SysEmailHistory;
import com.hzjm.service.mapper.SysEmailHistoryMapper;
import com.hzjm.service.service.ISysEmailHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * 邮件历史表 服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-07
 */
@Slf4j
@Service
public class SysEmailHistoryServiceImpl extends ServiceImpl<SysEmailHistoryMapper, SysEmailHistory> implements ISysEmailHistoryService {

    @Override
    public SysEmailHistory getByIdWithoutLogic(Integer id) {
        SysEmailHistory data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该邮件历史表"));
        }

        return data;
    }

    @Override
    public SysEmailHistoryVo getDetail(Integer id) {
        SysEmailHistory data = getByIdWithoutLogic(id);

        SysEmailHistoryVo vo = new SysEmailHistoryVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysEmailHistory(SysEmailHistory dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysEmailHistoryListVo> searchList(SysEmailHistoryPageDto dto) {

        LambdaQueryWrapper<SysEmailHistory> qw = Wrappers.<SysEmailHistory>lambdaQuery();


        IPage<SysEmailHistory> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysEmailHistoryListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                SysEmailHistoryListVo vo = new SysEmailHistoryListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<SysEmailHistoryListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysEmailHistory> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {


            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysEmailHistory> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysEmailHistory> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
