package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.CurrencyCode;
import com.hzjm.service.entity.LabelCenterStatus;
import com.hzjm.service.entity.ShopLabelCenter;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.mapper.ShopLabelCenterMapper;
import com.hzjm.service.model.DTO.ShopLabelCenterPageDto;
import com.hzjm.service.model.VO.ShopLabelCenterListVo;
import com.hzjm.service.model.VO.ShopLabelCenterVo;
import com.hzjm.service.service.IShopLabelCenterService;
import com.hzjm.service.service.IShopUserService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * label 中心 服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
@Slf4j
@Service
public class ShopLabelCenterServiceImpl extends ServiceImpl<ShopLabelCenterMapper, ShopLabelCenter> implements IShopLabelCenterService {

    @Resource
    IShopUserService iShopUserService;

    @Override
    public ShopLabelCenter getByIdWithoutLogic(Integer id) {
        ShopLabelCenter data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该label 中心"));
        }

        return data;
    }

    @Override
    public ShopLabelCenterVo getDetail(Integer id) {
        ShopLabelCenter data = getByIdWithoutLogic(id);

        ShopLabelCenterVo vo = new ShopLabelCenterVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveShopLabelCenter(ShopLabelCenter dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    public LambdaQueryWrapper<ShopLabelCenter> buildLambdaQueryWrapper(ShopLabelCenterPageDto dto) {
        LambdaQueryWrapper<ShopLabelCenter> qw = Wrappers.<ShopLabelCenter>lambdaQuery();

        Date endTime = dto.dealEndTime();
        qw.orderByDesc(ShopLabelCenter::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ShopLabelCenter::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ShopLabelCenter::getGmtCreate, endTime)
                .in(!ObjectUtils.isEmpty(dto.getTrackingNoList()), ShopLabelCenter::getTrackingNo, dto.getTrackingNoList())
                .in(!ObjectUtils.isEmpty(dto.getBatchNoList()), ShopLabelCenter::getBatchNo, dto.getBatchNoList())
                .in(!ObjectUtils.isEmpty(dto.getCarrierList()), ShopLabelCenter::getCarrier, dto.getCarrierList())
                .in(!ObjectUtils.isEmpty(dto.getStatusList()), ShopLabelCenter::getStatus, dto.getStatusList())
                .eq(!ObjectUtils.isEmpty(dto.getTrackingNo()), ShopLabelCenter::getTrackingNo, dto.getTrackingNo())
                .eq(!ObjectUtils.isEmpty(dto.getBatchNo()), ShopLabelCenter::getBatchNo, dto.getBatchNo())
                .eq(!ObjectUtils.isEmpty(dto.getCarrier()), ShopLabelCenter::getCarrier, dto.getCarrier())
                .eq(!ObjectUtils.isEmpty(dto.getStatus()), ShopLabelCenter::getStatus, dto.getStatus())
                .eq(!ObjectUtils.isEmpty(dto.getUid()), ShopLabelCenter::getShopUid, dto.getUid())
                .in(!ObjectUtils.isEmpty(dto.getUidList()), ShopLabelCenter::getShopUid, dto.getUidList())
                .and(!ObjectUtils.isEmpty(dto.getSearchValue())
                        , qw1 -> qw1.like(ShopLabelCenter::getTrackingNo, dto.getSearchValue())
                                .or()
                                .like(ShopLabelCenter::getBatchNo, dto.getSearchValue())
                )
        ;
        // 商家
        if (!ObjectUtils.isEmpty(JwtContentHolder.getRoleType()) && JwtContentHolder.getRoleType() == 5) {
            qw.eq(!ObjectUtils.isEmpty(JwtContentHolder.getUserId()), ShopLabelCenter::getShopId, JwtContentHolder.getUserId());
        }

        // 管理
        /*if (!ObjectUtils.isEmpty(JwtContentHolder.getRoleType()) && JwtContentHolder.getRoleType() == 1) {
            // xxxx 无需限制条件，查询全部上架
            if (JwtContentHolder.getUserId() != 1) {
                // 获取当前用户有权限的商家ID集合
                Set<Integer> set = iShopUserService.shopListByUserId(JwtContentHolder.getUserId());
                qw.in(!ObjectUtils.isEmpty(set), ShopLabelCenter::getShopId, set);
            }
        }*/

        return qw;
    }

    @Override
    @ReadOnly
    public IPage<ShopLabelCenterListVo> searchList(ShopLabelCenterPageDto dto) {
        List<ShopLabelCenterListVo> voList = new ArrayList<>();
        LambdaQueryWrapper<ShopLabelCenter> qw = this.buildLambdaQueryWrapper(dto);
        IPage<ShopLabelCenter> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }
        // 处理返回结果
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> shopIds = pageResult.getRecords().stream().map(ShopLabelCenter::getShopId).distinct().collect(Collectors.toList());
            List<ShopUser> shopUserLists = new ArrayList<>(iShopUserService.listByIds(shopIds));
            Map<Integer, String> shopMap = shopUserLists.stream()
                    .collect(Collectors.toMap(ShopUser::getId, ShopUser::getRealname, (existing, replacement) -> existing));
            if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
                pageResult.getRecords().forEach(data -> {
                    ShopLabelCenterListVo vo = new ShopLabelCenterListVo();
                    BeanUtils.copyProperties(data, vo);
                    vo.setShopName(shopMap.get(data.getShopId()));//商家名称
                    voList.add(vo);
                });
            }
        }

        IPage<ShopLabelCenterListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<ShopLabelCenter> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<ShopLabelCenter> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public String exportExcel(ShopLabelCenterPageDto dto) {

        boolean isAdmin = false;
        if (JwtContentHolder.getRoleType() == 1) {
            isAdmin = true;
        }

        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        List<String> shopHeaders = new LinkedList<>(Arrays.asList(
                "物流单号", "订单号", "标签费用", "收货人", "状态", "承运商", "购买时间","保价金额","签名","保价费","签名费","备注"
        ));

        List<String> adminHeaders = new LinkedList<>(Arrays.asList(
                "购买时间", "物流单号", "订单号", "识别码", "商家姓名", "发货地址", "收货地址", "尺寸", "重量", "UPS 账号", "状态", "承运商", "服务类型", "KNET 扣费", "商家扣费"
                ,"保价金额","签名","保价费","签名费","备注"
        ));

        if (isAdmin) {
            dataList.add(LanguageConfigService.i18nForMsg(adminHeaders));
        } else {
            dataList.add(LanguageConfigService.i18nForMsg(shopHeaders));
        }
        //填充数据
        List<ShopLabelCenterListVo> entityList = this.searchList(dto).getRecords();

        for (ShopLabelCenterListVo data : entityList) {
            List<String> cowList = new ArrayList<>();

            if (!isAdmin) { // 商家端
                cowList.add(data.getTrackingNo());
                cowList.add(data.getBatchNo());
                cowList.add(data.getShopLabelFee() != null ? data.getShopLabelFee().toString() : "");
                cowList.add(data.getRecipientName());
                cowList.add(data.getStatus() != null ? data.getStatus().getCode() : "");
                cowList.add(data.getCarrier() != null ? data.getCarrier().getCode() : "");
                cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtCreate()));
                cowList.add(data.getInsuranceFee() != null ? data.getInsuranceFee().toString() : "");
                cowList.add(data.getSignature() != null ? data.getSignature().getValue() : "");
                cowList.add(data.getShopInsuranceFee() != null ? data.getShopInsuranceFee().toString() : "");
                cowList.add(data.getShopSignatureFee() != null ? data.getShopSignatureFee().toString() : "");
                cowList.add(data.getReference());

            } else { // 管理端
                cowList.add(DateTimeUtils.formatToEST(DateTimeUtils.sdfTime, data.getGmtCreate()));
                cowList.add(data.getTrackingNo());
                cowList.add(data.getBatchNo());
                cowList.add(data.getShopUid());
                cowList.add(data.getShopName());
                cowList.add(data.getShipperCountry() + " , " + data.getShipperProvince() + " , " + data.getShipperCity() + " , " + data.getShipperDetail());
                cowList.add(data.getRecipientCountry() + " , " + data.getRecipientProvince() + " , " + data.getRecipientCity() + " , " + data.getRecipientDetail());
                cowList.add(data.getLength() + " * " + data.getWidth() + " * " + data.getHeight() + " " + data.getSizeUnit());
                cowList.add(data.getWeight1() + " " + data.getWeight1Unit());
                cowList.add(data.getCarrierAccount());
                cowList.add(data.getStatus().getCode());
                cowList.add("UPS");
                cowList.add(data.getCarrier().getCode());
                cowList.add(data.getLabelFee() != null ? data.getLabelFee().toString() : "");
                cowList.add(data.getShopLabelFee() != null ? data.getShopLabelFee().toString() : "");
                cowList.add(data.getInsuranceFee() != null ? data.getInsuranceFee().toString() : "");
                cowList.add(data.getSignature() != null ? data.getSignature().getValue() : "");
                cowList.add(data.getShopInsuranceFee() != null ? data.getShopInsuranceFee().toString() : "");
                cowList.add(data.getShopSignatureFee() != null ? data.getShopSignatureFee().toString() : "");
                cowList.add(data.getReference());

            }

            dataList.add(cowList);
        }
        String url = null;
        try {
            url = ExcelReader.generateExcelFile(dataList,
                    LanguageConfigService.i18nForMsg("标签中心") + "-" + DateTimeUtils.getFileSuffix() + ".xlsx");
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }

        return url;
    }

    @Override
    public List<ShopLabelCenterVo> getDetailList(ShopLabelCenterPageDto dto) {
        List<ShopLabelCenterVo> shopLabelCenterVos = new ArrayList<>();
        LambdaQueryWrapper<ShopLabelCenter> qw = this.buildLambdaQueryWrapper(dto);
        List<ShopLabelCenter> shopLabelCenters = this.listWithoutLogic(qw);
        if (!ObjectUtils.isEmpty(shopLabelCenters)) {
            List<Integer> shopIds = shopLabelCenters.stream().map(ShopLabelCenter::getShopId).distinct().collect(Collectors.toList());
            List<ShopUser> shopUserLists = new ArrayList<>(iShopUserService.listByIds(shopIds));
            Map<Integer, String> shopMap = shopUserLists.stream()
                    .collect(Collectors.toMap(ShopUser::getId, ShopUser::getRealname, (existing, replacement) -> existing));
            shopLabelCenters.forEach(shopLabelCenter -> {
                ShopLabelCenterVo shopLabelCenterVo = new ShopLabelCenterVo();
                BeanUtils.copyProperties(shopLabelCenter, shopLabelCenterVo);
                shopLabelCenterVo.setShopName(shopMap.get(shopLabelCenter.getShopId()));
                shopLabelCenterVos.add(shopLabelCenterVo);
            });
        }
        return shopLabelCenterVos;
    }


    @Override
    public List<ShopLabelCenter> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }


    /**
     * 更新物流状态
     *
     * @trackingNos 物流信息
     */
    @Override
    public Boolean updateStatus(List<String> trackingNos, LabelCenterStatus status) {
        Assert.notEmpty(trackingNos, "trackingNos 不能为空");
        Assert.notNull(status, "status不能为空");

        LambdaUpdateWrapper<ShopLabelCenter> updateWrapper = Wrappers.<ShopLabelCenter>lambdaUpdate()
                .in(ShopLabelCenter::getTrackingNo, trackingNos)
                .set(ShopLabelCenter::getStatus, status)
                .set(ShopLabelCenter::getGmtModify, DateTimeUtils.getNow());

        return this.update(updateWrapper);
    }

    /**
     * 查询非最终状态的物流信息
     */
    @Override
    public List<ShopLabelCenter> listNotFinalStatus() {

        List<LabelCenterStatus> labelCenterStatuses = Arrays.asList(
                LabelCenterStatus.CREATED
                , LabelCenterStatus.TRANSIT
        );

        LambdaQueryWrapper<ShopLabelCenter> qw = Wrappers.<ShopLabelCenter>lambdaQuery()
                .in(ShopLabelCenter::getStatus, labelCenterStatuses)
                // 60天内
                .apply(" DATEDIFF(NOW(),GMT_CREATE) < 60")
                // 查询结果
                .select(ShopLabelCenter::getId
                        , ShopLabelCenter::getTrackingNo
                        , ShopLabelCenter::getStatus
                        , ShopLabelCenter::getGmtCreate
                        , ShopLabelCenter::getGmtModify
                );

        return this.list(qw);
    }

    /**
     * 查询 label 部分字段
     */
    @Override
    public ShopLabelCenter selectLabel(String trackingNo) {
        Assert.notNull(trackingNo, "selectLabel voidShipmentDto is null");

        LambdaQueryWrapper<ShopLabelCenter> qw = Wrappers.<ShopLabelCenter>lambdaQuery()
                .eq(ShopLabelCenter::getTrackingNo, trackingNo)
                // 查询结果
                .select(ShopLabelCenter::getId
                        , ShopLabelCenter::getLabelFee
                        , ShopLabelCenter::getLabelFeeUnit
                        , ShopLabelCenter::getCarrierAccount
                        , ShopLabelCenter::getStatus
                );

        ShopLabelCenter shopLabelCenter = this.getOne(qw, false);
        Assert.notNull(shopLabelCenter.getLabelFeeUnit(), "selectLabel getLabelFeeUnit is null");
        Assert.notNull(shopLabelCenter.getLabelFee(), "selectLabel getLabelFee is null");
        Assert.isTrue(shopLabelCenter.getLabelFeeUnit() == CurrencyCode.USD, "selectLabel getLabelFee is not USD");

        return shopLabelCenter;
    }

}
