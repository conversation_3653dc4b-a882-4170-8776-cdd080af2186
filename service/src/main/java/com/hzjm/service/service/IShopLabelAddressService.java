package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.ShopLabelAddress;
import com.hzjm.service.model.DTO.ShopLabelAddressPageDto;
import com.hzjm.service.model.VO.ShopLabelAddressListVo;
import com.hzjm.service.model.VO.ShopLabelAddressVo;

import java.util.List;

/**
 * label 收件信息 服务类
 *
 * <AUTHOR>
 * @since 2024-12-30
 */
public interface IShopLabelAddressService extends IService<ShopLabelAddress> {

    ShopLabelAddress getByIdWithoutLogic(Integer id);

    ShopLabelAddressVo getDetail(Integer id);

    Boolean saveShopLabelAddress(ShopLabelAddress dto);

    Boolean insertList(List<ShopLabelAddress> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<ShopLabelAddressListVo> searchList(ShopLabelAddressPageDto dto);

    List<ShopLabelAddress> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<ShopLabelAddress> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
