package com.hzjm.service.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.service.entity.RecapHotSku;
import com.hzjm.service.entity.RecapMonthly;
import com.hzjm.service.entity.RecapYearly;
import com.hzjm.service.mapper.DashboardAdminMapper;
import com.hzjm.service.mapper.DashboardMapper;
import com.hzjm.service.mapper.RecapYearlyMapper;
import com.hzjm.service.model.DTO.RecapYearlyItemDto;
import com.hzjm.service.model.DTO.TaskRecapReqDto;
import com.hzjm.service.model.VO.PlatformOrderVo;
import com.hzjm.service.model.VO.RecapYearlyVo;
import com.hzjm.service.model.VO.ShopUidPlListVo;
import com.hzjm.service.model.VO.ShopUserRankVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thymeleaf.context.Context;

import javax.annotation.Resource;
import java.time.Year;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.hzjm.service.constants.ServiceConstants.EMAIL_RECAP_HOT_SKU_SORTS;
import static com.hzjm.service.constants.ServiceConstants.TEN;

/**
 * <AUTHOR>
 * @date 2024/12/17 18:04
 * @description: 商家年度统计报表·服务实现类
 */
@Slf4j
@Service
public class RecapYearlyServiceImpl extends ServiceImpl<RecapYearlyMapper, RecapYearly> implements IRecapYearlyService {

    @Resource
    DashboardAdminMapper dashboardAdminMapper;
    @Resource
    private DashboardMapper dashboardMapper;
    @Resource
    private IRecapMonthlyService iRecapMonthlyService;
    @Resource
    private IRecapHotSkuService iRecapHotSkuService;
    @Resource
    private IRecapYearlySaveService iRecapYearlySaveService;
    @Resource
    private org.thymeleaf.spring5.SpringTemplateEngine templateEngine;

    @Override
    public List<PlatformOrderVo> queryShopProductTop(Long shopUid, Integer top, Integer year) {
        if (BeanUtil.isEmpty(shopUid) || BeanUtil.isEmpty(top) || BeanUtil.isEmpty(year)) {
            log.error("商家uid或top或year为空");
            return Collections.emptyList();
        }
        return dashboardMapper.queryShopProductTop(shopUid, top, year);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean createRecapYearly(TaskRecapReqDto recapReqDto) {
        log.info("生成商家年度统计数据，商家id：{}，年份：{}", recapReqDto.getShopUid(), recapReqDto.getYear());
        if (!TaskRecapReqDto.isValidYearRequest(recapReqDto)) {
            return false;
        }
        CompletableFuture<List<RecapMonthly>> recapMonthliesFuture = CompletableFuture.supplyAsync(() ->
                iRecapMonthlyService.queryRecapYearlyByCondition(recapReqDto));
        CompletableFuture<List<PlatformOrderVo>> platformOrderVosFuture = CompletableFuture.supplyAsync(() ->
                queryShopProductTop(recapReqDto.getShopUid(), TEN, recapReqDto.getYear()));
        CompletableFuture<RecapMonthly> bestMonthRecapMonthlyFuture = CompletableFuture.supplyAsync(() ->
                iRecapMonthlyService.queryBestMonthRecapMonthly(recapReqDto));
        CompletableFuture<List<ShopUidPlListVo>> platformCountFuture = CompletableFuture.supplyAsync(() ->
                dashboardAdminMapper.queryShopIdPlatformCount(recapReqDto.getShopUid(), recapReqDto.getStartYearDate(), recapReqDto.getEndYearDate()));
        CompletableFuture<Long> listedTotalFuture = CompletableFuture.supplyAsync(() ->
                dashboardAdminMapper.queryItemListedTotal(recapReqDto.getShopUid(), null, null));
        CompletableFuture<List<ShopUserRankVo>> shopUserRankVosFuture = CompletableFuture.supplyAsync(() -> {
            TaskRecapReqDto rankShopReq = new TaskRecapReqDto(recapReqDto.getStartYearDate(), recapReqDto.getEndYearDate());
            return dashboardMapper.queryShopUserRank(rankShopReq);
        });
        CompletableFuture<Void> allOf = CompletableFuture.allOf(
                recapMonthliesFuture,
                platformOrderVosFuture,
                bestMonthRecapMonthlyFuture,
                platformCountFuture,
                listedTotalFuture,
                shopUserRankVosFuture);
        try {
            allOf.join();
            List<RecapMonthly> recapMonthlies = recapMonthliesFuture.join();
            List<PlatformOrderVo> platformOrderVos = platformOrderVosFuture.join();
            RecapMonthly bestMonthRecapMonthly = bestMonthRecapMonthlyFuture.join();
            List<ShopUidPlListVo> platformCount = platformCountFuture.join();
            long listedTotal = listedTotalFuture.join();
            List<ShopUserRankVo> shopUserRankVos = shopUserRankVosFuture.join();
            RecapYearly recapYearlyVo = RecapYearly.dataYearlyProcessing(
                    recapMonthlies
                    , recapReqDto.getShopUid()
                    , bestMonthRecapMonthly
                    , platformCount
                    , listedTotal
                    , shopUserRankVos);
            iRecapYearlySaveService.saveByCustom(recapYearlyVo);
            if (BeanUtil.isNotEmpty(recapYearlyVo)) {
                // 保存商家年度热门商品(年度数据保存成功后，继续保存热门商品)
                iRecapHotSkuService.batchSaveSortSku(platformOrderVos, Year.of(recapReqDto.getYear()));
            }
            log.info("商家年度统计数据生成 成功");
            return true;
        } catch (Exception exception) {
            log.error("商家年度统计数据生成失败", exception);
            return false;
        }
    }

    @Override
    public List<RecapYearlyVo> queryRecapYearlyByCondition(TaskRecapReqDto recapReqDto) {
        if (CollUtil.isEmpty(recapReqDto.getShopIds()) || BeanUtil.isEmpty(recapReqDto.getYear())) {
            return Collections.emptyList();
        }
        log.info("查询商家年度统计，商家id：{}，年份：{}", recapReqDto.getShopIds(), recapReqDto.getYear());
        QueryWrapper<RecapYearly> queryWrapper = new QueryWrapper<>();
        queryWrapper
                .in("shop_uid", recapReqDto.getShopIds())
                .eq("YEAR(start_date)", recapReqDto.getYear())
                .eq("YEAR(end_date)", recapReqDto.getYear());
        List<RecapYearlyVo> recapMonthlyVos = list(queryWrapper).stream()
                .map(RecapYearly::createYearlyVo)
                .collect(Collectors.toList());
        for (RecapYearlyVo recapYearlyVo : recapMonthlyVos) {
            List<RecapHotSku> recapHotSkus = iRecapHotSkuService.query()
                    .eq("shop_uid", recapYearlyVo.getShopUid())
                    .eq("YEAR(start_date)", recapReqDto.getYear())
                    .eq("YEAR(end_date)", recapReqDto.getYear()).list();
            List<RecapYearlyItemDto> itemDtoList = recapHotSkus.stream().map(RecapHotSku::createItemDto).collect(Collectors.toList());
            recapYearlyVo.setRecapYearlyItems(itemDtoList);
        }
        log.info("查询商家年度统计成功");
        return recapMonthlyVos;
    }

    @Override
    public String createEmailContent(RecapYearlyVo recapYearlyVo) {
        log.info("生成邮件内容(年度统计邮件),商户ID:{}", recapYearlyVo.getShopUid());
        if (BeanUtil.isEmpty(recapYearlyVo)) {
            log.error("recapYearlyVo");
            return null;
        }
        Map<String, Object> variables = BeanUtil.beanToMap(recapYearlyVo);
        variables.putAll(processHotSkus(recapYearlyVo.getRecapYearlyItems()));
        String content = processTemplate(variables);
        log.info("生成邮件内容（年度统计邮件）成功,商户ID:{}", recapYearlyVo.getShopUid());
        return content;
    }

    /**
     * 处理年度商家热门sku
     *
     * @param recapHotSkus 热门SKU
     * @return map key: topOne, topTwo, topThree, topFour, topFive, topSix, topSeven, topEight, topNine, topTen
     */
    private Map<String, Object> processHotSkus(List<RecapYearlyItemDto> recapHotSkus) {
        Map<String, Object> resultMap = new HashMap<>(10);
        if (CollUtil.isEmpty(recapHotSkus)) {
            for (String sortKey : EMAIL_RECAP_HOT_SKU_SORTS) {
                resultMap.put(sortKey, null);
            }
            return resultMap;
        }
        for (String sortKey : EMAIL_RECAP_HOT_SKU_SORTS) {
            // 获取对应的序号 (将 "TOP-ONE" 转换为 sort
            long index = EMAIL_RECAP_HOT_SKU_SORTS.indexOf(sortKey) + 1;
            RecapYearlyItemDto foundItem = recapHotSkus.stream()
                    .filter(item -> item.getSort() != null && item.getSort().equals(index))
                    .findFirst()
                    .orElse(null);
            String imgUrl = foundItem != null ? foundItem.getImgUrl() : null;
            resultMap.put(sortKey, imgUrl);
        }
        return resultMap;
    }

    /**
     * 处理邮件模板
     *
     * @param variables template variables
     * @return template content
     */
    private String processTemplate(Map<String, Object> variables) {
        Context context = new Context();
        context.setVariables(variables);
        return templateEngine.process(com.hzjm.service.constants.ServiceConstants.EMAIL_RECAP_YEARLY, context);
    }
}