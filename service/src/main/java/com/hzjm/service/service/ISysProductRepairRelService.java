package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hzjm.service.entity.SysProductRepairRel;
import com.hzjm.service.model.DTO.SysProductRepairRelPageDto;
import com.hzjm.service.model.VO.SysProductRepairRelListVo;
import com.hzjm.service.model.VO.SysProductRepairRelVo;

import java.util.List;

/**
 * 商品维修关联表 服务类
 *
 * <AUTHOR>
 * @since 2025-04-09
 */
public interface ISysProductRepairRelService extends IService<SysProductRepairRel> {

    SysProductRepairRel getByIdWithoutLogic(Integer id);

    SysProductRepairRelVo getDetail(Integer id);

    Boolean saveSysProductRepairRel(SysProductRepairRel dto);

    Boolean insertList(List<SysProductRepairRel> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);

    IPage<SysProductRepairRelListVo> searchList(SysProductRepairRelPageDto dto);

    List<SysProductRepairRel> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProductRepairRel> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
