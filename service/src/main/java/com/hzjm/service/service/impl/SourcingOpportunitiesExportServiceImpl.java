package com.hzjm.service.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.model.DTO.req.SourcingOpportunitiesReq;
import com.hzjm.service.model.VO.SourcingOpportunitiesVo;
import com.hzjm.service.service.DashboardService;
import com.hzjm.service.service.ISourcingOpportunitiesExportService;
import com.hzjm.service.service.LanguageConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.hzjm.common.constants.SysConstants.LANGUAGE_EN_US;
import static com.hzjm.common.constants.SysConstants.LANGUAGE_ZH_CN;
import static com.hzjm.service.constants.ServiceConstants.SOURCING_OPPORTUNITIES_TT_CN_EXCEL_HEADER_TEMPLATE;
import static com.hzjm.service.constants.ServiceConstants.SOURCING_OPPORTUNITIES_TT_EN_EXCEL_HEADER_TEMPLATE;

/**
 * <AUTHOR>
 * @date 2025/5/20 10:56
 * @description:
 */
@Slf4j
@Service
public class SourcingOpportunitiesExportServiceImpl implements ISourcingOpportunitiesExportService {
    @Resource
    private DashboardService dashboardService;

    @Override
    public String export(SourcingOpportunitiesReq req, String timeZone, String language) {
        IPage<SourcingOpportunitiesVo> page = dashboardService.queryTiktokSourcingOpportunities(req);
        List<SourcingOpportunitiesVo> opportunities = page.getRecords();
        List<List<String>> dataList = new ArrayList<>();
        List<String> headers;
        if (LANGUAGE_EN_US.equals(getUserLanguage(language))) {
            headers = SOURCING_OPPORTUNITIES_TT_EN_EXCEL_HEADER_TEMPLATE;
        } else {
            headers = SOURCING_OPPORTUNITIES_TT_CN_EXCEL_HEADER_TEMPLATE;
        }
        dataList.add(LanguageConfigService.i18nForMsg(headers));
        for (SourcingOpportunitiesVo data : opportunities) {
            List<String> rowList = new ArrayList<>();
            rowList.add(BaseUtils.covertString(data.getProductName()));
            rowList.add(BaseUtils.covertString(data.getSku()));
            rowList.add(BaseUtils.covertString(data.getMaxPrice()));
            rowList.add(BaseUtils.covertString(data.getLowestPrice()));
            rowList.add(BaseUtils.covertString(data.getInventoryLevelNum()));
            rowList.add(BaseUtils.covertString(data.getInventoryLevelDen()));
            rowList.add(BaseUtils.covertString(data.getLast7DaysSoldTotal()));
            rowList.add(BaseUtils.covertString(data.getAllDaysSoldTotal()));
            dataList.add(rowList);
        }
        String url;
        try {
            url = ExcelReader.exportExcel(dataList, LanguageConfigService.i18nForMsg("抖音采购机会", getUserLanguage(language)) +
                    "(" + DateTimeUtils.getFileSuffix() + BaseUtils.getRandomStr(3) + ").xlsx");
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }
        return url;
    }

    /**
     * 用户语言
     *
     * @param language language
     */
    public String getUserLanguage(String language) {
        if (LANGUAGE_EN_US.equals(language) || LANGUAGE_ZH_CN.equals(language)) {
            return language;
        } else {
            return LANGUAGE_EN_US;
        }
    }
}
