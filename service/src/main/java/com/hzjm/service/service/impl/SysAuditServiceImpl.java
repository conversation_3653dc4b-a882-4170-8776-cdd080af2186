package com.hzjm.service.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.constants.SysConstants;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.*;
import com.hzjm.service.mapper.SysAuditMapper;
import com.hzjm.service.model.DTO.SysAuditPageDto;
import com.hzjm.service.model.VO.SysAuditListVo;
import com.hzjm.service.model.VO.SysAuditVo;
import com.hzjm.service.model.VO.SysProdDealListVo;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 平台审核 服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-14
 */
@Slf4j
@Service
public class SysAuditServiceImpl extends ServiceImpl<SysAuditMapper, SysAudit> implements ISysAuditService {

    @Autowired
    private ISysProdService iSysProdService;

    @Autowired
    private ISysProdDealService iSysProdDealService;

    @Autowired
    private ISysProdTransportService iSysProdTransportService;

    @Autowired
    private ISysProdTransferService iSysProdTransferService;

    @Autowired
    private ISysProdCashService iSysProdCashService;

    @Override
    public SysAudit getByIdWithoutLogic(Integer id) {
        SysAudit data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException(LanguageConfigService.i18nForMsg("查询失败，未找到该平台审核"));
        }

        return data;
    }

    @Override
    public SysAuditVo getDetail(Integer id) {
        SysAudit data = getByIdWithoutLogic(id);

        SysAuditVo vo = new SysAuditVo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean saveSysAudit(SysAudit dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<SysAuditListVo> searchList(SysAuditPageDto dto) {

        LambdaQueryWrapper<SysAudit> qw = Wrappers.<SysAudit>lambdaQuery();

        Date endTime = dto.dealEndTime();
        Date outEndTime = dto.dealOutEndTime();
        List<Integer> types = dto.conversionTypeList(dto.getType());
        qw
                .in(!ObjectUtils.isEmpty(dto.getIdList()), SysAudit::getId, dto.getIdList())
                .in(!ObjectUtils.isEmpty(types), SysAudit::getType, types)
                .like(!ObjectUtils.isEmpty(dto.getOddNo()), SysAudit::getOddNo, dto.getOddNo())
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), SysAudit::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), SysAudit::getGmtCreate, endTime)
                .ge(!ObjectUtils.isEmpty(dto.getOutBeginTime()), SysAudit::getGmtModify, dto.getOutBeginTime())
                .lt(!ObjectUtils.isEmpty(outEndTime), SysAudit::getGmtModify, outEndTime);

        if (ObjectUtils.isEmpty(dto.getStatusList())) {
                qw.eq(!ObjectUtils.isEmpty(dto.getStatus()), SysAudit::getStatus, dto.getStatus());
            }
        else {
            qw.in(SysAudit::getStatus, dto.getStatusList());
        }

        if (!ObjectUtils.isEmpty(dto.getSearchIdList())){
            qw.in(!ObjectUtils.isEmpty(dto.getSearchIdList()), SysAudit::getId, dto.getSearchIdList());
        }

        Integer shopId = JwtContentHolder.getShopId();
        if (!ObjectUtils.isEmpty(shopId)) {
            qw.eq(SysAudit::getShopId, shopId);
        }

        if (!ObjectUtils.isEmpty(dto.getWareId()) || !ObjectUtils.isEmpty(dto.getWareIdList())
                || !ObjectUtils.isEmpty(dto.getOneId()) || !ObjectUtils.isEmpty(dto.getOneIdList())
                || !ObjectUtils.isEmpty(dto.getSpecList()) || !ObjectUtils.isEmpty(dto.getSkuList())
                || !ObjectUtils.isEmpty(dto.getSpec()) || !ObjectUtils.isEmpty(dto.getSku())) {
            LambdaQueryWrapper<SysProd> qw1 = Wrappers.<SysProd>lambdaQuery()
                    .select(SysProd::getId)
                    .like(!ObjectUtils.isEmpty(dto.getOneId()), SysProd::getOneId, dto.getOneId())
                    .like(!ObjectUtils.isEmpty(dto.getSku()), SysProd::getSku, dto.getSku())
                    .eq(!ObjectUtils.isEmpty(dto.getSpec()), SysProd::getSpec, dto.getSpec());

            if (!ObjectUtils.isEmpty(dto.getSpecList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSpecList().forEach(spec -> {
                    sb.append("(spec = '" + spec + "') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            if (!ObjectUtils.isEmpty(dto.getSkuList())) {
                StringBuffer sb = new StringBuffer();
                dto.getSkuList().forEach(sku -> {
                    sb.append("(sku = '" + sku + "') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            if (!ObjectUtils.isEmpty(dto.getOneIdList())) {
                StringBuffer sb = new StringBuffer();
                dto.getOneIdList().forEach(oneId -> {
                    sb.append("(one_id like '%" + oneId + "%') or ");
                });
                qw1.apply(" (" + sb.substring(0, sb.length() - 4) + ") ");
            }

            List<SysProd> prodList = iSysProdService.list(qw1);

            List<Integer> prodIdList = BaseUtils.initList();
            prodIdList.addAll(prodList.stream().map(SysProd::getId).collect(Collectors.toList()));

            StringBuffer sb = new StringBuffer();
            List<SysProdDeal> dealList = iSysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .in(!ObjectUtils.isEmpty(dto.getWareIdList()), SysProdDeal::getWareId, dto.getWareIdList())
                    .eq(!ObjectUtils.isEmpty(dto.getWareId()), SysProdDeal::getWareId, dto.getWareId())
                    .in(SysProdDeal::getProdId, prodIdList));
            if (!ObjectUtils.isEmpty(dealList)) {
                Map<Integer, List<SysProdDeal>> dealMap = dealList.stream().collect(Collectors.groupingBy(SysProdDeal::getType));
                dealMap.keySet().forEach(type -> {
                    List<Integer> relationIdList = dealMap.get(type).stream().map(SysProdDeal::getRelationId).collect(Collectors.toList());
                    sb.append(" or (relation_id in (" + BaseUtils.listToStr(",", relationIdList) + ") and type = " + type + ")");
                });
            } else {
                sb.append(" or 1 = 0");
            }
            qw.apply(" (" + sb.substring(4) + ")");
        }

        if (!ObjectUtils.isEmpty(dto.getStatus()) && dto.getStatus() == 6) {
            qw.orderByDesc(SysAudit::getGmtModify);
        }

        qw.orderByDesc(SysAudit::getGmtCreate);

        IPage<SysAudit> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<SysAuditListVo> voList = new ArrayList<>();
        if (!ObjectUtils.isEmpty(pageResult.getRecords())) {
            List<Integer> relationIdList = BaseUtils.initList();
            relationIdList.addAll(pageResult.getRecords().stream().map(SysAudit::getRelationId).collect(Collectors.toList()));
            Map<Integer, Map<Integer, List<SysProdDealListVo>>> dealGroup = iSysProdDealService.dealGroup(relationIdList,
                    pageResult.getRecords().stream().map(SysAudit::getType).collect(Collectors.toList()), null);

            List<SysProdTransfer> transferList = iSysProdTransferService.list(Wrappers.<SysProdTransfer>lambdaQuery().in(SysProdTransfer::getId, relationIdList));
            Map<Integer, SysProdTransfer> transferMap = transferList.stream().collect(Collectors.toMap(SysProdTransfer::getId, a -> a));

            List<SysProdTransport> transportList = iSysProdTransportService.list(Wrappers.<SysProdTransport>lambdaQuery().in(SysProdTransport::getId, relationIdList));
            Map<Integer, SysProdTransport> transportMap = transportList.stream().collect(Collectors.toMap(SysProdTransport::getId, a -> a));

            List<SysProdCash> cashList = iSysProdCashService.list(Wrappers.<SysProdCash>lambdaQuery().in(SysProdCash::getId, relationIdList));
            Map<Integer, SysProdCash> cashMap = cashList.stream().collect(Collectors.toMap(SysProdCash::getId, a -> a));

            pageResult.getRecords().forEach(data -> {
                SysAuditListVo vo = new SysAuditListVo();
                BeanUtils.copyProperties(data, vo);

                List<SysProdDealListVo> dealList = dealGroup.get(data.getType()).get(data.getRelationId());
                if (ObjectUtils.isEmpty(dealList)) {
                    vo.setProdNum(0);
                } else {
                    BigDecimal wareFee = SysConstants.zero;
                    BigDecimal saleTotalFee = SysConstants.zero; // 实际报价/寄售价
                    BigDecimal preTotalFee = SysConstants.zero; // 预计报价
                    List<String> wareName = new ArrayList<>();
                    for (SysProdDealListVo deal : dealList) {
                        wareName.add(deal.getWareName());
                        wareFee = wareFee.add(deal.getWareFee());

                        if (!ObjectUtils.isEmpty(preTotalFee) && !ObjectUtils.isEmpty(deal.getPrePrice()))
                            preTotalFee = preTotalFee.add(deal.getPrePrice());
                        else
                            preTotalFee = null;
                        if (!ObjectUtils.isEmpty(saleTotalFee) && !ObjectUtils.isEmpty(deal.getSalePrice()))
                            saleTotalFee = saleTotalFee.add(deal.getSalePrice());
                        else
                            saleTotalFee = null;
                    }

                    // 仓库名
                    try {
                        vo.setWareName(BaseUtils.listToStr(",", wareName.stream().distinct().collect(Collectors.toList())));
                    }catch (StringIndexOutOfBoundsException e){
                        e.printStackTrace();
                        log.info("SysAuditServiceImpl searchList error wareName ={}", JSON.toJSONString(wareName));
                        vo.setWareName("");
                    }

                    // 商品数量
                    vo.setProdNum(dealList.size());

                    vo.setProdList(dealList);

                    // 费用合计
                    // 3-代发，4-转运，5-套现，7-平台内转移
                    switch (data.getType()) {
                        case SysProdEvent.TypeTransport:
                            SysProdTransport transport = transportMap.get(data.getRelationId());
                            if (transport.getStatus() != 2) {
                                vo.setTotalFee(wareFee.add(transport.getPlatFee().multiply(new BigDecimal(dealList.size()))).add(transport.getDeliveryFee()).subtract(transport.getFreeFee()));
                            }
                            vo.setDeliveryFee(transport.getDeliveryFee());
                            vo.setPlatFee(transport.getPlatFee());
                            vo.setFreeFee(transport.getFreeFee());
                            vo.setIeType(-1);
                            break;
                        case SysProdEvent.TypeSend:
                            SysProdTransport send = transportMap.get(data.getRelationId());
                            if(!ObjectUtils.isEmpty(send)){
                                if (send.getStatus() != 2) {
                                    vo.setTotalFee(wareFee.add(send.getPlatFee().multiply(new BigDecimal(dealList.size()))).add(send.getDeliveryFee()).subtract(send.getFreeFee()));
                                }
                                vo.setDeliveryFee(send.getDeliveryFee());
                                vo.setPlatFee(send.getPlatFee());
                                vo.setFreeFee(send.getFreeFee());
                            }
                            vo.setIeType(-1);
                            break;
                        case SysProdEvent.TypeCash:
                            SysProdCash cash = cashMap.get(data.getRelationId());
                            if (ObjectUtils.isEmpty(cash.getTotalFee()) && !ObjectUtils.isEmpty(saleTotalFee))
                                vo.setTotalFee(saleTotalFee.subtract(wareFee));
                            else {
                                vo.setTotalFee(cash.getTotalFee());
                            }
                            vo.setFreeFee(cash.getFreeFee());
                            vo.setIeType(1);
                            break;
                        case SysProdEvent.TypeTransfer:
                            SysProdTransfer transfer = transferMap.get(data.getRelationId());
                            if (transfer.getStatus() != 2) {
                                vo.setTotalFee(wareFee.add(transfer.getPlatFee().multiply(new BigDecimal(dealList.size()))).add(transfer.getDeliveryFee()).subtract(transfer.getFreeFee()));
                            }
                            vo.setDeliveryFee(transfer.getDeliveryFee());
                            vo.setPlatFee(transfer.getPlatFee());
                            vo.setFreeFee(transfer.getFreeFee());
                            vo.setIeType(-1);
                            break;
                    }
                }

                voList.add(vo);
            });
        }

        IPage<SysAuditListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<SysAudit> dataList) {
        if (ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

            data.setGmtCreate(date);
            data.setGmtModify(date);

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
            int begin = num * (i - 1);
            int end = begin + num;
            if (end > total) {
                end = total;
            }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<SysAudit> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<SysAudit> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
