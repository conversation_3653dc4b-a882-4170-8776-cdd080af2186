package com.hzjm.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hzjm.common.annotation.ReadOnly;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.service.entity.PlatformListingFlexStatusCount;
import com.hzjm.service.entity.ShopUser;
import com.hzjm.service.entity.StockxFlexPre;
import com.hzjm.service.entity.StockxFlexProd;
import com.hzjm.service.mapper.StockxFlexPreMapper;
import com.hzjm.service.model.DTO.StockxFlexPreDto;
import com.hzjm.service.model.DTO.StockxFlexPreSearchDto;
import com.hzjm.service.model.DTO.StockxFlexProdDto;
import com.hzjm.service.model.enums.FlexPreStatus;
import com.hzjm.service.model.enums.FlexProdStatus;
import com.hzjm.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StockxFlexPreServiceImpl extends ServiceImpl<StockxFlexPreMapper, StockxFlexPre> implements IStockxFlexPreService {

    @Autowired
    IStockxFlexProdService stockxFlexProdService;

    @Autowired
    ISysWareInService sysWareInService;

    @Autowired
    IShopUserService shopUserService;

    @Autowired
    ISysCodePoolService sysCodePoolService;

    @Autowired
    ISysProdService sysProdService;


    /**
     * 创建一个 新的 stockX Flex 预报
     *
     * @param userId 用户信息
     * @return
     */
    @Override
    public StockxFlexPreDto create(Integer userId) {
        // 获取创建人信息
        ShopUser shopUser = shopUserService.getOne(Wrappers.<ShopUser>lambdaQuery()
                .eq(ShopUser::getId, userId)
                .last("LIMIT 1")
        );

        // 获取 不到创建的 用户信息
        if (ObjectUtils.isEmpty(shopUser)) {
            throw new BaseException("Invalid create User info: you have not permission to create stockX Flex Package.");
        }

        // 生成 新的 stockX Flex 预报
        StockxFlexPre pre = new StockxFlexPre();
        pre.setGmtCreate(DateTimeUtils.getNow());
        pre.setGmtModify(DateTimeUtils.getNow());

        // 生成 pre ID
        String preId = sysCodePoolService.build(25, 1).stream().findFirst().orElse(null);
        if (ObjectUtils.isEmpty(preId)) {
            throw new BaseException("create pre batch number error.");
        }
        pre.setPreId(preId);

        // 填充 创建人信息
        pre.setUserId(shopUser.getId());
        pre.setUserName(shopUser.getRealname());
        pre.setUid(shopUser.getUid());

        // 设置 预报的 状态 为 待处理
        pre.setFlexPreStatus(FlexPreStatus.PENDING_PROCESS);

        getBaseMapper().insert(pre);

        StockxFlexPreDto returnDto = new StockxFlexPreDto();
        BeanUtils.copyProperties(pre, returnDto);

        return returnDto;
    }

    /**
     * 根据 Pre ID 获取一个 stockX Flex 预报的详细信息
     *
     * @param preId
     * @return
     */
    @Override
    public StockxFlexPre getDetailBy(String preId) {
        // 输入了 无效的 预报id
        if (ObjectUtils.isEmpty(preId)) {
            throw new BaseException("Empty stockX flex package batch number.");
        }

        StockxFlexPre pre = getBaseMapper().selectOne(Wrappers.<StockxFlexPre>lambdaQuery().eq(StockxFlexPre::getPreId, preId));

        // 数据库里没有找到相关的数据
        if (ObjectUtils.isEmpty(pre)) {
            throw new BaseException("can't find any information by batch number: " + preId);
        }

        List<StockxFlexProd> containedProds = stockxFlexProdService.list(Wrappers.<StockxFlexProd>lambdaQuery().eq(StockxFlexProd::getFlexPreId, preId));

        // 非空的话就设置 pre 的数据
        if (!ObjectUtils.isEmpty(containedProds)) {
            pre.setStockxFlexProds(containedProds);
            pre.setProdNum(containedProds.size());
        }

        return pre;
    }

    /**
     * 获取 stockX Flex Pre 列表 统计
     */
    @Override
    @ReadOnly
    public PlatformListingFlexStatusCount searchListCount(StockxFlexPreSearchDto searchDto) {
        log.info("StockxFlexPreServiceImpl searchListCount start searchDto ={}", JSON.toJSONString(searchDto));
        searchDto.setStatus(null);
        LambdaQueryWrapper<StockxFlexPre> qw = this.buildWrapper(searchDto);
        qw.select(StockxFlexPre::getId, StockxFlexPre::getFlexPreStatus);
        List<StockxFlexPre> stockxFlexPres = this.list(qw);
        if (ObjectUtils.isEmpty(stockxFlexPres)) {
            return new PlatformListingFlexStatusCount();
        }
        PlatformListingFlexStatusCount flexStatusCount = new PlatformListingFlexStatusCount()
                .setPendingListings(stockxFlexPres.stream().filter(i -> !ObjectUtils.isEmpty(i.getFlexPreStatus()) && FlexPreStatus.getPendingStatus().contains(i.getFlexPreStatus()) ).count())
                .setInProgressListings(stockxFlexPres.stream().filter(i -> !ObjectUtils.isEmpty(i.getFlexPreStatus()) && FlexPreStatus.getInProgressListings().contains(i.getFlexPreStatus())).count())
                .setCompletedListings(stockxFlexPres.stream().filter(i -> !ObjectUtils.isEmpty(i.getFlexPreStatus()) && FlexPreStatus.getCompletedListings().contains(i.getFlexPreStatus())).count());

        if (searchDto.getIsAdmin()) {
            flexStatusCount.setPushedListings(stockxFlexPres.size() - flexStatusCount.getPendingListings());
        }
        log.info("StockxFlexPreServiceImpl searchListCount end");
        return flexStatusCount;
    }

    private LambdaQueryWrapper<StockxFlexPre> buildWrapper(StockxFlexPreSearchDto searchDto) {
        return Wrappers.<StockxFlexPre>lambdaQuery()
                .orderByDesc(StockxFlexPre::getGmtModify)
                .orderByDesc(StockxFlexPre::getId)
                .eq(!ObjectUtils.isEmpty(searchDto.getPreId()), StockxFlexPre::getPreId, searchDto.getPreId())
                .eq(!ObjectUtils.isEmpty(searchDto.getUserId()) && !searchDto.getIsAdmin(), StockxFlexPre::getUserId, searchDto.getUserId())
                .eq(!ObjectUtils.isEmpty(searchDto.getStatus()), StockxFlexPre::getFlexPreStatus, searchDto.getStatus())
                .eq(!ObjectUtils.isEmpty(searchDto.getDisplayId()), StockxFlexPre::getDisplayId, searchDto.getDisplayId())
                .ge(!ObjectUtils.isEmpty(searchDto.getBeginTime()), StockxFlexPre::getGmtCreate, searchDto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(searchDto.getEndTime()), StockxFlexPre::getGmtCreate, searchDto.dealEndTime());
    }

    /**
     * 获取 stockX Flex Pre 列表
     *
     * @param searchDto
     * @return
     */
    @Override
    @ReadOnly
    public IPage<StockxFlexPre> searchList(StockxFlexPreSearchDto searchDto) {
        LambdaQueryWrapper<StockxFlexPre> qw = this.buildWrapper(searchDto);

        IPage<StockxFlexPre> pageResult = new Page();
        if (!ObjectUtils.isEmpty(searchDto.getSize()) && !ObjectUtils.isEmpty(searchDto.getCurrent())) {
            pageResult = page(new Page<>(searchDto.getCurrent(), searchDto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        return pageResult;
    }

    /**
     * 获取已经处理过的 stockX Flex pre
     *
     * @return
     */
    @Override
    public List<StockxFlexPre> getProcessedFlexPre() {
        List<StockxFlexPre> processed = getBaseMapper().selectList(Wrappers.<StockxFlexPre>lambdaQuery()
                .eq(StockxFlexPre::getFlexPreStatus, FlexPreStatus.IN_TRANSIT)
        );

        processed.forEach(singlePre -> {
            List<StockxFlexProd> prods = stockxFlexProdService.list(Wrappers
                    .<StockxFlexProd>lambdaQuery()
                    .eq(StockxFlexProd::getFlexPreId, singlePre.getPreId())
            );

            singlePre.setStockxFlexProds(prods);
        });

        return processed;
    }

    /**
     * 为某一个 预报新增商品
     *
     * @param preId   预报单号
     * @param prodDto
     * @return
     */
    @Override
    public boolean addProducts(String preId, List<StockxFlexProdDto> prodDto) {

        // 无效的 pre id
        if (ObjectUtils.isEmpty(preId)) {
            throw new BaseException("Invalid stockX flex pre batch number");
        }

        // 增加的商品 为空
        if (ObjectUtils.isEmpty(prodDto)) {
            throw new BaseException("Empty stock flex products need to add.");
        }

        StockxFlexPre pre = getBaseMapper().selectOne(Wrappers
                .<StockxFlexPre>lambdaQuery().eq(StockxFlexPre::getPreId, preId).last("LIMIT 1")
        );
        if (ObjectUtils.isEmpty(pre)) {
            throw new BaseException("Can't find any stock flex package info by batch number: " + preId);
        }

        if (!Objects.equals(pre.getFlexPreStatus(), FlexPreStatus.PENDING_PROCESS)) {
            throw new BaseException("You can only add products when stockX flex package status are: PENDING PROCESS");
        }

        // 需要新增的数据
        List<StockxFlexProd> updatedProds = prodDto.stream().map(dto -> {
                    try {
                        StockxFlexProd prod = new StockxFlexProd();
                        BeanUtils.copyProperties(dto, prod);
                        prod.setFlexPreId(pre.getPreId());
                        prod.setUserId(pre.getUserId());
                        return prod;
                    } catch (Exception e) {
                        log.error("convert stockX flex prod dto error.");
                    }
                    return null;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 添加最多不能超过十个 商品
        if (updatedProds.size() > 10) {
            throw new BaseException("same stockX flex package contains 10 products max");
        }

        List<StockxFlexProd> existedProds = stockxFlexProdService.list(Wrappers.<StockxFlexProd>lambdaQuery()
                .eq(StockxFlexProd::getFlexPreId, preId)
        );

        // 已经添加的 和 需要添加的加起来也不能超过有 10 个 商品
        int allProdNumbers = updatedProds.size() + existedProds.size();
        if (allProdNumbers > 10) {
            throw new BaseException("same stockX flex package contains 10 products max");
        }

        // 更新关联的 prods 数量 和 对应的 pre 包含数量
        boolean updateProdsResult = stockxFlexProdService.saveBatch(updatedProds);

        // 更新 pre 包含的 number
        pre.setProdNum(allProdNumbers);
        boolean updatePreResult = getBaseMapper().updateById(pre) > 0;

        return updateProdsResult && updatePreResult;
    }

    /**
     * 为某一个 预报 删除商品
     *
     * @param preId     预报单号
     * @param removeIds
     * @return
     */
    @Override
    public boolean removeProducts(String preId, List<Integer> removeIds) {
        // 无效的 pre id
        if (ObjectUtils.isEmpty(preId)) {
            throw new BaseException("Invalid stockX flex pre batch number");
        }

        // 增加的商品 为空
        if (ObjectUtils.isEmpty(removeIds)) {
            throw new BaseException("Submitted empty products that need to be removed.");
        }

        StockxFlexPre pre = getBaseMapper().selectOne(Wrappers
                .<StockxFlexPre>lambdaQuery().eq(StockxFlexPre::getPreId, preId).last("LIMIT 1")
        );
        if (ObjectUtils.isEmpty(pre)) {
            throw new BaseException("Can't find any stock flex package info by batch number: " + preId);
        }
        if (!Objects.equals(pre.getFlexPreStatus(), FlexPreStatus.PENDING_PROCESS)) {
            throw new BaseException("You can only remove products when stockX flex package status are: PENDING PROCESS");
        }

        boolean deleteResult = stockxFlexProdService.deleteBatchByIds(removeIds);

        // 更新 pre 包含的 prod num
        Integer containsResult = stockxFlexProdService.count(Wrappers.<StockxFlexProd>lambdaQuery().eq(StockxFlexProd::getFlexPreId, pre.getPreId()));
        pre.setProdNum(containsResult);
        boolean updateProdsResult = getBaseMapper().updateById(pre) > 0;

        return deleteResult && updateProdsResult;
    }

    /**
     * 更新 预报 商品 的 状态
     *
     * @param oneId
     * @param status
     * @return
     */
    @Override
    public boolean updateProductsStatus(String oneId, FlexProdStatus status) {

        if (ObjectUtils.isEmpty(oneId) || ObjectUtils.isEmpty(status)) {
            throw new BaseException("Invalid prods Id or Flex Prod Status.");
        }

        StockxFlexProd existedFlexProds = stockxFlexProdService.getOne(Wrappers.<StockxFlexProd>lambdaQuery().eq(StockxFlexProd::getOneId, oneId));

        if (ObjectUtils.isEmpty(existedFlexProds)) {
            throw new BaseException("Can't find any StockX Flex Prod info by oneId: " + oneId);
        }

        existedFlexProds.setFlexProdStatus(status);
        existedFlexProds.setGmtModify(DateTimeUtils.getNow());

        return stockxFlexProdService.saveOrUpdate(existedFlexProds);
    }


    /**
     * @param preId
     * @return
     */
    @Override
    public boolean deletePre(String preId) {
        // 没有 预报ID 去删除
        if (ObjectUtils.isEmpty(preId)) {
            throw new BaseException("empty stockX flex package batch number to delete.");
        }

        //
        StockxFlexPre selectedPre = getBaseMapper().selectOne(Wrappers.<StockxFlexPre>lambdaQuery()
                .eq(StockxFlexPre::getPreId, preId)
                .last("LIMIT 1")
        );

        if (ObjectUtils.isEmpty(selectedPre)) {
            throw new BaseException("Can't find any stockX Flex package info by batch number: " + preId);
        }

        if (!Objects.equals(selectedPre.getFlexPreStatus(), FlexPreStatus.PENDING_PROCESS)) {
            throw new BaseException("Only PENDING_PROCESS Package could delete, batch number:" + preId);
        }

        // 删除预报表
        boolean preDeleteResult = getBaseMapper().deleteById(selectedPre.getId()) > 0;

        // 删除预报包含的商品
        boolean prodDeleteResult = stockxFlexProdService.remove(Wrappers.<StockxFlexProd>lambdaQuery().eq(StockxFlexProd::getFlexPreId, preId));

        return preDeleteResult && prodDeleteResult;
    }

}
