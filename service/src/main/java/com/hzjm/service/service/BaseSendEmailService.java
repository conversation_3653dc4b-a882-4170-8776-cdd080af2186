package com.hzjm.service.service;

import com.hzjm.service.entity.SysEmailHistory;

import java.util.List;

public interface BaseSendEmailService {

    /**
     * 邮件发送
     */
    void sendEmail(String email,String bccEmail, String content, String subject, SysEmailHistory sysEmailHistory);

    /**
     * 返回所有人饿的邮箱
     */
    List<String> getUserEamilAll(String sql);

    /**
     * 返回所有人饿的邮箱数量，用于分组
     */
    Integer getUserEamilAllCount();
}
