package com.hzjm.service.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import com.hzjm.service.entity.SysProdSalePool;
import com.hzjm.service.model.DTO.SysProdSalePoolPageDto;
import com.hzjm.service.model.VO.SysProdSalePoolListVo;
import com.hzjm.service.model.VO.SysProdSalePoolVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 寄售结算池 服务类
 *
 * <AUTHOR>
 * @since 2023-07-25
 */
public interface ISysProdSalePoolService extends IService<SysProdSalePool> {

    SysProdSalePool getByIdWithoutLogic(Integer id);

    SysProdSalePoolVo getDetail(Integer id);

    Boolean saveSysProdSalePool(SysProdSalePool dto);

    Boolean insertList(List<SysProdSalePool> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<SysProdSalePoolListVo> searchList(SysProdSalePoolPageDto dto);

    List<SysProdSalePool> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<SysProdSalePool> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
