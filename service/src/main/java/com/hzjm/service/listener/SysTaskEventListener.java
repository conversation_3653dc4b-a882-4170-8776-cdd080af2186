package com.hzjm.service.listener;

import com.hzjm.service.model.event.SysTaskEvent;
import com.hzjm.service.mq.producer.SysTaskProducer;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/14 15:45
 * @description: 系统任务监听器
 */
@Component
public class SysTaskEventListener {
    @Resource
    private SysTaskProducer sysTaskProducer;

    @TransactionalEventListener(
            classes = SysTaskEvent.class,
            phase = TransactionPhase.AFTER_COMMIT
    )
    public void handleOrderCreatedEvent(SysTaskEvent event) {
        sysTaskProducer.sendTaskDownloadEvent(event.getSysTask());
    }
}
