package com.hzjm.service.constants;

import com.alibaba.fastjson.TypeReference;
import com.hzjm.common.infrastructure.HttpMethod;
import com.hzjm.common.infrastructure.HttpRequest;
import com.hzjm.common.model.HttpResult;

import java.util.List;

public class UpdateOrderEndPoint {

    private static final String BASE_URL = "https://system-api.knetgroup.com";
    private static final String CONFIRM_ORDER_RETURNED = "/order/return/confirm";

    public static HttpRequest<Boolean> confirmOrderReturned(String trackingNumber) {
        return new HttpRequest<>(
                BASE_URL,
                CONFIRM_ORDER_RETURNED,
                HttpMethod.PUT,
                null,
                null,
                null,
                new TypeReference<Boolean>() {
                }
        );
    }

}
