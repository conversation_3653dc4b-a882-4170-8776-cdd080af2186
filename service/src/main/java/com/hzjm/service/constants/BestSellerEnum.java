package com.hzjm.service.constants;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/5/12 14:01
 * @description: 销售机会bestSeller 枚举
 */
@Getter
@AllArgsConstructor
public enum BestSellerEnum {

    /**
     * 最近七天
     */
    LAST_7_DAYS("last 7 days"),
    /**
     * 全部
     */
    ALL_TIME("all time");

    @EnumValue
    @JSONField
    private final String description;

    @Override
    public String toString() {
        return this.description;
    }
}
