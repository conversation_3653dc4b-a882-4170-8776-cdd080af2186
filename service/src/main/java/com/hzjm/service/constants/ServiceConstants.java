package com.hzjm.service.constants;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/28 17:57
 * @description: 服务层常量
 */
public class ServiceConstants {
    /**
     * goat 查询sku 调用地址
     */
    public static final String GOAT_SEARCH_SKU_API_URL = "https://www.goat.com/api/v1/product_templates/search";

    //excel 相关
    /**
     * 最大导出记录数
     */
    public static final Integer MAX_EXPORT_COUNT = 1000000;
    /**
     * 套现审核 Excel 头部模版
     */
    public static final List<String> SYS_PROD_CASH_OUT_EXCEL_HEADER_TEMPLATE = Arrays.asList(
            "套现编号", "商家姓名", "识别码", "申请时间", "货品数量", "出库类别", "完成时间", "预计回收价($)", "套现订单总额",
            "品名", "sku", "尺码", "oneid", "状态", "成本", "货源", "仓位", "在仓天数", "套现价格"
    );
    /**
     * 平台内转移 Excel 头部模版
     */
    public static final List<String> SYS_PROD_TRANSFER_EXCEL_HEADER_TEMPLATE = Arrays.asList(
            "出库单号", "创建时间", "货品数量", "原识别码", "新识别码", "出库类别", "完成时间", "仓储费用", "服务费",
            "品名", "sku", "尺码", "oneid", "审核状态", "成本", "货源", "仓位", "在仓天数");

    public static final Integer ONE_HUNDRED = 100;
    public static final Integer TEN = 10;
    /**
     * 月度统计邮件模板
     */
    public static final String EMAIL_RECAP_MONTHLY = "recap-monthly";
    /**
     * 年度统计邮件主题
     */
    public static final String EMAIL_RECAP_YEARLY = "recap-yearly";
    /**
     * 月度统计邮件主题
     */
    public static String EMAIL_RECAP_MONTHLY_SUB = "Monthly Stats Are In! See Your %s KNET Recap \uD83C\uDF89";

    /**
     * 年度统计邮件主题
     */
    public static final String EMAIL_RECAP_YEARLY_SUB = "Your 2024 KNET Recap Is Here \uD83C\uDF89";
    /**
     * 年度邮件热门SKU 对应图片地址
     */
    public static final List<String> EMAIL_RECAP_HOT_SKU_SORTS = Arrays.asList(
            "topOne", "topTwo", "topThree", "topFour", "topFive"
            , "topSix", "topSeven", "topEight", "topNine", "topTen");
    /**
     * 月度统计
     */
    public static final String MONTHLY = "monthly";
    /**
     * 年度统计
     */
    public static final String YEARLY = "yearly";
    /**
     * 商家钱包 Excel 头部模版
     */
    public static final List<String> SYS_PROD_WALLET_EXCEL_HEADER_TEMPLATE = Arrays.asList(
            "交易编号", "平台出库单号", "类型", "寄卖平台单号", "申请时间", "入账时间", "识别码", "商家姓名", "金额交易后钱包金额", "状态",
            "品名", "sku", "尺码", "oneid", "仓库", "寄售价格", "寄售到手价", "服务费", "最终到手价", "备注", "寄卖来源", "寄卖平台", "Knet 到手价", "TTS 订单号"
    );
    /**
     * 下载文件任务ID 对应的文件名称
     */
    public static final String DOWNLOAD_FILE_TASK_ID = "DOWNLOAD_FILE_TASK_ID:taskId";
    /**
     * 发送提醒邮件，15天没有使用knet服务
     */
    public static final String RULER_NOTED_EMAIL = "RULER_NOTED_EMAIL:userId:";
    /**
     * 15天
     */
    public static final Integer FIFTEEN_DAY = 15;
    /**
     * 30天
     */
    public static final Integer THIRTY_DAY = 30;
    /**
     * 15天没使用knet,邮件标题
     */
    public static String RULER_NOTED_EMAIL_SUB = "Action Needed: Avoid Account Suspension";

    /**
     * 15天没使用knet,邮件中文邮件模板
     */
    public static String RULER_NOTED_EMAIL_ZH_CN_TEMPLATE =
            "<p>尊敬的 %s，</p>" +
                    "<p>我们注意到自您加入KNET 15天以来，您还没有提交任何库存。我们的平台旨在帮助您以快速高效的方式最大化销售，但这只有在您积极参与时才有效。</p>" +
                    "<p>为了保持市场的公平和顺畅运行，创建后30天内仍然不活跃的账户将被暂时冻结。</p>" +
                    "<p>我们希望看到您成功并扩大您的转售业务。为了避免您的账户被冻结，请尽快提交您的第一批商品。如果您有任何问题或需要帮助，请随时联系我们的客户体验团队。</p>" +
                    "<p>感谢您成为KNET的一部分 - 我们期待看到您的成功。</p>" +
                    "<p>此致，</p>" +
                    "<p>KNET团队</p>";
    /**
     * 15天没使用knet,邮件英文邮件模板
     */
    public static String RULER_NOTED_EMAIL_EN_US_TEMPLATE =
            "<p>Hi %s,</p>" +
                    "<p>We noticed that you haven't submitted any inventory since you joined KNET 15 days ago. Our platform is designed to help you maximize sales with speed and efficiency, but that only works when you're actively participating.</p>" +
                    "<p>To keep the marketplace fair and running smoothly, accounts that remain inactive for 30 days after creation are subject to temporary suspension.</p>" +
                    "<p>We'd love to see you succeed and scale your reselling business with KNET. To avoid your account being frozen, please send in your first pairs as soon as possible. If you have any questions or need help getting started, our Customer Experience team is ready to assist you. Please don't hesitate to reach out.</p>" +
                    "<p>Thank you for being part of KNET - we're excited to see your success.</p>" +
                    "<p>Best,</p>" +
                    "<p>The KNET Team</p>";
    /**
     * 15天没使用knet,功能开启时间
     */
    public static final String REGISTRATION_TIME_START = "2025-01-17";
    /**
     * 24小时-秒
     */
    public static final Long TWENTY_FOUR_HOUR = 60 * 60 * 24L;
    /**
     * 销售订单导出模版-
     */
    public static List<String> PLATFORM_ORDER_EXPORT_EXCEL_HEADER_TEMPLATE = Arrays.asList(
            "KNET订单编号", "品名", "SKU", "尺码", "ONE ID", "物流单号", "所在仓库", "货架",
            "寄售平台", "销售价格", "服务费", "商家到手价", "状态", "创建日期", "更新时间", "备注"
    );
    /**
     * 库存导出模版
     */
    public static List<String> INVENTORY_EXPORT_EXCEL_HEADER_TEMPLATE = Arrays.asList(
            "品名", "sku", "尺码", "one id", "商家姓名", "识别码", "状态",
            "验货结果", "预报类型", "在仓天数", "所在仓库", "所在仓位", "物流单号", "入库批次", "入库时间",
            "出库类型", "寄售平台", "平台出库单号", "寄售平台单号", "出库时间", "PKU", "关联物流单号", "验货备注", "成本", "货源"
    );
    /**
     * 导出文件名称模版
     */
    public static final String EXPORT_FILE_NAME_TEMPLATE = "%s(%s%s).xlsx";
    /**
     * sku导出模版
     */
    public static List<String> SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE = Arrays.asList(
            "类型", "品牌", "品名", "PKU", "SKU", "尺码", "颜色", "性别", "创建人", "创建时间");
    /**
     * sku导出模版-英文
     */
    public static List<String> SYS_SKU_EXPORT_EXCEL_HEADER_TEMPLATE_EN = Arrays.asList(
            "productType", "brand", "remarks", "PKU", "SKU", "spec", "color", "gender", "createBy", "img");
    /**
     * 1天的毫秒数
     */
    public static final long ONE_DAYS_IN_MILLISECONDS = 24 * 60 * 60 * 1000L;

    /**
     * SKU缓存key前缀
     */
    public static final String KG_SYS_SKU_CACHE_KEY_PREFIX = "kg:sys:sku:%s";
    /**
     * 系统sku数据-过期时间 1小时
     */
    public static final Long KG_SYS_SKU_EXPIRED_TIME = 3600L;

    /**
     * 销售机会 tt Excel 头部模版
     */
    public static final List<String> SOURCING_OPPORTUNITIES_TT_EN_EXCEL_HEADER_TEMPLATE =
            Arrays.asList(
                    "Product Name", "SKU", "Max Price", "Current Lowest Listed", "Current Listed", "Recommended Inventory Level", "Last 7d Sales", "Total Sales");
    public static final List<String> SOURCING_OPPORTUNITIES_TT_CN_EXCEL_HEADER_TEMPLATE =
            Arrays.asList(
                    "商品名称", "SKU", "最高价", "最低价", "上架数量", "推荐库存等级", "近7天销量", "总销量");

}
