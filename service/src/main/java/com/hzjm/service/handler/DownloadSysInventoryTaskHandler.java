package com.hzjm.service.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.hzjm.common.model.JwtContentHolder;
import com.hzjm.service.entity.DownloadFileRecord;
import com.hzjm.service.entity.SysTask;
import com.hzjm.service.model.DTO.SysInventoryExportDto;
import com.hzjm.service.model.enums.SysTaskStatus;
import com.hzjm.service.model.enums.SysTaskType;
import com.hzjm.service.service.export.ISysWarehouseExportService;
import com.hzjm.service.service.job.IDownloadFileRecordService;
import com.hzjm.service.service.job.impl.SysTaskSaveServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @date 2025/1/2 14:45
 * @description: 下载任务策略实现类-管理端-导出库存
 */
@Slf4j
@Component
public class DownloadSysInventoryTaskHandler implements TaskStrategy {
    @Resource
    private SysTaskSaveServiceImpl sysTaskSaveService;
    @Resource
    private ISysWarehouseExportService sysWarehouseExportService;
    @Resource
    private IDownloadFileRecordService iDownloadFileRecordService;
    @Resource(name = "exportTaskExecutor")
    private Executor exportExecutor;

    @Override
    public SysTaskType getHandleType() {
        return SysTaskType.DOWNLOAD_SYS_INVENTORY;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void execute(SysTask sysTask) {
        if (BeanUtil.isEmpty(sysTask)) {
            log.error("DownloadSysInventoryTaskHandler handle task is null");
            return;
        }
        log.info("DownloadSysInventoryTaskHandler handle start task: {}", sysTask);
        if (!SysTaskType.DOWNLOAD_SYS_INVENTORY.equals(sysTask.getType())) {
            return;
        }
        log.info("DownloadSysInventoryTaskHandler  DOWNLOAD_SYS_INVENTORY handle pending task: {}", sysTask);
        final SysInventoryExportDto req;
        try {
            req = JSON.parseObject(sysTask.getParams(), SysInventoryExportDto.class);
            req.setLanguage(sysTask.getLanguage());
        } catch (Exception e) {
            log.error("DownloadPlatformOrderTaskHandler DOWNLOAD_SYS_INVENTORY handle params error task: {}", sysTask);
            iDownloadFileRecordService.setDoneByTaskId(sysTask.getId(), SysTaskStatus.FAILURE);
            sysTaskSaveService.updateTaskStatus(sysTask.getId(), SysTaskStatus.FAILURE);
            return;
        }
        log.info("DownloadSysInventoryTaskHandler DOWNLOAD_SYS_INVENTORY handle req: {}", req);
        //更新文件下载记录状态为处理中
        String fileName = iDownloadFileRecordService.setPendingAndReturnFileName(sysTask.getId());
        if (StrUtil.isBlank(fileName)) {
            log.error("下载文件名，不应该为空，参数异常，taskId:{}", sysTask.getId());
            return;
        }
        CompletableFuture.supplyAsync(
                        () -> sysWarehouseExportService.exportSysInventory(req, fileName), exportExecutor)
                .thenApply(result -> processTask(sysTask, result))
                .exceptionally(e -> handlerException(sysTask, e));
        log.info("导出任务已提交异步处理，taskId: {}", sysTask.getId());
    }

    /**
     * 异常处理
     *
     * @param sysTask 任务
     * @param e       异常
     * @return HashMap
     */
    private HashMap<String, String> handlerException(SysTask sysTask, Throwable e) {
        taskRetry(sysTask);
        //处理导出文件可能产生的异常
        iDownloadFileRecordService.setDoneByTaskId(sysTask.getId(), SysTaskStatus.FAILURE);
        sysTaskSaveService.updateTaskStatus(sysTask.getId(), SysTaskStatus.FAILURE);
        JwtContentHolder.clearAll();
        log.error("DownloadSysInventoryTaskHandler DOWNLOAD_SYS_INVENTORY handle exception task: {}，message: {}", sysTask, e.getMessage(), e);
        return null;
    }

    /**
     * 处理导出任务-获取导出结果
     *
     * @param sysTask 任务
     * @param result  结果
     * @return HashMap
     */
    private HashMap<String, String> processTask(SysTask sysTask, HashMap<String, String> result) {
        if (MapUtil.isEmpty(result)) {
            iDownloadFileRecordService.setDoneByTaskId(sysTask.getId(), SysTaskStatus.FAILURE);
            sysTaskSaveService.updateTaskStatus(sysTask.getId(), SysTaskStatus.FAILURE);
            JwtContentHolder.clearAll();
            log.error("DownloadSysInventoryTaskHandler DOWNLOAD_SYS_INVENTORY handle failure task: {}", sysTask);
        }
        if (MapUtil.isNotEmpty(result)) {
            iDownloadFileRecordService.updateDownloadFileRecord(DownloadFileRecord.builder()
                    .taskId(sysTask.getId())
                    .fileUrl(result.get("fileUrl"))
                    .status(SysTaskStatus.SUCCESS)
                    .build());
            sysTaskSaveService.updateTaskStatus(sysTask.getId(), SysTaskStatus.SUCCESS);
            JwtContentHolder.clearAll();
            log.info("DownloadSysInventoryTaskHandler DOWNLOAD_SYS_INVENTORY handle success task: {}", sysTask);
        }
        JwtContentHolder.clearAll();
        return result;
    }

    /**
     * 任务重试
     *
     * @param sysTask 任务
     */
    private void taskRetry(SysTask sysTask) {
        // todo 利用redis实现任务重试
/*        String retryKey = "task:retry:" + sysTask.getId();
        int retryCount = redisTemplate.opsForValue().increment(retryKey, 1).intValue();
        if (retryCount <= 3) {
            log.info("Retrying task: {}, attempt: {}", sysTask.getId(), retryCount);
            execute(sysTask);
        } else {
            log.error("Task retry limit reached for task: {}", sysTask.getId());
            redisTemplate.delete(retryKey);
        }*/
    }
}
