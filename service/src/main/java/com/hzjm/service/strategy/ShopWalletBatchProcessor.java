package com.hzjm.service.strategy;

import com.hzjm.common.model.BaseException;
import com.hzjm.service.model.DTO.KnetBatchRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 钱包批量操作
 */
@Slf4j
@Service
public class ShopWalletBatchProcessor {

    private final Map<String, IKnetBatchOperationStrategy> strategyMap;

    @Autowired
    public ShopWalletBatchProcessor(List<IKnetBatchOperationStrategy> strategies) {
        this.strategyMap = strategies.stream()
                .collect(Collectors.toMap(
                        IKnetBatchOperationStrategy::getStrategyType,
                        strategy -> strategy
                ));
    }

    /**
     * Execute batch operation
     */
    public boolean executeBatchOperation(String operationType, List<KnetBatchRecordDto> records) {
        IKnetBatchOperationStrategy strategy = strategyMap.get(operationType);
        if (strategy == null) {
            throw new BaseException("Unsupported operation type: " + operationType);
        }

        log.info("Using strategy: {} to execute batch operation, record count: {}", operationType, records.size());
        return strategy.execute(records);
    }
}
