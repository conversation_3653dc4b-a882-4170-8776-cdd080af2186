package com.hzjm.service.strategy;

import com.hzjm.service.model.DTO.KnetBatchRecordDto;

import java.util.List;

/**
 * Knet Batch Operation Strategy Interface
 */
public interface IKnetBatchOperationStrategy {

    /**
     * Execute batch operation
     *
     * @param records operation record list
     * @return operation result
     */
    boolean execute(List<KnetBatchRecordDto> records);

    /**
     * Get strategy type
     *
     * @return strategy type
     */
    String getStrategyType();
}
