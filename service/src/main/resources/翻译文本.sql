insert into sys_language_config (zh_cn, en_us, msg) values ('启用', 'On', '启用');
insert into sys_language_config (zh_cn, en_us, msg) values ('禁用', 'Off', '禁用');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值', 'Recharge', '充值');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现', 'Withdraw', '提现');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发费用', 'Reship Fee', '代发费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运费用', 'Reship Fee', '转运费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现收款', 'KNET Cashout', '套现收款');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖收款', 'Sales', '寄卖收款');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台内转移', 'Transfer Fee', '平台内转移');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖费用', 'Service Fee', '寄卖费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家扣款', 'KNET Deduction', '商家扣款');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台退货扣款', 'Rejected ltem Fee', '平台退货扣款');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现退款', 'Withdrawal Refund', '提现退款');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请中', 'In transaction', '申请中');
insert into sys_language_config (zh_cn, en_us, msg) values ('成功', 'Completed', '成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('取消', 'Cancelled', '取消');
insert into sys_language_config (zh_cn, en_us, msg) values ('已预约', 'Scheduled', '已预约');
insert into sys_language_config (zh_cn, en_us, msg) values ('已入库', 'Received', '已入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台打回', 'Platform returned', '平台打回');
insert into sys_language_config (zh_cn, en_us, msg) values ('已查验', 'Checked-authenticated', '已查验');
insert into sys_language_config (zh_cn, en_us, msg) values ('合格', 'Qualified', '合格');
insert into sys_language_config (zh_cn, en_us, msg) values ('鞋盒轻微破损-S', 'Box Slightly Damaged-S', '鞋盒轻微破损-S');
insert into sys_language_config (zh_cn, en_us, msg) values ('鞋盒严重破损-S', 'Box Badly Damaged-S', '鞋盒严重破损-S');
insert into sys_language_config (zh_cn, en_us, msg) values ('非原盒-S', 'No Original Box-S', '非原盒-S');
insert into sys_language_config (zh_cn, en_us, msg) values ('无鞋盒盖-S', 'Missing Lid-S', '无鞋盒盖-S');
insert into sys_language_config (zh_cn, en_us, msg) values ('单只脚', 'Missing Pair', '单只脚');
insert into sys_language_config (zh_cn, en_us, msg) values ('球鞋有瑕疵', 'Shoes Defects', '球鞋有瑕疵');
insert into sys_language_config (zh_cn, en_us, msg) values ('假货', 'Fake', '假货');
insert into sys_language_config (zh_cn, en_us, msg) values ('空闲', 'Available', '空闲');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖中', 'On consignment', '寄卖中');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库中', 'Outbounding', '出库中');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现中', 'Cashing out', '套现中');
insert into sys_language_config (zh_cn, en_us, msg) values ('已出库', 'Shipped', '已出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台转移中', 'Switch User', '平台转移中');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发中', 'To be shipped', '代发中');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运中', 'In transit', '转运中');
insert into sys_language_config (zh_cn, en_us, msg) values ('待审核', 'Reviewing', '待审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('待确认', 'Pending Confirmation', '待确认');
insert into sys_language_config (zh_cn, en_us, msg) values ('待出库', 'Awaiting Shipment', '待出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('已转移', 'Completed', '已转移');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单异常', 'Order exception', '订单异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日待处理', 'Today’s Pending review', '今日待处理');
insert into sys_language_config (zh_cn, en_us, msg) values ('待处理', 'Pending review', '待处理');
insert into sys_language_config (zh_cn, en_us, msg) values ('已关闭', 'Closed', '已关闭');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售中', 'Consigning', '寄售中');
insert into sys_language_config (zh_cn, en_us, msg) values ('已完成', 'Completed', '已完成');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发', 'Ship with label', '代发');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运', 'Reship without label', '转运');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖', 'Consignment sold', '寄卖');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现', 'Cash out', '套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台流水', 'Platform cash flow', '平台流水');
insert into sys_language_config (zh_cn, en_us, msg) values ('现金编号', 'Cash number', '现金编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家姓名', 'User Name', '商家姓名');
insert into sys_language_config (zh_cn, en_us, msg) values ('识别码', 'User id', '识别码');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请时间', 'Application time', '申请时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('金额($)', 'Transaction amount($)', '金额($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('类型', 'Type', '类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('备注', 'Remark', '备注');
insert into sys_language_config (zh_cn, en_us, msg) values ('状态', 'Status', '状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家流水', 'User cash flow', '商家流水');
insert into sys_language_config (zh_cn, en_us, msg) values ('关联交易号', 'Related transaction number', '关联交易号');
insert into sys_language_config (zh_cn, en_us, msg) values ('交易类型', 'Transaction Type', '交易类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('交易金额', 'Amount of the transaction', '交易金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('交易时间', 'Transaction time', '交易时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家钱包', 'User wallet', '商家钱包');
insert into sys_language_config (zh_cn, en_us, msg) values ('钱包余额', 'Wallet balance($)', '钱包余额');
insert into sys_language_config (zh_cn, en_us, msg) values ('最新交易时间', 'Latest transaction time', '最新交易时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('交易编号', 'Transaction number', '交易编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台出库单号', 'Platform order number', '平台出库单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖平台单号', 'Consignment number', '寄卖平台单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('入账时间', 'Transaction TIme', '入账时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('金额交易后钱包金额', 'Balance', '金额交易后钱包金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('品名', 'Product Name', '品名');
insert into sys_language_config (zh_cn, en_us, msg) values ('sku', 'SKU', 'sku');
insert into sys_language_config (zh_cn, en_us, msg) values ('尺码', 'Size', '尺码');
insert into sys_language_config (zh_cn, en_us, msg) values ('oneid', 'ONE ID', 'oneid');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库', 'Warehouse', '仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售价格', 'Sale price', '寄售价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售到手价', 'Consignment revenue', '寄售到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('最终到手价', 'Final revenue', '最终到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家账号', 'User Account', '商家账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号名称', 'Account number', '账号名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('电子邮箱', 'Email', '电子邮箱');
insert into sys_language_config (zh_cn, en_us, msg) values ('引荐人', 'Referrer', '引荐人');
insert into sys_language_config (zh_cn, en_us, msg) values ('创建时间', 'Create time', '创建时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('服务费', 'Service Fee', '服务费');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖权限', 'Consignment permission', '寄卖权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报包裹导入模板', 'Package Template', '预报包裹导入模板');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家识别码', 'User id', '商家识别码');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报类型（必填）', 'Schedule type(*)', '预报类型（必填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('物流单号（必填）', 'Tracking number(*)', '物流单号（必填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('SKU（选填）', 'SKU', 'SKU（选填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('尺码（选填）', 'Size', '尺码（选填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('数量（选填）', 'Quantity', '数量（选填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('成本（选填）', 'Cost', '成本（选填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('货源（选填）', 'Source', '货源（选填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库（选填）', 'Warehouse', '仓库（选填）');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报包裹', 'Package', '预报包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报批次', 'Serial number', '预报批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('物流单号', 'Tracking number', '物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('数量', 'Quantity', '数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报类型', 'Schedule type', '预报类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹状态', 'Status', '包裹状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报时间', 'Schedule time', '预报时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库时间', 'Authenticated time', '入库时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库批次', 'Authenticated batch', '入库批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('查验时间', 'Inspection time', '查验时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('One ID', 'One ID', 'One ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('成本', 'Cost', '成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('验货结果', 'Result', '验货结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓储状态', 'Product Status', '仓储状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓位', 'Position', '仓位');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核状态', 'Status', '审核状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓储时间', 'Stock in Time', '仓储时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓储费', 'Storage fee', '仓储费');
insert into sys_language_config (zh_cn, en_us, msg) values ('运费', 'Shipping fees', '运费');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台优惠', 'Discount', '平台优惠');
insert into sys_language_config (zh_cn, en_us, msg) values ('费用合计', 'Total cost', '费用合计');
insert into sys_language_config (zh_cn, en_us, msg) values ('实际报价', 'Customer offer($)', '实际报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台报价', 'KnetGroup offer($)', '平台报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('到手价格', 'KnetGroup offer($)', '到手价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('所在仓库', 'Warehouse', '所在仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报价格($)', 'Estimate offer($)', '预报价格($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('实际报价($)', 'KnetGroup offer($)', '实际报价($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家报价($)', 'Customer offer($)', '商家报价($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖审核', 'Consignment review', '寄卖审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单号', 'Outbound Order Number', '出库单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售平台', 'Consignment platform', '寄售平台');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售平台单号', 'Consignment Order number', '寄售平台单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('销售日期', 'Sale date', '销售日期');
insert into sys_language_config (zh_cn, en_us, msg) values ('label', 'Label', 'label');
insert into sys_language_config (zh_cn, en_us, msg) values ('运单号[', 'Tracking number [', '运单号[');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖管理', 'Consignment', '寄卖管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架天数', 'Listing date', '上架天数');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售出库单号', 'Consignment order number', '寄售出库单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('本次寄售平台', 'Consignment platform', '本次寄售平台');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库时间', 'Outbound time', '出库时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('完成时间', 'Complete time', '完成时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('SKU管理', 'SKU management', 'SKU管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('品牌', 'Brand', '品牌');
insert into sys_language_config (zh_cn, en_us, msg) values ('pku', 'PKU', 'pku');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库批次号', 'Inbound batch number', '入库批次号');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库类型', 'Received type', '入库类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作员', 'Operator', '操作员');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库单', 'Inbound', '入库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库编号', 'Inbound batch number', '入库编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库批次', 'Outbound batch management', '出库批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库批次号', 'Outbound batch number', '出库批次号');
insert into sys_language_config (zh_cn, en_us, msg) values ('拣货单', 'Packing list', '拣货单');
insert into sys_language_config (zh_cn, en_us, msg) values ('批次编号', 'Batch number', '批次编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('label链接', 'Label', 'label链接');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库类型', 'Outbound type', '出库类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('货源', 'Source', '货源');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库日期', 'Schedule time', '入库日期');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品库存', 'Stock inventory', '商品库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库价格', 'Outbound offer($)', '出库价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('收件人', 'Receiver', '收件人');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库物流单号', 'Outbound tracking number', '出库物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('我的钱包', 'My wallet', '我的钱包');
insert into sys_language_config (zh_cn, en_us, msg) values ('发生时间', 'Application time', '发生时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('转仓拣货单', 'Switch user management', '转仓拣货单');
insert into sys_language_config (zh_cn, en_us, msg) values ('原仓库', 'Original warehouse', '原仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('转移至仓库', 'Transfer to warehouse', '转移至仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请转仓时间', 'Application transfer time', '申请转仓时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单', 'Outbound list', '出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('序号', 'No.', '序号');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请出库批次', 'Apply for outbound batch number', '申请出库批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请出库单号', 'Apply for outbound order number', '申请出库单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('预约出库时间', 'Book an outbound time', '预约出库时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('系统异常，请联系开发人员', 'System error', '系统异常，请联系开发人员');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该记录', 'Not found', '查询失败，未找到该记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该收费管理', 'Not found', '查询失败，未找到该收费管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该出库批次任务情况', 'Not found', '查询失败，未找到该出库批次任务情况');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该包裹货品', 'Not found', '查询失败，未找到该包裹货品');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该预报包裹', 'Not found', '查询失败，未找到该预报包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该预报批次', 'Not found', '查询失败，未找到该预报批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商家地址', 'Not found', '查询失败，未找到该商家地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商家', 'Not found', '查询失败，未找到该商家');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该用户钱包', 'Not found', '查询失败，未找到该用户钱包');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该套现', 'Not found', '查询失败，未找到该套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该平台审核', 'Not found', '查询失败，未找到该平台审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该留言', 'Not found', '查询失败，未找到该留言');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该寄售结算池', 'Not found', '查询失败，未找到该寄售结算池');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该三方寄售单', 'Not found', '查询失败，未找到该三方寄售单');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该转仓明细', 'Not found', '查询失败，未找到该转仓明细');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该平台内转移', 'Not found', '查询失败，未找到该平台内转移');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该转运&代发', 'Not found', '查询失败，未找到该转运&代发');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该入库单', 'Not found', '查询失败，未找到该入库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该出库批次商品', 'Not found', '查询失败，未找到该出库批次商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该出库批次', 'Not found', '查询失败，未找到该出库批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该仓库', 'Not found', '查询失败，未找到该仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该货架商品', 'Not found', '查询失败，未找到该货架商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该仓库货架', 'Not found', '查询失败，未找到该仓库货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该仓库人员', 'Not found', '查询失败，未找到该仓库人员');
insert into sys_language_config (zh_cn, en_us, msg) values ('未查询到该单号的信息', 'Not found', '未查询到该单号的信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('请勾选要审批的鞋子', 'Empty selected', '请勾选要审批的鞋子');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单下未包含商品，不受理', 'Empty selected', '订单下未包含商品，不受理');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意订单', 'Empty selected', '未选中任意订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意记录', 'Empty selected', '未选中任意记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('至少要有一个商品', 'Empty selected', '至少要有一个商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意出库单', 'Empty selected', '未选中任意出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意商品', 'Empty selected', '未选中任意商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('您尚未绑定微信', 'Not bind wechat account', '您尚未绑定微信');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂未开通该类支付', 'Not support yet', '暂未开通该类支付');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不支持此操作', 'Not support yet', '暂不支持此操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的流水号', 'Invalid number', '无效的流水号');
insert into sys_language_config (zh_cn, en_us, msg) values ('微信支付只能退一年内的款项', 'The item over a year, please concat us', '微信支付只能退一年内的款项');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹未预约', 'The package not scheduled', '包裹未预约');
insert into sys_language_config (zh_cn, en_us, msg) values ('该物流单已提交预约单', 'The tracking number already scheduled', '该物流单已提交预约单');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹已入库，您没有操作权限', 'The package already received', '包裹已入库，您没有操作权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('该手机号已被占用', 'The phone has been used', '该手机号已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('只有仓库人员可操作', 'You have no permission', '只有仓库人员可操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('运单号至少要有9位', 'Invalid tracking number', '运单号至少要有9位');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在相同的物流单号', 'The tracking number already exists', '存在相同的物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号名称不得为空', 'Empty account', '账号名称不得为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号密码不得为空', 'Empty password', '账号密码不得为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录账号已存在', 'The account already exists', '登录账号已存在');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮箱已被绑定', 'The email already exists', '邮箱已被绑定');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号标识生成繁忙，请稍后重试', 'The system is busy, please try again later', '账号标识生成繁忙，请稍后重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的引荐人', 'Invalid Referrer', '无效的引荐人');
insert into sys_language_config (zh_cn, en_us, msg) values ('状态同步失败', 'The status has been changed', '状态同步失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('已勾选的订单中，含有寄售状态异常的商品', 'Exist product status has been changed', '已勾选的订单中，含有寄售状态异常的商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已生成出库单', 'Exist product status has been changed', '商品已生成出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('新归属人不明', 'Empty new user id', '新归属人不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效oneId', 'Invalid one id', '无效oneId');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在记录无需审批', 'Exist audit status has been changed', '存在记录无需审批');
insert into sys_language_config (zh_cn, en_us, msg) values ('该平台仍存在寄售商品，无法删除', 'Exists product sold in the platform', '该平台仍存在寄售商品，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择批次类型', 'Empty type', '请选择批次类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填入批次编号', 'Empty batch number', '请填入批次编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('该批次编号已被使用', 'Exists batch number', '该批次编号已被使用');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库仓库不明', 'Unknown target warehouse', '入库仓库不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除已完成', 'Deleted', '删除已完成');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库已完成，无法删除', 'Receive success, can''t delete', '入库已完成，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品缺少oneId', 'Empty one id', '商品缺少oneId');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品缺少sku', 'Empty sku', '商品缺少sku');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品缺少spec', 'Empty spec', '商品缺少spec');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法识别的尺码', 'Invalid spec', '无法识别的尺码');
insert into sys_language_config (zh_cn, en_us, msg) values ('oneId异常', 'Invalid one id', 'oneId异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在出库单出现在待处理的出库批次中', 'Exists dealing outbound', '存在出库单出现在待处理的出库批次中');
insert into sys_language_config (zh_cn, en_us, msg) values ('任务情况不明', 'Lost task target', '任务情况不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('本仓库下有在仓商品，无法删除', 'Exists product in the warehouse', '本仓库下有在仓商品，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('该批次已有归属人', 'Exists owner', '该批次已有归属人');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品数量大于1的记录不可批量处理', 'Please deal alone when product more than 1', '商品数量大于1的记录不可批量处理');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在商品正在出库中，此类商品暂不支持上架', 'Exists product on the list of outbound', '存在商品正在出库中，此类商品暂不支持上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在商品正在出库中，此类商品暂不支持下架', 'Exists product on the list of outbound', '存在商品正在出库中，此类商品暂不支持下架');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择待上的货架', 'Empty target shelf', '请选择待上的货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架已废弃', 'Invalid shelf', '货架已废弃');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写货架名', 'Empty name', '请填写货架名');
insert into sys_language_config (zh_cn, en_us, msg) values ('该名称已被使用', 'Exists name', '该名称已被使用');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架名不可重复', 'Exists same name', '货架名不可重复');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架商品已超出货架最大数量', 'Over weight', '上架商品已超出货架最大数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('至少要有一个货架', 'At least one shelf in the warehouse', '至少要有一个货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架中仍有商品，无法删除', 'Exists product on the shelf', '货架中仍有商品，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('已移出仓库', 'Deleted', '已移出仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('系统异常：导出失败', 'System error, export failed', '系统异常：导出失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('相同出库类别可生成批次', 'Different outbound type', '相同出库类别可生成批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('相同寄售平台可生成批次', 'Different platform', '相同寄售平台可生成批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('选中的出库单不在同一个仓库', 'Different warehouse', '选中的出库单不在同一个仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库批次已完成，无法删除', 'Outbound success, can''t delete', '出库批次已完成，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选择上架平台', 'Empty platform', '未选择上架平台');
insert into sys_language_config (zh_cn, en_us, msg) values ('瑕疵商品无法申请寄售', 'Exist defects', '瑕疵商品无法申请寄售');
insert into sys_language_config (zh_cn, en_us, msg) values ('请先关联touch账号', 'Please relate Touch‘s account', '请先关联touch账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('该商家未拥有对应的寄售权限', 'No permission to sold in the platform', '该商家未拥有对应的寄售权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在商品已寄售', 'Exist sold', '存在商品已寄售');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不支持此类支付', 'This type of payment is not supported', '暂不支持此类支付');
insert into sys_language_config (zh_cn, en_us, msg) values ('支付金额不得小于等于0', 'Payment amount must be greater than 0', '支付金额不得小于等于0');
insert into sys_language_config (zh_cn, en_us, msg) values ('公众号支付，openId为必填项', 'For WeChat public account payment, openId is required', '公众号支付，openId为必填项');
insert into sys_language_config (zh_cn, en_us, msg) values ('小程序支付，openId为必填项', 'For WeChat mini program payment, openId is required', '小程序支付，openId为必填项');
insert into sys_language_config (zh_cn, en_us, msg) values ('微信支付参数异常', 'WeChat payment parameter error', '微信支付参数异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('微信支付失败', 'WeChat payment failed', '微信支付失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('微信订单查询参数异常', 'WeChat order query parameter error', '微信订单查询参数异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('银联支付失败：签名错误', 'UnionPay payment failed: signature error', '银联支付失败：签名错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('银联支付失败：未获得银联的正确响应', 'UnionPay payment failed: no correct response from UnionPay', '银联支付失败：未获得银联的正确响应');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不支持此类订单', 'This type of order is not supported', '暂不支持此类订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('卡号错误', 'Card number error', '卡号错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('卡号格式错误，请检查卡号是否正常填写', 'Card number format error, please check if the card number is filled correctly', '卡号格式错误，请检查卡号是否正常填写');
insert into sys_language_config (zh_cn, en_us, msg) values ('加密失败', 'Encryption failed', '加密失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不支持此搜索', 'This search is not supported', '暂不支持此搜索');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法识别的手机号', 'Unrecognized phone number', '无法识别的手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法识别的身份证号', 'Unrecognized ID number', '无法识别的身份证号');
insert into sys_language_config (zh_cn, en_us, msg) values ('文件异常', 'File error', '文件异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('token生成失败', 'Token generation failed', 'token生成失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('开通网易云信失败', 'Failed to activate NetEase Cloud Messaging', '开通网易云信失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该平台真正的寄售单', 'Query failed, the real consignment order of the platform was not found', '查询失败，未找到该平台真正的寄售单');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该', 'Query failed, not found', '查询失败，未找到该');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该平台订单', 'Query failed, the platform order was not found', '查询失败，未找到该平台订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该Knet Product Template Map from 3rd parties platform', 'Query failed, the Knet Product Template Map from 3rd parties platform was not found', '查询失败，未找到该Knet Product Template Map from 3rd parties platform');
insert into sys_language_config (zh_cn, en_us, msg) values ('文件读取异常', 'File reading error', '文件读取异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('未读取到任意数据', 'No data read', '未读取到任意数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('请检查物流单号，不允许为空', 'Please check the logistics tracking number, it cannot be empty', '请检查物流单号，不允许为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法识别的预报类型', 'Unrecognized forecast type', '无法识别的预报类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法识别的账号', 'Unrecognized account', '无法识别的账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('该用户未开通钱包', 'The user has not activated the wallet', '该用户未开通钱包');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询时间跨度不能超过90天', 'The query time span cannot exceed 90 days', '查询时间跨度不能超过90天');
insert into sys_language_config (zh_cn, en_us, msg) values ('审批结果未知', 'Approval result unknown', '审批结果未知');
insert into sys_language_config (zh_cn, en_us, msg) values ('未填写报价', 'No quotation filled', '未填写报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('报价对象不明', 'Quotation target unknown', '报价对象不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('当前状态不能修改报价', 'Quotation cannot be modified in the current state', '当前状态不能修改报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核结果不明', 'Review result unknown', '审核结果不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品瑕疵状态未知', 'Product defect status unknown', '商品瑕疵状态未知');
insert into sys_language_config (zh_cn, en_us, msg) values ('该商品无入库记录', 'No warehousing record for this product', '该商品无入库记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的寄售平台单号', 'Invalid consignment platform order number', '无效的寄售平台单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已失效', 'Product has expired', '商品已失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('不支持的操作类型', 'Unsupported operation type', '不支持的操作类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出内容不明', 'Export content unknown', '导出内容不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意sku', 'No SKU selected', '未选中任意sku');
insert into sys_language_config (zh_cn, en_us, msg) values ('查看对象不明', 'Viewing object unknown', '查看对象不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意批次', 'No batch selected', '未选中任意批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在已完成的批次', 'There are completed batches', '存在已完成的批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在处理中的出库批次，无法撤回', 'There are ongoing outbound batches that cannot be withdrawn', '存在处理中的出库批次，无法撤回');
insert into sys_language_config (zh_cn, en_us, msg) values ('生成失败', 'Generation failed', '生成失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意账号', 'No account selected', '未选中任意账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在已出库的出库单', 'There are outbound orders that have been shipped', '存在已出库的出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在处理中的出库单，无法撤回', 'There are ongoing outbound orders that cannot be withdrawn', '存在处理中的出库单，无法撤回');
insert into sys_language_config (zh_cn, en_us, msg) values ('运单号不能为空', 'The waybill number cannot be empty', '运单号不能为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商家的magic账号', 'Query failed, the merchant’s magic account was not found', '查询失败，未找到该商家的magic账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商户寄售权限', 'Query failed, the merchant consignment permissions were not found', '查询失败，未找到该商户寄售权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商家的touch账号', 'Query failed, the merchant’s touch account was not found', '查询失败，未找到该商家的touch账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('身份不明', 'Identity unknown', '身份不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的登录方式，请重新选择', 'Invalid login method, please choose again', '无效的登录方式，请重新选择');
insert into sys_language_config (zh_cn, en_us, msg) values ('该账号未注册', 'This account is not registered', '该账号未注册');
insert into sys_language_config (zh_cn, en_us, msg) values ('禁止操作！', 'Operation prohibited!', '禁止操作！');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入密码', 'Please enter the password', '请输入密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码错误，请重试', 'Incorrect password, please try again', '密码错误，请重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('该用户已被注销', 'This user has been deregistered', '该用户已被注销');
insert into sys_language_config (zh_cn, en_us, msg) values ('该用户已被禁用', 'This user has been disabled', '该用户已被禁用');
insert into sys_language_config (zh_cn, en_us, msg) values ('该用户没有管理权限', 'This user does not have administrative permissions', '该用户没有管理权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('该用户没有仓库管理权限', 'This user does not have warehouse management permissions', '该用户没有仓库管理权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('账单类型不明', 'Unknown bill type', '账单类型不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('审批已完成', 'Approval completed', '审批已完成');
insert into sys_language_config (zh_cn, en_us, msg) values ('扣款对象不明', 'Unknown deduction target', '扣款对象不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('扣款金额需大于0', 'Deduction amount must be greater than 0', '扣款金额需大于0');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值单状态不同步', 'Recharge order status is not synchronized', '充值单状态不同步');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现单状态不同步', 'Withdrawal order status is not synchronized', '提现单状态不同步');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该充值记录', 'Query failed, the recharge record was not found', '查询失败，未找到该充值记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择充值方式', 'Please select a recharge method', '请选择充值方式');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择要充值的对象', 'Please select the object to recharge', '请选择要充值的对象');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该编号池（每天0点清除）', 'Query failed, the number pool was not found (cleared daily at midnight)', '查询失败，未找到该编号池（每天0点清除）');
insert into sys_language_config (zh_cn, en_us, msg) values ('编号类型不可知', 'Unknown number type', '编号类型不可知');
insert into sys_language_config (zh_cn, en_us, msg) values ('生成数量不可知', 'Unknown generation quantity', '生成数量不可知');
insert into sys_language_config (zh_cn, en_us, msg) values ('网络繁忙，请重试', 'Network busy, please try again', '网络繁忙，请重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该邮件历史表', 'Query failed, the email history table was not found', '查询失败，未找到该邮件历史表');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该公告', 'Query failed, the announcement was not found', '查询失败，未找到该公告');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在未报价商品', 'There are unquoted products', '存在未报价商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('不支持的操作', 'Unsupported operation', '不支持的操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商品处理绑定关系', 'Query failed, the product processing binding relationship was not found', '查询失败，未找到该商品处理绑定关系');
insert into sys_language_config (zh_cn, en_us, msg) values ('不可识别的数据类型', 'Unrecognized data type', '不可识别的数据类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询对象不明', 'Unknown query object', '查询对象不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('已勾选商品的寄售状态存在异常', 'The consignment status of the selected product is abnormal', '已勾选商品的寄售状态存在异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('如需转自营，请与商家协商进行套现', 'If you need to switch to self-operated, please negotiate with the merchant for cash-out', '如需转自营，请与商家协商进行套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作不明', 'Unknown operation', '操作不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售状态异常', 'Abnormal consignment status', '寄售状态异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在商品已生成出库单', 'There are products that have generated outbound orders', '存在商品已生成出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商品事件', 'Query failed, the product event was not found', '查询失败，未找到该商品事件');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该上架失败校验池', 'Query failed, the listing failure verification pool was not found', '查询失败，未找到该上架失败校验池');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商品筛选', 'Query failed, the product filter was not found', '查询失败，未找到该商品筛选');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商品信息', 'Query failed, the product information was not found', '查询失败，未找到该商品信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('录入商品失败，缺少OneId', 'Product entry failed, missing OneId', '录入商品失败，缺少OneId');
insert into sys_language_config (zh_cn, en_us, msg) values ('录入商品失败，oneId已被使用', 'Product entry failed, OneId has been used', '录入商品失败，oneId已被使用');
insert into sys_language_config (zh_cn, en_us, msg) values ('非空闲状态不可删除', 'Cannot delete in non-idle state', '非空闲状态不可删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('No permission', 'No permission', 'No permission');
insert into sys_language_config (zh_cn, en_us, msg) values ('非空闲状态不可操作', 'Cannot operate in non-idle state', '非空闲状态不可操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作类型不明', 'Unknown operation type', '操作类型不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择上架平台并填写寄售价格', 'Please select a listing platform and fill in the consignment price', '请选择上架平台并填写寄售价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作失败，所有商品需归属同一个商家', 'Operation failed, all products must belong to the same merchant', '操作失败，所有商品需归属同一个商家');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作失败，存在商品正在转仓中', 'Operation failed, there are products in the process of warehouse transfer', '操作失败，存在商品正在转仓中');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作失败，所有商品需在同一个仓库', 'Operation failed, all products must be in the same warehouse', '操作失败，所有商品需在同一个仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不支持此功能', 'This feature is not supported', '暂不支持此功能');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法识别单号类型', 'Unrecognized order number type', '无法识别单号类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请单状态发生变化', 'Application order status has changed', '申请单状态发生变化');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售状态发生变化', 'Consignment status has changed', '寄售状态发生变化');
insert into sys_language_config (zh_cn, en_us, msg) values ('该状态下不可支付', 'Cannot pay in this status', '该状态下不可支付');
insert into sys_language_config (zh_cn, en_us, msg) values ('支付超时，撤销申请', 'Payment timeout, application canceled', '支付超时，撤销申请');
insert into sys_language_config (zh_cn, en_us, msg) values ('钱包余额不足', 'Insufficient wallet balance', '钱包余额不足');
insert into sys_language_config (zh_cn, en_us, msg) values ('扣款失败', 'Deduction failed', '扣款失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售不走此通道', 'Consignment does not go through this channel', '寄售不走此通道');
insert into sys_language_config (zh_cn, en_us, msg) values ('撤回失败，状态不同步', 'Withdrawal failed, status not synchronized', '撤回失败，状态不同步');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品状态不同步', 'Product status not synchronized', '商品状态不同步');
insert into sys_language_config (zh_cn, en_us, msg) values ('转仓已失效', 'Warehouse transfer has expired', '转仓已失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫描失败，目标仓库错误', 'Scan failed, target warehouse error', '扫描失败，目标仓库错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该转仓', 'Query failed, the warehouse transfer was not found', '查询失败，未找到该转仓');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中目标仓库', 'No target warehouse selected', '未选中目标仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单中的商品不可被选中', 'Products in the outbound order cannot be selected', '出库单中的商品不可被选中');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在商品已处于转仓中', 'There are products already in the process of warehouse transfer', '存在商品已处于转仓中');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该sku池', 'Query failed, the SKU pool was not found', '查询失败，未找到该sku池');
insert into sys_language_config (zh_cn, en_us, msg) values ('pku已存在', 'PKU already exists', 'pku已存在');
insert into sys_language_config (zh_cn, en_us, msg) values ('pku不可重复', 'PKU cannot be duplicated', 'pku不可重复');
insert into sys_language_config (zh_cn, en_us, msg) values ('请检查参数', 'Please check the parameters', '请检查参数');
insert into sys_language_config (zh_cn, en_us, msg) values ('pku、sku、尺码为必填项', 'PKU, SKU, and size are required fields', 'pku、sku、尺码为必填项');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写账号', 'Please fill in the account', '请填写账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写密码', 'Please fill in the password', '请填写密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该商家数据权限', 'Query failed, the merchant data permissions were not found', '查询失败，未找到该商家数据权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该入库商品', 'Query failed, the warehoused product was not found', '查询失败，未找到该入库商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库单已失效', 'The warehousing order has expired', '入库单已失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('核验已完成', 'Verification completed', '核验已完成');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已入库', 'Product has been warehoused', '商品已入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('该包裹未曾预报', 'The package was not forecasted', '该包裹未曾预报');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已入库且非空闲状态，不可删除', 'Product has been warehoused and is not in idle state, cannot delete', '商品已入库且非空闲状态，不可删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库已完成，商品信息不可修改', 'Warehousing completed, product information cannot be modified', '入库已完成，商品信息不可修改');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在出库单正在操作', 'There are outbound orders being processed', '存在出库单正在操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在丢失的商品，无法生成出库批次', 'There are missing products, cannot generate outbound batch', '存在丢失的商品，无法生成出库批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('清单对象不明', 'Unknown list object', '清单对象不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的oneId', 'Invalid OneId', '无效的oneId');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品无需出库', 'Product does not need to be outbound', '商品无需出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('该商品正在待处理的出库批次中', 'This product is in the pending outbound batch', '该商品正在待处理的出库批次中');
insert into sys_language_config (zh_cn, en_us, msg) values ('该商品不在此出库单中', 'This product is not in this outbound order', '该商品不在此出库单中');
insert into sys_language_config (zh_cn, en_us, msg) values ('该商品不在此出库批次中', 'This product is not in this outbound batch', '该商品不在此出库批次中');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已丢失，如已找到请先取消丢失', 'Product is missing, if found please cancel the missing status first', '商品已丢失，如已找到请先取消丢失');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已拣货', 'Product has been picked', '商品已拣货');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已打包', 'Product has been packed', '商品已打包');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品未拣货', 'Product not picked', '商品未拣货');
insert into sys_language_config (zh_cn, en_us, msg) values ('该商品无需出库', 'This product does not need to be outbound', '该商品无需出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('记录已失效', 'Record has expired', '记录已失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已出库', 'Product has been outbound', '商品已出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品未丢失，无需操作', 'Product not missing, no action needed', '商品未丢失，无需操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('至少要有一个已打包的商品', 'There must be at least one packed product', '至少要有一个已打包的商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该出库商品', 'Query failed, the outbound product was not found', '查询失败，未找到该出库商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该出库单', 'Query failed, the outbound order was not found', '查询失败，未找到该出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单状态已变更', 'Outbound order status has changed', '出库单状态已变更');
insert into sys_language_config (zh_cn, en_us, msg) values ('该出库单正在待处理的出库批次中', 'This outbound order is in the pending outbound batch', '该出库单正在待处理的出库批次中');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在丢失的商品，无法完成出库', 'There are missing products, cannot complete outbound', '存在丢失的商品，无法完成出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该出库单任务情况', 'Query failed, the outbound order task situation was not found', '查询失败，未找到该出库单任务情况');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在货架名已被使用', 'There is a shelf name that has been used', '存在货架名已被使用');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该仓库人员权限', 'Query failed, the warehouse personnel permissions were not found', '查询失败，未找到该仓库人员权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询失败，未找到该提现', 'Query failed, the withdrawal was not found', '查询失败，未找到该提现');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写充值金额', 'Please fill in the recharge amount', '请填写充值金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写收款账号', 'Please fill in the receiving account', '请填写收款账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('可提现金额不足', 'Insufficient withdrawable amount', '可提现金额不足');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入验证码', 'Please enter the verification code', '请输入验证码');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码失效', 'Verification code expired', '验证码失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码错误，请重试', 'Verification code error, please try again', '验证码错误，请重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('公众号配置未设置', 'Public account configuration not set', '公众号配置未设置');
insert into sys_language_config (zh_cn, en_us, msg) values ('授权失败', 'Authorization failed', '授权失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('评论内容中含有违法违规内容', 'The comment contains illegal content', '评论内容中含有违法违规内容');
insert into sys_language_config (zh_cn, en_us, msg) values ('未作任意修改', 'No modification made', '未作任意修改');
insert into sys_language_config (zh_cn, en_us, msg) values ('表单信息不完整', 'Incomplete form information', '表单信息不完整');
insert into sys_language_config (zh_cn, en_us, msg) values ('执行定时任务失败', 'Failed to execute scheduled task', '执行定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('获取定时任务CronTrigger出现异常', 'Exception occurred while getting scheduled task CronTrigger', '获取定时任务CronTrigger出现异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('创建定时任务失败', 'Failed to create scheduled task', '创建定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('更新定时任务失败', 'Failed to update scheduled task', '更新定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('立即执行定时任务失败', 'Failed to execute scheduled task immediately', '立即执行定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂停定时任务失败', 'Failed to pause scheduled task', '暂停定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('恢复定时任务失败', 'Failed to resume scheduled task', '恢复定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除定时任务失败', 'Failed to delete scheduled task', '删除定时任务失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选中任意仓库', 'No warehouse selected', '未选中任意仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库不明', 'Unknown warehouse', '仓库不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('未找到该商品', 'Product not found', '未找到该商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('非本仓库商品', 'Product not in this warehouse', '非本仓库商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品正在出库中', 'Product is being outbound', '商品正在出库中');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 静默生成预报包裹失败，根据 Sys Prod 的 id 未找到关联的 Sys Prod Deal 数据', 'StockX silent forecast package generation failed, no associated Sys Prod Deal data found based on Sys Prod id', 'StockX 静默生成预报包裹失败，根据 Sys Prod 的 id 未找到关联的 Sys Prod Deal 数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 静默生成预报包裹失败，运单号已经存在, tracking code:', 'StockX silent forecast package generation failed, waybill number already exists, tracking code:', 'StockX 静默生成预报包裹失败，运单号已经存在, tracking code:');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 静默生成预报包裹失败，未找到关联的 Sys Prod 数据.', 'StockX silent forecast package generation failed, no associated Sys Prod data found.', 'StockX 静默生成预报包裹失败，未找到关联的 Sys Prod 数据.');
insert into sys_language_config (zh_cn, en_us, msg) values ('请检查sku的格式[', 'Please check the format of the SKU[', '请检查sku的格式[');
insert into sys_language_config (zh_cn, en_us, msg) values ('请检查尺码的格式[', 'Please check the format of the size[', '请检查尺码的格式[');
insert into sys_language_config (zh_cn, en_us, msg) values (']，勿出现连续的空格', '], no consecutive spaces allowed', ']，勿出现连续的空格');
insert into sys_language_config (zh_cn, en_us, msg) values (']，首尾部分勿出现符号', '], no symbols allowed at the beginning or end', ']，首尾部分勿出现符号');
insert into sys_language_config (zh_cn, en_us, msg) values ('预约入库', 'To Warehouse', '预约入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库预约', 'Warehousing reservation', '入库预约');
insert into sys_language_config (zh_cn, en_us, msg) values ('预约详情', 'Reservation details', '预约详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫码入库', 'Scan code into warehouse', '扫码入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架管理', 'Shelf management', '货架管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库管理', 'Outbound management', '出库管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('极速套现', 'Quick cash out', '极速套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库批次管理', 'Outbound batch management', '出库批次管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存查看', 'Inventory detail', '库存查看');
insert into sys_language_config (zh_cn, en_us, msg) values ('转仓管理', 'Switch user management', '转仓管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增预报', 'New reservation', '新增预报');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现详情', 'Cash out details', '套现详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运详情', 'Transshipment details', '转运详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发详情', 'Reshipping details', '代发详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售', 'Consignment', '寄售');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核中心', 'Audit Center', '审核中心');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台服务', 'KnetGroup services', '平台服务');
insert into sys_language_config (zh_cn, en_us, msg) values ('自营仓库', 'Self-operated warehouse', '自营仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库管理', 'Warehouse management', '仓库管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库设置', 'Warehouse settings', '仓库设置');
insert into sys_language_config (zh_cn, en_us, msg) values ('新建仓库', 'Create a new warehouse', '新建仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库批次管理', 'Receiving batch management', '入库批次管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发审核', 'Ship with label review', '代发审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发审核详情', 'Ship with label review detail', '代发审核详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运审核', 'Transshipment review', '转运审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运审核详情', 'Transshipment review detail', '转运审核详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现审核', 'Cash out review', '套现审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现审核详情', 'Cash out review detail', '套现审核详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台内转移详情', 'Switch user detail', '平台内转移详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖审核详情', 'Consignment review detail', '寄卖审核详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('任务管理', 'Task management', '任务管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单任务', 'Outbound order tasks', '出库单任务');
insert into sys_language_config (zh_cn, en_us, msg) values ('批次任务', 'Batch task', '批次任务');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售详情', 'Consignment details', '寄售详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('财务管理', 'Financial Management', '财务管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现详情页', 'Withdrawal details', '提现详情页');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值详情页', 'Recharge details', '充值详情页');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值详情', 'Recharge Detail', '充值详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值说明', 'Recharge Instructions', '充值说明');
insert into sys_language_config (zh_cn, en_us, msg) values ('公告管理', 'Announcement management', '公告管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('收费管理', 'Charge management', '收费管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('单号数量', 'Order number quantity', '单号数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('批次总计', 'Batch total', '批次总计');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库数量', 'Outbound quantity', '出库数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('时间区间', 'Day range', '时间区间');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品库存数', 'Product inventory', '商品库存数');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖中商品数', 'Number of items on consignment', '寄卖中商品数');
insert into sys_language_config (zh_cn, en_us, msg) values ('sku总量', 'Total amount of sku', 'sku总量');
insert into sys_language_config (zh_cn, en_us, msg) values ('已预约包裹数量', 'Number of reserved packages', '已预约包裹数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台收入', 'Platform revenue', '平台收入');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现总支出', 'Total cash out expenses', '套现总支出');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日待充值总笔数', 'The total number of transactions to be recharged today', '今日待充值总笔数');
insert into sys_language_config (zh_cn, en_us, msg) values ('待充值总金额', 'Total amount to be recharged', '待充值总金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日待提现总笔数', 'The total number of withdrawals pending today', '今日待提现总笔数');
insert into sys_language_config (zh_cn, en_us, msg) values ('待提现总金额', 'Total amount to be withdrawn', '待提现总金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('预约包裹', 'Reserve packages', '预约包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('待验货包裹', 'Packages awaiting inspection', '待验货包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单总数', 'Total number of orders', '订单总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品总数', 'Total number of items', '商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发订单数', 'Number of shipping with label orders', '代发订单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发商品数', 'Total number of ship with label orders', '代发商品数');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运订单数', 'Number of reship without label orders', '转运订单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运商品数', 'Total number of reship without label orders', '转运商品数');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖订单数', 'Number of consignment orders', '寄卖订单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖商品数', 'Total number of consignment orders', '寄卖商品数');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现订单数', 'Number of cash out orders', '套现订单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台内转移订单数', 'Number of switch user orders', '平台内转移订单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库情况', 'Inbound status', '入库情况');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库情况', 'Outbound status', '出库情况');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库排名', 'Inbound ranking', '入库排名');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库排名', 'Outbound ranking', '出库排名');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库统计', 'Inbound statistics', '入库统计');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库统计', 'Outbound statistics', '出库统计');
insert into sys_language_config (zh_cn, en_us, msg) values ('财务统计', 'Financial Statistics', '财务统计');
insert into sys_language_config (zh_cn, en_us, msg) values ('财务待办', 'Financial to-do', '财务待办');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库待办', 'Warehouse to-do', '仓库待办');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日待出库订单', 'Orders to be shipped out today', '今日待出库订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('待验货包裹总数', 'Total number of packages to be inspected', '待验货包裹总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('待出库商品总数', 'Total number of items to be outbound', '待出库商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('已入库包裹数量', 'Number of packages inbound', '已入库包裹数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日入库', 'Total number of items inbound today', '今日入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日出库', 'Total number of items outbound today', '今日出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('未上架库存总数', 'Total number of unlisted items', '未上架库存总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日验货', 'Inspection today', '今日验货');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日待出库任务', 'Tasks to be shipped out today', '今日待出库任务');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日待出库批次任务', 'Batch tasks to be shipped out today', '今日待出库批次任务');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单数量', 'Quantity of orders', '订单数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品数', 'Number of items', '商品数');
insert into sys_language_config (zh_cn, en_us, msg) values ('公告', 'Announcement', '公告');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹', 'Package', '包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('批次数量', 'Batch quantity', '批次数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改成本', 'Batch modification cost', '批量修改成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改成本', 'Modification cost', '修改成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改货源', 'Batch modification supply', '批量修改货源');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改货源', 'Modification supply', '修改货源');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改验货结果', 'Batch modification Inspection result', '批量修改验货结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改验货结果', 'Modification Inspection result', '修改验货结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库数量', 'Inbound Quantity', '入库数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库成本', 'Inbound cost', '入库成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库成本', 'Outbound cost', '出库成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('已上架数量', 'Listed', '已上架数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('未上架数量', 'Unlisted', '未上架数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('总价值', 'Total Value', '总价值');
insert into sys_language_config (zh_cn, en_us, msg) values ('瑕疵商品', 'Defects', '瑕疵商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖售出', 'Consignment sold', '寄卖售出');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核中', 'Reviewing', '审核中');
insert into sys_language_config (zh_cn, en_us, msg) values ('已拒绝', 'Rejected', '已拒绝');
insert into sys_language_config (zh_cn, en_us, msg) values ('待确认报价', 'Quotation to be confirmed', '待确认报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('已套现', 'Completed', '已套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('已取消', 'Cancelled', '已取消');
insert into sys_language_config (zh_cn, en_us, msg) values ('待商家确认', 'To be confirmed by user', '待商家确认');
insert into sys_language_config (zh_cn, en_us, msg) values ('急速套现', 'Cashout request', '急速套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库', 'Received', '入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('修复中', 'Repairing', '修复中');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发审批结果', 'Approval results for ship with label', '代发审批结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运审批结果', 'Approval results for reship without label', '转运审批结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现审批结果', 'Approval results for cashout', '套现审批结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售审批结果', 'Approval results for consign', '寄售审批结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台内转移审批结果', 'Approval results for switch user', '平台内转移审批结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('瑕疵-鞋盒破损', 'Defects - shoe box broken', '瑕疵-鞋盒破损');
insert into sys_language_config (zh_cn, en_us, msg) values ('瑕疵-球鞋破损', 'Defects - shoe Partially broken', '瑕疵-球鞋破损');
insert into sys_language_config (zh_cn, en_us, msg) values ('质检失败-假鞋', 'Failed- Fake Shoes', '质检失败-假鞋');
insert into sys_language_config (zh_cn, en_us, msg) values ('转自营', 'Switch to knetgroup', '转自营');
insert into sys_language_config (zh_cn, en_us, msg) values ('已售出', 'Sold', '已售出');
insert into sys_language_config (zh_cn, en_us, msg) values ('超管', 'Super administrator', '超管');
insert into sys_language_config (zh_cn, en_us, msg) values ('管理端', 'Management terminal', '管理端');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家端', 'User terminal', '商家端');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家', 'User', '商家');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家预报', 'User Forecast', '商家预报');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家充值', 'Deposit', '商家充值');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家提现', 'Withdrawal', '商家提现');
insert into sys_language_config (zh_cn, en_us, msg) values ('交易中', 'In transaction', '交易中');
insert into sys_language_config (zh_cn, en_us, msg) values ('已过期', 'Expired', '已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台直接出库', 'Platform directly outbound', '平台直接出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('已生成批次', 'Batch generated', '已生成批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('部分出库', 'Partial outbound', '部分出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('完全出库', 'Fully outbound', '完全出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET收购', 'Sold To KNET', 'KNET收购');
insert into sys_language_config (zh_cn, en_us, msg) values ('转运出库', 'Outbound Shipment', '转运出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台账号', 'KG Employees Account', '平台账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('账户名称', 'Name', '账户名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('引荐人（标识）', 'Manager account', '引荐人（标识）');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号状态', 'Account Status', '账号状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('新建账号', 'Create account', '新建账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('上次登录时间', 'Last login time', '上次登录时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('查看账号', 'Review', '查看账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('编辑账号', 'Edit', '编辑账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增成功', 'Create successfully', '新增成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改成功', 'Change successfully', '修改成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号', 'Employee Account', '账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('创建日期', 'Create time', '创建日期');
insert into sys_language_config (zh_cn, en_us, msg) values ('使用人', 'Employee Name', '使用人');
insert into sys_language_config (zh_cn, en_us, msg) values ('角色', 'Character', '角色');
insert into sys_language_config (zh_cn, en_us, msg) values ('角色ID', 'Character ID', '角色ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家数据权限', 'User data permission', '商家数据权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('端口', 'Interface', '端口');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库端', 'Warehouse Interface', '仓库端');
insert into sys_language_config (zh_cn, en_us, msg) values ('超管中心', 'Mangement interface', '超管中心');
insert into sys_language_config (zh_cn, en_us, msg) values ('说明', 'Introduction', '说明');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号密码', 'Account number password', '账号密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('昵称', 'Nick name', '昵称');
insert into sys_language_config (zh_cn, en_us, msg) values ('姓名', 'Name', '姓名');
insert into sys_language_config (zh_cn, en_us, msg) values ('联系方式', 'Contact information', '联系方式');
insert into sys_language_config (zh_cn, en_us, msg) values ('弹窗', 'Tab', '弹窗');
insert into sys_language_config (zh_cn, en_us, msg) values ('重置密码', 'Reset password', '重置密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库', 'Outbound', '出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('下架', 'Deactivate', '下架');
insert into sys_language_config (zh_cn, en_us, msg) values ('撤回', 'Recall', '撤回');
insert into sys_language_config (zh_cn, en_us, msg) values ('补发', 'Replacement', '补发');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认丢失', 'Confirm Lost', '确认丢失');
insert into sys_language_config (zh_cn, en_us, msg) values ('确定要 {type} 吗?', 'Checking', '确定要 {type} 吗?');
insert into sys_language_config (zh_cn, en_us, msg) values ('请上传图片', 'Please upload pictures', '请上传图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('请上传Label', 'Please upload label', '请上传Label');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写收货人', 'Please upload address', '请填写收货人');
insert into sys_language_config (zh_cn, en_us, msg) values ('图片大小不能超过2M', 'Picture size can’t be more than 2M', '图片大小不能超过2M');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号选项', 'Interface selection', '账号选项');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库菜单权限请在仓库设置中选择', 'Set warehouse menu permission in Warehouse setting', '仓库菜单权限请在仓库设置中选择');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库权限', 'Warehouse permission', '仓库权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('有', 'Yes', '有');
insert into sys_language_config (zh_cn, en_us, msg) values ('无', 'No', '无');
insert into sys_language_config (zh_cn, en_us, msg) values ('超管中心菜单权限', 'Management menu permission', '超管中心菜单权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作权限', 'Edit permission', '操作权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('审批权限', 'Review permission', '审批权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家数据', 'User data permission', '商家数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部查看', 'Review all', '全部查看');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品权限', 'Product permission', '商品权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除商品', 'Delete product', '删除商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('超管权限', 'Manager permission', '超管权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入', 'Please input', '请输入');
insert into sys_language_config (zh_cn, en_us, msg) values ('数字', 'Number', '数字');
insert into sys_language_config (zh_cn, en_us, msg) values ('符号', 'Symbol', '符号');
insert into sys_language_config (zh_cn, en_us, msg) values ('保存', 'Save', '保存');
insert into sys_language_config (zh_cn, en_us, msg) values ('已勾选', 'Selected', '已勾选');
insert into sys_language_config (zh_cn, en_us, msg) values ('原识别码', 'Original user id', '原识别码');
insert into sys_language_config (zh_cn, en_us, msg) values ('新识别码', 'New user id', '新识别码');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库类别', 'Type of outbound', '出库类别');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择仓库', 'Please select warehouse', '请选择仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('查看详情', 'Review detail', '查看详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('货品数量', 'Product quantity', '货品数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓储费用', 'Storage fee', '仓储费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓储费用($)', 'Storage fee($)', '仓储费用($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('服务费用', 'Service fee', '服务费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('服务费用($)', 'Service fee($)', '服务费用($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台内转仓详情', 'Switch user detail', '平台内转仓详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('转移到', 'Switch to', '转移到');
insert into sys_language_config (zh_cn, en_us, msg) values ('编号', 'Outbound order number', '编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('费用计算', 'Cost', '费用计算');
insert into sys_language_config (zh_cn, en_us, msg) values ('保存修改', 'Save edit', '保存修改');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改费用', 'Change fee', '修改费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('本次运费', 'Shipping fees', '本次运费');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核通过', 'Approved', '审核通过');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不提现', 'Not cashing', '暂不提现');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核拒绝', 'Audit rejected', '审核拒绝');
insert into sys_language_config (zh_cn, en_us, msg) values ('在仓费用', 'Storage fee', '在仓费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('在仓费用($)', 'Storage fee($)', '在仓费用($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('无主件', 'Unknown', '无主件');
insert into sys_language_config (zh_cn, en_us, msg) values ('在仓库存', 'In Stock Inventory', '在仓库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台寄售', 'Platform Consignment', '平台寄售');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台审核', 'Review', '平台审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('未提交', 'Unsubmitted', '未提交');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作成功', 'Successful operation', '操作成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台单号', 'Outbound Order Number', '平台单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量操作', 'Batch operation', '批量操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('按品名分类表格', 'List by product name', '按品名分类表格');
insert into sys_language_config (zh_cn, en_us, msg) values ('查看', 'Reviews', '查看');
insert into sys_language_config (zh_cn, en_us, msg) values ('关闭订单', 'Close order', '关闭订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量出库', 'Batch outbound', '批量出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量导出', 'Batch export', '批量导出');
insert into sys_language_config (zh_cn, en_us, msg) values ('关闭', 'Closed', '关闭');
insert into sys_language_config (zh_cn, en_us, msg) values ('运行中', 'Running', '运行中');
insert into sys_language_config (zh_cn, en_us, msg) values ('异常', 'Error', '异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择商品', 'Please select product', '请选择商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖出库', 'Consignment outbound', '寄卖出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认出库', 'Confirm outbound', '确认出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('联系电话', 'Contact number', '联系电话');
insert into sys_language_config (zh_cn, en_us, msg) values ('地址', 'Address', '地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('上传', 'Upload', '上传');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品数量', 'Product Quantity', '商品数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('所在库位', 'Shelf number', '所在库位');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售价格($)', 'Sale price($)', '寄售价格($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台收费合计($)', 'Total platform charges($)', '平台收费合计($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增地址', 'Add address', '新增地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改地址', 'Edit address', '修改地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('最后一个无法删除', 'Can’t delete last one', '最后一个无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出', 'Export', '导出');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量导入', 'Batch input', '批量导入');
insert into sys_language_config (zh_cn, en_us, msg) values ('模板下载', 'Format download', '模板下载');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售平台到手', 'Consignment payout', '寄售平台到手');
insert into sys_language_config (zh_cn, en_us, msg) values ('发货出库', 'Outbound', '发货出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('下载', 'Download', '下载');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫描', 'Scan', '扫描');
insert into sys_language_config (zh_cn, en_us, msg) values ('输入', 'Input', '输入');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库状态', 'Outbound status', '出库状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('已扫描', 'Scanned', '已扫描');
insert into sys_language_config (zh_cn, en_us, msg) values ('所在仓位', 'Shelf Number', '所在仓位');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫码状态', 'Scan status', '扫码状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量审批', 'Batch review', '批量审批');
insert into sys_language_config (zh_cn, en_us, msg) values ('运输费用($)', 'Shipping fee($)', '运输费用($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择出库单', 'Please select outbound order number', '请选择出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('通过成功', 'Approve', '通过成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('拒绝成功', 'Reject', '拒绝成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核结果', 'Review result', '审核结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('通过', 'Approve', '通过');
insert into sys_language_config (zh_cn, en_us, msg) values ('拒绝', 'Reject', '拒绝');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现编号', 'Cashout outbound order number', '套现编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('预计回收价($)', 'Pre-offer($)', '预计回收价($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台报价($)', 'Knetgroup offer($)', '平台报价($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现审批编号', 'Cashout Outbound order number', '套现审批编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量更改价格', 'Batch change price', '批量更改价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改报价', 'Change offer', '修改报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现价格', 'Cashout price', '套现价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('价格时效', 'Time', '价格时效');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报总价', 'Total pre-offer payout', '预报总价');
insert into sys_language_config (zh_cn, en_us, msg) values ('实际到手价', 'Total payout', '实际到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('报价完毕', 'Confirm offer', '报价完毕');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择货品', 'Please select product', '请选择货品');
insert into sys_language_config (zh_cn, en_us, msg) values ('报价状态', 'Offer status', '报价状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('球鞋修复申请记录', 'Shoe Repair Application Record', '球鞋修复申请记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品名称', 'Product name', '商品名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('留言', 'Message', '留言');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹信息', 'Package information', '包裹信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('维修报价', 'Repair Quotationr', '维修报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('本次申请修复商品', 'Apply for Repair', '本次申请修复商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('已报价', 'Quoted', '已报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('修复费用合计', 'Total repair cost', '修复费用合计');
insert into sys_language_config (zh_cn, en_us, msg) values ('撤销申请', 'Application canceled', '撤销申请');
insert into sys_language_config (zh_cn, en_us, msg) values ('同意报价', 'Agree to offer', '同意报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('查验结果', 'Check result', '查验结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('修复报价($)', 'Repair quote($)', '修复报价($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('打回', 'Returned', '打回');
insert into sys_language_config (zh_cn, en_us, msg) values ('已退货', 'Returned1', '已退货');
insert into sys_language_config (zh_cn, en_us, msg) values ('产品数量', 'Schedule item quantity', '产品数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库商品数量', 'Quantity of Inventory', '入库商品数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('已预报', 'Scheduled', '已预报');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部', 'All', '全部');
insert into sys_language_config (zh_cn, en_us, msg) values ('打回成功', 'Returned successfully', '打回成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报信息', 'Schedule information', '预报信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('取消上架', 'Cancel listing', '取消上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库实收', 'Received by warehouse', '仓库实收');
insert into sys_language_config (zh_cn, en_us, msg) values ('到货仓库', 'Warehouse', '到货仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('来源', 'Source', '来源');
insert into sys_language_config (zh_cn, en_us, msg) values ('打回原因', 'Returned reason', '打回原因');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库代号', 'Warehouse code', '仓库代号');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增包裹', 'Adding new package', '新增包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增商品', 'Add product', '新增商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除包裹', 'Delete package', '删除包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('货品', 'Item', '货品');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除货品', 'Delete item', '删除货品');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认', 'Confirm', '确认');
insert into sys_language_config (zh_cn, en_us, msg) values ('必须有货品信息', 'Must have product information', '必须有货品信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('保存成功', 'Saved successful', '保存成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台管理', 'Platform management', '平台管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号管理', 'Account management', '账号管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增', 'Add new', '新增');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖平台', 'Consign platform', '寄卖平台');
insert into sys_language_config (zh_cn, en_us, msg) values ('第三方手续费', 'Commission', '第三方手续费');
insert into sys_language_config (zh_cn, en_us, msg) values ('第三方提现费', 'Cashout fee', '第三方提现费');
insert into sys_language_config (zh_cn, en_us, msg) values ('第三方平台其他费用', 'Other cost', '第三方平台其他费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现费', 'Cashout Fee', '提现费');
insert into sys_language_config (zh_cn, en_us, msg) values ('手续费', 'Commission Fee', '手续费');
insert into sys_language_config (zh_cn, en_us, msg) values ('更新时间', 'Upgrade time', '更新时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台名称', 'Platform name', '平台名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('提交', 'Submit', '提交');
insert into sys_language_config (zh_cn, en_us, msg) values ('计算公式说明', 'Calculation formula description', '计算公式说明');
insert into sys_language_config (zh_cn, en_us, msg) values ('到手价', 'Revenue price', '到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入数字', 'Please input number', '请输入数字');
insert into sys_language_config (zh_cn, en_us, msg) values ('统计日期范围', 'Statistics date range', '统计日期范围');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家数量', 'Number of users', '商家数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家余额', 'User’s Wallet Balance', '商家余额');
insert into sys_language_config (zh_cn, en_us, msg) values ('本月平台收入', 'Platform revenue this month', '本月平台收入');
insert into sys_language_config (zh_cn, en_us, msg) values ('本月平台支出', 'Platform expenses this month', '本月平台支出');
insert into sys_language_config (zh_cn, en_us, msg) values ('查看商家流水', 'Check user’s cash flows', '查看商家流水');
insert into sys_language_config (zh_cn, en_us, msg) values ('钱包余额($)', 'Wallet balance($)', '钱包余额($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('本月交易金额($)', 'Transaction amount for this month($)', '本月交易金额($)');
insert into sys_language_config (zh_cn, en_us, msg) values ('本月交易单数', 'Number of transactions this month', '本月交易单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('收支分析', 'Income analysis', '收支分析');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖单号', 'Consignment number', '寄卖单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('用户名', 'Username', '用户名');
insert into sys_language_config (zh_cn, en_us, msg) values ('余额', 'Balance', '余额');
insert into sys_language_config (zh_cn, en_us, msg) values ('月', 'Month', '月');
insert into sys_language_config (zh_cn, en_us, msg) values ('单位', 'Unit', '单位');
insert into sys_language_config (zh_cn, en_us, msg) values ('元', 'Dollar', '元');
insert into sys_language_config (zh_cn, en_us, msg) values ('支出', 'Expenses', '支出');
insert into sys_language_config (zh_cn, en_us, msg) values ('收入', 'Revenue', '收入');
insert into sys_language_config (zh_cn, en_us, msg) values ('总交易笔数', 'Total number of transactions', '总交易笔数');
insert into sys_language_config (zh_cn, en_us, msg) values ('最近交易时间', 'Last transaction time', '最近交易时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请提交', 'Application submission', '申请提交');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台核对', 'Platform check', '平台核对');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值成功', 'Recharge successful', '充值成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值申请', 'Recharge application', '充值申请');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台充值', 'Platform recharge', '平台充值');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值审核', 'Recharge review', '充值审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请人', 'Applicant', '申请人');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请充值金额', 'Application recharge amount', '申请充值金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值后钱包余额', 'Wallet balance after recharge', '充值后钱包余额');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值方式', 'Recharge method', '充值方式');
insert into sys_language_config (zh_cn, en_us, msg) values ('银行卡', 'Bank card', '银行卡');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值码', 'Recharge code', '充值码');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核', 'Check', '审核');
insert into sys_language_config (zh_cn, en_us, msg) values ('打款', 'Paying', '打款');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核成功', 'Audit successful', '审核成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出当前列表', 'Export current list', '导出当前列表');
insert into sys_language_config (zh_cn, en_us, msg) values ('新建充值', 'New recharge', '新建充值');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核完成', 'Audit completed', '审核完成');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现成功', 'Successful withdrawal', '提现成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现申请', 'Withdrawal application', '提现申请');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台打款', 'Payment on the platform', '平台打款');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请提现金额', 'Application for cash withdrawal amount', '申请提现金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现后钱包余额', 'Wallet balance after withdrawal', '提现后钱包余额');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现方式', 'Withdrawal method', '提现方式');
insert into sys_language_config (zh_cn, en_us, msg) values ('银行汇款', 'Bank transfer', '银行汇款');
insert into sys_language_config (zh_cn, en_us, msg) values ('支付宝', 'Alipay', '支付宝');
insert into sys_language_config (zh_cn, en_us, msg) values ('账户姓名', 'Account name', '账户姓名');
insert into sys_language_config (zh_cn, en_us, msg) values ('开户行', 'Bank of account', '开户行');
insert into sys_language_config (zh_cn, en_us, msg) values ('打款成功', 'Successful payment', '打款成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值金额', 'Recharge amount', '充值金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('充值商家识别码', 'Recharge user ID', '充值商家识别码');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核操作', 'Audit operation', '审核操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('上传凭证', 'Upload certificate', '上传凭证');
insert into sys_language_config (zh_cn, en_us, msg) values ('打款操作', 'Payment operation', '打款操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('同意', 'Agree', '同意');
insert into sys_language_config (zh_cn, en_us, msg) values ('公告名称', 'Bulletin name', '公告名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增公告', 'New announcement', '新增公告');
insert into sys_language_config (zh_cn, en_us, msg) values ('公告图片', 'Announcement image', '公告图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('内容', 'Content', '内容');
insert into sys_language_config (zh_cn, en_us, msg) values ('可见端口', 'Visible port', '可见端口');
insert into sys_language_config (zh_cn, en_us, msg) values ('查看公告', 'View announcement', '查看公告');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改公告', 'Modification announcement', '修改公告');
insert into sys_language_config (zh_cn, en_us, msg) values ('多选框', 'Checkbox', '多选框');
insert into sys_language_config (zh_cn, en_us, msg) values ('发布端口', 'Publish port', '发布端口');
insert into sys_language_config (zh_cn, en_us, msg) values ('上传公告图片', 'Upload Announcement Image', '上传公告图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄卖出库单号', 'Consignment order number', '寄卖出库单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('按件平铺', 'Single product', '按件平铺');
insert into sys_language_config (zh_cn, en_us, msg) values ('按品名分类', 'Classified by product name', '按品名分类');
insert into sys_language_config (zh_cn, en_us, msg) values ('当前上架', 'Currently listing', '当前上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量售出并出库', 'Sold in bulk and out of warehouse', '批量售出并出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('销售时间', 'Sold time', '销售时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品图片', 'Product image', '商品图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('总库存', 'All of inventory', '总库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('合格库存', 'Qualified inventory', '合格库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('瑕疵库存', 'Defective inventory', '瑕疵库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存状态', 'Inventory status', '库存状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('在售', 'On the listing', '在售');
insert into sys_language_config (zh_cn, en_us, msg) values ('待售', 'Waiting for listing', '待售');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量删除', 'Batch delete', '批量删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('添加数据', 'Adding data', '添加数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台回收预估价', 'Estimatedprice of platform recovery', '平台回收预估价');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存告警', 'Alert Inventory', '库存告警');
insert into sys_language_config (zh_cn, en_us, msg) values ('创建人', 'Founder', '创建人');
insert into sys_language_config (zh_cn, en_us, msg) values ('添加', 'Add', '添加');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改', 'Modify', '修改');
insert into sys_language_config (zh_cn, en_us, msg) values ('告警库存', 'Alarm inventory', '告警库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('件以内', 'Within_item', '件以内');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台回收价格', 'Platform recovery price', '平台回收价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报', 'Schedule', '预报');
insert into sys_language_config (zh_cn, en_us, msg) values ('货品照片', 'Product image', '货品照片');
insert into sys_language_config (zh_cn, en_us, msg) values ('照片', 'Image', '照片');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作人', 'Operator', '操作人');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量打印', 'Batch printing', '批量打印');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量打印出库单', 'Batch print outbound list', '批量打印出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('分配任务', 'Assign work', '分配任务');
insert into sys_language_config (zh_cn, en_us, msg) values ('分配', 'Assign', '分配');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核人', 'Reviewer', '审核人');
insert into sys_language_config (zh_cn, en_us, msg) values ('分配时间', 'Assign time', '分配时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('已分配', 'Assigned', '已分配');
insert into sys_language_config (zh_cn, en_us, msg) values ('收件信息', 'Receiving information', '收件信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('有商品未打包', 'There are products that are not packaged', '有商品未打包');
insert into sys_language_config (zh_cn, en_us, msg) values ('是否继续出库', 'Whether to continue outbound', '是否继续出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库成功', 'Outbound successfully', '出库成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('label缺失，无法打印', 'The label is missing and cannot be printed', 'label缺失，无法打印');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择任务', 'Please select a task', '请选择任务');
insert into sys_language_config (zh_cn, en_us, msg) values ('分配成功', 'Assign successful', '分配成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('预约出库日期', 'Book an outbound date', '预约出库日期');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择仓位', 'Please select a position', '请选择仓位');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择升降序', 'Please select ascending/descending order', '请选择升降序');
insert into sys_language_config (zh_cn, en_us, msg) values ('默认', 'Default', '默认');
insert into sys_language_config (zh_cn, en_us, msg) values ('升序', 'Ascending', '升序');
insert into sys_language_config (zh_cn, en_us, msg) values ('降序', 'Descending', '降序');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择', 'Select', '请选择');
insert into sys_language_config (zh_cn, en_us, msg) values ('单一', 'Single', '单一');
insert into sys_language_config (zh_cn, en_us, msg) values ('多双', 'Multiple pairs', '多双');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量生成出库单', 'Batch generate outbound orders', '批量生成出库单');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部展开', 'Expand all', '全部展开');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部收起', 'Put all away', '全部收起');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单生成成功', 'Outbound orders generated successfully', '出库单生成成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('打印', 'Print', '打印');
insert into sys_language_config (zh_cn, en_us, msg) values ('拣货', 'Picking', '拣货');
insert into sys_language_config (zh_cn, en_us, msg) values ('打包', 'Pack', '打包');
insert into sys_language_config (zh_cn, en_us, msg) values ('未扫描', 'Not scanned', '未扫描');
insert into sys_language_config (zh_cn, en_us, msg) values ('格式错误', 'Wrong format', '格式错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('快速搜索', 'Quickfind', '快速搜索');
insert into sys_language_config (zh_cn, en_us, msg) values ('空闲合格', 'QuickfindQualified', '空闲合格');
insert into sys_language_config (zh_cn, en_us, msg) values ('空闲可售瑕疵', 'QuickfindDefects', '空闲可售瑕疵');
insert into sys_language_config (zh_cn, en_us, msg) values ('规则描述', 'Rule description', '规则描述');
insert into sys_language_config (zh_cn, en_us, msg) values ('品名/图片为空', 'CheckEM', '品名/图片为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入至少五个字符的规则描述', 'Please enter a rule description of at least five characters', '请输入至少五个字符的规则描述');
insert into sys_language_config (zh_cn, en_us, msg) values ('非一个仓库的商品不能一起代发或者转运', 'Products are not in the same warehouse', '非一个仓库的商品不能一起代发或者转运');
insert into sys_language_config (zh_cn, en_us, msg) values ('用第一个', 'Use the first', '用第一个');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询不同的数量', 'Query different quantities', '查询不同的数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('非一个商家的商品不能一起转运', 'Products from different users cannot be transshipped together', '非一个商家的商品不能一起转运');
insert into sys_language_config (zh_cn, en_us, msg) values ('重新上报', 'Resubmit', '重新上报');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库名称', 'Warehouse name', '仓库名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库人员', 'Warehouse employees', '仓库人员');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增仓库', 'Add warehouse', '新增仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('当前库存', 'Inventory', '当前库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('库位数量', 'Location Quantity', '库位数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('按货架分类', 'Classified by rack', '按货架分类');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出库存', 'Export inventory', '导出库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量代发', 'Batch ship with label', '批量代发');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量套现', 'Batch Cashout request', '批量套现');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量转运', 'Batch reship without label', '批量转运');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量寄卖', 'Batch consign', '批量寄卖');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量平台内转移', 'Batch intra-platform transfer', '批量平台内转移');
insert into sys_language_config (zh_cn, en_us, msg) values ('归属', 'Belong', '归属');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存数量', 'Inventory', '库存数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('成本合计', 'Total Cost', '成本合计');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量上架(Touch)', 'Cross ListingT', '批量上架(Touch)');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架商品', 'Cross Listing', '上架商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架全新商品', 'Cross Listing Label', '上架全新商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架瑕疵商品', 'Cross Listing Issue', '上架瑕疵商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量发货', 'Reshipping', '批量发货');
insert into sys_language_config (zh_cn, en_us, msg) values ('在线', 'ON', '在线');
insert into sys_language_config (zh_cn, en_us, msg) values ('离线', 'OFF', '离线');
insert into sys_language_config (zh_cn, en_us, msg) values ('打开', 'Open', '打开');
insert into sys_language_config (zh_cn, en_us, msg) values ('正常', 'Normal', '正常');
insert into sys_language_config (zh_cn, en_us, msg) values ('冻结', 'Frozen', '冻结');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库信息', 'Warehouse information', '仓库信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('存仓超过', 'Storage limited time', '存仓超过');
insert into sys_language_config (zh_cn, en_us, msg) values ('天', 'Day', '天');
insert into sys_language_config (zh_cn, en_us, msg) values ('单日存仓费用', 'Daily storage fee', '单日存仓费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('日', 'Day', '日');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库地址', 'Warehouse Address', '仓库地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓库等级', 'Warehouse Level', '仓库等级');
insert into sys_language_config (zh_cn, en_us, msg) values ('基础仓库', 'Base Warehouse', '基础仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('高级仓库', 'Premium Warehouse', '高级仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('移出仓库', 'Move out of warehouse', '移出仓库');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓位号', 'Position number', '仓位号');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增货架', 'Add Rack', '新增货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('打印仓位号', 'Print rack information', '打印仓位号');
insert into sys_language_config (zh_cn, en_us, msg) values ('打印货架码', 'Print', '打印货架码');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改货架', 'Edit Rack information', '修改货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('菜单权限', 'Menu permission', '菜单权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架号', 'Rack number', '货架号');
insert into sys_language_config (zh_cn, en_us, msg) values ('最大可放置数量', 'The maximum number of places that can be placed', '最大可放置数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选择权限菜单', 'Permissions menu not selected', '未选择权限菜单');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架号不能为中文', 'Name of rack can not be Chinese', '货架号不能为中文');
insert into sys_language_config (zh_cn, en_us, msg) values ('请保存仓库账号', 'Please save warehouse account', '请保存仓库账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('中文不能打印', 'Chinese cannot be printed', '中文不能打印');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增货架号数量', 'Add rack quantity', '新增货架号数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('生成货架', 'Add rack', '生成货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入SKU/PKU进行查询', 'Please enter SKU/PKU to query', '请输入SKU/PKU进行查询');
insert into sys_language_config (zh_cn, en_us, msg) values ('总数量', 'Qty', '总数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('当前商家等级', 'Current user level', '当前商家等级');
insert into sys_language_config (zh_cn, en_us, msg) values ('出入库情况', 'Inbound and outbound status', '出入库情况');
insert into sys_language_config (zh_cn, en_us, msg) values ('出入库金额情况', 'Inbound and outbound amount', '出入库金额情况');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库总金额', 'Total amount warehousing', '入库总金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库总金额', 'Total amount of shipment', '出库总金额');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库商品总数', 'Total number of items inbound', '入库商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库商品总数', 'Total number of items outbound', '出库商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('在途资金', 'Pending credit', '在途资金');
insert into sys_language_config (zh_cn, en_us, msg) values ('入驻商家', 'Seller', '入驻商家');
insert into sys_language_config (zh_cn, en_us, msg) values ('预约包裹数', 'Total number of reserved packages', '预约包裹数');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日预约包裹数', 'Reserved Packages', '今日预约包裹数');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日入库数', 'Inbound Today', '今日入库数');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日出库数', 'Outbound Today', '今日出库数');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日订单数', 'Pending Orders', '今日订单数');
insert into sys_language_config (zh_cn, en_us, msg) values ('待处理订单', 'Today’s pending orders', '待处理订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存成本', 'Inventory costs', '库存成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('总成本', 'Total product cost', '总成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('瑕疵库存成本', 'Defective inventory cost', '瑕疵库存成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('绑定信息', 'Binding information', '绑定信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('关联账号', 'Link account', '关联账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('在仓库存成本', 'Inventory costs in warehouse', '在仓库存成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库申请编号', 'Outbound application number', '出库申请编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('总销售额', 'Total Sales', '总销售额');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日销售额', 'Today Sales', '今日销售额');
insert into sys_language_config (zh_cn, en_us, msg) values ('预估总利润', 'Total Profit', '预估总利润');
insert into sys_language_config (zh_cn, en_us, msg) values ('预估今日利润', 'Today Profit', '预估今日利润');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日', 'Today', '今日');
insert into sys_language_config (zh_cn, en_us, msg) values ('未填写成本的商品，成本当作$0来预估利润', 'Total Profit Tips', '未填写成本的商品，成本当作$0来预估利润');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单平台分布', 'Order Platform Distribution', '订单平台分布');
insert into sys_language_config (zh_cn, en_us, msg) values ('已销售', 'Orders', '已销售');
insert into sys_language_config (zh_cn, en_us, msg) values ('日销售额', 'Daily Sales Statistics', '日销售额');
insert into sys_language_config (zh_cn, en_us, msg) values ('销售利润', 'Sales Profit', '销售利润');
insert into sys_language_config (zh_cn, en_us, msg) values ('日销售平台分布', 'Daily Sales Platform Distribution', '日销售平台分布');
insert into sys_language_config (zh_cn, en_us, msg) values ('畅销排行榜', 'TOP Sales Shoe', '畅销排行榜');
insert into sys_language_config (zh_cn, en_us, msg) values ('活跃卖家', 'TOP Sales Shop', '活跃卖家');
insert into sys_language_config (zh_cn, en_us, msg) values ('过去30天', 'In past 30 days', '过去30天');
insert into sys_language_config (zh_cn, en_us, msg) values ('今日数据', 'DayData', '今日数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('钱包', 'Wallet', '钱包');
insert into sys_language_config (zh_cn, en_us, msg) values ('提现方式选择银行汇款时需要', 'Withdrawal method-Required when choosing bank transfer', '提现方式选择银行汇款时需要');
insert into sys_language_config (zh_cn, en_us, msg) values ('地址管理', 'Address management', '地址管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('收货人', 'Recipient', '收货人');
insert into sys_language_config (zh_cn, en_us, msg) values ('国家', 'Nation', '国家');
insert into sys_language_config (zh_cn, en_us, msg) values ('所在地区', 'Area', '所在地区');
insert into sys_language_config (zh_cn, en_us, msg) values ('详细地址', 'Address', '详细地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮编', 'Zip code', '邮编');
insert into sys_language_config (zh_cn, en_us, msg) values ('地址类型', 'Address type', '地址类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('省/州', 'Province / State', '省/州');
insert into sys_language_config (zh_cn, en_us, msg) values ('手机号', 'Phone', '手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('城市', 'City', '城市');
insert into sys_language_config (zh_cn, en_us, msg) values ('地址信息', 'Address information', '地址信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('收货人姓名', 'Recipient name', '收货人姓名');
insert into sys_language_config (zh_cn, en_us, msg) values ('身份证号(发货至中国必填)', 'ID Card Number (Ship to China Required)', '身份证号(发货至中国必填)');
insert into sys_language_config (zh_cn, en_us, msg) values ('设置为默认地址', 'Make this my default address', '设置为默认地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('设置为默认账户', 'Make this my default Account', '设置为默认账户');
insert into sys_language_config (zh_cn, en_us, msg) values ('个人中心', 'Personal center', '个人中心');
insert into sys_language_config (zh_cn, en_us, msg) values ('语言', 'Language', '语言');
insert into sys_language_config (zh_cn, en_us, msg) values ('个人信息', 'Personal information', '个人信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('第三方关联', 'Third party association', '第三方关联');
insert into sys_language_config (zh_cn, en_us, msg) values ('交易账户', 'Transaction account', '交易账户');
insert into sys_language_config (zh_cn, en_us, msg) values ('头像', 'Avatar', '头像');
insert into sys_language_config (zh_cn, en_us, msg) values ('标识', 'Identifier', '标识');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮箱', 'Email', '邮箱');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码', 'Password', '密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改密码', 'Changepassword', '修改密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('新密码', 'Newpassword', '新密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认密码', 'Confirmpassword', '确认密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('已关联', 'Linked', '已关联');
insert into sys_language_config (zh_cn, en_us, msg) values ('未关联', 'Not linked', '未关联');
insert into sys_language_config (zh_cn, en_us, msg) values ('支付宝信息', 'Alipay information', '支付宝信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('支付宝账号', 'Alipay account', '支付宝账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号姓名', 'Account name', '账号姓名');
insert into sys_language_config (zh_cn, en_us, msg) values ('银行卡信息', 'Bank card information', '银行卡信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('银行账号', 'Bank Account', '银行账号');
insert into sys_language_config (zh_cn, en_us, msg) values ('银行名称', 'Bank name', '银行名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('支行名称', 'Branch name', '支行名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('xxx', 'ABA Routing number', 'xxx');
insert into sys_language_config (zh_cn, en_us, msg) values ('正在扫描包裹', 'Scanning package', '正在扫描包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('正在扫描PKU', 'Scanning PKU', '正在扫描PKU');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫描PKU', 'Scan PKU', '扫描PKU');
insert into sys_language_config (zh_cn, en_us, msg) values ('直接输入SKU', 'Enter SKU', '直接输入SKU');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量', 'Batch', '批量');
insert into sys_language_config (zh_cn, en_us, msg) values ('请扫描/输入ONE ID', 'Please scan/enter ONE ID', '请扫描/输入ONE ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('请扫描/输入PKU', 'Please scan/enter PKU', '请扫描/输入PKU');
insert into sys_language_config (zh_cn, en_us, msg) values ('请扫描/输入物流单号', 'Please scan package no', '请扫描/输入物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫描时间', 'Scan time', '扫描时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('批次状态', 'Batch status', '批次状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增批次', 'Add new batch', '新增批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('待上架商品', 'Ready for listing items', '待上架商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部上架', 'LISTING all', '全部上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('请扫描/输入货架名', 'Please scan/enter the shelf name', '请扫描/输入货架名');
insert into sys_language_config (zh_cn, en_us, msg) values ('未上架', 'Ready for listing', '未上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('已扫描货品', 'Scanned items', '已扫描货品');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品信息', 'Product information', '商品信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部清空', 'Clear all', '全部清空');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架', 'LISTING', '上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架时间', 'Added time', '上架时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('扫描ONE ID', 'Scan ONE ID', '扫描ONE ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('打印拣货单', 'Print picking list', '打印拣货单');
insert into sys_language_config (zh_cn, en_us, msg) values ('下载label', 'Download label', '下载label');
insert into sys_language_config (zh_cn, en_us, msg) values ('丢失', 'Lost', '丢失');
insert into sys_language_config (zh_cn, en_us, msg) values ('取消丢失', 'Cancel lost', '取消丢失');
insert into sys_language_config (zh_cn, en_us, msg) values ('拣货记录', 'Picking records', '拣货记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('已拣货', 'Picked', '已拣货');
insert into sys_language_config (zh_cn, en_us, msg) values ('待入库', 'Pending into warehouse', '待入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('待拣货', 'To be picking', '待拣货');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量取消', 'Batch cancellation', '批量取消');
insert into sys_language_config (zh_cn, en_us, msg) values ('主页', 'Home', '主页');
insert into sys_language_config (zh_cn, en_us, msg) values ('首页', 'Top', '首页');
insert into sys_language_config (zh_cn, en_us, msg) values ('数据看板', 'Dashboard', '数据看板');
insert into sys_language_config (zh_cn, en_us, msg) values ('分析页', 'Analysis', '分析页');
insert into sys_language_config (zh_cn, en_us, msg) values ('监控页', 'Monitor', '监控页');
insert into sys_language_config (zh_cn, en_us, msg) values ('工作台', 'Workplace', '工作台');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹管理', 'To Warehouse', '包裹管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存管理', 'In Warehouse', '库存管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存列表', 'Inventory', '库存列表');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库订单', 'Dropshipping', '出库订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架管理', 'Cross-listings', '上架管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('销售订单', 'SaleOrder', '销售订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架详情', 'Listing-detail', '上架详情');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售管理', 'Sales', '寄售管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单管理', 'Orders', '订单管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('寄售订单', 'Consigned Orders', '寄售订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('套现订单', 'Sell KNET Orders', '套现订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('资金流水', 'Wallet', '资金流水');
insert into sys_language_config (zh_cn, en_us, msg) values ('设置', 'Settings', '设置');
insert into sys_language_config (zh_cn, en_us, msg) values ('钱包管理', 'Wallet', '钱包管理');
insert into sys_language_config (zh_cn, en_us, msg) values ('请务必输入准确的物流单号', 'Form.placeholder.input.login', '请务必输入准确的物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('查询', 'Search', '查询');
insert into sys_language_config (zh_cn, en_us, msg) values ('重置', 'Reset', '重置');
insert into sys_language_config (zh_cn, en_us, msg) values ('导入', 'Import', '导入');
insert into sys_language_config (zh_cn, en_us, msg) values ('消息', 'Title', '消息');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认退出。', 'Content', '确认退出。');
insert into sys_language_config (zh_cn, en_us, msg) values ('在仓天数', 'Days-in-warehouse', '在仓天数');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量删除包裹', 'Bulk Delete', '批量删除包裹');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报货品数量', 'ScheduledItemQuantitv', '预报货品数量');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作', 'Operate', '操作');
insert into sys_language_config (zh_cn, en_us, msg) values ('订单编号', 'Order number', '订单编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('出售时间', 'Sale Time', '出售时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('您的报价/到手价', 'Your Price/Estimated Payout', '您的报价/到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('退货原因', 'Returned Reason', '退货原因');
insert into sys_language_config (zh_cn, en_us, msg) values ('货品图片', 'Lmage', '货品图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('三方平台状态', 'PlatStatus', '三方平台状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('用户ID', 'ShopUserUid', '用户ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架', 'RackId', '货架');
insert into sys_language_config (zh_cn, en_us, msg) values ('销售价格', 'Sale Price', '销售价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('Knet到手价', 'Knet Owning', 'Knet到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家到手价', 'Seller Owning', '商家到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('KG 备注', 'KG Ops Remark', 'KG 备注');
insert into sys_language_config (zh_cn, en_us, msg) values ('结束时间', 'End Time', '结束时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('费用', 'Fee', '费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('发起时间', 'Request Time', '发起时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入备注', 'Please enter the remark', '请输入备注');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入KG备注', 'Please enter the kg remark', '请输入KG备注');
insert into sys_language_config (zh_cn, en_us, msg) values ('您是第一个在KNET平台出价的', 'First list', '您是第一个在KNET平台出价的');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量确认', 'Bulk Confirm', '批量确认');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量撤回', 'Bulk Withdraw', '批量撤回');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改出库价格', 'Bulk Update Outbound Pricing', '批量修改出库价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架成功', 'Listing Successful', '上架成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('上架失败', 'Listing Failed', '上架失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('下架失败', 'Delisting Failed', '下架失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('下架成功', 'Delisting Successful', '下架成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('获取数据失败', 'Data Retrieval Failed', '获取数据失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('获取市场价失败', 'Market Price Retrieval Failed', '获取市场价失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('售价不能低于最低价格的30%', 'Pricecheck30', '售价不能低于最低价格的30%');
insert into sys_language_config (zh_cn, en_us, msg) values ('底线价不能小于等于0', 'Bottomline', '底线价不能小于等于0');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除成功', 'Deletion Successful', '删除成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台价格不能为0或空', 'Pricenull', '平台价格不能为0或空');
insert into sys_language_config (zh_cn, en_us, msg) values ('价格不得低于$10', 'Price10', '价格不得低于$10');
insert into sys_language_config (zh_cn, en_us, msg) values ('数据加载中...', 'Loading Data', '数据加载中...');
insert into sys_language_config (zh_cn, en_us, msg) values ('当前账号没有 listing 权限', 'ListingAuth', '当前账号没有 listing 权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品不是空闲状态', 'Notavailabel', '商品不是空闲状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品不合格', 'Notqualify', '商品不合格');
insert into sys_language_config (zh_cn, en_us, msg) values ('请联系管理开通寄卖权限', 'OpenAuth', '请联系管理开通寄卖权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('已出库，寄卖不能改出库价格', 'Notchangeprice', '已出库，寄卖不能改出库价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('撤销成功', 'Cancellation Successful', '撤销成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认成功', 'Confirmation Successful', '确认成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入相同的密码', 'Samepassword', '请输入相同的密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('历史记录', 'History Records', '历史记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹入库', 'Package Received', '包裹入库');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹查验', 'Package Inspected', '包裹查验');
insert into sys_language_config (zh_cn, en_us, msg) values ('完成验货', 'Complete Inspection', '完成验货');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发(自助上传Label)', 'Self-Provided label', '代发(自助上传Label)');
insert into sys_language_config (zh_cn, en_us, msg) values ('代发(KNET提供Label)', 'KNET-Provided label', '代发(KNET提供Label)');
insert into sys_language_config (zh_cn, en_us, msg) values ('在架全新商品', 'Qualified Listing', '在架全新商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('在架瑕疵商品', 'Defects Listing', '在架瑕疵商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('已上架/总库存数', 'Current inventory', '已上架/总库存数');
insert into sys_language_config (zh_cn, en_us, msg) values ('总库存数', 'Inventory num', '总库存数');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入账户', 'Account input', '请输入账户');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入右侧验证码', 'Code input', '请输入右侧验证码');
insert into sys_language_config (zh_cn, en_us, msg) values ('记住密码', 'Remember', '记住密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('忘记密码', 'Forget', '忘记密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录', 'Login', '登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码', 'Code', '验证码');
insert into sys_language_config (zh_cn, en_us, msg) values ('隐私政策和服务条款', 'Policy', '隐私政策和服务条款');
insert into sys_language_config (zh_cn, en_us, msg) values ('在售商品数据', 'On-Sale Product Data', '在售商品数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('真实在售商品数据', 'Real On-Sale Product Data', '真实在售商品数据');
insert into sys_language_config (zh_cn, en_us, msg) values ('最新记录', 'Latest Records', '最新记录');
insert into sys_language_config (zh_cn, en_us, msg) values ('生命周期', 'Lifecycle', '生命周期');
insert into sys_language_config (zh_cn, en_us, msg) values ('商家信息', 'Seller Information', '商家信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('验货时间', 'Inspection Time', '验货时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('验货信息', 'Inspection Information', '验货信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('图片', 'Picture', '图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库信息', 'Inbound Information', '入库信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('验货状态', 'Inspection Status', '验货状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('存仓状态', 'Warehouse Status', '存仓状态');
insert into sys_language_config (zh_cn, en_us, msg) values ('在仓时长', 'Warehouse Duration', '在仓时长');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库信息', 'Outbound Information', '出库信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('出库单编号', 'Outbounding Number', '出库单编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('预报人', 'Forecaster', '预报人');
insert into sys_language_config (zh_cn, en_us, msg) values ('查验批次', 'Inspection Batch', '查验批次');
insert into sys_language_config (zh_cn, en_us, msg) values ('检验结果', 'Inspection Result', '检验结果');
insert into sys_language_config (zh_cn, en_us, msg) values ('验货照片', 'Inspection Picture', '验货照片');
insert into sys_language_config (zh_cn, en_us, msg) values ('物流费用', 'Logistics Fee', '物流费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('其他费用', 'Other Fee', '其他费用');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请审批', 'Application Approval', '申请审批');
insert into sys_language_config (zh_cn, en_us, msg) values ('审批类型', 'Approval Type', '审批类型');
insert into sys_language_config (zh_cn, en_us, msg) values ('审批编号', 'Approval Number', '审批编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('编辑', 'Edit', '编辑');
insert into sys_language_config (zh_cn, en_us, msg) values ('删除', 'Delete', '删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('更多', 'More', '更多');
insert into sys_language_config (zh_cn, en_us, msg) values ('物流编号', 'Tracking number', '物流编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量下架', 'Batch removal', '批量下架');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增上架', 'New Listing', '新增上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('开始上架', 'Start Listing', '开始上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('检查遗漏订单', 'Check Missing Orders', '检查遗漏订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('系统自动带出识别码姓名', 'DealName', '系统自动带出识别码姓名');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改成本和货源', 'BulkCostSupplier', '批量修改成本和货源');
insert into sys_language_config (zh_cn, en_us, msg) values ('我的地址', 'My Address', '我的地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('客户地址', 'Client Address', '客户地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('点击上传图片', 'Click to upload image', '点击上传图片');
insert into sys_language_config (zh_cn, en_us, msg) values ('两次密码输入不一致', 'Match', '两次密码输入不一致');
insert into sys_language_config (zh_cn, en_us, msg) values ('总平均成本', 'Total average cost', '总平均成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('平均成本', 'Av cost', '平均成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('暂不支持售卖该类型产品', 'IsNotAvailabelSize', '暂不支持售卖该类型产品');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择空闲并且为可销售的瑕疵产品', 'Select available issue product', '请选择空闲并且为可销售的瑕疵产品');
insert into sys_language_config (zh_cn, en_us, msg) values ('创建BOX', 'Create Box', '创建BOX');
insert into sys_language_config (zh_cn, en_us, msg) values ('确认要将所选订单进行打包推送？', 'Create Box Check', '确认要将所选订单进行打包推送？');
insert into sys_language_config (zh_cn, en_us, msg) values ('你确定要关闭该订单吗?', 'Close Order Check', '你确定要关闭该订单吗?');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择同一个仓库创建Box', 'Different warehouses tips', '请选择同一个仓库创建Box');
insert into sys_language_config (zh_cn, en_us, msg) values ('所选项目前正在执行其他任务，请稍后再次尝试。', 'Update failed tips', '所选项目前正在执行其他任务，请稍后再次尝试。');
insert into sys_language_config (zh_cn, en_us, msg) values ('商品已售出,请耐心等待订单处理。', 'Not operation', '商品已售出,请耐心等待订单处理。');
insert into sys_language_config (zh_cn, en_us, msg) values ('您确定要拆箱吗？', 'Unpack Box Check', '您确定要拆箱吗？');
insert into sys_language_config (zh_cn, en_us, msg) values ('拆箱', 'Unpack Box', '拆箱');
insert into sys_language_config (zh_cn, en_us, msg) values ('部分选定产品正在进行其他任务，不支持重新改价', 'Bulk Pricing Check', '部分选定产品正在进行其他任务，不支持重新改价');
insert into sys_language_config (zh_cn, en_us, msg) values ('跳过并继续', 'SkipContinue', '跳过并继续');
insert into sys_language_config (zh_cn, en_us, msg) values ('部分出价低于平台最低价30%以上，确定以超低价出售吗？', 'More than 30% below', '部分出价低于平台最低价30%以上，确定以超低价出售吗？');
insert into sys_language_config (zh_cn, en_us, msg) values ('创建成功', 'Packaged successfully', '创建成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹ID', 'Package ID', '包裹ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('复制', 'Copy', '复制');
insert into sys_language_config (zh_cn, en_us, msg) values ('填写利润率', 'Fill in the profit margin', '填写利润率');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择改价策略', 'Please Select a Price Modify Strategy', '请选择改价策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('将商品上架的平台中到手价更高的平合价格，批量改为该平台最低价', 'Find a platform with a higher profit and modify the price in batches.', '将商品上架的平台中到手价更高的平合价格，批量改为该平台最低价');
insert into sys_language_config (zh_cn, en_us, msg) values ('将待改商品中，当前不是最低价的商品，批量改为平台最低价', 'The product that is not currently the lowest price will be modified to the lowest price on the platform in batches.', '将待改商品中，当前不是最低价的商品，批量改为平台最低价');
insert into sys_language_config (zh_cn, en_us, msg) values ('将根据商品成本和设置的利润率计算出理想售价，系统将自动比较理想售价与当前平台最低价并给出最优的待改价格，进行批量改价', 'The ideal selling price will be calculated based on the cost of the product and the set proft margin. It will be compared with the lowest price on the current platform, and the most suitable price will be obtained to modify the price in batches.', '将根据商品成本和设置的利润率计算出理想售价，系统将自动比较理想售价与当前平台最低价并给出最优的待改价格，进行批量改价');
insert into sys_language_config (zh_cn, en_us, msg) values ('盈利更多策略是如何改价的？', 'Earn More Profit Title', '盈利更多策略是如何改价的？');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动识别出到手价最高的平台，进行批量改价。待改价格可以选择与该平合的最低价持平或压价（$1）。通过设置差价，可以同时对差价范围内的其它平台进行改价。', 'Earn More Profit Des', '自动识别出到手价最高的平台，进行批量改价。待改价格可以选择与该平合的最低价持平或压价（$1）。通过设置差价，可以同时对差价范围内的其它平台进行改价。');
insert into sys_language_config (zh_cn, en_us, msg) values ('快速售出策略是如何改价的?', 'Quick Sale Title', '快速售出策略是如何改价的?');
insert into sys_language_config (zh_cn, en_us, msg) values ('将待改商品中，当前不是最低价的商品，批量改为平台最低价。可以选择持平或压价的改价方式进行批量改价。', 'Quick Sale Des', '将待改商品中，当前不是最低价的商品，批量改为平台最低价。可以选择持平或压价的改价方式进行批量改价。');
insert into sys_language_config (zh_cn, en_us, msg) values ('保障利润策略是如何改价的?', 'Profit Control Title', '保障利润策略是如何改价的?');
insert into sys_language_config (zh_cn, en_us, msg) values ('您确定要从平台上下架该商品吗？', 'Delist checking', '您确定要从平台上下架该商品吗？');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入您的价格, 默认为平台最低价.', 'Enter price', '请输入您的价格, 默认为平台最低价.');
insert into sys_language_config (zh_cn, en_us, msg) values ('您确定要修改该商品的价格吗？', 'Update check', '您确定要修改该商品的价格吗？');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动让你的出价与StockX自动持平', 'AutoPricingMode Stock des', '自动让你的出价与StockX自动持平');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动让你的出价比StockX最低价少 $1', 'AutoPricingMode Stock des1', '自动让你的出价比StockX最低价少 $1');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动让你的出价与GOAT自动持平', 'AutoPricingMode Goat des', '自动让你的出价与GOAT自动持平');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动让你的出价比GOAT最低价少 $1', 'AutoPricingMode Goat des1', '自动让你的出价比GOAT最低价少 $1');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动让你的出价与KNET自动持平', 'AutoPricingMode Goat Is des', '自动让你的出价与KNET自动持平');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动让你的出价比KNET最低价少 $1', 'AutoPricingMode Goat Is des1', '自动让你的出价比KNET最低价少 $1');
insert into sys_language_config (zh_cn, en_us, msg) values ('选择改价方式', 'Select the Method', '选择改价方式');
insert into sys_language_config (zh_cn, en_us, msg) values ('持平', 'Follow the price', '持平');
insert into sys_language_config (zh_cn, en_us, msg) values ('压价', 'Lower the price', '压价');
insert into sys_language_config (zh_cn, en_us, msg) values ('差价设置(非必填项)', 'Set price difference (optional)', '差价设置(非必填项)');
insert into sys_language_config (zh_cn, en_us, msg) values ('选择销售渠道', 'Select the Sales Channel', '选择销售渠道');
insert into sys_language_config (zh_cn, en_us, msg) values ('全部销售渠道', 'All Sales Channel', '全部销售渠道');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET', 'GOAT IS', 'KNET');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动跟价设置', 'Auto Pricing Settings', '自动跟价设置');
insert into sys_language_config (zh_cn, en_us, msg) values ('跟价模式', 'Pricing Strategy', '跟价模式');
insert into sys_language_config (zh_cn, en_us, msg) values ('出价高于平台最低价', 'Your price is higher than the platform‘s lowest price', '出价高于平台最低价');
insert into sys_language_config (zh_cn, en_us, msg) values ('跟价底线', 'Pricing Bottom Line', '跟价底线');
insert into sys_language_config (zh_cn, en_us, msg) values ('品名 / SKU', 'ProductNameSKU', '品名 / SKU');
insert into sys_language_config (zh_cn, en_us, msg) values ('品名 / SKU / ONE ID', 'ProductNameSKUOneId', '品名 / SKU / ONE ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('Product Name / SKU / 用户名 / ONE ID', 'ProductNameSKUUserIdOneId', 'Product Name / SKU / 用户名 / ONE ID');
insert into sys_language_config (zh_cn, en_us, msg) values ('品名 / SKU / ONE ID / 订单编号', 'ProductNameSKUOneIdOrderNumber', '品名 / SKU / ONE ID / 订单编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('品名 / SKU / ONE ID / 物流单号', 'ProductNameSKUOneIdTrackingNumber', '品名 / SKU / ONE ID / 物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('高级搜索', 'Filter', '高级搜索');
insert into sys_language_config (zh_cn, en_us, msg) values ('筛选', 'Filter', '筛选');
insert into sys_language_config (zh_cn, en_us, msg) values ('取消筛选', 'Clear Filter', '取消筛选');
insert into sys_language_config (zh_cn, en_us, msg) values ('支付', 'Payout', '支付');
insert into sys_language_config (zh_cn, en_us, msg) values ('操作留言', 'Operation message', '操作留言');
insert into sys_language_config (zh_cn, en_us, msg) values ('在售商品/品名总数', 'Listing count', '在售商品/品名总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('在售商品总数', 'TotalActive', '在售商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('在售品名总数', 'TotalSkuCategory', '在售品名总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX在售商品总数', 'StockXActive', 'StockX在售商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('GOAT在售商品总数', 'GoatActive', 'GOAT在售商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET在售商品总数', 'GoatIsActive', 'KNET在售商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('GOAT瑕疵在售商品总数', 'GoatDefect', 'GOAT瑕疵在售商品总数');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择需要下架的平台', 'Delelist platform selecr', '请选择需要下架的平台');
insert into sys_language_config (zh_cn, en_us, msg) values ('确定要将下列{num}个产品从{name}平台下架？', 'Confirm delist tips', '确定要将下列{num}个产品从{name}平台下架？');
insert into sys_language_config (zh_cn, en_us, msg) values ('进行中', 'In Progress', '进行中');
insert into sys_language_config (zh_cn, en_us, msg) values ('有异常', 'With Issue', '有异常');
insert into sys_language_config (zh_cn, en_us, msg) values ('已推送', 'Processed', '已推送');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出成功', 'Export Success', '导出成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出失败', 'Export Failed', '导出失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('单次最高上架商品数量为200', 'SelectLessThan200', '单次最高上架商品数量为200');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择空闲中并且没有瑕疵的商品', 'Select available product', '请选择空闲中并且没有瑕疵的商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('压价金额大于$5, 或达到限制价格，系统暂停自动跟价', 'Autopricing pause tip', '压价金额大于$5, 或达到限制价格，系统暂停自动跟价');
insert into sys_language_config (zh_cn, en_us, msg) values ('立即上架', 'Sell Now', '立即上架');
insert into sys_language_config (zh_cn, en_us, msg) values ('选择尺寸', 'Choose Size', '选择尺寸');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改价格', 'Bulk Pricing Modification', '批量修改价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('请先选择尺码', 'Add Sizes to Start Listing', '请先选择尺码');
insert into sys_language_config (zh_cn, en_us, msg) values ('平均库存成本', 'Avg.Cost', '平均库存成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台', 'PlatForm', '平台');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET 服务费', 'KNET Service Fee', 'KNET 服务费');
insert into sys_language_config (zh_cn, en_us, msg) values ('您的报价', 'Your Price', '您的报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('自动跟价', 'Auto Pricing', '自动跟价');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台最低价/到手价', 'Lowest Price', '平台最低价/到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('平台全新商品最低价', 'Lowest Brand New Price', '平台全新商品最低价');
insert into sys_language_config (zh_cn, en_us, msg) values ('预估到手价', 'Estimated Payout', '预估到手价');
insert into sys_language_config (zh_cn, en_us, msg) values ('批量修改策略', 'Bulk Strategy Change', '批量修改策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('盈利更多', 'Earn More Profit', '盈利更多');
insert into sys_language_config (zh_cn, en_us, msg) values ('快速出售', 'Quick Sale', '快速出售');
insert into sys_language_config (zh_cn, en_us, msg) values ('控制利润', 'Profit Control', '控制利润');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX/GOAT/KNET 价格更新时间', 'StockX/GOAT prices update time', 'StockX/GOAT/KNET 价格更新时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('导出中，请勿操作...', 'Export loading', '导出中，请勿操作...');
insert into sys_language_config (zh_cn, en_us, msg) values ('复制成功', 'Copy Success', '复制成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('忽略尺码/性别', 'Ignore Size Gender', '忽略尺码/性别');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增银行卡', 'New bank card', '新增银行卡');
insert into sys_language_config (zh_cn, en_us, msg) values ('新增支付宝', 'New alipay', '新增支付宝');
insert into sys_language_config (zh_cn, en_us, msg) values ('指定价格修改', 'Specified price change', '指定价格修改');
insert into sys_language_config (zh_cn, en_us, msg) values ('指定自动跟价', 'Bulk auto pricing change', '指定自动跟价');
insert into sys_language_config (zh_cn, en_us, msg) values ('快速自动跟价', 'QuickAuto', '快速自动跟价');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 价格', 'StockX Listing Price', 'StockX 价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('GOAT 价格', 'GOAT Listing Price', 'GOAT 价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET 价格', 'KNET Listing Price', 'KNET 价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 跟价策略', 'StockX Pricing Strategy', 'StockX 跟价策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('GOAT 跟价策略', 'GOAT Pricing Strategy', 'GOAT 跟价策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET 跟价策略', 'KNET Pricing Strategy', 'KNET 跟价策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 底线价格', 'StockX Bottom Line', 'StockX 底线价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('GOAT 底线价格', 'GOAT Bottom Line', 'GOAT 底线价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET 底线价格', 'KNET Bottom Line', 'KNET 底线价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('全平台开启自动跟价', 'To Active AutoPricing for All Channels', '全平台开启自动跟价');
insert into sys_language_config (zh_cn, en_us, msg) values ('修改为', 'Change To', '修改为');
insert into sys_language_config (zh_cn, en_us, msg) values ('之前价格', 'Previous listing price', '之前价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('新的价格', 'New listing price', '新的价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('之前底线价格', 'Previous limited price', '之前底线价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('新的底线价格', 'New limited price', '新的底线价格');
insert into sys_language_config (zh_cn, en_us, msg) values ('之前策略', 'Previous pricing strategy', '之前策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('新的策略', 'New pricing strategy', '新的策略');
insert into sys_language_config (zh_cn, en_us, msg) values ('之前成本', 'Previous Cost', '之前成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('新的成本', 'New Cost', '新的成本');
insert into sys_language_config (zh_cn, en_us, msg) values ('快速开启在售平台的自动跟价', 'Quick AutoPricing Tips', '快速开启在售平台的自动跟价');
insert into sys_language_config (zh_cn, en_us, msg) values ('更新成功', 'Update Success', '更新成功');
insert into sys_language_config (zh_cn, en_us, msg) values ('请先关闭自动出价功能再修改报价', 'Please stop Auto Pricing to edit Your Ask pricing', '请先关闭自动出价功能再修改报价');
insert into sys_language_config (zh_cn, en_us, msg) values ('请输入准确的成本价，以计算准确的盈利', 'Please enter the exact cost price to calculate the accurate profit', '请输入准确的成本价，以计算准确的盈利');
insert into sys_language_config (zh_cn, en_us, msg) values ('注意！此产品在本平台不支持上架。您可以尝试在其他平台上架此产品，或查看本平台的政策了解更多详情。', 'Product is not supported', '注意！此产品在本平台不支持上架。您可以尝试在其他平台上架此产品，或查看本平台的政策了解更多详情。');
insert into sys_language_config (zh_cn, en_us, msg) values ('您暂时没有权限使用该渠道', 'User not permission', '您暂时没有权限使用该渠道');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存管理-在仓库存', 'In Warehouse-Warehouse', '库存管理-在仓库存');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存管理-已出库', 'In Warehouse-WarehouseOutof', '库存管理-已出库');
insert into sys_language_config (zh_cn, en_us, msg) values ('库存管理-全部', 'In Warehouse-warehouseAll', '库存管理-全部');
insert into sys_language_config (zh_cn, en_us, msg) values ('未找到该钱包信息', 'Wallet not found', '未找到该钱包信息');
insert into sys_language_config (zh_cn, en_us, msg) values ('默认支付方式不允许删除', 'Default payment method cannot be deleted', '默认支付方式不允许删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的订单', 'Invalid order', '无效的订单');
insert into sys_language_config (zh_cn, en_us, msg) values ('该订单无需支付', 'This order does not require payment', '该订单无需支付');
insert into sys_language_config (zh_cn, en_us, msg) values ('该仓库没有该包裹的录入权限', 'This warehouse does not have entry permission for this package', '该仓库没有该包裹的录入权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('该预报批次已被使用，刷新页面后重试', 'This forecast batch has been used, please refresh the page and try again', '该预报批次已被使用，刷新页面后重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('识别码为必填项', 'Identification code is required', '识别码为必填项');
insert into sys_language_config (zh_cn, en_us, msg) values ('当日生成次数已达上限', 'The number of generations for the day has reached the limit', '当日生成次数已达上限');
insert into sys_language_config (zh_cn, en_us, msg) values ('仅平台管理员有该权限', 'Only platform administrators have this permission', '仅平台管理员有该权限');
insert into sys_language_config (zh_cn, en_us, msg) values ('单次操作最多150双', 'A maximum of 150 pairs per operation', '单次操作最多150双');
insert into sys_language_config (zh_cn, en_us, msg) values ('该批次下存在包裹，无法删除', 'There are packages in this batch, cannot delete', '该批次下存在包裹，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('该批次下存在商品，无法删除', 'There are products in this batch, cannot delete', '该批次下存在商品，无法删除');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹已失效', 'Package has expired', '包裹已失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('该批次未预报', 'This batch has not been forecasted', '该批次未预报');
insert into sys_language_config (zh_cn, en_us, msg) values ('存在商品已在货架上', 'There are products already on the shelf', '存在商品已在货架上');
insert into sys_language_config (zh_cn, en_us, msg) values ('货架所在仓库不明', 'Warehouse where the shelf is located is unknown', '货架所在仓库不明');
insert into sys_language_config (zh_cn, en_us, msg) values ('touch请求失败', 'Touch request failed', 'touch请求失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('手机号格式错误', 'Incorrect phone number format', '手机号格式错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('System error!', 'System error!', 'System error!');
insert into sys_language_config (zh_cn, en_us, msg) values ('StockX 静默生成预报包裹失败，根据 Sys Prod 的 id 未找到关联的 Sys Prod Deal 数据. ', 'StockX silent generation of forecast package failed, associated Sys Prod Deal data not found based on Sys Prod id.', 'StockX 静默生成预报包裹失败，根据 Sys Prod 的 id 未找到关联的 Sys Prod Deal 数据. ');
insert into sys_language_config (zh_cn, en_us, msg) values ('文件读取失败：', 'File read failed:', '文件读取失败：');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败', 'Login failed', '登录失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('无法获取手机号', 'Unable to obtain phone number', '无法获取手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('请填写要分享的链接', 'Please fill in the link to share', '请填写要分享的链接');
insert into sys_language_config (zh_cn, en_us, msg) values ('No available shopId or platId to judge user has rights.', 'No available shopId or platId to judge user has rights.', 'No available shopId or platId to judge user has rights.');
insert into sys_language_config (zh_cn, en_us, msg) values ('This email address has already been associated with an account.', 'This email address has already been associated with an account.', 'This email address has already been associated with an account.');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的验证码', 'Invalid verification code', '无效的验证码');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码已过期', 'Verification code has expired', '验证码已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码不正确', 'Incorrect verification code', '验证码不正确');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码发送失败', 'Verification code sending failed', '验证码发送失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码发送频率过高', 'Verification code sending frequency too high', '验证码发送频率过高');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的邮箱地址', 'Invalid email address', '无效的邮箱地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮箱地址已被占用', 'Email address already occupied', '邮箱地址已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的手机号', 'Invalid phone number', '无效的手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('手机号已被占用', 'Phone number already occupied', '手机号已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('无效的用户名或密码', 'Invalid username or password', '无效的用户名或密码');
insert into sys_language_config (zh_cn, en_us, msg) values ('用户名或密码错误', 'Incorrect username or password', '用户名或密码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码错误', 'Incorrect password', '密码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码格式错误', 'Incorrect password format', '密码格式错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码长度不足', 'Password length insufficient', '密码长度不足');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码不匹配', 'Passwords do not match', '密码不匹配');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码修改失败', 'Password modification failed', '密码修改失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号不存在', 'Account does not exist', '账号不存在');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已被禁用', 'Account has been disabled', '账号已被禁用');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号未激活', 'Account not activated', '账号未激活');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已激活', 'Account already activated', '账号已激活');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已锁定', 'Account has been locked', '账号已锁定');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已过期', 'Account has expired', '账号已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('会话已过期，请重新登录', 'Session has expired, please log in again', '会话已过期，请重新登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录超时，请重新登录', 'Login timeout, please log in again', '登录超时，请重新登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败次数过多，请稍后再试', 'Too many failed login attempts, please try again later', '登录失败次数过多，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，请稍后再试', 'Login failed, please try again later', '登录失败，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，请检查网络连接', 'Login failed, please check your network connection', '登录失败，请检查网络连接');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，请联系管理员', 'Login failed, please contact the administrator', '登录失败，请联系管理员');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，未知错误', 'Login failed, unknown error', '登录失败，未知错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号或密码错误', 'Login failed, incorrect account or password', '登录失败，账号或密码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，验证码错误', 'Login failed, incorrect verification code', '登录失败，验证码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，验证码已过期', 'Login failed, verification code has expired', '登录失败，验证码已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，验证码发送失败', 'Login failed, verification code sending failed', '登录失败，验证码发送失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，验证码发送频率过高', 'Login failed, verification code sending frequency too high', '登录失败，验证码发送频率过高');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，无效的邮箱地址', 'Login failed, invalid email address', '登录失败，无效的邮箱地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，无效的手机号', 'Login failed, invalid phone number', '登录失败，无效的手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，手机号已被占用', 'Login failed, phone number already occupied', '登录失败，手机号已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号不存在', 'Login failed, account does not exist', '登录失败，账号不存在');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号已被禁用', 'Login failed, account has been disabled', '登录失败，账号已被禁用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号未激活', 'Login failed, account not activated', '登录失败，账号未激活');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号已激活', 'Login failed, account already activated', '登录失败，账号已激活');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号已锁定', 'Login failed, account has been locked', '登录失败，账号已锁定');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，账号已过期', 'Login failed, account has expired', '登录失败，账号已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，会话已过期，请重新登录', 'Login failed, session has expired, please log in again', '登录失败，会话已过期，请重新登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录超时，请重新登录', 'Login failed, login timeout, please log in again', '登录失败，登录超时，请重新登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败次数过多，请稍后再试', 'Login failed, too many failed login attempts, please try again later', '登录失败，登录失败次数过多，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，请稍后再试', 'Login failed, login failed, please try again later', '登录失败，登录失败，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，请检查网络连接', 'Login failed, login failed, please check your network connection', '登录失败，登录失败，请检查网络连接');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，请联系管理员', 'Login failed, login failed, please contact the administrator', '登录失败，登录失败，请联系管理员');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，未知错误', 'Login failed, login failed, unknown error', '登录失败，登录失败，未知错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号或密码错误', 'Login failed, login failed, incorrect account or password', '登录失败，登录失败，账号或密码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，邮箱地址已被占用', 'Login failed, email address already occupied', '登录失败，邮箱地址已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，验证码错误', 'Login failed, login failed, incorrect verification code', '登录失败，登录失败，验证码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，验证码已过期', 'Login failed, login failed, verification code has expired', '登录失败，登录失败，验证码已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，验证码发送失败', 'Login failed, login failed, verification code sending failed', '登录失败，登录失败，验证码发送失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，验证码发送频率过高', 'Login failed, login failed, verification code sending frequency too high', '登录失败，登录失败，验证码发送频率过高');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，无效的邮箱地址', 'Login failed, login failed, invalid email address', '登录失败，登录失败，无效的邮箱地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，邮箱地址已被占用', 'Login failed, login failed, email address already occupied', '登录失败，登录失败，邮箱地址已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，无效的手机号', 'Login failed, login failed, invalid phone number', '登录失败，登录失败，无效的手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，手机号已被占用', 'Login failed, login failed, phone number already occupied', '登录失败，登录失败，手机号已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号不存在', 'Login failed, login failed, account does not exist', '登录失败，登录失败，账号不存在');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号已被禁用', 'Login failed, login failed, account has been disabled', '登录失败，登录失败，账号已被禁用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号未激活', 'Login failed, login failed, account not activated', '登录失败，登录失败，账号未激活');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号已激活', 'Login failed, login failed, account already activated', '登录失败，登录失败，账号已激活');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号已锁定', 'Login failed, login failed, account has been locked', '登录失败，登录失败，账号已锁定');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，账号已过期', 'Login failed, login failed, account has expired', '登录失败，登录失败，账号已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，会话已过期，请重新登录', 'Login failed, login failed, session has expired, please log in again', '登录失败，登录失败，会话已过期，请重新登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录超时，请重新登录', 'Login failed, login failed, login timeout, please log in again', '登录失败，登录失败，登录超时，请重新登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败次数过多，请稍后再试', 'Login failed, login failed, too many failed login attempts, please try again later', '登录失败，登录失败，登录失败次数过多，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，请稍后再试', 'Login failed, login failed, login failed, please try again later', '登录失败，登录失败，登录失败，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，请检查网络连接', 'Login failed, login failed, login failed, please check your network connection', '登录失败，登录失败，登录失败，请检查网络连接');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，请联系管理员', 'Login failed, login failed, login failed, please contact the administrator', '登录失败，登录失败，登录失败，请联系管理员');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，未知错误', 'Login failed, login failed, login failed, unknown error', '登录失败，登录失败，登录失败，未知错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，账号或密码错误', 'Login failed, login failed, login failed, incorrect account or password', '登录失败，登录失败，登录失败，账号或密码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，验证码错误', 'Login failed, login failed, login failed, incorrect verification code', '登录失败，登录失败，登录失败，验证码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，验证码已过期', 'Login failed, login failed, login failed, verification code has expired', '登录失败，登录失败，登录失败，验证码已过期');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，验证码发送失败', 'Login failed, login failed, login failed, verification code sending failed', '登录失败，登录失败，登录失败，验证码发送失败');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，验证码发送频率过高', 'Login failed, login failed, login failed, verification code sending frequency too high', '登录失败，登录失败，登录失败，验证码发送频率过高');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，无效的邮箱地址', 'Login failed, login failed, login failed, invalid email address', '登录失败，登录失败，登录失败，无效的邮箱地址');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，邮箱地址已被占用', 'Login failed, login failed, login failed, email address already occupied', '登录失败，登录失败，登录失败，邮箱地址已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，无效的手机号', 'Login failed, login failed, login failed, invalid phone number', '登录失败，登录失败，登录失败，无效的手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('登录失败，登录失败，登录失败，手机号已被占用', 'Login failed, login failed, login failed, phone number already occupied', '登录失败，登录失败，登录失败，手机号已被占用');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码不能为空', 'Verification code cannot be empty', '验证码不能为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码错误', 'Incorrect verification code', '验证码错误');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码已失效', 'Verification code has expired', '验证码已失效');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码发送失败，请稍后重试', 'Verification code sending failed, please try again later', '验证码发送失败，请稍后重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('验证码发送频率过高，请稍后再试', 'Verification code sending frequency too high, please try again later', '验证码发送频率过高，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮箱地址不能为空', 'Email address cannot be empty', '邮箱地址不能为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮箱地址格式不正确', 'Incorrect email address format', '邮箱地址格式不正确');
insert into sys_language_config (zh_cn, en_us, msg) values ('邮箱地址已被占用，请使用其他邮箱', 'Email address already occupied, please use another email', '邮箱地址已被占用，请使用其他邮箱');
insert into sys_language_config (zh_cn, en_us, msg) values ('手机号不能为空', 'Phone number cannot be empty', '手机号不能为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('手机号格式不正确', 'Incorrect phone number format', '手机号格式不正确');
insert into sys_language_config (zh_cn, en_us, msg) values ('手机号已被占用，请使用其他手机号', 'Phone number already occupied, please use another phone number', '手机号已被占用，请使用其他手机号');
insert into sys_language_config (zh_cn, en_us, msg) values ('用户名不能为空', 'Username cannot be empty', '用户名不能为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码不能为空', 'Password cannot be empty', '密码不能为空');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码格式不正确', 'Incorrect password format', '密码格式不正确');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码长度不足，请输入至少6位字符', 'Password length insufficient, please enter at least 6 characters', '密码长度不足，请输入至少6位字符');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码不匹配，请重新输入', 'Passwords do not match, please re-enter', '密码不匹配，请重新输入');
insert into sys_language_config (zh_cn, en_us, msg) values ('密码修改失败，请稍后重试', 'Password modification failed, please try again later', '密码修改失败，请稍后重试');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号不存在，请注册', 'Account does not exist, please register', '账号不存在，请注册');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已被禁用，请联系管理员', 'Account has been disabled, please contact the administrator', '账号已被禁用，请联系管理员');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号未激活，请激活后再登录', 'Account not activated, please activate before logging in', '账号未激活，请激活后再登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已激活，请直接登录', 'Account already activated, please log in directly', '账号已激活，请直接登录');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已锁定，请稍后再试', 'Account has been locked, please try again later', '账号已锁定，请稍后再试');
insert into sys_language_config (zh_cn, en_us, msg) values ('账号已过期，请联系管理员', 'Account has expired, please contact the administrator', '账号已过期，请联系管理员');
insert into sys_language_config (zh_cn, en_us, msg) values ('SHIPPING', 'SHIPPING', 'SHIPPING');
insert into sys_language_config (zh_cn, en_us, msg) values ('DROPOFF', 'DROPOFF', 'DROPOFF');
insert into sys_language_config (zh_cn, en_us, msg) values ('RETURNED', 'RETURNED', 'RETURNED');
insert into sys_language_config (zh_cn, en_us, msg) values ('未检测到参数[sign]', 'Parameter [sign] Not Detected', '未检测到参数[sign]');
insert into sys_language_config (zh_cn, en_us, msg) values ('未检测到参数[appSecret]', 'Parameter [appSecret] Not Detected', '未检测到参数[appSecret]');
insert into sys_language_config (zh_cn, en_us, msg) values ('未选择任意商品', 'No Item Selected', '未选择任意商品');
insert into sys_language_config (zh_cn, en_us, msg) values ('KNET订单编号', 'KNET Order Number', 'KNET订单编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('申请审批时间', 'Application Approval Time', '申请审批时间');
insert into sys_language_config (zh_cn, en_us, msg) values ('审核编号', 'Audit Number', '审核编号');
insert into sys_language_config (zh_cn, en_us, msg) values ('请选择待调换的顺序', 'Please select the order to be swapped', '请选择待调换的顺序');
insert into sys_language_config (zh_cn, en_us, msg) values ('入库物流单号', 'Warehouse Receipt Number', '入库物流单号');
insert into sys_language_config (zh_cn, en_us, msg) values ('包裹[', 'The tracking number:', '包裹[');
insert into sys_language_config (zh_cn, en_us, msg) values (']已有归属者', ' already exists', ']已有归属者');
insert into sys_language_config (zh_cn, en_us, msg) values ('这个运单号[', 'Oops！There is something ', '这个运单号[');
insert into sys_language_config (zh_cn, en_us, msg) values (']好像遇到了点麻烦，请联系客服人员解决。', ' wrong with this Tracking No. Please ask admin for help.', ']好像遇到了点麻烦，请联系客服人员解决。');
insert into sys_language_config (zh_cn, en_us, msg) values ('您的提现请求已被拒绝：在过去24小时内，您已超过2次提现。请稍后再试。', 'Your withdrawal request has been denied: You have exceeded 2 withdrawals in the past 24 hours. Please try again later.', '您的提现请求已被拒绝：在过去24小时内，您已超过2次提现。请稍后再试。');
insert into sys_language_config (zh_cn, en_us, msg) values ('您的提现请求已被拒绝：在过去24小时内，您已使用过该账号。请稍后再试。', 'Your withdrawal request has been denied: You have used this account in the past 24 hours. Please try again later.', '您的提现请求已被拒绝：在过去24小时内，您已使用过该账号。请稍后再试。');
insert into sys_language_config (zh_cn, en_us, msg) values ('余额不足，请充值后再提交。', 'Insufficient balance, please recharge and try later.', '操作失败，余额不足');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓位名称', 'Position name', '仓位名称');
insert into sys_language_config (zh_cn, en_us, msg) values ('仓位导入模版', 'Position import template', '仓位导入模版');
