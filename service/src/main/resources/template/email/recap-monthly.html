<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,minimum-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fustat:wght@200..800&display=swap" rel="stylesheet">
    <title>MONTHLY</title>
    <style>
        body {
            font-family: 'Fustat', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #000000;
            background-color: #ffffff;
        }

        .container {
            width: 100%;
            max-width: 440px;
            min-width: 440px;
            background-image: url('https://res.knetgroup.com/email/assets/monthly_bg1.png');
            background-size: cover;
            background-repeat: no-repeat;
            color: #000000;
        }

        .header-left-content-text, .header-right-content-text {
            font-size: 12px;
            font-weight: 700;
            line-height: 18px;
            color: #000000;
        }

        .header-left-content-number, .header-right-content-number {
            color: #000000;
        }

        .header-right-content {
            text-align: left;
        }

        .data-left-content {
            margin-top: 15px;
            margin-left: 35px;
        }

        .data-left-content-line {
            display: flex;
        }

        .data-left-content-line-left, .data-left-content-line-right {
            width: 90px;
            display: flex;
        }

        .data-left-content-line-left-number, .data-left-content-line-right-number {
            font-size: 40px;
            font-weight: 800;
            line-height: 40px;
        }

        .data-left-content-line-left-unit, .data-left-content-line-right-unit {
            margin-left: 4px;
        }

        .data-left-content-line-left-unit-percent, .data-left-content-line-right-unit-percent {
            margin-top: 6px;
            font-size: 24px;
            font-weight: 800;
            line-height: 15px;
        }

        .data-left-content-line-left-unit-website, .data-left-content-line-right-unit-website {
            font-size: 10px;
            font-weight: 700;
        }

        .data-right-content-top-number, .data-right-content-bottom-number {
            font-size: 40px;
            font-weight: 800;
            line-height: 40px;
            color: #000000;
        }

        .data-right-content-top-text, .data-right-content-bottom-text {
            font-size: 12px;
            font-weight: 700;
            line-height: 18px;
            color: #000000;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="header" style="height: 506px;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td style="text-align: left; width: 50%; position: relative; padding-left: 35px;">
                    <div class="header-left-content">
                        <div class="header-left-content-text" style="margin-top: 63px;">You’re in the top</div>
                        <div class="header-left-content-number" style="margin-top: 20px;">
                            <span style="font-size: 80px; font-weight: 800; line-height: 67px;"
                                  th:text="${sellerPercentile}"></span>
                            <span style="font-size: 50px; font-weight: 800; line-height: 67px;">%</span>
                        </div>
                        <div class="header-left-content-text" style="margin-top: 5px;">of KNET sellers</div>
                        <div class="header-left-content-number" style="margin-top: 85px;">
                            <span style="font-size: 50px; font-weight: 800; line-height: 90px;">$</span>
                            <span style="font-size: 50px; font-weight: 800; line-height: 90px;"
                                  th:text="${averageOrderValue}"></span>
                        </div>
                        <div class=" header-left-content-text" style="margin-top: -15px;">Average Order Value</div>
                    </div>
                </td>
                <td style="text-align: right; width: 50%; position: relative;">
                    <div class="header-right-content">
                        <div class="header-right-content-number" style="margin-top: 55px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalItemsSold}"></span>
                        </div>
                        <div class="header-right-content-text">Total Items Sold</div>
                        <div class="header-right-content-number" style="margin-top: 20px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;">$</span>
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalGmv}"></span>
                        </div>
                        <div class=" header-right-content-text">Total GMV
                        </div>
                        <div class="header-right-content-number" style="margin-top: 20px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalInboundItems}"></span>
                        </div>
                        <div class=" header-right-content-text">Total Inbound Items
                        </div>
                        <div class="header-right-content-number" style="margin-top: 20px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalReships}"></span>
                        </div>
                        <div class=" header-right-content-text">Total Reships
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <div class="data" style="height: 450px;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td style="width: 50%; position: relative;">
                    <div class="data-left-content">
                        <div class="data-left-content-line" style="margin-top: 195px;">
                            <div class="data-left-content-line-left">
                                <div class="data-left-content-line-left-number" style="color: #8DF695;"
                                     th:text="${percentOfSalesGoat}"></div>
                                <div class=" data-left-content-line-left-unit
                                ">
                                    <div class="data-left-content-line-left-unit-percent" style="color: #8DF695;">%
                                    </div>
                                    <div class="data-left-content-line-left-unit-website" style="color: #8DF695;">Goat
                                    </div>
                                </div>
                            </div>
                            <div class="data-left-content-line-right">
                                <div class="data-left-content-line-right-number" style="color: #7FDE86;"
                                     th:text="${percentOfSalesKicksCrew}"></div>
                                <div class=" data-left-content-line-right-unit
                            ">
                                    <div class="data-left-content-line-right-unit-percent" style="color: #7FDE86;">%
                                    </div>
                                    <div class="data-left-content-line-right-unit-website" style="color: #7FDE86;">Kicks
                                        Crew
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="data-left-content-line" style="margin-top: 35px;">
                            <div class="data-left-content-line-left">
                                <div class="data-left-content-line-left-number" style="color: #71C577;"
                                     th:text="${percentOfSalesEbay}">
                                </div>
                                <div class=" data-left-content-line-left-unit
            ">
                                    <div class="data-left-content-line-left-unit-percent" style="color: #71C577;">%
                                    </div>
                                    <div class="data-left-content-line-left-unit-website" style="color: #71C577;">eBay
                                    </div>
                                </div>
                            </div>
                            <div class="data-left-content-line-right">
                                <div class="data-left-content-line-right-number" style="color: #63AD69;"
                                     th:text="${percentOfSalesPoizon}">
                                </div>
                                <div class=" data-left-content-line-right-unit
        ">
                                    <div class="data-left-content-line-right-unit-percent" style="color: #63AD69;">%
                                    </div>
                                    <div class="data-left-content-line-right-unit-website" style="color: #63AD69;">
                                        Poizon
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="data-left-content-line" style="margin-top: 35px;">
                            <div class="data-left-content-line-left">
                                <div class="data-left-content-line-left-number" style="color: #559459;"
                                     th:text="${percentOfSalesKnet}">
                                </div>
                                <div class=" data-left-content-line-left-unit
        ">
                                    <div class="data-left-content-line-left-unit-percent" style="color: #559459;">%
                                    </div>
                                    <div class="data-left-content-line-left-unit-website" style="color: #559459;">KNET
                                    </div>
                                </div>
                            </div>
                            <div class="data-left-content-line-right">
                                <div class="data-left-content-line-right-number" style="color: #467B4A;"
                                     th:text="${percentOfSalesStockx}">
                                </div>
                                <div class=" data-left-content-line-right-unit
    ">
                                    <div class="data-left-content-line-right-unit-percent" style="color: #467B4A;">%
                                    </div>
                                    <div class="data-left-content-line-right-unit-website" style="color: #467B4A;">
                                        StockX
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 20px; font-size: 16px; font-weight: 700; line-height: 14px; text-align: center;">
                        Percent of Sales
                    </div>
                </td>
                <td style="width: 50%;">
                    <div class="data-right-content" style="margin-left: 59px; width: 108px; text-align: center;">
                        <div class="data-right-content-top">
                            <div class="data-right-content-top-number" th:text="${itemsStillListed}+ '%'">
                            </div>
                            <div class=" data-right-content-top-text
            " style="margin-bottom: 83px;">Items Still Listed
                            </div>
                        </div>
                        <div class="data-right-content-bottom">
                            <div class="data-right-content-bottom-number" th:text="${itemsSold}+ '%'">
                            </div>
                            <div class=" data-right-content-bottom-text">Items Sold
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>
