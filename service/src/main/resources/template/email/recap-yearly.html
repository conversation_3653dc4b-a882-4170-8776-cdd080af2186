<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Fustat:wght@200..800&display=swap" rel="stylesheet">
    <title>YEARLY</title>
    <style>
        body {
            font-family: 'Fustat', Arial, sans-serif;
            margin: 0;
            padding: 0;
            color: #000000;
            background-color: #ffffff;
        }

        .container {
            width: 100%;
            max-width: 440px;
            min-width: 440px;
            background-image: url('https://res.knetgroup.com/email/assets/yearly_bg.png');
            background-size: cover;
            background-repeat: no-repeat;
            color: #000000;
        }

        .header-left-content-text, .header-right-content-text {
            font-size: 12px;
            font-weight: 700;
            line-height: 16px;
            color: #000000;
        }

        .header-left-content-number, .header-right-content-number {
            color: #000000;
        }

        .header-right-content {
            text-align: left;
        }

        .data-left-content {
            margin-left: 35px;
        }

        .data-left-content-line {
            display: flex;
        }

        .data-left-content-line-left, .data-left-content-line-right {
            width: 90px;
            display: flex;
        }

        .data-left-content-line-left-number, .data-left-content-line-right-number {
            font-size: 40px;
            font-weight: 800;
            line-height: 40px;
        }

        .data-left-content-line-left-unit, .data-left-content-line-right-unit {
            margin-left: 4px;
        }

        .data-left-content-line-left-unit-percent, .data-left-content-line-right-unit-percent {
            margin-top: 6px;
            font-size: 24px;
            font-weight: 800;
            line-height: 15px;
        }

        .data-left-content-line-left-unit-website, .data-left-content-line-right-unit-website {
            font-size: 10px;
            font-weight: 700;
        }

        .data-right-content-top-number, .data-right-content-bottom-number {
            font-size: 40px;
            font-weight: 800;
            line-height: 40px;
            color: #000000;
        }

        .data-right-content-top-text, .data-right-content-bottom-text {
            font-size: 12px;
            font-weight: 700;
            line-height: 18px;
            color: #000000;
        }

        .best-title {
            text-align: center;
            font-size: 15px;
            font-weight: 700;
            line-height: 29px;
            color: #000000;
        }

        .best-number {
            margin-top: 128px;
            text-align: center;
            font-size: 50px;
            font-weight: 800;
            line-height: 72px;
            color: #000000;
        }

        .best-text {
            margin-top: 8px;
            text-align: center;
            font-size: 12px;
            font-weight: 700;
            line-height: 18px;
            color: #000000;
        }

        .best-month {
            margin-top: 121px;
            text-align: center;
            font-size: 15px;
            font-weight: 700;
            line-height: 29px;
            color: #000000;
        }

        .footer-line {
            display: flex;
        }

        .footer-line-background {
            margin-top: 45px;
            width: 33px;
            height: 33px;
            background-color: #000000;
            border-radius: 50%;
        }

        .footer-line-number {
            margin-top: 7px;
            text-align: center;
            font-size: 20px;
            font-weight: 800;
            line-height: 19px;
            color: #85F58D;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="header" style="height: 510px;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td style="text-align: left; width: 50%; position: relative; padding-left: 35px;">
                    <div class="header-left-content">
                        <div class="header-left-content-text" style="margin-top: 63px;">You’re in the top</div>
                        <div class="header-left-content-number" style="margin-top: 10px;">
                <span style="font-size: 40px; font-weight: 800; line-height: 27px;"
                      th:text="${sellerPercentile}"></span>
                            <span style="font-size: 30px; font-weight: 800; line-height: 27px;">%</span>
                        </div>
                        <div class="header-left-content-text" style="margin-top: 5px;">of KNET sellers</div>
                        <div class="header-left-content-number" style="margin-top: 95px;">
                            <span style="font-size: 50px; font-weight: 800; line-height: 90px;">$</span>
                            <span style="font-size: 50px; font-weight: 800; line-height: 90px;"
                                  th:text="${averageOrderValue}"></span>
                        </div>
                        <div class="header-left-content-text" style="margin-top: -15px;">Average Order Value</div>
                    </div>
                </td>
                <td style="text-align: right; width: 50%; position: relative;">
                    <div class="header-right-content">
                        <div class="header-right-content-number" style="margin-top: 55px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalItemsSold}"></span>
                        </div>
                        <div class="header-right-content-text">Total Items Sold</div>
                        <div class="header-right-content-number" style="margin-top: 20px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="'$'+${totalGmv}"></span>
                        </div>
                        <div class="header-right-content-text">Total GMV</div>
                        <div class="header-right-content-number" style="margin-top: 23px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalInboundItems}"></span>
                        </div>
                        <div class="header-right-content-text">Total Inbound Items</div>
                        <div class="header-right-content-number" style="margin-top: 28px;">
                            <span style="font-size: 40px; font-weight: 800; line-height: 50px;"
                                  th:text="${totalReships}"></span>
                        </div>
                        <div class="header-right-content-text">Total Reships</div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <div class="data" style="height: 480px;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td style="width: 50%; position: relative;">
                    <div class="data-left-content">
                        <div class="data-left-content-line" style="margin-top: 195px;">
                            <div class="data-left-content-line-left">
                                <div class="data-left-content-line-left-number" style="color: #8DF695;"
                                     th:text="${percentOfSalesGoat}"></div>
                                <div class="data-left-content-line-left-unit">
                                    <div class="data-left-content-line-left-unit-percent" style="color: #8DF695;">%
                                    </div>
                                    <div class="data-left-content-line-left-unit-website" style="color: #8DF695;">Goat
                                    </div>
                                </div>
                            </div>
                            <div class="data-left-content-line-right">
                                <div class="data-left-content-line-right-number" style="color: #7FDE86;"
                                     th:text="${percentOfSalesKicksCrew}"></div>
                                <div class="data-left-content-line-right-unit">
                                    <div class="data-left-content-line-right-unit-percent" style="color: #7FDE86;">%
                                    </div>
                                    <div class="data-left-content-line-right-unit-website" style="color: #7FDE86;">Kicks
                                        Crew
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="data-left-content-line" style="margin-top: 35px;">
                            <div class="data-left-content-line-left">
                                <div class="data-left-content-line-left-number" style="color: #71C577;"
                                     th:text="${percentOfSalesEbay}"></div>
                                <div class="data-left-content-line-left-unit">
                                    <div class="data-left-content-line-left-unit-percent" style="color: #71C577;">%
                                    </div>
                                    <div class="data-left-content-line-left-unit-website" style="color: #71C577;">eBay
                                    </div>
                                </div>
                            </div>
                            <div class="data-left-content-line-right">
                                <div class="data-left-content-line-right-number" style="color: #63AD69;"
                                     th:text="${percentOfSalesPoizon}"></div>
                                <div class="data-left-content-line-right-unit">
                                    <div class="data-left-content-line-right-unit-percent" style="color: #63AD69;">%
                                    </div>
                                    <div class="data-left-content-line-right-unit-website" style="color: #63AD69;">
                                        Poizon
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="data-left-content-line" style="margin-top: 35px;">
                            <div class="data-left-content-line-left">
                                <div class="data-left-content-line-left-number" style="color: #559459;"
                                     th:text="${percentOfSalesKnet}"></div>
                                <div class="data-left-content-line-left-unit">
                                    <div class="data-left-content-line-left-unit-percent" style="color: #559459;">%
                                    </div>
                                    <div class="data-left-content-line-left-unit-website" style="color: #559459;">KNET
                                    </div>
                                </div>
                            </div>
                            <div class="data-left-content-line-right">
                                <div class="data-left-content-line-right-number" style="color: #467B4A;"
                                     th:text="${percentOfSalesStockx}"></div>
                                <div class="data-left-content-line-right-unit">
                                    <div class="data-left-content-line-right-unit-percent" style="color: #467B4A;">%
                                    </div>
                                    <div class="data-left-content-line-right-unit-website" style="color: #467B4A;">
                                        StockX
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 20px; font-size: 16px; font-weight: 700; line-height: 14px; text-align: center;">
                        Percent of Sales
                    </div>
                </td>
                <td style="width: 50%;">
                    <div class="data-right-content" style="margin-left: 55px; width: 108px; text-align: center;">
                        <div class="data-right-content-top">
                            <div class="data-right-content-top-number" th:text="${itemsStillListed}+ '%'"></div>
                            <div class="data-right-content-top-text" style="margin-bottom: 83px;">Items Still Listed
                            </div>
                        </div>
                        <div class="data-right-content-bottom">
                            <div class="data-right-content-bottom-number" th:text="${itemsSold}+ '%'"></div>
                            <div class="data-right-content-bottom-text">Items Sold</div>
                        </div>
                    </div>
                </td>
            </tr>
        </table>
    </div>
    <div class="best" style="height: 507px;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0"></table>
        <tr>
            <td style="width: 50%; text-align: center;">
                <div class="best-title">Your best month of the year was</div>
                <div class="best-number" th:text="'$' +${bestMonthGmv}"></div>
                <div class="best-text">Total GMV</div>
                <div class="best-month" th:text="${bestMonth}"></div>
            </td>
        </tr>
        </table>
    </div>
    <div class="footer" style="height: 600px; padding-bottom: 117px;">
        <table width="100%" border="0" cellspacing="0" cellpadding="0">
            <tr>
                <td style="width: 50%;">
                    <div class="footer-line" style="margin-left: 35px;">
                        <div th:if="${topOne != null}" class="footer-line-background">
                            <div class="footer-line-number">1</div>
                        </div>
                        <img th:if="${topOne != null}" th:src="${topOne}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 35px;">
                        <div th:if="${topThree != null}" class="footer-line-background">
                            <div class="footer-line-number">3</div>
                        </div>
                        <img th:if="${topThree != null}" th:src="${topThree}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 35px;">
                        <div th:if="${topFive != null}" class="footer-line-background">
                            <div class="footer-line-number">5</div>
                        </div>
                        <img th:if="${topFive != null}" th:src="${topFive}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 35px;">
                        <div th:if="${topSeven != null}" class="footer-line-background">
                            <div class="footer-line-number">7</div>
                        </div>
                        <img th:if="${topSeven != null}" th:src="${topSeven}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 35px;">
                        <div th:if="${topNine != null}" class="footer-line-background">
                            <div class="footer-line-number">9</div>
                        </div>
                        <img th:if="${topNine != null}" th:src="${topNine}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                </td>
                <td style="width: 50%;">
                    <div class="footer-line" style="margin-left: 5px;">
                        <div th:if="${topTwo != null}" class="footer-line-background">
                            <div class="footer-line-number">2</div>
                        </div>
                        <img th:if="${topTwo != null}" th:src="${topTwo}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 5px;">
                        <div th:if="${topFour != null}" class="footer-line-background">
                            <div class="footer-line-number">4</div>
                        </div>
                        <img th:if="${topFour != null}" th:src="${topFour}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 5px;">
                        <div th:if="${topSix != null}" class="footer-line-background">
                            <div class="footer-line-number">6</div>
                        </div>
                        <img th:if="${topSix != null}" th:src="${topSix}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 5px;">
                        <div th:if="${topEight != null}" class="footer-line-background">
                            <div class="footer-line-number">8</div>
                        </div>
                        <img th:if="${topEight != null}" th:src="${topEight}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                    <div class="footer-line" style="margin-left: 5px;">
                        <div th:if="${topTen != null}" class="footer-line-background">
                            <div class="footer-line-number">10</div>
                        </div>
                        <img th:if="${topTen != null}" th:src="${topTen}" alt="shoe"
                             style="margin-left: 10px; width: 120px; height: 120px;">
                    </div>
                </td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>
