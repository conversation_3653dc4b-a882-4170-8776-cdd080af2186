<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysWareOutBatchProdMapper">

    <!--出库批次管理——统计查询-->
    <select id="selectWareBatchOutCount" resultType="com.hzjm.service.model.VO.SysWareBatchCountVo">
        select
            count(distinct t1.batch_no) num, count(0) prodNum
        from sys_ware_out_batch t1
        <include refid="coreViewTable" />
    </select>

    <!--出库批次管理-list 查询-->
    <select id="selectWareBatchOutList" resultType="com.hzjm.service.model.VO.SysWareOutBatchProdListVo">
        select
        <include refid="selectList" />
        from (
            select distinct t1.batch_no from sys_ware_out_batch t1

            <include refid="coreTable" />

            <include refid="orderBy" />

            limit #{dto.size,jdbcType=INTEGER} offset #{dto.offset,jdbcType=INTEGER}
        ) limit_table
        inner join sys_ware_out_batch t1 on limit_table.batch_no = t1.batch_no

        <include refid="coreViewTable" />

        <include refid="orderBy" />

    </select>

    <!-- 出库批次管理 导出查询 - CSV格式数据行 -->
    <select id="selectWareBatchOutToExportCsvData" parameterType="com.hzjm.service.model.DTO.SysWareOutBatchProdPageDto"
            resultType="java.lang.String">

        SELECT
            CONCAT_WS(',',
                <include refid="csvDataColumns" />
            ) as csv_row
        FROM sys_ware_out_batch t1
        <include refid="coreViewTable" />
        LEFT JOIN (
            SELECT batch_id, COUNT(*) as cnt
            FROM sys_ware_out_batch_prod
            WHERE del_flag = 0
            GROUP BY batch_id
        ) batch_count ON t1.id = batch_count.batch_id
        <include refid="orderBy" />

    </select>

    <!--出库批次管理-核心表关联-->
    <sql id="coreTable">
        inner join sys_ware_out_batch_prod t2
        on t1.id = t2.batch_id and t1.del_flag = 0 and t2.del_flag = 0 and t2.gmt_out is not null
        <!--OutBatchNo-->
        <if test="dto.outBatchNo != null and dto.outBatchNo != ''">
            and t1.batch_no = #{dto.outBatchNo,jdbcType=VARCHAR}
        </if>
        <!-- wareIdList -->
        <if test="dto.wareIdList != null and dto.wareIdList.size() > 0">
            and t1.ware_id in
            <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- beginTime -->
        <if test="dto.beginTime != null">
            and t2.gmt_out &gt;= #{dto.beginTime,jdbcType=TIMESTAMP}
        </if>
        <!-- endTime -->
        <if test="dto.endTime != null">
            and t2.gmt_out &lt;= #{dto.endTime,jdbcType=TIMESTAMP}
        </if>

        <if test="
            (dto.outType != null and dto.outType != '')
            or (dto.oddNo != null and dto.oddNo != '')
        ">
            inner join sys_ware_out t3 on t3.id = t2.out_id and t3.del_flag = 0
            <!-- outType -->
            <if test="dto.outType != null and dto.outType != ''">
                and t3.type = #{dto.outType,jdbcType=INTEGER}
            </if>
            <!-- OddNo -->
            <if test="dto.oddNo != null and dto.oddNo != ''">
                and t3.odd_no = #{dto.oddNo,jdbcType=VARCHAR}
            </if>
        </if>

        <if test="
            (dto.remarks != null and dto.remarks != '')
            or (dto.skuList != null and dto.skuList.size() > 0)
            or (dto.specList != null and dto.specList.size() > 0)
            or (dto.oneIdList != null and dto.oneIdList.size() > 0)
            or (dto.beginInTime != null)
            or (dto.endInTime != null)
        ">
            inner join sys_prod t7 on t7.id = t2.prod_id and t7.del_flag = 0
            inner join sys_prod_search search on search.prod_id = t7.id and search.search_type = 1 and search.del_flag =
            0
            <!-- remarks -->
            <if test="dto.remarks != null and dto.remarks != ''">
                and t7.remarks = #{dto.remarks,jdbcType=VARCHAR}
            </if>
            <!-- skuList -->
            <if test="dto.skuList != null and dto.skuList.size() > 0">
                and t7.sku in
                <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- specList -->
            <if test="dto.specList != null and dto.specList.size() > 0">
                and t7.spec in
                <foreach item="item" index="index" collection="dto.specList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- oneIdList -->
            <if test="dto.oneIdList != null and dto.oneIdList.size() > 0">
                and t7.one_id in
                <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <!-- beginInTime -->
            <if test="dto.beginInTime != null">
                and t7.gmt_in &gt; #{dto.beginInTime,jdbcType=TIMESTAMP}
            </if>
            <!-- endInTime -->
            <if test="dto.endInTime != null">
                and t7.gmt_in &lt; #{dto.endInTime,jdbcType=TIMESTAMP}
            </if>
        </if>

        <if test="(dto.shopUid != null and dto.shopUid != '') or (dto.shopId != null and dto.shopId != 0)">
            inner join shop_user t11 on t11.id = t3.shop_id and t11.del_flag = 0
            <if test="dto.shopUid != null and dto.shopUid != ''">
                and t11.uid = #{dto.shopUid,jdbcType=VARCHAR}
            </if>
            <if test="dto.shopId != null">
                and t11.id = #{dto.shopId,jdbcType=INTEGER}
            </if>
        </if>

        <!-- checker -->
        <if test="dto.checker != null and dto.checker != ''">
            inner join sys_user t6 on t6.id = t1.checker_id
            and t6.nickname = #{dto.checker,jdbcType=VARCHAR}
        </if>

        <!-- platName -->
        <if test="dto.platName != null and dto.platName != ''">
            inner join sys_prod_sale t4 on t4.odd_no = t3.odd_no and t4.del_flag = 0
            and t4.plat_name = #{dto.platName,jdbcType=VARCHAR}
        </if>

    </sql>


    <!--出库批次管理-核心表关联 返回select查询数据-->
    <sql id="coreViewTable">
        inner join sys_ware_out_batch_prod t2
        on t1.id = t2.batch_id and t1.del_flag = 0 and t2.del_flag = 0 and t2.gmt_out is not null
        <!--OutBatchNo-->
        <if test="dto.outBatchNo != null and dto.outBatchNo != ''">
            and t1.batch_no = #{dto.outBatchNo,jdbcType=VARCHAR}
        </if>
        <!-- wareIdList -->
        <if test="dto.wareIdList != null and dto.wareIdList.size() > 0">
            and t1.ware_id in
            <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- beginTime -->
        <if test="dto.beginTime != null">
            and t2.gmt_out &gt;= #{dto.beginTime,jdbcType=TIMESTAMP}
        </if>
        <!-- endTime -->
        <if test="dto.endTime != null">
            and t2.gmt_out &lt;= #{dto.endTime,jdbcType=TIMESTAMP}
        </if>

        <choose>
            <when test="
                (dto.outType != null and dto.outType != '')
                or (dto.oddNo != null and dto.oddNo != '')
            ">
                inner join sys_ware_out t3 on t3.id = t2.out_id and t3.del_flag = 0
                <!-- outType -->
                <if test="dto.outType != null and dto.outType != ''">
                    and t3.type = #{dto.outType,jdbcType=INTEGER}
                </if>
                <!-- OddNo -->
                <if test="dto.oddNo != null and dto.oddNo != ''">
                    and t3.odd_no = #{dto.oddNo,jdbcType=VARCHAR}
                </if>
            </when>
            <otherwise>
                left join sys_ware_out t3 on t3.id = t2.out_id and t3.del_flag = 0
            </otherwise>
        </choose>

        <choose>
            <when test="
                (dto.remarks != null and dto.remarks != '')
                or (dto.skuList != null and dto.skuList.size() > 0)
                or (dto.specList != null and dto.specList.size() > 0)
                or (dto.oneIdList != null and dto.oneIdList.size() > 0)
                or (dto.beginInTime != null)
                or (dto.endInTime != null)
            ">
                inner join sys_prod t7 on t7.id = t2.prod_id and t7.del_flag = 0
                inner join sys_prod_search search on search.prod_id = t7.id and search.search_type = 1 and search.del_flag = 0
                <!-- remarks -->
                <if test="dto.remarks != null and dto.remarks != ''">
                    and t7.remarks = #{dto.remarks,jdbcType=VARCHAR}
                </if>
                <!-- skuList -->
                <if test="dto.skuList != null and dto.skuList.size() > 0">
                    and t7.sku in
                    <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- specList -->
                <if test="dto.specList != null and dto.specList.size() > 0">
                    and t7.spec in
                    <foreach item="item" index="index" collection="dto.specList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- oneIdList -->
                <if test="dto.oneIdList != null and dto.oneIdList.size() > 0">
                    and t7.one_id in
                    <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- beginInTime -->
                <if test="dto.beginInTime != null">
                    and t7.gmt_in &gt; #{dto.beginInTime,jdbcType=TIMESTAMP}
                </if>
                <!-- endInTime -->
                <if test="dto.endInTime != null">
                    and t7.gmt_in &lt; #{dto.endInTime,jdbcType=TIMESTAMP}
                </if>
            </when>
            <otherwise>
                left join sys_prod t7 on t7.id = t2.prod_id and t7.del_flag = 0
                left join sys_prod_search search on search.prod_id = t7.id and search.search_type = 1 and search.del_flag = 0
            </otherwise>
        </choose>

        <choose>
            <when test="(dto.shopUid != null and dto.shopUid != '') or (dto.shopId != null and dto.shopId != 0)">
                inner join shop_user t11 on t11.id = t3.shop_id and t11.del_flag = 0
                <if test="dto.shopUid != null and dto.shopUid != ''">
                    and t11.uid = #{dto.shopUid,jdbcType=VARCHAR}
                </if>
                <if test="dto.shopId != null">
                    and t11.id = #{dto.shopId,jdbcType=INTEGER}
                </if>
            </when>
            <otherwise>
                left join shop_user t11 on t11.id = t3.shop_id and t11.del_flag = 0
            </otherwise>
        </choose>

        <!-- checker -->
        <choose>
            <when test="dto.checker != null and dto.checker != ''">
                inner join sys_user t6 on t6.id = t1.checker_id
                and t6.nickname = #{dto.checker,jdbcType=VARCHAR}
            </when>
            <otherwise>
                left join sys_user t6 on t6.id = t1.checker_id
            </otherwise>
        </choose>

        <!-- platName -->
        <choose>
            <when test="dto.platName != null and dto.platName != ''">
                inner join sys_prod_sale t4 on t4.odd_no = t3.odd_no and t4.del_flag = 0
                and t4.plat_name = #{dto.platName,jdbcType=VARCHAR}
            </when>
            <otherwise>
                left join sys_prod_sale t4 on t4.odd_no = t3.odd_no and t4.del_flag = 0
            </otherwise>
        </choose>

        left join sys_ware_in_prod in_prod on in_prod.id = t2.prod_id and in_prod.del_flag = 0
        left join sys_ware t5 on t5.id = t1.ware_id and t5.del_flag = 0
        left join sys_ware_out_prod t8 on t8.prod_id = t2.prod_id and t8.del_flag = 0
        left join sys_ware_shelves t9 on t9.id = t8.shelves_id and t9.del_flag = 0

    </sql>


    <!--出库批次管理-list 查询字段-->
    <sql id="selectList">
        t2.id
     , t1.batch_no out_batch_no
     , t3.type      out_Type
     , t4.plat_name
     , t2.gmt_out
     , t5.name      ware_name
     , t6.nickname  checker
     , t3.odd_no    out_odd_no
     , t4.plat_order_no
     , t7.img
     , t7.remarks
     , t7.sku
     , t7.spec
     , t7.one_id
     , in_prod.check_result
     , t9.name      shelves_name
     , search.shop_name
     , search.shop_uid
    </sql>

    <!-- CSV数据列 -->
    <sql id="csvDataColumns">
        IFNULL(t1.batch_no, ''),
        IFNULL(t3.odd_no, ''),
        <include refid="outTypeTranslation" />,
        IFNULL(batch_count.cnt, ''),
        IFNULL(t4.plat_name, ''),
        IFNULL(DATE_FORMAT(CONVERT_TZ(t2.gmt_out, '+00:00', 'America/New_York'), '%Y-%m-%d %H:%i:%s'), ''),
        IFNULL(t5.name, ''),
        IFNULL(t6.nickname, ''),
        IFNULL(t4.plat_order_no, ''),
        CONCAT('"', IFNULL(REPLACE(t7.remarks, '"', '""'), ''), '"'),
        IFNULL(t7.sku, ''),
        IFNULL(t7.spec, ''),
        IFNULL(t7.one_id, ''),
        <include refid="checkResultTranslation" />,
        IFNULL(t9.name, ''),
        IFNULL(search.shop_name, ''),
        IFNULL(search.shop_uid, '')
    </sql>

    <!-- 出库类型翻译 -->
    <sql id="outTypeTranslation">
        CASE t3.type
            <choose>
                <when test="language == 'zh-CN'">
                WHEN 3 THEN '代发'
                WHEN 4 THEN '转运'
                WHEN 6 THEN '寄卖'
                </when>
                <otherwise>
                    WHEN 3 THEN 'Ship with Label'
                    WHEN 4 THEN 'Ship without Label		'
                    WHEN 6 THEN 'Consignment Sold'
                </otherwise>
        </choose>
        ELSE ''
        END
    </sql>

    <!-- 检查结果翻译 -->
    <sql id="checkResultTranslation">
        CASE in_prod.check_result
            <choose>
                <when test="language == 'zh-CN'">
                    WHEN 1 THEN '合格'
                    WHEN 2 THEN '鞋盒严重破损-S'
                    WHEN 3 THEN '球鞋有瑕疵'
                    WHEN 4 THEN '假货'
                    WHEN 5 THEN '鞋盒轻微破损-S'
                    WHEN 6 THEN '非原盒-S'
                    WHEN 7 THEN '无鞋盒盖-S'
                    WHEN 8 THEN '单只鞋'
                </when>
                <otherwise>
                    WHEN 1 THEN 'Qualified'
                    WHEN 2 THEN 'Box Badly Damaged-S'
                    WHEN 3 THEN 'Shoes Defects'
                    WHEN 4 THEN 'Fake'
                    WHEN 5 THEN 'Box Slightly Damaged-S'
                    WHEN 6 THEN 'No Original Box-S'
                    WHEN 7 THEN 'Missing Lid-S'
                    WHEN 8 THEN 'Missing Pair'
                </otherwise>
            </choose>
            ELSE ''
        END
    </sql>

    <!--出库批次管理-排序-->
    <sql id="orderBy">
        order by t2.gmt_out desc
    </sql>

</mapper>
