<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.DashboardAdminMapper">
    <!--销售额，订单数，转运/代发 数量，利润，入库数 -->
    <select id="selectAdminHomeBiDataComparison" resultType="com.hzjm.service.model.VO.AdminHomeBiDataComparisonLongVo">
        select convert(max(totalGMV) / 100, decimal(12, 2)) total_gmv
        , max(totalSaleOrders) total_sale_orders
        , max(totalReshipOrders) total_reship_orders
        , convert(max(totalProfit) / 100, decimal(12, 2)) total_profit
        , max(totalInbound) total_inbound
        from (select ifnull(sum(t1.sale_price), 0) totalGMV,
        count(1) totalSaleOrders,
        0 totalReshipOrders,
        ifnull(sum(t1.knet_owning), 0)
        - ifnull(sum(t1.seller_owning), 0) totalProfit,
        0 totalInbound
        from platform_order t1
        where del_flag = 0
        and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP}, 10)
        and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
        left(#{endTime,jdbcType=TIMESTAMP}, 10)
        <!--                and t1.knet_order_status IN-->
        <!--                    ('pending_payout', 'completed', 'payout_completed')-->

        union all

        select 0 totalGMV,
        0 totalSaleOrders,
        count(distinct one_id) totalReshipOrders,
        0 totalProfit,
        0 totalInbound
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and odd_type in (3, 4)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP}, 10)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &lt;=
        left(#{endTime,jdbcType=TIMESTAMP}, 10)

        union all

        select 0 totalGMV,
        0 totalSaleOrders,
        0 totalReshipOrders,
        0 totalProfit,
        count(distinct one_id) totalInbound
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and status in (1, 2, 3, 4, 7, 8, 9, 10,12)
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP}, 10)
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &lt;=
        left(#{endTime,jdbcType=TIMESTAMP}, 10)) tt
    </select>

    <!--按照订单平台，查询销售额，订单数量，利润，查询结果按日分组-->
    <select id="selectAdminHomeBiOrderPlatform" resultType="com.hzjm.service.model.VO.AdminHomeBiOrderPlatformVo">
        SELECT left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) date
        , IFNULL(convert(sum(sale_price) / 100, decimal(12, 2)), 0) totalGMV
        , IFNULL(count(1), 0) totalSaleOrders
        , IFNULL(convert(sum(knet_owning - seller_owning) / 100, decimal(12, 2)), 0) totalProfit
        from platform_order
        where del_flag = 0
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP}, 10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
        left(#{endTime,jdbcType=TIMESTAMP}, 10)
        and platform = #{platform,jdbcType=VARCHAR}
        <!--          and knet_order_status IN-->
        <!--              ('pending_payout', 'completed', 'payout_completed')-->
        GROUP BY date
        ORDER BY date
    </select>

    <!--查询入库数量，查询结果按日分组-->
    <select id="selectGmtInSumByDay" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT left (CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) date
                , count (distinct one_id) totalReshipOrders
        from sys_prod_search
        where del_flag = 0
          and search_type = 1
          and status in (1
            , 2
            , 3
            , 4
            , 7
            , 8
            , 9
            , 10)
          and left (CONVERT_TZ(gmt_in
            , '+00:00'
            , 'America/New_York')
            , 10) &gt;= left (#{beginTime,jdbcType=TIMESTAMP}
            , 10)
          and left (CONVERT_TZ(gmt_in
            , '+00:00'
            , 'America/New_York')
            , 10) &lt;= left (#{endTime,jdbcType=TIMESTAMP}
            , 10)
        GROUP BY date
        ORDER BY date
    </select>

    <!--查询转运/代发订单数量，查询结果按日分组-->
    <select id="selectGmtOutSumByDay" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT left (CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) date
                , count (distinct one_id) totalInbound
        from sys_prod_search t1
        where del_flag = 0
          and search_type = 1
          and odd_type in (3
            , 4)
          and left (CONVERT_TZ(gmt_out
            , '+00:00'
            , 'America/New_York')
            , 10) &gt;=
            left (#{beginTime,jdbcType=TIMESTAMP}
            , 10)
          and left (CONVERT_TZ(gmt_out
            , '+00:00'
            , 'America/New_York')
            , 10) &lt;= left (#{endTime,jdbcType=TIMESTAMP}
            , 10)
        GROUP BY date
        ORDER BY date;
    </select>

    <!--查询活跃用户  ：30天内 ，统计入库鞋子数量大于10的商家的数量-->
    <select id="selectActionUser" resultType="long">
        SELECT COUNT(1) AS active_shop_count
        FROM (SELECT t1.shop_id
        FROM sys_prod_search t1
        WHERE t1.del_flag = 0
        AND t1.search_type = 1
        and status in (1, 2, 3, 4, 7, 8, 9, 10,12)
        AND left(CONVERT_TZ(t1.gmt_in, '+00:00', 'America/New_York'), 10) >=
        DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
        and t1.shop_id is not null
        <if test="wareIds!=null and wareIds.size() > 0">
            and t1.ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="beginTime!=null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY t1.shop_id
        HAVING COUNT(DISTINCT t1.one_id) >= 10) AS subquery
    </select>

    <!--查询昨日生成过订单的用户数量-->
    <select id="selectOrderActionUser" resultType="long">
        select count(distinct shop_id) shop_Count
        from platform_order
        where del_flag = 0
        <if test="beginTime!=null">
            and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--全新商品在架数量-->
    <select id="selectListedTotalInventory" resultType="long">
        SELECT COUNT(distinct kpl.one_id) num
        FROM knet_product_listing kpl
        WHERE (kpl.knet_listing_status IN ('CREATED', 'UPDATED') AND kpl.shoe_condition = 'NEW' AND
        kpl.box_condition = 'GOOD_CONDITION')
        <if test="userId!=null">
            AND kpl.shop_user_id = #{userId}
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and kpl.shop_user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--瑕疵商品在架数量-->
    <select id="selectDefectInventory" resultType="long">
        SELECT COUNT(distinct kpl.one_id) num
        FROM knet_product_listing kpl
        WHERE (kpl.knet_listing_status IN ('CREATED', 'UPDATED') AND
        NOT (kpl.shoe_condition = 'NEW' AND kpl.box_condition = 'GOOD_CONDITION'))
        <if test="userId!=null">
            AND kpl.shop_user_id = #{userId}
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and kpl.shop_user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 查询在架商品的sku、size、各平台价格 -->
    <select id="selectSkuSizeLowPrice" resultType="com.hzjm.service.model.VO.SkuSizeLowPriceVo">
        select tt.*
        from (select id
        , concat(REPLACE(REPLACE(sku, ' ', ''), '-', ''), size) search_key
        , REPLACE(REPLACE(sku, ' ', ''), '-', '') sku
        , size size
        , IF(stockx_status = 'open', CAST(stockx_sale_price AS INT) * 100, 0) stockx_price
        , IF(goat_status = 'open', CAST(goat_sale_price AS INT) * 100, 0) goat_price
        , IF(goatis_status = 'open', CAST(goatis_sale_price AS INT) * 100, 0) goat_is_price
        , IF(kc_status = 'open', CAST(kc_sale_price AS INT) * 100, 0) kc_price
        , IF(ebay_status = 'open', CAST(ebay_sale_price AS INT) * 100, 0) ebay_price
        , IF(poizon_status = 'open', CAST(poizon_sale_price AS INT) * 100, 0) poizon_price
        , IF(tts_status = 'open', CAST(tts_sale_price AS INT) * 100, 0) tts_price
        from knet_product_listing
        where knet_listing_status IN ('CREATED', 'UPDATED')
        <if test="userId!=null">
            AND shop_user_id = #{userId}
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) tt
        where tt.stockx_price > 0
        or tt.goat_price > 0
        or tt.goat_is_price > 0
        or tt.kc_price > 0
        or tt.ebay_price > 0
        or tt.poizon_price > 0
        or tt.tts_price > 0
    </select>

    <!--通过 sku + size 查询商品最低价-->
    <select id="selectSkuSizeLowPriceBySkuSize" resultType="com.hzjm.service.model.VO.SkuSizeLowPriceVo">
        SELECT CONCAT(sku, size) search_key,
        MAX(IF(platform = 'STOCK_X', lowest_ask_price, 0)) stockx_price,
        MAX(IF(platform = 'GOAT_INSTANT_SHIP', lowest_ask_price, 0)) goat_price,
        MAX(IF(platform = 'GOAT_INSTANT_SHIP', lowest_instant_ask_price, 0)) goat_is_price,
        MAX(IF(platform = 'KICKS_CREW', lowest_ask_price, 0)) kc_price,
        MAX(IF(platform = 'EBAY', lowest_ask_price, 0)) ebay_price
        , MAX(IF(platform = 'POIZON', lowest_ask_price, 0)) poizon_price
        , MAX(IF(platform = 'TIKTOK_SHOP', lowest_ask_price, 0)) tts_lowest_price
        , MAX(IF(platform = 'TIKTOK_SHOP', highest_ask_price, 0)) tts_highest_price
        FROM knet_product USE INDEX (idx_knet_product_full_optimized)
        WHERE del_flag = 0
        AND (lowest_ask_price > 0 OR lowest_instant_ask_price > 0)

        <!--sku-->
        <if test="skuList!=null and skuList.size() > 0">
            and sku IN
            <foreach collection="skuList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!--size-->
        <if test="sizeList!=null and sizeList.size() > 0">
            and size IN
            <foreach collection="sizeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY sku, size
    </select>
    <!--查询超过120天的鞋子数量-->
    <select id="selectOver120DaysInventory" resultType="long">
        select count(distinct one_id) num
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &lt; DATE_SUB(CURRENT_DATE, INTERVAL 120 DAY)
        and status IN (1, 2, 3, 4, 7, 8, 9, 10,12)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>
        <if test="wareIds!=null and wareIds.size() > 0">
            and t1.ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by gmt_in
    </select>

    <!-- 统计库存和上架的 sku+size 的组合数量 -->
    <select id="selectSkuSizeSize" resultType="com.hzjm.service.model.VO.AdminHomeBiDefaultVo">
        select max(totalPku) total_pku, max(activePku) active_pku
        from (SELECT COUNT(DISTINCT sku, spec) AS totalPku, 0 activePku
        FROM sys_prod_search
        WHERE del_flag = 0
        and search_type = 1
        and status IN (1, 2, 3, 4, 7, 8, 9, 10,12)
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        union all

        SELECT 0 totalPku, COUNT(DISTINCT kpl.sku, kpl.size) AS activePku
        FROM knet_product_listing kpl
        where kpl.knet_listing_status IN ('CREATED', 'UPDATED')
        <if test="shopIds!=null and shopIds.size() > 0">
            and kpl.shop_user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) tt
    </select>

    <!--查询 总交易额 和 总订单数 和 利润 （美元单位），转运、代发总数量，总入库量-->
    <select id="selectOverview" resultType="com.hzjm.service.model.VO.AdminHomeBiOverviewVo">
        select ifnull(max(totalGMV), 0) total_gmv
        , ifnull(max(totalSaleOrders), 0) total_orders
        , ifnull(max(totalReshipOrders), 0) total_Reship_Orders
        , ifnull(max(totalProfit), 0) profit_generated
        , ifnull(max(totalInbound), 0) total_Inbound
        , ifnull(max(serviceRevenue), 0) service_revenue
        , ifnull(max(issueCount), 0) issue_count
        , ifnull(convert(max(issueCount) / max(totalSaleOrders) * 100, decimal(12, 2)), 0) issue_order_rate
        , ifnull(convert(max(totalGMV) / max(totalSaleOrders) , decimal(12, 2)), 0) aov

        from (
        select ifnull(convert(sum(sale_price)/100, decimal(12, 2)), 0) totalGMV,
        count(1) totalSaleOrders,
        0 totalReshipOrders,
        ifnull(convert(sum((knet_owning - seller_owning) / 100), decimal(12, 2)), 0) totalProfit,
        0 totalInbound,
        0 serviceRevenue,
        0 issueCount
        from platform_order
        where del_flag = 0
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)

        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        union all

        select 0 totalGMV,
        0 totalSaleOrders,
        ifnull(count(distinct one_id), 0) totalReshipOrders,
        0 totalProfit,
        0 totalInbound,
        0 serviceRevenue,
        0 issueCount
        from sys_prod_search
        where del_flag = 0
        and search_type = 1
        and odd_type in (3, 4)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        union all

        select 0 totalGMV,
        0 totalSaleOrders,
        0 totalReshipOrders,
        0 totalProfit,
        ifnull(count(distinct one_id), 0) totalInbound,
        0 serviceRevenue,
        0 issueCount
        from sys_prod_search
        where del_flag = 0
        and search_type = 1
        and status in (1,2,3,4,7,8,9,10)
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>
        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        union all

        select 0 totalGMV,
        0 totalSaleOrders,
        0 totalReshipOrders,
        0 totalProfit,
        0 totalInbound,
        sum(total_fee) serviceRevenue,
        0 issueCount
        from sys_bill
        where relation_type = 11
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND user_id = #{userId}
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        union all

        select 0 totalGMV,
        0 totalSaleOrders,
        0 totalReshipOrders,
        0 totalProfit,
        0 totalInbound,
        0 serviceRevenue,
        count(1) issueCount
        from platform_order
        where del_flag = 0
        and knet_order_status in ('with_issue', 'returned')
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) tt
    </select>
    <!-- 查询销售额，订单数量，利润，查询结果按日分组-->
    <select id="selectPlatformOrderByDay" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT IFNULL(convert(sum(sale_price) / 100, decimal(12, 2)), 0) total_GMV
        , IFNULL(count(1), 0) total_Sale_Orders
        , IFNULL(convert(sum(knet_owning - seller_owning) / 100, decimal(12, 2)), 0) total_Profit
        , left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) date
        from platform_order
        where del_flag = 0
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="platform != null">
            and platform = #{platform,jdbcType=VARCHAR}
        </if>
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="account!=null and account != ''">
            AND account = #{account}
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        GROUP BY date
    </select>
    <!--查询转运代发数量，查询结果按日分组-->
    <select id="selectReshipOrdersByDay" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT count(distinct one_id) total_Reship_Orders
        , left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) date
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and odd_type in (3, 4)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY date
    </select>
    <!--查询入库数量，查询结果按日分组-->
    <select id="selectInboundByDay" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT count(distinct one_id) total_Inbound
        , left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) date
        from sys_prod_search
        where del_flag = 0
        and search_type = 1
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY date
    </select>
    <!--统计销售额，订单数量，利润，统计结果按日分组-->
    <select id="selectPlatformOrder" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT IFNULL(convert(sum(sale_price) / 100, decimal(12, 2)), 0) total_GMV
        , IFNULL(count(1), 0) total_Sale_Orders
        , IFNULL(convert(sum(knet_owning - seller_owning) / 100, decimal(12, 2)), 0) total_Profit
        from platform_order
        where del_flag = 0
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <!--        and knet_order_status IN-->
        <!--        ('pending_payout', 'completed', 'payout_completed')-->
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <!--统计转运代发数量，统计结果按日分组-->
    <select id="selectReshipOrders" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT ifnull(count(distinct one_id), 0) total_Reship_Orders
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and odd_type in (3, 4)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_out, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <!--统计入库数量，查询结果按日分组-->
    <select id="selectInbound" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT ifnull(count(distinct one_id), 0) total_Inbound
        from sys_prod_search
        where del_flag = 0
        and search_type = 1
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>

        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <!--查询商家的提现历史记录-->
    <select id="selectSysBillByUserId" resultType="com.hzjm.service.model.VO.AdminHomeBiUserCashoutHistoryVo">
        select t1.total_fee amount
        , left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) transaction_Time
        , t1.out_trade_no transaction_Id
        from sys_bill t1
        where 1
        and t1.relation_type = 2
        and t1.user_id = #{userId}
        and t1.status = 2
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by t1.id desc
    </select>
    <!--查询各平台费率-->
    <select id="selectUserFee" resultType="com.hzjm.service.model.VO.AdminHomeBiUserPlatFeeVo">
        select platform
             , ifnull(convert(transaction_fee, decimal (12, 2)), 0)    transaction_fee
             , ifnull(convert(payment_proc, decimal (12, 2)), 0)       payment_proc
             , ifnull(convert(shipping_fee / 100, decimal (12, 2)), 0) shipping_fee
             , ifnull(convert(seller_fee, decimal (12, 2)), 0)         seller_fee
             , ifnull(convert(knet_service_fee, decimal (12, 2)), 0)   knet_service_fee
        from (select 'StockX'        platform
                   , t1.service_rate transaction_fee
                   , t1.draw_rate    payment_proc
                   , t1.shipping_fee
                   , 0               seller_fee
                   , t1.oc_fee       knet_service_fee
              from shop_user_plat t1
              where t1.shop_id = #{userId}
                and t1.del_flag = 0
                and plat_id = 11001
              union all
              select 'GOAT'          platform
                   , t1.service_rate transaction_fee
                   , t1.draw_rate    payment_proc
                   , t1.shipping_fee
                   , 0               seller_fee
                   , t1.oc_fee       knet_service_fee
              from shop_user_plat t1
              where t1.shop_id = #{userId}
                and t1.del_flag = 0
                and plat_id = 11002
              union all
              select 'KNET'          platform
                   , t1.service_rate transaction_fee
                   , t1.draw_rate    payment_proc
                   , t1.shipping_fee
                   , 0               seller_fee
                   , t1.oc_fee       knet_service_fee
              from shop_user_plat t1
              where t1.shop_id = #{userId}
                and t1.del_flag = 0
                and plat_id = 11003
              union all
              select 'Kicks Crew'    platform
                   , t1.service_rate transaction_fee
                   , t1.draw_rate    payment_proc
                   , t1.shipping_fee
                   , 0               seller_fee
                   , t1.oc_fee       knet_service_fee
              from shop_user_plat t1
              where t1.shop_id = #{userId}
                and t1.del_flag = 0
                and plat_id = 11005
              union all
              select 'eBay'          platform
                   , t1.service_rate transaction_fee
                   , t1.draw_rate    payment_proc
                   , t1.shipping_fee
                   , 0               seller_fee
                   , t1.oc_fee       knet_service_fee
              from shop_user_plat t1
              where t1.shop_id = #{userId}
                and t1.del_flag = 0
                and plat_id = 11006
              union all
              select 'StockX_Flex'          platform
                   , t1.service_rate transaction_fee
                   , t1.draw_rate    payment_proc
                   , t1.shipping_fee
                   , 0               seller_fee
                   , t1.oc_fee       knet_service_fee
              from shop_user_plat t1
              where t1.shop_id = #{userId}
                and t1.del_flag = 0
                and plat_id = 11009
             ) tt
    </select>
    <!--查询各个在架商品的数量-->
    <select id="selectUserPlatCount" resultType="com.hzjm.service.model.VO.AdminHomeBiUserPlatCountVo">
        select ifnull(sum(stockx_count), 0) stockx_count
        , ifnull(sum(goat_count), 0) goat_count
        , ifnull(sum(goat_is_count), 0) goat_is_count
        , ifnull(sum(kc_count), 0) kc_count
        , ifnull(sum(ebay_count), 0) ebay_count
        , ifnull(sum(poizon_count), 0) poizon_count
        , ifnull(sum(tiktok_count), 0) tiktok_count
        , ifnull(sum(surge_count), 0) surge_count
        from (SELECT CASE WHEN stockx_status = 'open' THEN 1 ELSE 0 END stockx_count
        , CASE WHEN goat_status = 'open' THEN 1 ELSE 0 END goat_count
        , CASE WHEN goatis_status = 'open' THEN 1 ELSE 0 END goat_is_count
        , CASE WHEN kc_status = 'open' THEN 1 ELSE 0 END kc_count
        , CASE WHEN ebay_status = 'open' THEN 1 ELSE 0 END ebay_count
        , CASE WHEN poizon_status = 'open' THEN 1 ELSE 0 END poizon_count
        , CASE WHEN tts_status = 'open' THEN 1 ELSE 0 END tiktok_count
        , CASE WHEN stockx_direct_status = 'open' THEN 1 ELSE 0 END surge_count
        FROM knet_product_listing kpl
        WHERE kpl.knet_listing_status IN ('CREATED', 'UPDATED')
        AND kpl.shop_user_id = #{userId}
        <if test="shopIds!=null and shopIds.size() > 0">
            and kpl.shop_user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        ) tt

    </select>
    <!--查看商家-->
    <select id="selectUserInfo" resultType="com.hzjm.service.model.VO.AdminHomeBiUserInfoVo">
        SELECT t1.realname                                                  AS                                                       user_name,
               t1.uid                                                       AS                                                       user_id,
               t1.gmt_create,
               TIMESTAMPDIFF(YEAR, t1.gmt_create, NOW())                                                                             years,
               TIMESTAMPDIFF(MONTH, DATE_ADD(
                                        left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10),
                       INTERVAL TIMESTAMPDIFF(YEAR,
                                              left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10),
                                              NOW()) YEAR),
                                    NOW())                                                                                           months,
               TIMESTAMPDIFF(DAY, DATE_ADD(
                                          DATE_ADD(left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10),
                                              INTERVAL TIMESTAMPDIFF(YEAR,
                                                                     left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10),
                                                                     NOW()) YEAR),
                                          INTERVAL TIMESTAMPDIFF(MONTH, DATE_ADD(
                                     left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10), INTERVAL
                                     TIMESTAMPDIFF(YEAR,
                                                   left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10),
                                                   NOW()) YEAR),
                                                            NOW()) MONTH), NOW()) days,
               case when t1.level = 0 then 'Seller' else 'Power Seller' end AS                                                       seller_level,
               t1.customer_manager                                          AS                                                       account_manager
        FROM shop_user t1
        WHERE t1.id = #{userId}
          AND t1.del_flag = 0
    </select>


    <!--统计销售额，订单数量，利润，统计结果按平台分组-->
    <select id="selectTotalByPlatform" resultType="com.hzjm.service.model.VO.AdminHomeBiDataTrendVo">
        SELECT if(account = 'STOCKX_FLEX', account,platform) platform
        , IFNULL(convert(sum(sale_price) / 100, decimal(12, 2)), 0) total_GMV
        , IFNULL(count(1), 0) total_Sale_Orders
        from platform_order
        where del_flag = 0
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)
        <!--        and knet_order_status IN-->
        <!--        ('pending_payout', 'completed', 'payout_completed')-->
        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by if(account = 'STOCKX_FLEX', account,platform)
    </select>
    <!--商家列表 - 查询商家的用户名，uid，客户经理 , 推荐人-->
    <select id="selectUserNmaeOrUidOrAccountManager" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        SELECT
        t1.id AS user_id,
        t1.id AS shop_id,
        t1.realname AS user_name,
        t1.uid AS user_uid,
        t1.customer_manager account_Manager,
        t1.parent_uid referrer_Name
        FROM shop_user t1
        WHERE 1
        AND t1.del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            and t1.id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="userUid != null and userUid != ''">
            and t1.uid = #{userUid}
        </if>
        <if test="userName != null and userName != ''">
            and t1.realname like concat('%', #{userName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="customerManager != null and customerManager != ''">
            and t1.customer_manager like concat('%', #{customerManager,jdbcType=VARCHAR}, '%')
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="referrerName != null and referrerName != ''">
            and t1.parent_uid like concat('%', #{referrerName,jdbcType=VARCHAR}, '%')
        </if>
        order by t1.gmt_create desc
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 陈旧库存百分比-->
    <select id="selectUserAgedInventory" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id
        , count(distinct one_id) / #{inventory} aged_Inventory
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and left(CONVERT_TZ(gmt_in, '+00:00', 'America/New_York'), 10) &lt; DATE_SUB(CURRENT_DATE, INTERVAL 120 DAY)
        and status IN (1, 2, 3, 4, 7, 8, 9, 10,12)

        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>

        group by shop_id

        <if test="sortOrder == 'ascend'">
            order by aged_Inventory
        </if>
        <if test="sortOrder == 'descend'">
            order by aged_Inventory desc
        </if>

        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 上架商品/总库存比率-->
    <select id="selectUserListedTotal" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        ifnull(convert(sum(t1.sale_price) / 100 / #{inventory}, decimal(12, 2)), 0) listedTotal_Inventory_Rate
        from platform_order t1
        where del_flag = 0
        <!--        and t1.knet_order_status IN-->
        <!--        ('pending_payout', 'completed', 'payout_completed')-->
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by listedTotal_Inventory_Rate
        </if>
        <if test="sortOrder == 'descend'">
            order by listedTotal_Inventory_Rate desc
        </if>

        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 问题订单率-->
    <select id="selectUserissueOrderRate" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        ifnull(convert(max(num1) / max(num2), decimal(12, 4)), 0) issue_Order_Rate
        from (
        select shop_id , count(1) num1 , 0 num2
        from platform_order t1
        where del_flag = 0
        and knet_order_status in ('with_issue', 'returned')
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id

        union all

        select shop_id , 0 num1 ,count(1) num2
        from platform_order t1
        where del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        ) tt

        group by shop_id
        having max(num1) > 0 and max(num2) > 0
        <if test="sortOrder == 'ascend'">
            order by issue_Order_Rate
        </if>
        <if test="sortOrder == 'descend'">
            order by issue_Order_Rate desc
        </if>
        limit #{limitStart},#{size}
    </select>

    <!--商家列表 - 每订单平均价值-->
    <select id="selectUserAovOrder" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        ifnull(convert(sum(t1.sale_price) /100 / count(1), decimal(12, 2)), 0) aov_per_order
        from platform_order t1
        where del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by aov_per_order
        </if>
        <if test="sortOrder == 'descend'">
            order by aov_per_order desc
        </if>
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 入库总量-->
    <select id="selectUserInbound" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        count(distinct one_id) inbound_total
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and shop_id
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="wareIds!=null and wareIds.size() > 0">
            and t1.ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by inbound_total
        </if>
        <if test="sortOrder == 'descend'">
            order by inbound_total desc
        </if>
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 总交易额-->
    <select id="selectUserTotalGMV" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        ifnull(convert(sum(t1.sale_price)/100, decimal(12, 0)), 0) total_gmv
        from platform_order t1
        where del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by total_gmv
        </if>
        <if test="sortOrder == 'descend'">
            order by total_gmv desc
        </if>
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 总订单数-->
    <select id="selectUserTotalOrder" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        count(1) total_Order
        from platform_order t1
        where del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by total_Order
        </if>
        <if test="sortOrder == 'descend'">
            order by total_Order desc
        </if>
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 产生的利润-->
    <select id="selectUserProfitGenerated" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        ifnull(convert((sum(t1.knet_owning) - sum(t1.seller_owning))/100, decimal(12, 2)), 0) profit_Generated
        from platform_order t1
        where del_flag = 0
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by profit_Generated
        </if>
        <if test="sortOrder == 'descend'">
            order by profit_Generated desc
        </if>
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 服务收入-->
    <select id="selectUserServiceRevenue" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select user_id shopId,
        sum(total_fee) service_Revenue
        from sys_bill t1
        where relation_type = 11
        <if test="userIds != null and userIds.size() > 0">
            and t1.user_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.user_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by user_id
        <if test="sortOrder == 'ascend'">
            order by service_Revenue
        </if>
        <if test="sortOrder == 'descend'">
            order by service_Revenue desc
        </if>
        limit #{limitStart},#{size}
    </select>
    <!--商家列表 - 总库存-->
    <select id="selectUserTotal" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id,
        count(1) total_Inventory
        from sys_prod_search t1
        where del_flag = 0
        and search_type = 1
        and status in (1, 2, 3, 4, 7, 8, 9, 10,12)
        <if test="userIds != null and userIds.size() > 0">
            and t1.shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="beginTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="wareIds!=null and wareIds.size() > 0">
            and t1.ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and t1.shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        <if test="sortOrder == 'ascend'">
            order by total_Inventory
        </if>
        <if test="sortOrder == 'descend'">
            order by total_Inventory desc
        </if>
        limit #{limitStart},#{size}
    </select>

    <select id="selectFakeTotalRate" resultType="com.hzjm.service.model.VO.AdminHomeBiUserListVo">
        select shop_id
        , ifnull(convert(max(fakeTotal) / max(totalSaleOrders) * 100, decimal(12, 6)), 0) fake_order_rate
        from (
        select shop_id
        , count(1) fakeTotal
        ,0 totalSaleOrders
        from sys_prod_search
        where check_result = 4 and odd_type = 6 and del_flag = 0 and search_type = 1
        <if test="beginTime != null">
            and left(CONVERT_TZ(gmt_create, '+00:00', '-5:00'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(gmt_create, '+00:00', '-5:00'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id

        union all

        select shop_id
        ,0 fakeTotal,
        count(1) totalSaleOrders
        from platform_order
        where del_flag = 0
        <if test="beginTime != null">
            and left(CONVERT_TZ(gmt_create, '+00:00', '-5:00'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            and left(CONVERT_TZ(gmt_create, '+00:00', '-5:00'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="userIds != null and userIds.size() > 0">
            and shop_id in
            <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by shop_id
        ) tt
        group by shop_id
        having max(fakeTotal) >0 and max(totalSaleOrders) > 0
        <if test="sortOrder == 'ascend'">
            order by fake_order_rate
        </if>
        <if test="sortOrder == 'descend'">
            order by fake_order_rate desc
        </if>
        limit #{limitStart},#{size}
    </select>

    <!--寄卖过的假鞋数量-->
    <select id="selectFakeTotal" resultType="java.lang.Long">
        select count(1) total
        from sys_prod_search
        where check_result = 4 and odd_type = 6 and del_flag = 0 and search_type = 1
        and left(CONVERT_TZ(gmt_create, '+00:00', '-5:00'), 10) &gt;=
        left(#{beginTime,jdbcType=TIMESTAMP},10)
        and left(CONVERT_TZ(gmt_create, '+00:00', '-5:00'), 10) &lt;=
        left(#{endTime,jdbcType=TIMESTAMP},10)

        <if test="userId!=null">
            AND shop_id = #{userId}
        </if>
        <if test="wareIds!=null and wareIds.size() > 0">
            and ware_id not in
            <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="shopIds!=null and shopIds.size() > 0">
            and shop_id in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--获取统计数据需要忽略的仓库ID-->
    <select id="selectWareIds" resultType="java.lang.Integer">
        select ID
        from sys_ware
        where name in ('DE-3','StockX Flex')
    </select>
    <!--统计商家订单归属平台-->
    <select id="queryShopIdPlatformCount" resultType="com.hzjm.service.model.VO.ShopUidPlListVo">
        SELECT
        platform,
        count(*) total
        FROM
        platform_order
        <where>
            <if test="shopId!=null">
                shop_id = #{shopId}
            </if>
            and del_flag = 0
            and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            left(#{beginTime,jdbcType=TIMESTAMP},10)
            and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            left(#{endTime,jdbcType=TIMESTAMP},10)
            GROUP BY
            platform
        </where>
    </select>
    <!--查询商家商品总Listed数量-->
    <select id="queryItemListedTotal" resultType="java.lang.Long">
        SELECT
        COUNT(DISTINCT CASE
        WHEN kpl.shoe_condition = 'NEW'
        AND kpl.box_condition = 'GOOD_CONDITION'
        THEN kpl.one_id
        ELSE NULL
        END)
        +
        COUNT(DISTINCT CASE
        WHEN NOT (kpl.shoe_condition = 'NEW' AND kpl.box_condition = 'GOOD_CONDITION')
        THEN kpl.one_id
        ELSE NULL
        END) AS total_num
        FROM
        knet_product_listing kpl
        <where>
            kpl.knet_listing_status IN ('CREATED', 'UPDATED')
            <if test="shopId!=null">
                AND kpl.shop_user_id= #{shopId}
            </if>
            <if test="beginTime!=null">
                AND LEFT(CONVERT_TZ(kpl.created_at, '+00:00', 'America/New_York'), 10) &gt;=LEFT(#{beginTime,jdbcType=TIMESTAMP},10)
                AND LEFT(CONVERT_TZ(kpl.created_at, '+00:00', 'America/New_York'), 10) &lt;=LEFT(#{endTime,jdbcType=TIMESTAMP},10)
            </if>
        </where>
    </select>
    <!--查询日期范围内，有订单的商家s-->
    <select id="queryUsedShopIds" resultType="java.lang.Long">
        SELECT
        DISTINCT shop_id
        FROM
        platform_order
        <where>
            del_flag = 0
            <if test="beginTime!=null">
                AND LEFT(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;=LEFT(#{beginTime,jdbcType=TIMESTAMP},10)
                AND LEFT(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;=LEFT(#{endTime,jdbcType=TIMESTAMP},10)
            </if>
        </where>
    </select>

    <!--查询商家订单部分统计数据，gmv 总入库商品-->
    <select id="queryRecapMonthlyOrder" resultType="com.hzjm.service.model.VO.RecapMonthlyOrderVo">
        SELECT
        shop_id,
        COUNT(1) total_Order,
        IFNULL(CONVERT( SUM( t1.sale_price ) / 100, DECIMAL ( 12, 0 ) ), 0 ) total_gmv
        FROM
        platform_order t1
        WHERE del_flag = 0
        <if test="userId != null">
            AND t1.shop_id = #{userId}
        </if>
        <if test="beginTime != null">
            AND LEFT(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &gt;=
            LEFT(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>
        <if test="endTime != null">
            AND LEFT(CONVERT_TZ(t1.gmt_create, '+00:00', 'America/New_York'), 10) &lt;=
            LEFT(#{endTime,jdbcType=TIMESTAMP},10)
        </if>
    </select>

    <select id="selectFinancialStatistics" resultType="com.hzjm.service.model.VO.FinancialStatisticsVo">
        select
        sum(if(relation_type = 5, total_fee, 0)) totalCashout
        , sum(if(relation_type = 1, total_fee, 0)) totalDeposit
        , sum(if(relation_type = 2, total_fee, 0)) totalWithdrawal
        , sum(if(relation_type = 22, total_fee, 0)) knetDeduction
        , sum(if(relation_type = 17, total_fee, 0)) returnDeduction
        from sys_bill
        where 1
        and relation_type in (1,2,5,17,22)
        and status = 2
        and del_flag = 0
        and user_type = 5

        <if test="beginTime != null">
            AND LEFT(CONVERT_TZ(gmt_modify, '+00:00', 'America/New_York'), 10) &gt;=
            LEFT(#{beginTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="endTime != null">
            AND LEFT(CONVERT_TZ(gmt_modify, '+00:00', 'America/New_York'), 10) &lt;=
            LEFT(#{endTime,jdbcType=TIMESTAMP},10)
        </if>

        <if test="shopIds!=null and shopIds.size() > 0">
            and user_id not in
            <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>

    <select id="selectTransactionAmount" resultType="com.hzjm.service.model.VO.TransactionAmountVo">
        SELECT
        MAX(incomeAmount) AS incomeAmount,
        MAX(expenseAmount) AS expenseAmount,
        MAX(incomeAmountPendingDay) AS incomeAmountPendingDay,
        MAX(expenseAmountPendingDay) AS expenseAmountPendingDay,
        MAX(incomeAmountCompletedDay) AS incomeAmountCompletedDay,
        MAX(expenseAmountCompletedDay) AS expenseAmountCompletedDay
        FROM (
            SELECT
                SUM(IF(ie_type = 1, total_fee, 0)) AS incomeAmount,
                SUM(IF(ie_type = -1, total_fee, 0)) AS expenseAmount,
                0 AS incomeAmountPendingDay,
                0 AS expenseAmountPendingDay,
                0 AS incomeAmountCompletedDay,
                0 AS expenseAmountCompletedDay
            FROM sys_bill
            WHERE
            status = 1
            AND del_flag = 0
            AND user_type = 5
            <if test="shopIds!=null and shopIds.size() > 0">
                AND user_id NOT IN
                <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            UNION ALL

            SELECT
                0 AS incomeAmount,
                0 AS expenseAmount,
                SUM(IF(ie_type = 1 AND status = 1, total_fee, 0)) AS incomeAmountPendingDay,
                SUM(IF(ie_type = -1 AND status = 1, total_fee, 0)) AS expenseAmountPendingDay,
                SUM(IF(ie_type = 1 AND status = 2, total_fee, 0)) AS incomeAmountCompletedDay,
                SUM(IF(ie_type = -1 AND status = 2, total_fee, 0)) AS expenseAmountCompletedDay
            FROM sys_bill
            WHERE
                del_flag = 0
                AND user_type = 5
                AND LEFT(CONVERT_TZ(gmt_modify, '+00:00', 'America/New_York'), 10) = LEFT(CONVERT_TZ(SYSDATE(),'+00:00','America/New_York'),10)
                AND status IN (1, 2)
                <if test="shopIds!=null and shopIds.size() > 0">
                    AND user_id NOT IN
                    <foreach collection="shopIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
        ) AS combined_results;
    </select>

</mapper>
