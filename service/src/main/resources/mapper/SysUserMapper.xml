<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysUserMapper">
    <select id="querySysWareUserList" resultType="com.hzjm.service.model.VO.SysWareUserDto">
        SELECT s.id,
               s.account,
               s.nickname,
               u.ware_id
        FROM sys_user s
                 LEFT JOIN sys_ware_user u ON s.id = u.user_id
        WHERE s.del_flag = 0
          AND u.del_flag = 0
          AND s.ware_power = 1
          AND u.ware_id is not null
    </select>
</mapper>
