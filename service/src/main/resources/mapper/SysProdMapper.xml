<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysProdMapper">
    <!--查询合格的商品-->
    <select id="selectSysProdBySkuAndSpec" parameterType="com.hzjm.service.entity.SysProd"
            resultType="com.hzjm.service.entity.SysProd">
        SELECT DISTINCT
            t1.*
        FROM
            sys_prod t1
        INNER JOIN sys_prod_search t2 ON t1.id = t2.prod_id
        WHERE t1.del_flag = 0
            AND t2.check_result = 1
            AND t2.del_flag = 0
            AND t1.one_id is not null

            <if test="prod.sku != null and prod.sku != ''">
                AND t1.sku = #{prod.sku}
            </if>

            <if test="prod.spec != null and prod.spec != ''">
                AND t1.spec = #{prod.spec}
            </if>
            <if test="prod.shopid != null and prod.shopid != ''">
                AND t1.shop_id = #{prod.shopId}
            </if>
            <if test="prod.status != null and prod.status != ''">
                AND t1.status = #{prod.status}
            </if>

        order by gmt_in
        <if test="selectLimit != null and selectLimit > 0 ">
            LIMIT #{selectLimit}
        </if>
    </select>

    <select id="selectOrderLocked" resultType="java.lang.String">
        select distinct one_id from platform_order
        where del_flag = '0'
        and order_status in ('need_to_ship','did_not_ship','boxing','boxing_today','boxed')
        <if test='oneIdList != null and oneIdList.size() > 0'>
            and one_id in
            <foreach item='oneId' index='index' collection='oneIdList' open='(' separator=',' close=')'>
                #{oneId}
            </foreach>
        </if>
    </select>

    <select id="selectSysProdLocked" resultType="java.lang.String">
        SELECT distinct one_id from sys_prod where status in (7,8,9,10) and del_flag = '0'
         <if test='oneIdList != null and oneIdList.size() > 0'>
             and one_id in
             <foreach item='oneId' index='index' collection='oneIdList' open='(' separator=' ,' close=')'>
                 #{oneId}
             </foreach>
         </if>
    </select>

    <select id="selectIdByOrSkuSize" resultType="com.hzjm.service.entity.SysProd">
        select
            distinct  prod.one_id
            ,prod.ware_id
        from sys_prod prod
        inner join sys_prod_search search on search.prod_id = prod.id  and search.search_type = 1 and search.del_flag = 0
        WHERE prod.del_flag = 0
        and search.check_result = 1

        and prod.status = 1

        and concat(prod.sku,prod.spec)  = #{skuSize}
        and prod.shop_id = #{shopId}
        <if test='oneIdList != null and oneIdList.size() > 0'>
            and prod.one_id not in
            <foreach item='oneId' index='index' collection='oneIdList' open='(' separator=' ,' close=')'>
                #{oneId}
            </foreach>
        </if>

        <if test='wareIds != null and wareIds.size() > 0'>
            and prod.ware_id in
            <foreach item='wareId' index='index' collection='wareIds' open='(' separator=' ,' close=')'>
                #{wareId}
            </foreach>
        </if>

        order by prod.id desc
    </select>

</mapper>
