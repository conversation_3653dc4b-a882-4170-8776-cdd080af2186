<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysUpdatePriceEventsMapper">

    <!--需要处理的价格变化事件-->
    <select id="findNeedToUpdatePriceEvents" resultType="com.hzjm.service.entity.SysUpdatePriceEvents">
        SELECT
            e.*
        FROM
            sys_update_price_events e
                INNER JOIN(
                SELECT knet_listing_id,
                       MAX(create_time) AS latest_time
                FROM
                    sys_update_price_events
                WHERE
                    STATUS = 'PENDING' AND create_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
                GROUP BY
                    knet_listing_id) t
                          ON
                              e.knet_listing_id = t.knet_listing_id AND e.create_time = t.latest_time
        WHERE
            e.status = 'PENDING' AND e.create_time > DATE_SUB(NOW(), INTERVAL #{minutes} MINUTE)
        ORDER BY
            e.create_time DESC
        LIMIT #{total}
    </select>

</mapper>