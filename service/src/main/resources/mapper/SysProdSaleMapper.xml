<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysProdSaleMapper">


<!--    &lt;!&ndash;    部分的字段，用于列表&ndash;&gt;-->
<!--    <sql id="pageColumns">-->
<!--        sale.id-->
<!--        , sale.odd_no-->
<!--        , sale.plat_name-->
<!--        , sale.plat_order_no-->
<!--        , sale.gmt_create-->
<!--        , sale.label_img-->
<!--        , sale.note-->
<!--    </sql>-->

<!--    &lt;!&ndash;    全部的字段，用于导出&ndash;&gt;-->
<!--    <sql id="listColumns">-->
<!--        sale.id-->
<!--        , sale.odd_no-->
<!--        , sale.plat_name-->
<!--        , sale.plat_order_no-->
<!--        , sale.gmt_create-->
<!--        , sale.label_img-->
<!--        , sale.note-->
<!--        , sale.status-->
<!--        , user.realname shopName-->
<!--        , prod.sku-->
<!--        , prod.spec-->
<!--        , prod.remarks-->
<!--        , prod.oneId-->
<!--        , prod.ware_id-->
<!--        , sale.serviceFee-->
<!--    </sql>-->

    <!--    寄卖审核列表-->
    <select id="selectSaleList" resultType="com.hzjm.service.model.VO.SysProdSaleListVo"
            parameterType="com.hzjm.service.model.DTO.SysProdSalePageDto">
        select distinct
            sale.id
            , sale.odd_no
            , sale.plat_name
            , sale.plat_order_no
            , sale.gmt_create
            , sale.label_img
            , sale.note
            , sale.status

        from sys_prod_sale sale
        inner join sys_prod_deal deal on sale.id = deal.sale_id and sale.del_flag = 0 and deal.del_flag = 0
        left join sys_prod prod on  prod.del_flag = 0 and prod.id = deal.prod_id
        left join shop_user user on  user.del_flag = 0 and user.id = sale.shop_id
        where sale.del_flag = 0

            <!--        今日待处理-->
            <if test="dto.tabType != null and  dto.tabType != '' and dto.tabType == 1">
                and sale.gmt_deal &lt;= now() and sale.status = 1
            </if>

            <!--        待处理-->
            <if test="dto.tabType != null and  dto.tabType != '' and dto.tabType == 2">
                and sale.gmt_deal &gt;= now() and sale.status = 1
            </if>

            <!--        待出库-->
            <if test="dto.tabType != null and  dto.tabType != '' and dto.tabType == 3">
                and sale.status in (3,4)
            </if>

            <!--        已出库-->
            <if test="dto.tabType != null and  dto.tabType != '' and dto.tabType == 4">
                and sale.status = 5
            </if>

            <!--        已关闭-->
            <if test="dto.tabType != null and  dto.tabType != '' and dto.tabType == 5">
                and sale.status = 2
            </if>

            <include refid="whereSql" />

        order by sale.gmt_create desc
    </select>

    <!--    寄卖审核列表统计-->
    <select id="selectSaleListCount" resultType="com.hzjm.service.model.VO.SysProdSaleCountVo"
            parameterType="com.hzjm.service.model.DTO.SysProdSalePageDto">
        SELECT
        COUNT(DISTINCT CASE WHEN sale.gmt_deal &lt;= NOW() AND sale.status = 1 THEN sale.odd_no END) dayAuditNum,
        COUNT(DISTINCT CASE WHEN sale.gmt_deal &gt;= NOW() AND sale.status = 1 THEN sale.odd_no END) auditNum,
        COUNT(DISTINCT CASE WHEN sale.status IN (3, 4) THEN sale.odd_no END) outingNum,
        COUNT(DISTINCT CASE WHEN sale.status = 5 THEN sale.odd_no END) outNum,
        COUNT(DISTINCT CASE WHEN sale.status = 2 THEN sale.odd_no END) closeNum
        from sys_prod_sale sale
        left join sys_prod_deal deal on sale.id = deal.sale_id and sale.del_flag = 0 and deal.del_flag = 0
        left join sys_prod prod on prod.del_flag = 0 and prod.id = deal.prod_id
        left join shop_user user on user.del_flag = 0 and user.id = sale.shop_id
        where sale.del_flag = 0
        <include refid="whereSql"/>

    </select>


    <sql id="whereSql">
        <!--        仓库-->
        <if test="dto.wareIdList != null and dto.wareIdList.size() > 0">
            AND prod.ware_id IN
            <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--        销售日期-->
        <if test="dto.beginTime != null ">
            AND sale.gmt_deal &gt;= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null ">
            AND sale.gmt_deal &lt; #{dto.endTime}
        </if>
        <!--        ONE ID-->
        <if test="dto.oneIdList != null and dto.oneIdList.size() > 0">
            AND prod.one_id IN
            <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--        识别码-->
        <if test="dto.shopUid != null and  dto.shopUid != ''">
            and user.uid = #{dto.shopUid}
        </if>
        <!--        尺码-->
        <if test="dto.specList != null and dto.specList.size() > 0">
            AND prod.spec IN
            <foreach item="item" index="index" collection="dto.specList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--        SKU-->
        <if test="dto.skuList != null and dto.skuList.size() > 0">
            AND prod.sku IN
            <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--        寄售平台单号-->
        <if test="dto.platOrderNoList != null and dto.platOrderNoList.size() > 0">
            AND sale.plat_order_no IN
            <foreach item="item" index="index" collection="dto.platOrderNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--        平台单号-->
        <if test="dto.oddNo != null and  dto.oddNo != ''">
            and sale.odd_no = #{dto.oddNo}
        </if>
        <!--        寄卖平台-->
        <if test="dto.platName != null and dto.platName.size() > 0">
            AND sale.plat_name IN
            <foreach item="item" index="index" collection="dto.platName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </sql>

</mapper>
