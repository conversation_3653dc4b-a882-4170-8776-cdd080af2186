<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.RecapMonthlyMapper">

    <!--查询年度数据-->
    <select id="queryRecapYearlyByCondition"
            parameterType="com.hzjm.service.model.DTO.TaskRecapReqDto"
            resultType="com.hzjm.service.entity.RecapMonthly">
        SELECT
        r.id AS id,
        r.shop_uid AS shop_uid,
        SUM( r.total_items_sold ) AS total_items_sold,
        SUM( r.total_gmv ) AS total_gmv,
        SUM( r.total_inbound_items ) AS total_inbound_items,
        SUM( r.total_reships ) AS total_reships,
        ( SUM( r.total_gmv ) / NULLIF( SUM( r.total_items_sold ), 0 ) ) AS average_order_value,
        r.percent_of_sales_goat AS percent_of_sales_goat,
        r.percent_of_sales_kicks_crew AS percent_of_sales_kicks_crew,
        r.percent_of_sales_ebay AS percent_of_sales_ebay,
        r.percent_of_sales_poizon AS percent_of_sales_poizon,
        r.percent_of_sales_knet AS percent_of_sales_knet,
        r.percent_of_sales_stockx AS percent_of_sales_stockx,
        r.items_still_listed AS items_still_listed,
        r.items_sold AS items_sold,
        r.start_date AS start_date,
        r.end_date AS end_date,
        r.gmt_create AS gmt_create,
        r.gmt_modify AS gmt_modify
        FROM
        recap_monthly r
        <where>
            <if test="dto.shopUid != null ">
                r.shop_uid=#{dto.shopUid}
            </if>
            <if test="dto.year != null">
                AND YEAR (r.start_date) = #{dto.year}
                AND YEAR (r.end_date) = #{dto.year}
            </if>
        </where>
    </select>
</mapper>
