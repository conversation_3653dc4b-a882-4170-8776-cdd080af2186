<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysRepairOrderMapper">
    <!-- 查看维修单-->
    <select id="selectRepairOrderList" resultType="com.hzjm.service.entity.po.SysRepairOrderPO"
        parameterType="com.hzjm.service.model.DTO.SysRepairOrderPageDto">
        SELECT t1.id
            , t1.repair_batch_no
            , t1.repair_no
            , t1.approval_status
            , t1.repair_status
            , t1.gmt_create
            , t1.shop_uid
            , t1.repair_remark
            , t3.project_name
            , t3.amount
            , t4.realname
            , t5.name                        ware_name
            , t6.name                        shelf_name
            , t8.img
            , t8.remarks
            , t8.sku
            , t8.spec
            , t8.one_id
            , t5.name                        prod_in_name
            , DATEDIFF(now(), t8.gmt_in) stock_days
            , t7.check_result

        FROM   sys_repair_order t1
                left join sys_product_repair_rel t2 on t1.id = t2.repair_order_id and t2.del_flag = 0
                left join sys_repair_project t3 on t2.repair_project_id = t3.id and t3.del_flag = 0
                left join shop_user t4 on t1.shop_id = t4.id
                left join sys_ware t5 on t1.ware_id = t5.id and t5.del_flag = 0
                left join sys_ware_shelves t6 on t1.shelf_id = t6.id and t6.del_flag = 0
                left join sys_ware_in_prod t7 on t1.product_id = t7.prod_id
                inner join sys_prod t8 on t1.product_id =  t8.id


        where t1.del_flag = 0
        <include refid="whereSql"/>

        ORDER BY t1.gmt_create DESC
    </select>

    <!-- 查询维修订单列表总数 -->
    <select id="selectRepairOrderListCount" resultType="java.lang.Long"
        parameterType="com.hzjm.service.model.DTO.SysRepairOrderPageDto">
        SELECT COUNT(*) FROM sys_repair_order t1
        inner join sys_prod t8 on t1.product_id =  t8.id
        WHERE t1.del_flag = 0
        <include refid="whereSql"/>
    </select>

    <!-- 同用的查询条件： 维修单列表查询条件 || 维修单统计查询条件 -->
    <sql id="whereSql">
        <!-- 搜索值 -->
        <if test="dto.searchValue != null and dto.searchValue != ''">
            AND (t8.sku = #{dto.searchValue}
            or t8.remarks = #{dto.searchValue}
            or t1.repair_no = #{dto.searchValue}
            or t1.shop_uid = #{dto.searchValue}
            )
        </if>
        <!-- 店铺id -->
        <if test="shopId != null">
            AND t1.shop_id = #{shopId,jdbcType=INTEGER}
        </if>
        <!-- sku -->
        <if test="dto.sku != null and dto.sku != ''">
            AND t8.sku = #{dto.sku}
        </if>
        <!-- skuList -->
        <if test="dto.skuList != null and dto.skuList.size() > 0">
            and  t8.sku in
            <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- useridList 包含 -->
        <if test="dto.useridList != null and dto.useridList.size() > 0 ">
           and t1.shop_uid in
            <foreach item="item" index="index" collection="dto.useridList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- userIdNotList 不包含 -->
        <if test="dto.userIdNotList != null and dto.userIdNotList.size() > 0 ">
            and  t1.shop_uid not in
            <foreach item="item" index="index" collection="dto.userIdNotList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 维修单号 repairNo  -->
        <if test="dto.repairNo != null and dto.repairNo != ''">
            AND t1.repair_no = #{dto.repairNo}
        </if>
        <!-- 维修单号 repairNoList  -->
        <if test="dto.repairNoList != null and dto.repairNoList.size() > 0 ">
            and  t1.repair_no in
            <foreach item="item" index="index" collection="dto.repairNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- 维修批次单号 repairBatchNo  -->
        <if test="dto.repairBatchNo != null and dto.repairBatchNo != ''">
            AND t1.repair_batch_no = #{dto.repairBatchNo}
        </if>
        <!-- 维修批次单号 repairBatchNoList  -->
        <if test="dto.repairBatchNoList != null and dto.repairBatchNoList.size() > 0 ">
            and   t1.repair_batch_no in
            <foreach item="item" index="index" collection="dto.repairBatchNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--one_id-->
        <if test="dto.oneId != null and dto.oneId != ''">
            AND t1.one_id = #{dto.oneId}
        </if>
        <!-- oneIdList -->
        <if test="dto.oneIdList != null and dto.oneIdList.size() > 0 ">
            and   t1.one_id in
            <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- wareIdList 包含 -->
        <if test="dto.wareIdList != null and dto.wareIdList.size() > 0 ">
            and  t1.ware_id in
            <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- wareIdList 不包含 -->
        <if test="dto.wareIdNotList != null and dto.wareIdNotList.size() > 0 ">
            and   t1.ware_id not in
            <foreach item="item" index="index" collection="dto.wareIdNotList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.repairNo != null and dto.repairNo != ''">
            AND t1.repair_no LIKE CONCAT('%', #{dto.repairNo}, '%')
        </if>
        <if test="dto.approvalStatus != null and dto.approvalStatus.code != ''">
            AND t1.approval_status = #{dto.approvalStatus.code}
        </if>
        <if test="dto.repairStatus != null and dto.repairStatus.code != ''">
            AND t1.repair_status = #{dto.repairStatus.code}
        </if>
        <if test="dto.beginTime != null">
            AND t1.gmt_create &gt;= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null">
            AND t1.gmt_create &lt;= #{dto.endTime}
        </if>

        <!--taskStatus 维修单状态-->
        <!--这里修改列表分页逻辑的情况下，需要同步修改 SysRepairOrderServiceImpl 中 searchListCount 的统计逻辑-->
        <if test="dto.taskStatus != null and dto.taskStatus.code != ''">

            <!-- 管理端 - 维修订单: 正在进行中-->
            <if test="dto.taskStatus.code == 'REPAIR_ORDER_PENDING'">
                AND t1.repair_status not in ('REPAIR_FAILURE' , 'REPAIR_SUCCESS')
            </if>
            <!-- 管理端 - 维修订单: 已完成-->
            <if test="dto.taskStatus.code == 'REPAIR_ORDER_COMPLETED'">
                AND t1.repair_status in ('REPAIR_FAILURE' , 'REPAIR_SUCCESS')
            </if>
            <!-- 管理端 - 维修订单: 已取消-->
            <if test="dto.taskStatus.code == 'REPAIR_ORDER_CANCEL'">
                AND t1.approval_status = 'APPROVAL_CANCEL'
            </if>

            <!-- 管理端 - 审核中心: 今日待处理-->
            <!-- 美东时间的当前日期，格式为：YYYY-MM-DD DATE(CONVERT_TZ(NOW(), '+00:00', 'America/New_York'));-->
            <if test="dto.taskStatus.code == 'APPROVAL_ORDER_PENDING_TODAY'">
                AND t1.approval_status = 'APPROVAL_PENDING'
                and left(t1.gmt_create, 10) &gt;= DATE(CONVERT_TZ(NOW(), '+00:00', 'America/New_York'))
            </if>
            <!-- 管理端 - 审核中心: 待处理-->
            <if test="dto.taskStatus.code == 'APPROVAL_PENDING'">
                AND t1.approval_status = 'APPROVAL_PENDING'
                and t1.gmt_create &lt; DATE(CONVERT_TZ(NOW(), '+00:00', 'America/New_York'))
            </if>
            <!-- 管理端 - 审核中心: 已完成-->
            <if test="dto.taskStatus.code == 'APPROVAL_COMPLETED'">
                AND t1.approval_status in ('APPROVAL_APPROVE' , 'APPROVAL_REFUSE')
            </if>
            <!-- 管理端 - 审核中心: 已取消-->
            <if test="dto.taskStatus.code == 'APPROVAL_CANCEL'">
                AND t1.approval_status = 'APPROVAL_CANCEL'
            </if>

            <!-- 管理端 - 任务管理: 待处理-->
            <if test="dto.taskStatus.code == 'TASK_PENDING'">
                AND t1.approval_status = 'APPROVAL_APPROVE' and t1.repair_batch_no is null
            </if>
            <!-- 管理端 - 任务管理: 已完成-->
            <if test="dto.taskStatus.code == 'TASK_COMPLETED'">
                AND t1.approval_status = 'APPROVAL_APPROVE' and t1.repair_batch_no is not null
            </if>

            <!-- 商家端 - 维修订单 : 待处理 -->
            <if test="dto.taskStatus.code == 'REPAIR_SHOP_PENDING'">
                AND ( t1.repair_status in ('REPAIR_PENDING' ,'REPAIR_PROCESSING') or t1.approval_status  in ('APPROVAL_PENDING')  )
            </if>
            <!-- 商家端 - 维修订单 : 已完成 -->
            <if test="dto.taskStatus.code == 'REPAIR_SHOP_COMPLETED'">
                AND  (t1.repair_status in ('REPAIR_SUCCESS' ,'REPAIR_FAILURE','REPAIR_LOST_PARCEL') or t1.approval_status  in ('APPROVAL_REFUSE') )
            </if>
            <!-- 商家端 - 维修订单 : 已取消 -->
            <if test="dto.taskStatus.code == 'REPAIR_SHOP_CANCEL'">
                AND t1.approval_status = 'APPROVAL_CANCEL'
            </if>

        </if>
    </sql>

    <!-- 查看维修单详情 -->
    <select id="selectRepairOrderDetail" resultType="com.hzjm.service.entity.po.SysRepairOrderDetailPO">
        SELECT distinct t1.repair_no
            , t1.id as repair_order_id
            , t1.gmt_create
            , t3.project_name
            , t3.amount
            , t3.id as repair_project_id
            , t3.estimated_hours
            , t4.sku
            , t4.one_id
            , t4.spec
            , t4.check_result
            , t4.in_id
            , t5.realname as shop_name
            , t5.uid
            , t6.name as ware_name
            , t8.name as shelf_name
            , t1.repair_status
            , t1.approval_status
            , t1.repair_remark
        FROM sys_repair_order t1
                left join sys_product_repair_rel t2 on t1.id = t2.repair_order_id and t2.del_flag = 0
                left join sys_repair_project t3 on t2.repair_project_id = t3.id and t3.del_flag = 0
                left join sys_ware_in_prod t4 on t2.product_id = t4.prod_id and t4.del_flag = 0
                left join shop_user t5 on t1.shop_id = t5.id and t5.del_flag = 0
                left join sys_ware t6 on t1.ware_id = t6.id and t6.del_flag = 0
                left join sys_ware_shelves_prod t7 on t7.prod_id = t4.prod_id and t7.del_flag = 0
                left join sys_ware_shelves t8 on t7.shelves_id = t8.id and t8.del_flag = 0

        WHERE t1.del_flag = 0
        <if test="id != null and id != ''">
            and t1.id = #{id}
        </if>
        <if test="repairBatchNo != null and repairBatchNo != ''">
            and t1.repair_batch_no = #{repairBatchNo}
        </if>
        <if test="oneId != null and oneId != ''">
            and t4.one_id = #{oneId}
        </if>
        <if test="shopId != null">
            and t1.shop_id = #{shopId,jdbcType=INTEGER}
        </if>
    </select>

    <!-- 商家查询维修项目详情 -->
    <select id="selectShopRepairProjectDetail" resultType="com.hzjm.service.entity.po.SysRepairOrderDetailPO">
        SELECT distinct t3.project_name
        , t3.amount
        , t3.estimated_hours
        , t3.id as repair_project_id
        , t4.sku
        , t4.one_id
        , t4.spec
        , t4.check_result
        , t4.in_id
        FROM sys_ware_in_prod t4
        left join sys_product_repair_rel t2 on t2.product_id = t4.prod_id and t2.del_flag = 0
        left join sys_repair_project t3 on t2.repair_project_id = t3.id and t3.del_flag = 0
        WHERE t4.del_flag = 0

        <if test="oneId != null and oneId != ''">
            and t4.one_id = #{oneId}
        </if>
    </select>

    <!-- 按照时间查询前方维修鞋子，项目，预计工时 -->
    <select id="selectUpcomingShoeRepairTasksByTime" resultType="com.hzjm.service.entity.po.SysRepairOrderDetailPO">
        SELECT t1.one_id
            ,t3.project_name
            ,t3.estimated_hours
        FROM sys_repair_order t1
                left join sys_product_repair_rel t2 on t1.id = t2.repair_order_id and t2.del_flag = 0
                left join sys_repair_project t3 on t2.repair_project_id = t3.id and t3.del_flag = 0

        WHERE t1.del_flag = 0
        and t1.gmt_create &lt;= #{gmtCreate}
        and t1.approval_status = 'APPROVAL_APPROVE'
        and t1.repair_status in ('REPAIR_PENDING' ,'REPAIR_PROCESSING')
    </select>
</mapper>
