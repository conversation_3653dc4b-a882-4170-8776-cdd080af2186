<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysSkuMapper">

    <!--批量更新 color gender type category-->
    <update id="batchUpdateCustom">
        <foreach collection="skus" item="skuItem" separator=";">
            UPDATE sys_sku
            SET
            brand=#{skuItem.brand},
            gender = #{skuItem.gender},
            color = #{skuItem.color},
            product_type = #{skuItem.productType},
            product_category = #{skuItem.productCategory},
            gmt_modify = NOW()
            WHERE sku = #{skuItem.sku}
        </foreach>
    </update>

    <!-- 查询符合需求的sku 取第一个-->
    <select id="selectFirstSkuForEachIndexed" resultType="com.hzjm.service.entity.SysSku">
        <foreach collection="skuIndexedList" item="skuIndexed" separator="UNION ALL">
            (SELECT *
            FROM sys_sku s
            WHERE s.sku_indexed = #{skuIndexed}
            AND s.del_flag = 0
            LIMIT 1)
        </foreach>
    </select>
    <select id="queryDataGroupSkuAndSpec" resultType="com.hzjm.service.entity.SysSku">
        WITH ranked_skus AS (
            SELECT
                *,
                ROW_NUMBER( ) OVER ( PARTITION BY sku, spec ORDER BY gmt_create DESC ) AS rn
            FROM
                sys_sku
            WHERE
                del_flag = 0
              AND spec REGEXP '^[0-9]+(\.[0-9]+)?$'
            ) SELECT
                  *
              FROM
                  ranked_skus
              WHERE
                  rn = 1
    </select>

</mapper>
