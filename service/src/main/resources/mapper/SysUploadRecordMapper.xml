<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysUploadRecordMapper">

    <select id="selectOutBatchNoByProdId" resultType="java.lang.String">
        select odd_no from sys_prod_transport where del_flag = 0 and id in (
            select relation_id from sys_prod_deal where sys_prod_deal.type = 3
            <if test='prodId != null and prodId.size() > 0'>
                and sys_prod_deal.prod_id in
                <foreach item='item' index='index' collection='prodId' open='(' separator=' ,' close=')'>
                    #{item}
                </foreach>
            </if>
            and sys_prod_deal.del_flag = 0
        ) order by id desc
         limit 1
    </select>
</mapper>
