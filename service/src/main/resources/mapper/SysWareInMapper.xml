<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysWareInMapper">

    <select id="selectWareIn" resultType="com.hzjm.service.model.VO.SysPackWareInVo">

        select t1.log_no , CONVERT_TZ(t2.gmt_create, 'UTC', 'America/New_York') as gmt_create, t3.`nickname` ,t2.batch_no
        from shop_pack t1
                 inner join sys_ware_in t2 on t1.in_id = t2.id and t2.del_flag = 0
                 inner join sys_user t3 on t2.create_by_id = t3.id and t3.del_flag = 0
        where t1.log_no = #{logNo}
          and t1.del_flag = 0
          and t1.status = 2

    </select>

    <!--入库批次管理——统计查询-->
    <select id="selectWareBatchCount" resultType="com.hzjm.service.model.VO.SysWareBatchCountVo">
        select
            count(distinct t1.batch_no) num, count(0) prodNum
        from sys_ware_in t1
        <include refid="coreTable" />
    </select>

    <!--入库批次管理-list 查询-->
    <select id="selectWareBatchList" resultType="com.hzjm.service.model.VO.SysWareInProdListVo">
        select
        <include refid="selectList" />
        from (
            select distinct t1.batch_no from sys_ware_in t1

            <include refid="coreTable" />

            <include refid="orderBy" />

            limit #{dto.size,jdbcType=INTEGER} offset #{dto.offset,jdbcType=INTEGER}
        ) limit_table
        inner join sys_ware_in t1 on limit_table.batch_no = t1.batch_no

        <include refid="coreViewTable" />

        <include refid="orderBy" />

    </select>

    <!--入库批次管理-核心表 展示层-->
    <sql id="coreViewTable">
        inner join sys_ware_in_prod t2 FORCE INDEX (idx_ware_in_prod_composite)
            on t1.status = 3 and t1.del_flag = 0 and t2.del_flag = 0 and t1.id = t2.in_id
        <!--开始时间-->
        <if test=" dto.beginTime != null ">
            and t2.gmt_create &gt; #{dto.beginTime,jdbcType=TIMESTAMP}
        </if>
        <!--结束时间-->
        <if test="dto.endTime != null">
            and t2.gmt_create &lt; #{dto.endTime,jdbcType=TIMESTAMP}
        </if>
        <!--批次号-->
        <if test="dto.inBatchNo != null and dto.inBatchNo != ''">
            and t1.batch_no = #{dto.inBatchNo,jdbcType=VARCHAR}
        </if>
        <!--入库类型-->
        <if test="dto.type != null">
            and t1.type = #{dto.type,jdbcType=INTEGER}
        </if>
        <!--品名-->
        <if test="dto.remarks != null and dto.remarks != ''">
            and t2.remarks = #{dto.remarks,jdbcType=VARCHAR}
        </if>
        <!--skuList-->
        <if test="dto.skuList != null and dto.skuList.size()>0">
            and t2.sku in
            <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--规格-->
        <if test="dto.specList != null and dto.specList.size()>0">
            and t2.spec in
            <foreach item="item" index="index" collection="dto.specList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- oneIdList -->
        <if test="dto.oneIdList != null and dto.oneIdList.size()>0">
            and t2.one_id in
            <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- shopId -->
        <if test="dto.shopId != null">
            and t1.shop_id = #{dto.shopId,jdbcType=INTEGER}
        </if>

        <choose>
            <when test="dto.shopUid != null and dto.shopUid != ''">
                inner join shop_user t4 on t4.del_flag = 0 and t4.id = t1.shop_id
                <!-- shopUid -->
                and t4.uid = #{dto.shopUid,jdbcType=VARCHAR}
            </when>
            <otherwise>
                left join shop_user t4 on t4.del_flag = 0 and t4.id = t1.shop_id
            </otherwise>
        </choose>

        <choose>
            <when test="dto.wareIdList != null and dto.wareIdList.size()>0">
                inner join sys_ware t5 on t5.del_flag = 0 and t5.id = t2.ware_id
                <!-- wareIdList -->
                and t5.id in
                <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                left join sys_ware t5 on t5.del_flag = 0 and t5.id = t2.ware_id
            </otherwise>
        </choose>

        <choose>
            <when test="dto.checker != null and dto.checker != ''">
                inner join sys_user t6 on t6.del_flag = 0 and t6.id = COALESCE(t1.staff_id, t1.create_by_id)
                <!-- checker -->
                and t6.nickname = #{dto.checker,jdbcType=VARCHAR}
            </when>
            <otherwise>
                left join sys_user t6 on t6.del_flag = 0 and t6.id = COALESCE(t1.staff_id, t1.create_by_id)
            </otherwise>
        </choose>

        <choose>
            <when test="dto.logNoList != null and dto.logNoList.size()>0">
                inner join shop_pack t3 on t3.del_flag = 0 and t3.in_id = t1.id
                <!--物流编号-->
                and t3.log_no in
                <foreach item="item" index="index" collection="dto.logNoList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                left join shop_pack t3 on t3.del_flag = 0 and t3.in_id = t1.id
            </otherwise>
        </choose>

        left join (
            select distinct shelves_prod.prod_id , shelves.name shelves_name from sys_ware_shelves shelves
            inner join sys_ware_shelves_prod shelves_prod  on  shelves.id = shelves_prod.shelves_id where shelves.del_flag = 0 and shelves_prod.del_flag = 0
        ) shelves_name on shelves_name.prod_id = t2.prod_id

    </sql>

    <!--入库批次管理-核心表-->
    <sql id="coreTable">
        inner join sys_ware_in_prod t2 FORCE INDEX (idx_ware_in_prod_composite)
        on t1.status = 3 and t1.del_flag = 0 and t2.del_flag = 0 and t1.id = t2.in_id
        <!--开始时间-->
        <if test=" dto.beginTime != null ">
            and t2.gmt_create &gt; #{dto.beginTime,jdbcType=TIMESTAMP}
        </if>
        <!--结束时间-->
        <if test="dto.endTime != null">
            and t2.gmt_create &lt; #{dto.endTime,jdbcType=TIMESTAMP}
        </if>
        <!--批次号-->
        <if test="dto.inBatchNo != null and dto.inBatchNo != ''">
            and t1.batch_no = #{dto.inBatchNo,jdbcType=VARCHAR}
        </if>
        <!--入库类型-->
        <if test="dto.type != null">
            and t1.type = #{dto.type,jdbcType=INTEGER}
        </if>
        <!--品名-->
        <if test="dto.remarks != null and dto.remarks != ''">
            and t2.remarks = #{dto.remarks,jdbcType=VARCHAR}
        </if>
        <!--skuList-->
        <if test="dto.skuList != null and dto.skuList.size()>0">
            and t2.sku in
            <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!--规格-->
        <if test="dto.specList != null and dto.specList.size()>0">
            and t2.spec in
            <foreach item="item" index="index" collection="dto.specList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- oneIdList -->
        <if test="dto.oneIdList != null and dto.oneIdList.size()>0">
            and t2.one_id in
            <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- shopId -->
        <if test="dto.shopId != null">
            and t1.shop_id = #{dto.shopId,jdbcType=INTEGER}
        </if>

        <if test="dto.shopUid != null and dto.shopUid != ''">
            inner join shop_user t4 on t4.del_flag = 0 and t4.id = t1.shop_id
            <!-- shopUid -->
            and t4.uid = #{dto.shopUid,jdbcType=VARCHAR}
        </if>

        <if test="dto.wareIdList != null and dto.wareIdList.size()>0">
            inner join sys_ware t5 on t5.del_flag = 0 and t5.id = t2.ware_id
            <!-- wareIdList -->
            and t5.id in
            <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="dto.checker != null and dto.checker != ''">
            inner join sys_user t6 on t6.del_flag = 0 and t6.id = COALESCE(t1.staff_id, t1.create_by_id)
            <!-- checker -->
            and t6.nickname = #{dto.checker,jdbcType=VARCHAR}
        </if>

        <if test="dto.logNoList != null and dto.logNoList.size()>0">
            inner join shop_pack t3 on t3.del_flag = 0 and t3.in_id = t1.id
            <!--物流编号-->
            and t3.log_no in
            <foreach item="item" index="index" collection="dto.logNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </sql>

    <!--入库批次管理-list 查询-->
    <sql id="selectList">
        t1.id,
        t1.batch_no in_batch_no,
        t1.type in_type,
        t3.log_no,
        t4.realname shop_name,
        t4.uid shop_uid,
        t5.name ware_name,
        t2.gmt_create,
        t6.nickname checker,
        t2.img img,
        t2.remarks remarks,
        t2.sku,
        t2.spec,
        t2.one_id,
        t2.check_result check_result,
        shelves_name.shelves_name
    </sql>

    <!--入库批次管理-排序-->
    <sql id="orderBy">
        order by t2.gmt_create desc
    </sql>

    <!-- 入库批次管理 导出查询 - CSV格式数据行 -->
    <select id="selectWareBatchInToExportCsvData" parameterType="com.hzjm.service.model.DTO.SysWareInProdPageDto"
            resultType="java.lang.String">

        SELECT
            CONCAT_WS(',',
                <include refid="csvDataColumns" />
            ) as csv_row
        FROM sys_ware_in t1
        <include refid="coreViewTable" />
        LEFT JOIN (
            SELECT in_id, COUNT(*) as cnt
            FROM sys_ware_in_prod
            WHERE del_flag = 0
            GROUP BY in_id
        ) batch_count ON t1.id = batch_count.in_id
        <include refid="orderBy" />

    </select>

    <!-- 入库批次管理 导出查询 - CSV格式数据行 -->
    <select id="selectWareBatchInToExportCsvData" parameterType="com.hzjm.service.model.DTO.SysWareInProdPageDto"
            resultType="java.lang.String">

        SELECT
            CONCAT_WS(',',
                <include refid="csvDataColumns" />
            ) as csv_row
        FROM sys_ware_in t1
        <include refid="coreTable" />
        LEFT JOIN (
            SELECT in_id, COUNT(*) as cnt
            FROM sys_ware_in_prod
            WHERE del_flag = 0
            GROUP BY in_id
        ) batch_count ON t1.id = batch_count.in_id
        <include refid="orderBy" />

    </select>

    <!-- CSV数据列 -->
    <sql id="csvDataColumns">
        IFNULL(t1.batch_no, ''),
        CASE t1.type WHEN 1 THEN 'SHIPPING' WHEN 2 THEN 'DROPOFF' WHEN 3 THEN 'RETURNED' ELSE '' END,
        IFNULL(batch_count.cnt, ''),
        IFNULL(t3.log_no, ''),
        IFNULL(t4.realname, ''),
        IFNULL(t4.uid, ''),
        IFNULL(t5.name, ''),
        IFNULL(DATE_FORMAT(CONVERT_TZ(t2.gmt_create, '+00:00', 'America/New_York'), '%Y-%m-%d %H:%i:%s'), ''),
        IFNULL(t6.nickname, ''),
        CONCAT('"', IFNULL(REPLACE(t2.remarks, '"', '""'), ''), '"'),
        IFNULL(t2.sku, ''),
        IFNULL(t2.spec, ''),
        IFNULL(t2.one_id, ''),
        <include refid="checkResultTranslation" />,
        IFNULL(shelves_name.shelves_name, '')
    </sql>

    <!-- 检查结果翻译 -->
    <sql id="checkResultTranslation">
        CASE t2.check_result
            <choose>
                <when test="language == 'zh-CN'">
                    WHEN 1 THEN '合格'
                    WHEN 2 THEN '鞋盒严重破损-S'
                    WHEN 3 THEN '球鞋有瑕疵'
                    WHEN 4 THEN '假货'
                    WHEN 5 THEN '鞋盒轻微破损-S'
                    WHEN 6 THEN '非原盒-S'
                    WHEN 7 THEN '无鞋盒盖-S'
                    WHEN 8 THEN '单只鞋'
                </when>
                <otherwise>
                    WHEN 1 THEN 'Qualified'
                    WHEN 2 THEN 'Box Badly Damaged-S'
                    WHEN 3 THEN 'Shoes Defects'
                    WHEN 4 THEN 'Fake'
                    WHEN 5 THEN 'Box Slightly Damaged-S'
                    WHEN 6 THEN 'No Original Box-S'
                    WHEN 7 THEN 'Missing Lid-S'
                    WHEN 8 THEN 'Missing Pair'
                </otherwise>
            </choose>
            ELSE ''
        END
    </sql>

</mapper>
