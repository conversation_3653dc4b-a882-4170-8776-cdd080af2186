<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.ShopUserMapper">
    <select id="selectUserPlatfromFee" resultType="com.hzjm.service.model.VO.SysUserPlatfromFeeVo">
        select t1.realname real_name
             , t1.uid
             , t1.account
             , max(t2.oc_fee)                                                          oc_fee
             , max(case when t2.plat_id = 11001 then t2.shipping_fee / 100 else 0 end) stock_x_shipping_fee
             , max(case when t2.plat_id = 11001 then t2.service_rate else 0 end)       stock_x_service_rate
             , max(case when t2.plat_id = 11001 then t2.draw_rate else 0 end)          stock_x_draw_rate

             , max(case when t2.plat_id = 11012 then t2.shipping_fee / 100 else 0 end) surge_shipping_fee
             , max(case when t2.plat_id = 11012 then t2.service_rate else 0 end)       surge_service_rate
             , max(case when t2.plat_id = 11012 then t2.draw_rate else 0 end)          surge_draw_rate

             , max(case when t2.plat_id = 11002 then t2.shipping_fee / 100 else 0 end) goat_shipping_fee
             , max(case when t2.plat_id = 11002 then t2.service_rate else 0 end)       goat_service_rate
             , max(case when t2.plat_id = 11002 then t2.draw_rate else 0 end)          goat_draw_rate

             , max(case when t2.plat_id = 11003 then t2.shipping_fee / 100 else 0 end) goat_is_shipping_fee
             , max(case when t2.plat_id = 11003 then t2.service_rate else 0 end)       goat_is_service_rate
             , max(case when t2.plat_id = 11003 then t2.draw_rate else 0 end)          goat_is_draw_rate

             , max(case when t2.plat_id = 11004 then t2.shipping_fee / 100 else 0 end) goat_stv_defect_shipping_fee
             , max(case when t2.plat_id = 11004 then t2.service_rate else 0 end)       goat_stv_defect_service_rate
             , max(case when t2.plat_id = 11004 then t2.draw_rate else 0 end)          goat_stv_defect_draw_rate

             , max(case when t2.plat_id = 11005 then t2.shipping_fee / 100 else 0 end) kc_shipping_fee
             , max(case when t2.plat_id = 11005 then t2.service_rate else 0 end)       kc_service_rate
             , max(case when t2.plat_id = 11005 then t2.draw_rate else 0 end)          kc_draw_rate

             , max(case when t2.plat_id = 11006 then t2.shipping_fee / 100 else 0 end) ebay_shipping_fee
             , max(case when t2.plat_id = 11006 then t2.service_rate else 0 end)       ebay_service_rate
             , max(case when t2.plat_id = 11006 then t2.draw_rate else 0 end)          ebay_draw_rate

             , max(case when t2.plat_id = 11007 then t2.shipping_fee / 100 else 0 end) poizon_shipping_fee
             , max(case when t2.plat_id = 11007 then t2.service_rate else 0 end)       poizon_service_rate
             , max(case when t2.plat_id = 11007 then t2.draw_rate else 0 end)          poizon_draw_rate

             , max(case when t2.plat_id = 11010 then t2.shipping_fee / 100 else 0 end) TTS_shipping_fee
             , max(case when t2.plat_id = 11010 then t2.service_rate else 0 end)       TTS_service_rate
             , max(case when t2.plat_id = 11010 then t2.draw_rate else 0 end)          TTS_draw_rate

            ,  CONVERT_TZ(t1.gmt_create, 'UTC', 'America/New_York') as gmt_create
        from shop_user t1
                 left join shop_user_plat t2 on t1.id = t2.shop_id
        where t1.del_flag = 0
          and t2.del_flag = 0
        group by t1.realname, t1.uid, t1.account
        order by t1.gmt_create
    </select>

</mapper>
