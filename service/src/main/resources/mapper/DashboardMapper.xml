<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.DashboardMapper">


    <!--使用日期查询入库数量-->
    <select id="selectInNum" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">

        SELECT date, count(shop_id) count
        from (SELECT left(CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}), 10) date
                   , shop_id
              FROM sys_prod_search
              WHERE del_flag = 0
                  AND search_type = 1
                AND CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
                AND CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}) &lt; #{dto.endDate}
                AND shop_id = #{dto.shopId}
                <if test="wareIds!=null and wareIds.size() > 0">
                    and ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
             ) tt
        GROUP BY date
        order by date

    </select>

    <!--使用日期查询出库数量-->
    <select id="selectOutNum" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">

        SELECT date, count(shop_id) count
        from (SELECT left(CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}), 10) date
                   , shop_id
              FROM sys_prod_search
              WHERE del_flag = 0
                AND search_type = 1
                AND CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
                AND CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}) &lt; #{dto.endDate}
                AND status IN ('6')
                AND shop_id = #{dto.shopId}
                <if test="wareIds!=null and wareIds.size() > 0">
                    and ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
             ) tt
        GROUP BY date
        order by date

    </select>

    <!--使用日期查询入库成本-->
    <select id="selectInCost" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">

        SELECT date, sum(cost_price) amount
        from (SELECT left(CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}), 10) date
                   , cost_price
              FROM sys_prod_search
              WHERE del_flag = 0
                AND search_type = 1
                AND CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
                AND CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}) &lt; #{dto.endDate}
                AND shop_id = #{dto.shopId}

                <if test="wareIds!=null and wareIds.size() > 0">
                    and ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

             ) tt
        GROUP BY date
        order by date

    </select>

    <!--使用日期查询出库成本-->
    <select id="selectOutCost" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">

        SELECT date, sum(cost_price) amount
        from (SELECT left(CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}), 10) date
                   , cost_price
              FROM sys_prod_search
              WHERE del_flag = 0
                AND search_type = 1
                AND CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
                AND CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}) &lt; #{dto.endDate}
                AND status IN ('6')
                AND shop_id = #{dto.shopId}

                <if test="wareIds!=null and wareIds.size() > 0">
                    and ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

             ) tt
        GROUP BY date
        order by date

    </select>

    <!--统计销售金额和数量-->
    <select id="selectSaleSumAmount" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDashboardDataVo">

        SELECT IFNULL(convert(sum(sale_price) / 100, decimal(12, 2)), 0) saleSumAmount,
               IFNULL(count(1), 0)                                       saleSoldCount
        from platform_order
        where shop_id = #{dto.shopId}
          and del_flag = 0
          and sale_price is not null

    </select>

    <!--统计 今日，昨日的销售量和增长比 since yesterday-->
    <select id="selectDaySaleSumAmount" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDashboardDataVo">

        SELECT IFNULL(convert(tt.todaySales / 100, decimal(12, 2)), 0)     AS daySaleSumAmount,
               IFNULL(convert(tt.yesterdaySales / 100, decimal(12, 2)), 0) AS yesterdaySales,
               IFNULL(IF(tt.yesterdaySales > 0, (todaySales - yesterdaySales) / yesterdaySales * 100, 0),
                      0)                                                   AS increasePercentage,
               IFNULL(tt.saleSoldCountDay, 0)                              AS saleSoldCountDay
        FROM (SELECT SUM(CASE
                             WHEN CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.timeZoneStr}
                                 THEN sale_price
                             ELSE 0 END) AS   todaySales,
                     SUM(CASE
                             WHEN CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;=
                                  DATE_SUB(#{dto.timeZoneStr}, INTERVAL 1 DAY)
                                 AND CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &lt; #{dto.timeZoneStr}
                                 THEN sale_price
                             ELSE 0 END) AS   yesterdaySales,
                     Count(CASE
                               WHEN CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.timeZoneStr}
                                   THEN 1
                               ELSE null END) saleSoldCountDay
              FROM platform_order
              WHERE shop_id = #{dto.shopId}
                AND del_flag = 0
                AND sale_price IS NOT NULL
                AND CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;=
                    DATE_SUB(#{dto.timeZoneStr}, INTERVAL 2 DAY)) tt

    </select>

    <!--统计 商家端的总利润 、 今日利润 、昨日利润 、增长比 -->
    <select id="selectDayProfitSumAmount" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDashboardDataVo">
        select IFNULL(convert(sum(profitSumAmount) / 100, decimal(12, 2)), 0)          profitSumAmount,
               IFNULL(convert(sum(dayProfitSumAmount) / 100, decimal(12, 2)), 0)       dayProfitSumAmount,
               IFNULL(convert(sum(yesterDayProfitSumAmount) / 100, decimal(12, 2)), 0) yesterDayProfitSumAmount,
               IFNULL(sum(profitDiffPercentage), 0)                                    profitDiffPercentage
        from (SELECT IFNULL(sum(t1.seller_owning - t2.cost_price * 100), 0) profitSumAmount
                   , 0                                                      dayProfitSumAmount
                   , 0                                                      yesterDayProfitSumAmount
                   , 0                                                      profitDiffPercentage
              from platform_order t1
                       left JOIN sys_prod t2 on t1.one_id = t2.one_id
              where t1.del_flag = 0
                and t2.del_flag = 0
                and t1.shop_id = #{dto.shopId}

              UNION ALL

              SELECT 0                                      profitSumAmount,
                     IFNULL(dayProfitSumAmount, 0)       AS dayProfitSumAmount,
                     IFNULL(yesterDayProfitSumAmount, 0) AS yesterDayProfitSumAmount,
                     IFNULL(IF(yesterDayProfitSumAmount > 0,
                               (dayProfitSumAmount - yesterDayProfitSumAmount) / yesterDayProfitSumAmount * 100, 0),
                            0)                           AS profitDiffPercentage
              FROM (SELECT SUM(CASE
                                   WHEN CONVERT_TZ(t1.gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;=
                                        #{dto.timeZoneStr} THEN t1.seller_owning - t2.cost_price * 100
                                   ELSE 0 END) AS dayProfitSumAmount,
                           SUM(CASE
                                   WHEN CONVERT_TZ(t1.gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;=
                                        DATE_SUB(#{dto.timeZoneStr}, INTERVAL 1 DAY)
                                       AND CONVERT_TZ(t1.gmt_create, '+00:00', #{dto.timeZoneValue}) &lt;
                                           #{dto.timeZoneStr} THEN t1.seller_owning - t2.cost_price * 100
                                   ELSE 0 END) AS yesterDayProfitSumAmount
                    FROM platform_order t1
                             INNER JOIN sys_prod t2 ON t1.one_id = t2.one_id
                    WHERE t1.shop_id = #{dto.shopId}
                      AND t1.del_flag = 0
                      AND t2.del_flag = 0
                      AND CONVERT_TZ(t1.gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;=
                          DATE_SUB(#{dto.timeZoneStr}, INTERVAL 2 DAY)) AS tt) ttt

    </select>

    <!-- 商家各个订单平台的分步 -->
    <select id="selectPlatformList" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesPlatformDataVo">

        SELECT
        if(account = 'STOCKX_FLEX', account,platform) platform
        , IFNULL(COUNT(1),0) count
        FROM platform_order
        where del_flag = 0
        and shop_id = #{dto.shopId}
        <if test=" dto.timeZoneStr != null ">
            and CONVERT_TZ( gmt_create, '+00:00', #{dto.timeZoneValue} ) &gt;= #{dto.timeZoneStr}
        </if>
        GROUP BY if(account = 'STOCKX_FLEX', account,platform)

    </select>


    <!-- 日销售额分组 -->
    <select id="selectSaleGroupByDay" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">

        SELECT left(CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}), 10) date
             , IFNULL(convert(sum(sale_price) / 100, decimal(12, 2)), 0)        amount
        from platform_order
        where shop_id = #{dto.shopId}
          and del_flag = 0
          and sale_price is not null
          AND CONVERT_TZ(gmt_create
                  , '+00:00'
                  , #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
          AND CONVERT_TZ(gmt_create
                  , '+00:00'
                  , #{dto.timeZoneValue})
            &lt; #{dto.endDate}
        GROUP BY date
        ORDER BY date

    </select>

    <!-- 日销售利润分组 -->
    <select id="selectProfitGroupByDay" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">

        SELECT left(CONVERT_TZ(t1.gmt_create, '+00:00', #{dto.timeZoneValue}), 10)                   date
             , IFNULL(convert(sum(t1.seller_Owning - t2.cost_price * 100) / 100, decimal(12, 2)), 0) amount
        from platform_order t1
                 inner JOIN sys_prod t2
                            on t1.one_id = t2.one_id
        where t1.del_flag = 0
          and t2.del_flag = 0
          and t1.shop_id = #{dto.shopId}
          AND CONVERT_TZ(t1.gmt_create
                  , '+00:00'
                  , #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
          AND CONVERT_TZ(t1.gmt_create
                  , '+00:00'
                  , #{dto.timeZoneValue})
            &lt; #{dto.endDate}
        group by date
        ORDER BY date

    </select>

    <!-- 日销售平台分组 -->
    <select id="selectProfitPlatformGroupByDay" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.SalesDayDataVo">
        SELECT         if(account = 'STOCKX_FLEX', account,platform) platform
             , left(CONVERT_TZ(gmt_create
                        , '+00:00'
                        , #{dto.timeZoneValue})
            , 10) date
             , IFNULL(COUNT(order_id)
            , 0)  count
        FROM platform_order
        where del_flag = 0
          and shop_id = #{dto.shopId}
          AND CONVERT_TZ(gmt_create
                  , '+00:00'
                  , #{dto.timeZoneValue}) &gt;= #{dto.beginDate}
          AND CONVERT_TZ(gmt_create
                  , '+00:00'
                  , #{dto.timeZoneValue})
            &lt; #{dto.endDate}
        GROUP BY if(account = 'STOCKX_FLEX', account,platform), date
        order by platform, date

    </select>


    <!--  三十天内卖出去鞋子最多的商家,前十  -->
    <select id="selectShopUserTop10" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.ShopUserListVo">

        SELECT distinct
        t2.head_img,
        t2.uid
        from (
        SELECT shop_id ,count(1) countNum
        from platform_order
        where del_flag = 0
        and shop_id is not null
        and shop_id not in (1,203)
        AND LEFT(gmt_create, 10) >= DATE_SUB(left (CONVERT_TZ(NOW(), '+00:00', '-05:00'),10), INTERVAL 31 DAY)
        group by shop_id
        order by count(shop_id) desc
        limit 10
        ) t1
        left join shop_user t2 on t2.id = t1.shop_id
        order by t1.countNum desc

    </select>

    <!--  三十天内卖出去最多的鞋子，前二十  -->
    <select id="selectProductTop20" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.PlatformOrderVo">

        SELECT distinct
        sku ,
        max(img) img,
        max(product_name) productName ,
        count(*) skuCount
        from platform_order
        where 1=1
        and del_flag = 0
        and sku is not null

        <if test="dto.shopId != null and  dto.shopId != ''">
            and shop_id = #{dto.shopId}
        </if>

        <!--beginDate-->
        <choose>
            <when test="dto.beginDate != null">
                and gmt_create &gt;= #{dto.beginDate}
            </when>
            <otherwise>
                AND LEFT(gmt_create, 10) &gt;= DATE_SUB(left (CONVERT_TZ(NOW(), '+00:00', '-05:00'),10), INTERVAL 31 DAY)
            </otherwise>
        </choose>

        <!--endDate-->
        <if test=" dto.endDate != null ">
            and gmt_create &lt;= #{dto.endDate}
        </if>

        group by sku order by count(sku) desc limit 20

    </select>

    <!--  商家有多少种SKU ,今日预报的包裹数量,今日的入库数量，今日的出库数量，今日的订单数量  -->
    <select id="selectSysProdDay" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.PlatformOrderVo">
        SELECT SUM(skuCount)    skuCount
             , SUM(packCount)   packCount
             , SUM(inDayCount)  inDayCount
             , SUM(outDayCount) outDayCount
             , SUM(orderCount)  orderCount
        from (SELECT count(DISTINCT t2.sku) skuCount, 0 packCount, 0 inDayCount, 0 outDayCount, 0 orderCount
              FROM sys_prod_search t1
                       left join sys_prod t2 on t1.prod_id = t2.id
              WHERE t1.del_flag = 0
                and t2.del_flag = 0
                AND t2.shop_id = #{dto.shopId}
                and t1.status in (1, 2, 3, 4, 7, 8, 9, 10,12)
                and t1.search_type = 1
                <if test="wareIds!=null and wareIds.size() > 0">
                    and t1.ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

              UNION ALL
              SELECT 0 skuCount, count(1) packCount, 0 inDayCount, 0 outDayCount, 0 orderCount
              FROM shop_pack
              WHERE del_flag = 0
                AND shop_id = #{dto.shopId}
                and CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.timeZoneStr}
                and note != 'STOCKX_FLEX'
              union ALL
              select 0 skuCount, 0 packCount, count(1) inDayCount, 0 outDayCount, 0 orderCount
              from sys_prod_search
              where del_flag = 0
                and CONVERT_TZ(gmt_in, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.timeZoneStr}
                and shop_id = #{dto.shopId}
                and search_type = 1
                <if test="wareIds!=null and wareIds.size() > 0">
                    and ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
              union ALL
              select 0 skuCount, 0 packCount, 0 inDayCount, count(1) outDayCount, 0 orderCount
              from sys_prod_search
              where del_flag = 0
                and status = '6'
                and CONVERT_TZ(gmt_out, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.timeZoneStr}
                and shop_id = #{dto.shopId}
                and search_type = 1
                <if test="wareIds!=null and wareIds.size() > 0">
                    and ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
              union ALL
              SELECT 0 skuCount, 0 packCount, 0 inDayCount, 0 outDayCount, count(1) orderCount
              from platform_order
              where del_flag = 0
                and CONVERT_TZ(gmt_create, '+00:00', #{dto.timeZoneValue}) &gt;= #{dto.timeZoneStr}
                and shop_id = #{dto.shopId}) tt


    </select>

    <!--  [合格] 商品总数量 ，合格商品总价值 ,[瑕疵] 商品总数量 ，合格商品总价值  -->
    <select id="selectSysProdCostSum" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.PlatformOrderVo">

        SELECT IFNULL(SUM(oneCount), 0)                   oneCount,
               IFNULL(SUM(sumCost), 0)                    sumCost,
               IFNULL(SUM(UnOneCount), 0)                 UnOneCount,
               IFNULL(SUM(UnSumCost), 0)                  UnSumCost,
               IFNULL(SUM(oneCount) + SUM(UnOneCount), 0) sumCost
        from (SELECT COUNT(DISTINCT t1.one_id) oneCount, SUM(t2.cost_price) sumCost, 0 UnOneCount, 0 UnSumCost
              FROM sys_prod_search t1
                       left join sys_prod t2 on t1.prod_id = t2.id
              WHERE t1.del_flag = 0
                and t2.del_flag = 0
                AND t1.check_result = '1'
                and t2.shop_id = #{dto.shopId}
                and t1.status in (1, 2, 3, 4, 7, 8, 9, 10,12)
                and t1.search_type = 1

                <if test="wareIds!=null and wareIds.size() > 0">
                    and t1.ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

              union all

              SELECT 0 oneCount, 0 sumCost, COUNT(DISTINCT t1.one_id) UnOneCount, SUM(t2.cost_price) UnSumCost
              FROM sys_prod_search t1
                       left join sys_prod t2 on t1.prod_id = t2.id
              WHERE t1.del_flag = 0
                and t2.del_flag = 0
                AND t1.check_result != '1'
                and t2.shop_id = #{dto.shopId}
                and t1.status in (1, 2, 3, 4, 7, 8, 9, 10,12)
                and t1.search_type = 1

                <if test="wareIds!=null and wareIds.size() > 0">
                    and t1.ware_id not in
                    <foreach collection="wareIds" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
        ) t1
    </select>


    <!--  [合格] 商家已上架数量  -->
    <select id="selectShelvesCount" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.PlatformOrderVo">

        SELECT IFNULL(COUNT(DISTINCT one_id), 0) shelvesCount
        from knet_product_listing
        where knet_listing_status in ('CREATED', 'UPDATED')
          and shoe_condition = 'NEW'
          and box_condition = 'GOOD_CONDITION'
          and shop_user_id = #{dto.shopId}

    </select>


    <!--  [瑕疵] 商家已上架数量  -->
    <select id="selectUnShelvesCount" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.PlatformOrderVo">

        SELECT IFNULL(COUNT(DISTINCT one_id), 0) unShelvesCount
        from knet_product_listing
        where knet_listing_status in ('CREATED', 'UPDATED')
          and NOT (shoe_condition = 'NEW' AND box_condition = 'GOOD_CONDITION')
          and shop_user_id = #{dto.shopId}

    </select>

    <!--自营数据查询-->
    <select id="querySelfOperatedData" parameterType="com.hzjm.common.model.TableDataSearchDto"
            resultType="com.hzjm.service.model.VO.AdminHomeSelfOperatedDataVo">
        SELECT
            b.realname realName,
            b.id shop_id,
            b.uid shop_uid,
            TRUNCATE(IFNULL( a.totalInventoryCost, 0 )*100,0)  AS totalInventoryCost,
            IFNULL( a.totalInventoryNum, 0 ) AS totalInventoryNum,
            truncate(IFNULL( a.gtTotalInventoryCost, 0 )*100,0) AS gtTotalInventoryCost,
            IFNULL( a.gtTotalInventoryNum, 0 ) AS gtTotalInventoryNum,
            truncate(IFNULL( a.defectCost, 0 )*100,0) AS defectCost,
            IFNULL( a.defectNum, 0 ) AS defectNum
        FROM
        (
            SELECT
            shop_id,
            IFNULL( SUM( cost_price ), 0 ) AS totalInventoryCost,
            IFNULL( COUNT( one_id ), 0 ) AS totalInventoryNum,
            IFNULL( SUM( CASE WHEN gmt_in &lt; DATE_SUB( CURDATE( ), INTERVAL 120 DAY ) THEN cost_price ELSE 0 END ), 0 ) AS gtTotalInventoryCost,
            IFNULL( COUNT( CASE WHEN gmt_in &lt; DATE_SUB( CURDATE( ), INTERVAL 120 DAY ) THEN one_id END ), 0 ) AS gtTotalInventoryNum,
            IFNULL( SUM( CASE WHEN check_result != 1 THEN cost_price ELSE 0 END ), 0 ) AS defectCost,
            IFNULL( COUNT( CASE WHEN check_result != 1 THEN one_id END ), 0 ) AS defectNum
            FROM
            sys_prod_search
            WHERE del_flag = 0
            and search_type = 1
            AND STATUS in (1, 2, 3, 4, 7, 8, 9, 10,12)
            <if test="dto.wareIdList!=null and dto.wareIdList.size() > 0">
                and ware_id not in
                <foreach collection="dto.wareIdList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.shopUidList != null and dto.shopUidList.size() > 0">
                AND shop_uid IN
                <foreach item="shopId" collection="dto.shopUidList" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>

            <if test="dto.shopIdList != null and dto.shopIdList.size() > 0">
                AND shop_id IN
                <foreach item="shopId" collection="dto.shopIdList" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
            GROUP BY shop_id
        ) a LEFT JOIN shop_user b  ON b.id = a.shop_id
        WHERE b.del_flag = 0

        <if test="dto.shopUidList != null and dto.shopUidList.size() > 0">
            and b.uid IN
            <foreach item="shopId" collection="dto.shopUidList" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>

        <if test="dto.shopIdList != null and dto.shopIdList.size() > 0">
            AND b.id IN
            <foreach item="shopId" collection="dto.shopIdList" open="(" separator="," close=")">
                #{shopId}
            </foreach>
        </if>
    </select>

    <!--查询商家排行榜-->
    <select id="queryShopUserRank" parameterType="com.hzjm.service.model.DTO.TaskRecapReqDto"
            resultType="com.hzjm.service.model.VO.ShopUserRankVo">
        SELECT shop_id,
        COUNT(1) AS countNum
        FROM
        platform_order
        WHERE
        del_flag = 0
        AND shop_id IS NOT NULL
        <if test="dto.startDate != null">
            AND left (CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;= left
            (#{dto.startDate,jdbcType=TIMESTAMP}, 10)
            AND left (CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left
            (#{dto.endDate,jdbcType=TIMESTAMP}, 10)
        </if>
        GROUP BY
        shop_id
        ORDER BY
        countNum
        DESC
    </select>

    <!--查询商家年度前n名的热门商品-->
    <select id="queryShopProductTop" resultType="com.hzjm.service.model.VO.PlatformOrderVo">
        SELECT DISTINCT sku,
        shop_id shopUid,
        max(img) img,
        max(product_name) productName,
        count(sku) total
        FROM platform_order
        WHERE del_flag = 0
        AND sku IS NOT NULL
        <if test="year != null">
            AND YEAR (gmt_create) = #{year.value}
        </if>
        <if test="shopId != null">
            AND shop_id = #{shopId}
        </if>
        GROUP BY sku
        ORDER BY count(sku) DESC
        <if test="top != null">
            LIMIT #{top}
        </if>
    </select>

    <select id="selectShopHomeOrderTotalGmvbyPlatform" resultType="com.hzjm.service.model.VO.ShopHomeOrderPlatformVo">
        select if(account = 'STOCKX_FLEX', account,platform) platform
        , ifnull(convert(sum(sale_price)/100, decimal(12, 2)), 0) totalGMV
        , count(*) totalCount
        from platform_order
        where del_flag = 0
          and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &gt;= left(#{beginTime,jdbcType=TIMESTAMP},10)
          and left(CONVERT_TZ(gmt_create, '+00:00', 'America/New_York'), 10) &lt;= left(#{endTime,jdbcType=TIMESTAMP},10)

            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>

        group by if(account = 'STOCKX_FLEX', account,platform)

    </select>
    <!-- 抖音采购机会 -->
    <select id="queryTiktokSourcingOpportunities"
            parameterType="com.hzjm.service.model.DTO.req.SourcingOpportunitiesReq"
            resultType="com.hzjm.service.model.DTO.SourcingOpportunitiesDto">
        SELECT
        skus.sku,
        s.sku AS fullSku,
        s.remarks AS productName,
        s.img AS img,
        COALESCE( alls.last7DaysSoldTotal, 0 ) AS last7DaysSoldTotal,
        COALESCE( alls.allDaysSoldTotal, 0 ) AS allDaysSoldTotal,
        COALESCE( listings.listedTotal, 0 ) AS listedTotal
        FROM
        (
            SELECT
            kp.sku,
            MAX( kp.NAME ) AS productName,
            MAX( kp.img ) AS img
            FROM
            `knet_product` kp
            WHERE
            kp.platform = 'TIKTOK_SHOP'
            AND kp.del_flag = 0
            <if test="request.sku != null and request.sku != ''">
                AND kp.sku LIKE CONCAT(#{request.sku}, '%')
            </if>
            GROUP BY
            sku
        ) skus
        LEFT JOIN (
            SELECT
            p.sku,
            p.sku_indexed,
            COUNT( CASE WHEN p.gmt_create >= NOW() - INTERVAL 7 DAY THEN p.sku  END) AS last7DaysSoldTotal,
            COUNT( p.sku ) AS allDaysSoldTotal
            FROM
            `platform_order` p
            WHERE
            p.del_flag = 0
            AND p.account = 'TIKTOK_SHOP'
            GROUP BY
            p.sku
        ) alls ON skus.sku = alls.sku_indexed
        LEFT JOIN (
            SELECT
            k.sku,
            k.sku_indexed,
            COUNT( k.one_id ) AS listedTotal
            FROM
            `knet_product_listing` k
            WHERE
            k.tts_status = 'OPEN'
            AND k.tts_listing_status IN ( 'ACTIVE', 'PENDING')
            GROUP BY
            k.sku
        ) listings ON skus.sku = listings.sku_indexed
        LEFT JOIN(
            SELECT s.sku,
            s.sku_indexed,
            s.img,
            s.remarks
            FROM
            sys_sku s
            WHERE
            s.del_flag = 0
            GROUP BY
            s.sku_indexed
        ) s ON skus.sku = s.sku_indexed
        <choose>
            <when test="request.bestSeller != null">
                <choose>
                    <when test="request.bestSeller.name() == 'LAST_7_DAYS'">
                        ORDER BY last7DaysSoldTotal DESC, skus.sku
                    </when>
                    <when test="request.bestSeller.name() == 'ALL_TIME'">
                        ORDER BY allDaysSoldTotal DESC, skus.sku
                    </when>
                </choose>
            </when>
        </choose>
        <!-- 计算分页偏移量 -->
        <bind name="offset" value="(request.pageNo - 1) * request.pageSize"/>
        LIMIT #{offset}, #{request.pageSize}
    </select>

    <!-- 抖音采购机会 手动分页 -->
    <select id="queryTiktokSourcingOpportunitiesCount"
            parameterType="com.hzjm.service.model.DTO.req.SourcingOpportunitiesReq"
            resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT sku)
        FROM knet_product
        WHERE platform = 'TIKTOK_SHOP'
        AND del_flag = 0
        <if test="request.sku != null and request.sku != ''">
            AND sku LIKE CONCAT(#{request.sku}, '%')
        </if>
    </select>
    <!-- 抖音采购机会 详情 -->
    <select id="queryTiktokSourcingOpportunitiesDetails"
            parameterType="com.hzjm.service.model.DTO.req.SourcingOpportunitiesDetailReq"
            resultType="com.hzjm.service.model.VO.SourcingOpportunitiesDetailVo">
        SELECT
            k.size AS spec,
            COUNT( 1 ) AS qty
        FROM
            `knet_product_listing` k
        <where>
            k.tts_status = 'OPEN'
          AND k.tts_listing_status IN ( 'ACTIVE', 'PENDING'  )
            <if test="request.sku != null and request.sku != ''">
                AND k.sku = #{request.sku}
            </if>
        </where>
        GROUP BY
            k.size
    </select>

    <!-- 根据SKU列表查询TikTok价格 -->
    <select id="queryTiktokPrices" resultType="java.util.Map">
        SELECT
            kp.sku,
            MAX(kp.lowest_ask_price) AS lowestPrice,
            MAX(kp.highest_ask_price) AS  maxPrice
        FROM
            `knet_product` kp
        WHERE
            kp.platform = 'TIKTOK_SHOP'
            AND kp.sku IN
            <foreach collection="skuList" item="sku" open="(" separator="," close=")">
                #{sku}
            </foreach>
            AND kp.del_flag = 0
        GROUP BY
            kp.sku
    </select>
</mapper>
