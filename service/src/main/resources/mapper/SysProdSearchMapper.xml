<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysProdSearchMapper">
    <!-- 库存列表数量查询 -->
    <select id="selectSysProdSearchVOListNum" parameterType="com.hzjm.service.model.DTO.SysProdPageDto"
            resultType="java.lang.Long">
        select count(1)
        from sys_prod_search t1
        <if test="dto.pku !=null and dto.pku != ''">
            inner JOIN sys_prod  ON sys_prod.id = t1.prod_id and sys_prod.del_flag = 0 and sys_prod.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        where 1=1
        and t1.del_flag = 0
        <include refid="whereSql"/>

    </select>

    <!-- 库存列表 -->
    <select id="selectSysProdSearchVOList" parameterType="com.hzjm.service.model.DTO.SysProdPageDto"
            resultType="com.hzjm.service.model.VO.SysProdListVo">

        SELECT distinct
        <include refid="columns"/>
        from (
        select t1.*
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
        LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner JOIN sys_prod  ON sys_prod.id = t1.prod_id and sys_prod.del_flag = 0 and sys_prod.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where 1=1
        and t1.del_flag = 0
        <include refid="whereSql"/>

        ORDER BY
        <include refid="orderBY"/>

        <if test=" dto.limitSize!=null and dto.limitSize>0">
            limit #{dto.currSize} , #{dto.limitSize}
        </if>

        ) t1
        left join sys_prod t2 on t2.id = t1.prod_id and t2.del_flag = 0
        left join sys_ware t3 on t3.id = t1.ware_id and t3.del_flag = 0
        left join sys_ware_shelves t4 on t4.id = t1.shelves_id and t4.del_flag = 0
        left join sys_prod_deal t5 on t5.id = t1.deal_id and t5.del_flag = 0
        left join sys_prod_sale t6 on t6.id = t5.sale_id and t6.del_flag = 0
        left join sys_ware_in_prod t7 on t7.prod_id = t1.prod_id and t7.del_flag = 0
        left join shop_pack t8 on t8.id = t7.pack_id and t8.del_flag = 0
        left join sys_ware_shelves_prod t9 on t9.prod_id = t2.id and t9.del_flag = 0
        left join sys_repair_order t10 on t10.product_id = t1.prod_id and t10.del_flag = 0

        where 1=1

        ORDER BY
        <include refid="orderBY"/>

    </select>

    <!-- 按照商品的仓库+货架搜索商品 -->
    <select id="selectProductForWare" parameterType="com.hzjm.service.model.DTO.SysProdPageDto"
            resultType="com.hzjm.service.model.VO.SysProdListVo">

        SELECT distinct
        <include refid="columns"/>
        from (
        select t1.*
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner JOIN sys_prod  ON sys_prod.id = t1.prod_id and sys_prod.del_flag = 0 and sys_prod.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        left join sys_ware t3 on t3.id = t1.ware_id and t3.del_flag = 0
        left join sys_ware_shelves t4 on t4.id = t1.shelves_id and t4.del_flag = 0
        where 1=1
        and t1.del_flag = 0
        <!-- 按照商品的仓库+货架搜索商品 -->
        <if test="dto.wareNameAndShelvesName != null and dto.wareNameAndShelvesName.size() > 0 ">
            and concat(ifnull(t3.Name,'已出库') , ifnull(t4.name,'未上架')) IN
            <foreach item="item" index="index" collection="dto.wareNameAndShelvesName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <include refid="whereSql"/>

        order by
        <include refid="orderBY"/>

        ) t1
        left join sys_prod t2 on t2.id = t1.prod_id and t2.del_flag = 0
        left join sys_ware t3 on t3.id = t1.ware_id and t3.del_flag = 0
        left join sys_ware_shelves t4 on t4.id = t1.shelves_id and t4.del_flag = 0
        left join sys_prod_deal t5 on t5.id = t1.deal_id and t5.del_flag = 0
        left join sys_prod_sale t6 on t6.id = t5.sale_id and t6.del_flag = 0
        left join sys_ware_in_prod t7 on t7.prod_id = t1.prod_id and t7.del_flag = 0
        left join shop_pack t8 on t8.id = t7.pack_id and t8.del_flag = 0

        order by
        <include refid="orderBY"/>

    </select>

    <!-- 按品名分类，内部的商品嵌套查询 -->
    <select id="selectGroupBySkuForProduct" parameterType="com.hzjm.service.model.DTO.SysProdPageDto"
            resultType="com.hzjm.service.model.VO.SysProdListVo">

        <include refid="selectGroupBySkuForProductSql"/>

    </select>

    <!-- 按品名分类，内部的商品嵌套查询的统计 -->
    <select id="selectGroupBySkuForProductCount" parameterType="com.hzjm.service.model.DTO.SysProdPageDto"
            resultType="com.hzjm.service.model.VO.SysProdListVo">

        select t2.spec, COUNT(t2.spec) AS size ,ROUND(AVG(t2.cost_price),2) AS cost_price
        FROM sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        inner join sys_prod t2 on t1.prod_id = t2.id

        <if test="dto.wareNameAndShelvesName != null and dto.wareNameAndShelvesName.size() > 0 ">
            left join sys_ware t3 on t1.ware_id = t3.id
            left join sys_ware_shelves t4 on t4.id = t1.shelves_id and t4.del_flag = 0
        </if>

        WHERE t2.del_flag = 0

        <if test="dto.pku !=null and dto.pku != ''">
            and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>

        <!-- 按照商品的仓库+货架搜索商品 -->
        <if test="dto.wareNameAndShelvesName != null and dto.wareNameAndShelvesName.size() > 0 ">
            and concat(ifnull(t3.Name,'已出库') , ifnull(t4.name,'未上架')) IN
            <foreach item="item" index="index" collection="dto.wareNameAndShelvesName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <include refid="whereSql"/>

        GROUP BY t2.spec
        ORDER BY
        IF(t2.spec REGEXP '^[0-9]*\\.?[0-9]+$', CAST(t2.spec AS DECIMAL(10, 2)), NULL),
        IF(t2.spec REGEXP '^[0-9]*\\.?[0-9]+$', NULL, t2.spec)

    </select>

    <!-- 库存列表按照sku分组查询 -->
    <select id="selectSysProdSearchVOListGroupBySku" parameterType="java.util.Map"
            resultType="com.hzjm.service.model.VO.SysProdGroupListVo">

        SELECT distinct
        <include refid="columnsGroupBySku"/>
        from (
        select
        t1.sku,
        t1.spec
        ,max(t1.brand) brand
        ,max(t1.remarks) remarks
        ,max(t1.gmt_in) gmt_in
        ,max(t1.gmt_out) gmt_out
        ,max(t1.id) id
        , ifnull(COUNT(t1.prod_id),0) AS num
        , ifnull(SUM(t1.cost_price),0) AS sumCost
        , ifnull(SUM(CASE WHEN t1.check_result = 1 THEN 1 ELSE 0 END),0) AS intactNum
        , ifnull(SUM(CASE WHEN t1.check_result != 1 THEN 1 ELSE 0 END),0) AS brokenNum
        , ifnull(AVG(t1.cost_price),0) AS avgCost
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner join sys_prod t2 on t2.sku = t1.sku and t2.del_flag = 0 and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where 1=1
        and t1.del_flag = 0

        <include refid="whereSql"/>

        group by t1.sku

        ORDER BY
        <include refid="orderByGroupBy"/>

        <if test=" dto.limitSize!=null and dto.limitSize>0">
            limit #{dto.currSize} , #{dto.limitSize}
        </if>

        ) t1 left join sys_prod t2 on t2.sku = t1.sku and t2.del_flag = 0

        group by t1.sku

        ORDER BY
        <include refid="orderByGroupBy"/>

    </select>

    <!-- 库存列表按照sku分组查询  数量-->
    <select id="selectSysProdSearchVOListGroupBySkuCount" parameterType="java.util.Map" resultType="java.lang.Long">
        SELECT
        count(1)
        from (
        select
        count(1)
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner join sys_prod t2 on t2.sku = t1.sku and t2.del_flag = 0 and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where 1=1
        and t1.del_flag = 0

        <include refid="whereSql"/>

        group by t1.sku
        ) tt

    </select>

    <!-- 库存列表进行统计数量 -->
    <select id="selectSearchCount" resultType="com.hzjm.service.model.VO.SysProdCountV2Vo">
        select max(tt.wareNum)                    wareNum
             , max(tt.wareNum)                    totalInventoryNum
             , max(tt.outNum)                     outNum
             , max(tt.wareCost)                   wareCost
             , max(tt.outCost)                    outCost
             , max(tt.wareCost) + max(tt.outCost) allCost
             , max(tt.brokenNum)                  brokenNum
        from (select count(1)                                              wareNum,
        0                                                     outNum,
        sum(cost_price)                                       wareCost,
        0                                                     outCost,
        SUM(CASE WHEN t1.check_result != 1 THEN 1 ELSE 0 END) brokenNum
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner join sys_prod t2 on t2.sku = t1.sku and t2.del_flag = 0 and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where t1.del_flag = 0
        AND t1.status IN (1, 2, 3, 4, 7, 9, 10 , 12)
        <include refid="whereSql"/>

        union all

        select 0 wareNum, count(1) outNum, 0 wareCost, sum(cost_price) outCost, 0 brokenNum
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner join sys_prod t2 on t2.sku = t1.sku and t2.del_flag = 0 and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where t1.del_flag = 0
        AND t1.status IN (6)
        <include refid="whereSql"/>
        ) tt
    </select>

    <!--查询商品的列表 -->
    <sql id="selectGroupBySkuForProductSql">
        select distinct
        <include refid="sysProdListVoColumns"/>

        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        left join sys_ware t2 on t1.ware_id = t2.id
        left join shop_user t3 on t1.shop_id = t3.id
        left join sys_ware_shelves t4 on t4.id = t1.shelves_id and t4.del_flag = 0
        left join sys_prod t5 on t1.prod_id = t5.id
        where t1.del_flag = 0

        <!-- 按照商品的仓库+货架搜索商品 -->
        <if test="dto.wareNameAndShelvesName != null and dto.wareNameAndShelvesName.size() > 0 ">
            and concat(ifnull(t2.Name,'已出库') , ifnull(t4.name,'未上架')) IN
            <foreach item="item" index="index" collection="dto.wareNameAndShelvesName" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            and t5.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>

        <include refid="whereSql"/>

        ORDER BY
        IF(t5.spec REGEXP '^[0-9]*\\.?[0-9]+$', CAST(t5.spec AS DECIMAL(10, 2)), NULL),
        IF(t5.spec REGEXP '^[0-9]*\\.?[0-9]+$', NULL, t5.spec)
    </sql>

    <select id="selectGroupBySkuForProductPage" resultType="com.hzjm.service.model.VO.SysProdListVo">
        SELECT
            t.*
        FROM (
            <include refid="selectGroupBySkuForProductSql"/>
        ) t
    </select>

    <!-- 库存列表按照仓库分组查询 -->
    <select id="selectSysProdSearchVOListGroupByWare" parameterType="java.util.Map"
            resultType="com.hzjm.service.model.VO.SysProdGroupListVo">

        SELECT distinct
        <include refid="columnsGroupByWare"/>
        from (
        select
        t1.ware_id
        ,t1.spec
        ,t1.shelves_id
        ,max(t1.gmt_in) gmt_in
        ,max(t1.gmt_out) gmt_out
        ,max(t1.id) id
        , ifnull(COUNT(t1.prod_id),0) AS num
        , ifnull(SUM(t1.cost_price),0) AS sumCost
        , ifnull(SUM(CASE WHEN t1.check_result = 1 THEN 1 ELSE 0 END),0) AS intactNum
        , ifnull(SUM(CASE WHEN t1.check_result != 1 THEN 1 ELSE 0 END),0) AS brokenNum
        , ifnull(AVG(t1.cost_price),0) AS avgCost
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner join sys_prod t2 on t2.sku = t1.sku and t2.del_flag = 0 and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where 1=1
        and t1.del_flag = 0

        <include refid="whereSql"/>

        group by t1.ware_id ,t1.shelves_id

        ORDER BY
        <include refid="orderByGroupBy"/>

        <if test=" dto.limitSize!=null and dto.limitSize>0">
            limit #{dto.currSize} , #{dto.limitSize}
        </if>

        ) t1
        left join sys_ware t3 on t3.id = t1.ware_id and t3.del_flag = 0
        left join sys_ware_shelves t4 on t4.id = t1.shelves_id and t4.del_flag = 0
        group by t3.name , t4.name

        ORDER BY
        <include refid="orderByGroupBy"/>

    </select>
    <!-- 库存列表按照仓库分组查询 数量 -->
    <select id="selectSysProdSearchVOListGroupByWareCount" parameterType="java.util.Map" resultType="java.lang.Long">

        SELECT
        count(1)
        from (
        select
        count(1)
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        <if test="dto.pku !=null and dto.pku != ''">
            inner join sys_prod  on sys_prod.id = t1.prod_id and sys_prod.del_flag = 0 and sys_prod.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>
        where 1=1
        and t1.del_flag = 0

        <include refid="whereSql"/>

        group by t1.ware_id ,t1.shelves_id

        ) tt

    </select>

    <!-- 库存列表 这里是按照sku和size分组进行查询 -->
    <select id="selectSysProdSearchVOListGroupBySkuAndSize" parameterType="com.hzjm.service.model.DTO.SysProdPageDto"
            resultType="com.hzjm.service.model.VO.SysProdListVo">

        SELECT distinct
        <include refid="columnsGroupBySkuAndSize"/>
        from (
        select t1.sku ,t2.spec ,t1.remarks
        from sys_prod_search t1
        <if test="dto.inspectorId != null">
            LEFT JOIN sys_ware_in_prod p ON t1.prod_id = p.prod_id and p.del_flag=0
        </if>
        left join sys_prod t2 on t1.prod_id = t2.id and t2.del_flag = 0
        where 1=1

        and t1.del_flag = 0
        <if test="dto.pku !=null and dto.pku != ''">
            and t2.pku = #{dto.pku,jdbcType=VARCHAR}
        </if>

        <include refid="whereSql"/>

        ORDER BY
        <include refid="orderBY"/>

        ) t1

        where 1=1

        group by sku , spec
        order by sku , spec
    </select>


    <sql id="whereSql">

        <!-- 统一不查询 stock flex  wareId = 20034-->
        and t1.ware_id != 20034

        <if test="dto.shopId != null and  dto.shopId != ''">
            AND t1.shop_id = #{dto.shopId}
        </if>
        <if test="dto.status != null and  dto.status != ''">
            AND t1.status = #{dto.status}
        </if>
        <if test="dto.statusList != null and dto.statusList.size() > 0">
            AND t1.status IN
            <foreach item="item" index="index" collection="dto.statusList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.statusList2 != null and dto.statusList2.size() > 0">
            AND t1.status IN
            <foreach item="item" index="index" collection="dto.statusList2" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.oneIdEq != null and  dto.oneIdEq != ''">
            AND t1.one_id = #{dto.oneIdEq}
        </if>
        <if test="dto.skuEq != null and  dto.skuEq != ''">
            AND t1.sku = #{dto.skuEq}
        </if>
        <if test="dto.oneId != null and  dto.oneId != ''">
            AND t1.one_id LIKE CONCAT('%', #{dto.oneId}, '%')
        </if>
        <if test="dto.supply != null and  dto.supply != ''">
            AND t1.supply LIKE CONCAT('%', #{dto.supply}, '%')
        </if>
        <if test="dto.skuList != null and dto.skuList.size() > 0">
            AND t1.sku IN
            <foreach item="item" index="index" collection="dto.skuList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.spec != null and  dto.spec != ''">
            AND t1.spec = #{dto.spec}
        </if>
        <if test="dto.specSearchList != null and dto.specSearchList.size() > 0">
            AND t1.spec IN
            <foreach item="item" index="index" collection="dto.specSearchList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.brand != null and  dto.brand != ''">
            AND t1.brand LIKE CONCAT('%', #{dto.brand}, '%')
        </if>
        <if test="dto.costPrice != null and  dto.costPrice != ''">
            AND t1.cost_price = #{dto.costPrice}
        </if>
        <if test="dto.costPriceMin != null and  dto.costPriceMin != ''">
            AND IFNULL(t1.cost_price, 0) &gt;= #{dto.costPriceMin}
        </if>
        <if test="dto.costPriceMax != null and  dto.costPriceMax != ''">
            AND IFNULL(t1.cost_price, 0) &lt;= #{dto.costPriceMax}
        </if>
        <if test="dto.shopName != null and  dto.shopName != ''">
            AND t1.shop_name LIKE CONCAT('%', #{dto.shopName}, '%')
        </if>
        <if test="dto.transferStatus != null and  dto.transferStatus != ''">
            AND t1.transfer_status = #{dto.transferStatus}
        </if>
        <if test="dto.wareIdList != null and dto.wareIdList.size() > 0">
            AND t1.ware_id IN
            <foreach item="item" index="index" collection="dto.wareIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.wareId != null and  dto.wareId != ''">
            AND t1.ware_id = #{dto.wareId}
        </if>
        <if test="dto.checkResult != null and  dto.checkResult != ''">
            AND t1.check_result = #{dto.checkResult}
        </if>
        <if test="dto.checkResultList != null and dto.checkResultList.size() > 0">
            AND t1.check_result IN
            <foreach item="item" index="index" collection="dto.checkResultList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.type != null and  dto.type != ''">
            AND t1.odd_type = #{dto.type}
        </if>
        <if test="dto.thirdPlatId != null and  dto.thirdPlatId != ''">
            AND t1.third_plat_id = #{dto.thirdPlatId}
        </if>
        <if test="dto.platNameEqual != null and  dto.platNameEqual != ''">
            AND t1.third_plat_name = #{dto.platNameEqual}
        </if>
        <if test="dto.platName != null and  dto.platName != ''">
            AND t1.third_plat_name LIKE CONCAT('%', #{dto.platName}, '%')
        </if>
        <if test="dto.platOrderNo != null and  dto.platOrderNo != ''">
            AND t1.plat_order_no LIKE CONCAT('%', #{dto.platOrderNo}, '%')
        </if>
        <if test="dto.oddNo != null and  dto.oddNo != ''">
            AND t1.odd_no LIKE CONCAT('%', #{dto.oddNo}, '%')
        </if>
        <if test="dto.beginTime != null ">
            AND t1.gmt_in &gt;= #{dto.beginTime}
        </if>
        <if test="dto.endTime != null ">
            AND t1.gmt_in &lt; #{dto.endTime}
        </if>
        <if test="dto.outBeginTime != null ">
            AND t1.gmt_out &gt;= #{dto.outBeginTime}
        </if>
        <if test="dto.outEndTimeDate != null ">
            AND t1.gmt_out &lt; #{dto.outEndTimeDate}
        </if>

        <!-- SKU -->
        <if test="dto.sku != null  and  dto.sku.trim() != ''">
            <bind name="trimmedSku" value="dto.sku.trim()"/>
            AND (
            t1.remarks LIKE CONCAT('%', #{trimmedSku}, '%')
            OR t1.one_id LIKE CONCAT('%', #{trimmedSku}, '%')
            OR t1.in_log_no LIKE CONCAT('%', #{trimmedSku}, '%')
            <!-- searchSkuList 模糊搜索sku-->
            <if test="dto.searchSkuList!=null and dto.searchSkuList.size() > 0">
                <foreach collection="dto.searchSkuList" item="item">
                    OR t1.sku LIKE CONCAT('%', #{item}, '%')
                </foreach>
            </if>
            )
        </if>

        <!-- 查询可维修 -->
        <if test=" dto.repairFlag != null and dto.repairFlag.code == 'CAN_REPAIR' ">
            and  t1.repair_flag = 'CAN_REPAIR'
            and t1.status = '1'
            and t1.check_result != '1'
        </if>

        <!-- oneId批量搜索 -->
        <if test="dto.oneIdList != null and dto.oneIdList.size() > 0">
            AND t1.one_id IN
            <foreach item="item" index="index" collection="dto.oneIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- platOrderNoList 批量搜索 -->
        <if test="dto.platOrderNoList != null and dto.platOrderNoList.size() > 0">
            AND t1.plat_order_no IN
            <foreach item="item" index="index" collection="dto.platOrderNoList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- pku/sku -->
        <if test="dto.kuSearch != null  and  dto.kuSearch.trim() != ''">
            AND (t1.sku LIKE CONCAT('%', #{dto.kuSearch}, '%') OR t1.pku LIKE CONCAT('%', #{dto.kuSearch}, '%'))
        </if>

        <!-- 识别码 -->
        <if test="dto.shopUid != null  and  dto.shopUid.trim() != ''">
            <if test="dto.shopUid == '无主件' or dto.shopUid == 'Unknown' or dto.shopUid == 'unknown'">
                AND t1.shop_id IS NULL
            </if>
            <if test="dto.shopUid != '无主件' and dto.shopUid != 'Unknown' and dto.shopUid != 'unknown'">
                AND t1.shop_uid LIKE CONCAT('%', #{dto.shopUid}, '%')
            </if>

        </if>
        <!-- uid 列表查询 -->
        <if test="dto.shopUidList != null and dto.shopUidList.size() > 0">
            AND ( t1.shop_uid IN
            <foreach item="item" index="index" collection="dto.shopUidList" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="dto.shopIDEmpty">
                or t1.shop_id IS NULL
            </if>
            )
        </if>

        <if test="dto.shelvesIdList != null and dto.shelvesIdList.size() > 0">
            AND (
            t1.shelves_id IN
            <foreach item="item" index="index" collection="dto.shelvesIdList" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="dto.shelvesIdList.contains(-2)">
                OR t1.shelves_id IS NULL
            </if>
            )
        </if>

        <if test="dto.logNoList != null and dto.logNoList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="dto.logNoList" open="(" separator=" OR " close=")">
                t1.in_log_no LIKE CONCAT('%', #{item}, '%')
            </foreach>
            )
        </if>

        <if test="dto.checkRemark != null  and  dto.checkRemark.trim() != ''">
            AND t1.check_remark LIKE CONCAT('%', #{dto.checkRemark}, '%')
        </if>

        <if test="dto.logNoRelatedList != null and dto.logNoRelatedList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="dto.logNoRelatedList" open="(" separator=" OR " close=")">
                t1.in_log_no_related LIKE CONCAT('%', #{item}, '%')
            </foreach>
            )
        </if>

        <if test="dto.inBatchNoList != null and dto.inBatchNoList.size() > 0">
            AND (
            <foreach item="item" index="index" collection="dto.inBatchNoList" open="(" separator=" OR " close=")">
                t1.in_batch_no LIKE CONCAT('%', #{item}, '%')
            </foreach>
            )
        </if>

        <!-- 管理员商家权限 -->
        <if test="dto.shopIdPowerList != null and dto.shopIdPowerList.size() > 0">
            AND t1.shop_id IN
            <foreach item="item" index="index" collection="dto.shopIdPowerList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <!-- 身份判断 -->
        <if test="dto.roleType != null and dto.roleType == 1">
            AND t1.search_type = 1
        </if>
        <!-- 仓库端使用 -->
        <if test="dto.roleType != null and dto.roleType == 4 ">
            <if test="dto.wareId != null and dto.wareId != ''">
                AND t1.ware_id = #{dto.wareId}
            </if>
            AND t1.status != 6
            AND t1.search_type = 1
        </if>

        <!-- forecastType 预保类型 -->
        <if test="dto.forecastType != null and dto.forecastType.size() > 0 ">
           and exists (
                select 1 from sys_ware_in_prod ext1
                where ext1.del_flag = 0 and t1.prod_id = ext1.prod_id
                and ext1.in_type in
                <foreach item="item" index="index" collection="dto.forecastType" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        </if>


        <choose>
            <when test="dto.shopId != null">
                <!-- 商家 -->
                AND t1.shop_id = #{dto.shopId}
                <if test="dto.statusList != null and !dto.statusList.contains(6)">
                    AND t1.search_type = 1
                    <if test="dto.idList != null and dto.idList.size() > 0">
                        AND t1.prod_id IN
                        <foreach item="item" index="index" collection="dto.idList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="dto.statusList == null or dto.statusList.contains(6)">
                    <if test="dto.idList != null and dto.idList.size() > 0">
                        AND t1.deal_id IN
                        <foreach item="item" index="index" collection="dto.idList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </if>
                <if test="dto.searchIdList != null and dto.searchIdList.size() > 0">
                    <if test="dto.warehouseSearchIdArray == null">
                        AND t1.id IN
                        <foreach item="item" index="index" collection="dto.searchIdList" open="(" separator=","
                                 close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="dto.warehouseSearchIdArray != null">
                        AND t1.id IN
                        <foreach item="item" index="index" collection="dto.warehouseSearchIdArray" open="("
                                 separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                </if>
            </when>

            <otherwise>
                <!-- 超管/仓库 -->
                <if test="dto.idList != null and dto.idList.size() > 0">
                    AND t1.prod_id IN
                    <foreach item="item" index="index" collection="dto.idList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dto.searchIdList != null and dto.searchIdList.size() > 0">
                    AND t1.prod_id IN
                    <foreach item="item" index="index" collection="dto.searchIdList" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </otherwise>
        </choose>
        <!-- 验货员 -->
        <if test="dto.inspectorId != null">
            AND p.check_id=#{dto.inspectorId}
        </if>
    </sql>

    <sql id="orderBY">

        <!-- 根据排序类型设置排序 -->
        <choose>
            <when test="dto.sortType == 7">
                t1.gmt_in DESC, t1.id ASC
            </when>
            <when test="dto.sortType == 8">
                t1.gmt_in ASC , t1.id ASC
            </when>
            <when test="dto.sortType == 9">
                t1.sku DESC , t1.gmt_in DESC, t1.id ASC
            </when>
            <when test="dto.sortType == 10">
                t1.sku ASC , t1.gmt_in DESC, t1.id ASC
            </when>
            <when test="dto.sortType == 11">
                t1.spec DESC , t1.gmt_in DESC, t1.id ASC
            </when>
            <when test="dto.sortType == 12">
                t1.spec ASC , t1.gmt_in DESC, t1.id ASC
            </when>
            <when test="dto.sortType == 13">
                t1.gmt_out DESC , t1.gmt_in DESC, t1.id ASC
            </when>
            <when test="dto.sortType == 14">
                t1.gmt_out ASC , t1.gmt_in DESC, t1.id ASC
            </when>

            <!-- 默认按入库时间降序排列 -->
            <otherwise>
                t1.gmt_in DESC, t1.id ASC
            </otherwise>

        </choose>

    </sql>


    <sql id="orderByGroupBy">

        <!-- 排序方式，1-总库存倒序，2-总库存升序，3-合格库存倒序，4-合格库存升序，5-破损库存倒序，6-破损库存升序 -->
        <choose>
            <when test="dto.groupSortType == 1">
                num DESC
            </when>
            <when test="dto.groupSortType == 2">
                num ASC
            </when>
            <when test="dto.groupSortType == 3">
                intactNum DESC
            </when>
            <when test="dto.groupSortType == 4">
                intactNum ASC
            </when>
            <when test="dto.groupSortType == 5">
                brokenNum DESC
            </when>
            <when test="dto.groupSortType == 6">
                brokenNum ASC
            </when>

            <otherwise>
                <include refid="orderBY"/>
            </otherwise>

        </choose>

    </sql>

    <sql id="columns">
        t1.id as warehouseSearchId
        ,t1.id as searchId
        ,t1.in_log_no as logNo
        ,t1.gmt_create
        ,t1.gmt_modify
        ,t1.del_flag
        ,t1.prod_id
        ,t1.deal_id
        ,t1.shop_id
        ,t1.one_id
        ,t1.sku
        ,t1.brand
        ,t1.remarks
        ,t1.supply
        ,t1.cost_price
        ,t1.status
        ,t1.ware_id
        ,t1.shelves_id
        ,t1.check_result
        ,t1.shop_uid
        ,t1.shop_name
        ,t1.odd_type
        ,t1.odd_type eventType
        ,t1.in_log_no
        ,t1.in_batch_no
        ,t1.gmt_in
        ,t1.third_plat_id
        ,t1.third_plat_name
        ,t1.plat_order_no
        ,t1.out_no
        ,t1.gmt_out
        ,t1.search_type
        ,t1.gmt_pay
        ,t1.transfer_status
        ,TIMESTAMPDIFF(DAY, t1.gmt_create , COALESCE(t1.gmt_out, NOW())) + 1 wareDays
        ,t1.in_log_no_related
        ,t1.check_remark
        ,t2.id
        ,t2.img
        ,t2.spec
        ,t2.pku
        ,t3.Name wareName
        ,t4.name shelvesName
        ,case t5.type when 6 then t5.sold_price when 5 then t5.sale_price else 0 end as outPrice
        ,ifnull(t6.odd_no,t1.odd_no) odd_no
        ,t6.plat_name
        ,t6.log_no outLogNo
        ,t6.gmt_create gmtOrder
        ,t7.in_type type
        ,t8.gmt_ware
        ,t9.gmt_create gmt_Shelves_Create
        ,t1.repair_flag
        ,case when  t1.repair_flag = 'CAN_REPAIR'
                and t1.status = '1'
                and t1.check_result != '1'
        then 'YES'
        else 'NO' end is_repair_flag
    </sql>

    <sql id="sysProdListVoColumns">
        t5.id
        ,t1.id warehouseSearchId
        ,t1.id searchId
        ,t1.one_id
        ,t1.sku
        ,t5.spec
        ,t2.name wareName
        ,TIMESTAMPDIFF(DAY, t1.gmt_create , COALESCE(t1.gmt_out, NOW())) + 1 wareDays
        ,t1.check_result
        ,t1.gmt_in
        ,t1.status
        ,t1.odd_no
        ,t3.realname shopName
        ,t3.uid shopUid
        ,t4.name shelvesName
        ,t1.cost_Price
        ,t1.supply
    </sql>

    <sql id="columnsGroupBySku">
        max(t2.img) img
        ,t1.sku
        ,max(t1.brand) brand
        ,max(t1.remarks) remarks
        ,max(num) num
        ,max(sumCost) sumCost
        ,max(intactNum) intactNum
        ,max(brokenNum) brokenNum
        ,ROUND(MAX(COALESCE(avgCost, 0)), 2) avgCost
    </sql>

    <sql id="columnsGroupByWare">
        ifnull(t3.name,'已出库') wareName
        ,ifnull(t4.name,'未上架') shelvesName
        ,max(num) num
        ,max(sumCost) sumCost
        ,max(intactNum) intactNum
        ,max(brokenNum) brokenNum
        ,ROUND(MAX(COALESCE(avgCost, 0)), 2) avgCost
    </sql>

    <sql id="columnsGroupBySkuAndSize">
        t1.sku
        ,t1.spec
        ,max(t1.remarks) remarks
        , count(t1.sku) size
    </sql>

</mapper>
