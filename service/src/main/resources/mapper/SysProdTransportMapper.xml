<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysProdTransportMapper">
    <select id="selectFeeByShopId" resultType="java.math.BigDecimal">
        select sum(plat_fee) as total
        from sys_prod_transport t1
                 inner join sys_prod_deal t2 on t1.id = t2.relation_id
        where t1.type = 3
          and t1.status in (1, 7)
          and t1.shop_id = #{shopId}
          and t2.shop_id = #{shopId}
          and t1.del_flag = 0
          and t2.del_flag = 0
    </select>

</mapper>
