<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hzjm.service.mapper.SysWithdrawMapper">

    <select id="getMerchantWithdrawalsLast24Hours" resultType="com.hzjm.service.entity.SysWithdraw">
        select t1.*
        from sys_withdraw t1
        where user_id = #{userId,jdbcType=INTEGER}
          and status in  (1,2)
          and gmt_create >= now() - INTERVAL 24 hour
        order by t1.id desc
        limit 3
    </select>

    <select id="selectAllDrawAccount" resultType="java.lang.String">
        select distinct trim(draw_account) drawAccount from sys_withdraw where del_flag = 0 and status = 2
    </select>

    <select id="selectWithdrawOutTradeNo" parameterType="com.hzjm.service.model.VO.WithdrawOutTradeNoVO"
            resultType="java.lang.String">
        select t1.out_trade_no
        from sys_withdraw t1
        left join sys_money_many t2 on (t1.draw_name = t2.card_name and t1.draw_account = t2.card_account and t1.draw_bank = t2.card_bank and t2.del_flag = 0)
        where t1.del_flag = 0

            <if test="dto.userType != null and  dto.userType != ''">
                and t1.user_type = #{dto.userType}
            </if>

            <if test="dto.drawAccount != null and  dto.drawAccount != ''">
                and t1.draw_account = #{dto.drawAccount}
            </if>

            <if test="dto.shopId != null and  dto.shopId != ''">
                and t1.shop_id = #{dto.shopId}
            </if>

            <if test="dto.applyName != null and  dto.applyName != ''">
                and t1.apply_name = #{dto.applyName}
            </if>

            <if test="dto.note != null and  dto.note != ''">
                AND t1.note like CONCAT('%', #{dto.note}, '%')
            </if>

            <if test="dto.accountCurrency != null and  dto.accountCurrency != ''">
                AND t2.account_currency = #{dto.accountCurrency}
            </if>
        <!--充值备注-->
        <if test="dto.note != null and  dto.note != ''">
            union all

            select t1.out_trade_no
            from sys_charge t1
            where t1.del_flag = 0
            AND t1.note like CONCAT('%', #{dto.note}, '%')
        </if>
        <!--流水备注-->
        <if test="dto.note != null and  dto.note != ''">
            union all

            select t1.out_trade_no
            from sys_bill t1
            where t1.del_flag = 0
            and t1.relation_type = 22
            AND t1.remark like CONCAT('%', #{dto.note}, '%')
        </if>

    </select>

    <select id="selectWithdrawOutTradeNoByAlibaba" parameterType="com.hzjm.service.model.VO.WithdrawOutTradeNoVO"
            resultType="java.lang.String">
        select t1.out_trade_no
        from sys_withdraw t1
        left join sys_money_many t2 on t1.draw_account = t2.ali_account
        where t1.del_flag = 0
        and t2.del_flag = 0


        <if test="dto.userType != null and  dto.userType != ''">
            and t1.user_type = #{dto.userType}
        </if>

        <if test="dto.shopId != null and  dto.shopId != ''">
            and t1.shop_id = #{dto.shopId}
        </if>

        <if test="dto.applyName != null and  dto.applyName != ''">
            and t1.apply_name = #{dto.applyName}
        </if>

        <if test="dto.note != null and  dto.note != ''">
            AND t1.note like CONCAT('%', #{dto.note}, '%')
        </if>

        <!--充值备注-->
        <if test="dto.note != null and  dto.note != ''">
            union all

            select t1.out_trade_no
            from sys_charge t1
            where t1.del_flag = 0
            AND t1.note like CONCAT('%', #{dto.note}, '%')
        </if>
        <!--流水备注-->
        <if test="dto.note != null and  dto.note != ''">
            union all

            select t1.out_trade_no
            from sys_bill t1
            where t1.del_flag = 0
            and t1.relation_type = 22
            AND t1.remark like CONCAT('%', #{dto.note}, '%')
        </if>

    </select>

</mapper>
