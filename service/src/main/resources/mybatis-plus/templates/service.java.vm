package ${package.Service};

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;
import ${package.Entity}.${entity};
import com.hzjm.service.model.DTO.${entity}PageDto;
import com.hzjm.service.model.VO.${entity}ListVo;
import com.hzjm.service.model.VO.${entity}Vo;
import ${superServiceClassPackage};

/**
 * $!{table.comment} 服务类
 *
 * <AUTHOR>
 * @since ${date}
 */
public interface ${table.serviceName} extends ${superServiceClass}<${entity}> {

    ${entity} getByIdWithoutLogic(Integer id);

    ${entity}Vo getDetail(Integer id);

    Boolean save${entity}(${entity} dto);

    Boolean insertList(List<${entity}> dataList);

    Boolean hardDelete(LambdaQueryWrapper qw);
    
    IPage<${entity}ListVo> searchList(${entity}PageDto dto);

    List<${entity}> listWithoutLogic(LambdaQueryWrapper qw);

    IPage<${entity}> pageWithoutLogic(Page page, LambdaQueryWrapper qw);

}
