package ${package.Controller};

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hzjm.common.model.HttpResult;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.BaseUtils;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.common.utils.ExcelReader;
import com.hzjm.service.entity.${entity};
import com.hzjm.service.model.VO.${entity}Vo;
import com.hzjm.service.model.VO.${entity}ListVo;
import com.hzjm.service.model.DTO.${entity}PageDto;
import com.hzjm.service.model.DTO.${entity}SaveDto;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import com.fasterxml.jackson.annotation.JsonView;
import com.hzjm.service.service.I${entity}Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.List;

#if(${superControllerClassPackage})
import ${superControllerClassPackage};
#end

/**
 * $!{table.comment} 前端控制器
 *
 * <AUTHOR>
 * @since ${date}
 */
@Api(tags = "$!{table.comment}")
@Slf4j
@RestController
@RequestMapping("/${table.name}")
public class ${table.controllerName} {

    @Autowired
    private ${table.serviceName} i${entity}Service;

    @ApiOperation(value = "保存$!{table.comment}", notes = "说明：\n" +
            "添加：不传id和delFlag\n" +
            "修改：传id，不传delFlag\n" +
            "删除：传id，delFlag固定传-1")
    @PostMapping("/save")
    public HttpResult<Boolean> save(@RequestBody ${entity}SaveDto dto){
        ${entity} entity = new ${entity}();
        BeanUtils.copyProperties(dto, entity);

        return HttpResult.ok(i${entity}Service.save${entity}(entity));
    }

    @ApiOperation("查看$!{table.comment}")
    @GetMapping("/get")
    public HttpResult<${entity}Vo> get(@RequestParam Integer id){
        return HttpResult.ok(i${entity}Service.getDetail(id));
    }

    @ApiOperation("查询$!{table.comment}列表")
    @PostMapping("/list")
    public HttpResult<IPage<${entity}ListVo>> list(@RequestBody ${entity}PageDto dto){
        return HttpResult.ok(i${entity}Service.searchList(dto));
    }

    @ApiOperation("导出$!{table.comment}")
    @PostMapping("/export")
    public HttpResult<String> export(@RequestBody ${entity}PageDto dto) {
        // 生成表头
        List<List<String>> dataList = new ArrayList<>();
        List<String> headers = Arrays.asList(
            "序号", "唯一标识", "状态", "创建时间"
        );
        dataList.add(LanguageConfigService.i18nForMsg(headers));

        List<${entity}ListVo> entityList = i${entity}Service.searchList(dto).getRecords();

        // 填充数据
        int i = 1;
        for (${entity}ListVo data : entityList) {
            List<String> cowList = new ArrayList<>();
            cowList.add(i + "");
            cowList.add(data.getId() + "");
            cowList.add(data.getDelFlag() == 0 ? "启用" : "禁用");
            // cowList.add(DateTimeUtils.format(DateTimeUtils.sdfTime,data.getGmtCreate()));
            dataList.add(cowList);
            i = i + 1;
        }

        String url = null;
        try {
        url = ExcelReader.generateExcelFile(dataList,
                    "$!{table.comment}(" + DateTimeUtils.getFileSuffix() + BaseUtils.getRandomStr(3) + ").xlsx");
        } catch (IOException e) {
            throw new BaseException(LanguageConfigService.i18nForMsg("系统异常：导出失败"));
        }

        return HttpResult.ok(url);
    }

}
