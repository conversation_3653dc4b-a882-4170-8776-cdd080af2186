package ${package.ServiceImpl};

import com.hzjm.common.model.BaseException;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;
import java.util.ArrayList;

import lombok.extern.slf4j.Slf4j;
import com.hzjm.service.model.DTO.${entity}PageDto;
import com.hzjm.service.model.VO.${entity}ListVo;
import com.hzjm.service.model.VO.${entity}Vo;
import ${package.Entity}.${entity};
import ${package.Mapper}.${table.mapperName};
import ${package.Service}.${table.serviceName};
import ${superServiceImplClassPackage};
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @since ${date}
 */
@Slf4j
@Service
public class ${table.serviceImplName} extends ${superServiceImplClass}<${table.mapperName}, ${entity}> implements ${table.serviceName} {

    @Override
    public ${entity} getByIdWithoutLogic(Integer id) {
        ${entity} data = baseMapper.selectByIdWithoutLogic(id);
        if (ObjectUtils.isEmpty(data)) {
            throw new BaseException("查询失败，未找到该$!{table.comment}");
        }

        return data;
    }

    @Override
    public ${entity}Vo getDetail(Integer id) {
        ${entity} data = getByIdWithoutLogic(id);

        ${entity}Vo vo = new ${entity}Vo();
        BeanUtils.copyProperties(data, vo);

        return vo;
    }

    @Override
    public Boolean save${entity}(${entity} dto) {
        Boolean rs = false;
        Boolean isDelete = !ObjectUtils.isEmpty(dto.getDelFlag()) && -1 == dto.getDelFlag();

        if (ObjectUtils.isEmpty(dto.getId())) {
            rs = baseMapper.insert(dto) > 0;
        } else if (isDelete) {
            rs = baseMapper.deleteById(dto.getId()) > 0;
        } else {
            rs = baseMapper.updateById(dto) > 0;
        }
        return rs;
    }

    @Override
    public IPage<${entity}ListVo> searchList(${entity}PageDto dto) {

        LambdaQueryWrapper<${entity}> qw = Wrappers.<${entity}>lambdaQuery();

    #foreach($field in ${table.fields})
    #if(${field.propertyName.equals("gmtCreate")})
        Date endTime = dto.dealEndTime();
        qw.orderByDesc(${entity}::getGmtCreate)
                .ge(!ObjectUtils.isEmpty(dto.getBeginTime()), ${entity}::getGmtCreate, dto.getBeginTime())
                .lt(!ObjectUtils.isEmpty(endTime), ${entity}::getGmtCreate, endTime);
    #end
    #end

        IPage<${entity}> pageResult = new Page();
        if (!ObjectUtils.isEmpty(dto.getSize()) && !ObjectUtils.isEmpty(dto.getCurrent())) {
            pageResult = page(new Page<>(dto.getCurrent(), dto.getSize()), qw);
        } else {
            pageResult.setRecords(list(qw));
            pageResult.setTotal(pageResult.getRecords().size());
        }

        List<${entity}ListVo> voList = new ArrayList<>();
        if(!ObjectUtils.isEmpty(pageResult.getRecords())) {
            pageResult.getRecords().forEach(data -> {
                ${entity}ListVo vo = new ${entity}ListVo();
                BeanUtils.copyProperties(data, vo);

                voList.add(vo);
            });
        }

        IPage<${entity}ListVo> voResult = new Page();
        BeanUtils.copyProperties(pageResult, voResult);
        voResult.setRecords(voList);

        return voResult;
    }

    @Override
    public Boolean insertList(List<${entity}> dataList) {
        if(ObjectUtils.isEmpty(dataList)) {
            return false;
        }

        // 填充默认值
        Date date = DateTimeUtils.getNow();
        dataList.forEach(data -> {

    #foreach($field in ${table.fields})
    #if(${field.propertyName.equals("gmtCreate")})
            data.setGmtCreate(date);
    #end
    #if(${field.propertyName.equals("gmtModify")})
            data.setGmtModify(date);
    #end
    #end

            data.setDelFlag(0);
        });

        int num = 1000;
        int total = dataList.size();
        int page = (total / num) + (total % num == 0 ? 0 : 1);
        for (int i = 1; i <= page; i++) {
        int begin = num * (i - 1);
        int end = begin + num;
        if(end > total) {
            end = total;
        }
            baseMapper.insertList(dataList.subList(begin, end));
        }

        return true;
    }

    @Override
    public Boolean hardDelete(LambdaQueryWrapper qw) {
        return baseMapper.hardDelete(qw) > 0;
    }

    @Override
    public IPage<${entity}> pageWithoutLogic(Page page, LambdaQueryWrapper qw) {
        return baseMapper.pageWithoutLogic(page, qw);
    }

    @Override
    public List<${entity}> listWithoutLogic(LambdaQueryWrapper qw) {
        return baseMapper.listWithoutLogic(qw);
    }

}
