package ${package.Mapper};

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import ${package.Entity}.${entity};
import ${superMapperClassPackage};

/**
 * $!{table.comment} Mapper 接口
 *
 * <AUTHOR>
 * @since ${date}
 */
#if(${kotlin})
interface ${table.mapperName} : ${superMapperClass}<${entity}>
#else
@Mapper
public interface ${table.mapperName} extends ${superMapperClass}<${entity}> {

    @Select("select * from ${table.name} where id = #{id}")
    ${entity} selectByIdWithoutLogic(@Param("id") Integer id);

    @Delete("delete from ${table.name} ${ew.customSqlSegment}")
    int hardDelete(@Param("ew") LambdaQueryWrapper qw);

    @Insert({"<script>" +
            "insert into ${table.name}(" +
            "#foreach($field in ${table.fields})#if($!{foreach.index}==0)${field.name}#else, ${field.name}#end#end" +
            ") values " +
            "<foreach collection='dataList' item='item' separator=','>" +
            "(" +
            "#foreach($field in ${table.fields})#if($!{foreach.index}==0)#{item.${field.propertyName}}#else, #{item.${field.propertyName}}#end#end" +
            ")" +
            "</foreach>" +
            "</script>"})
    int insertList(@Param("dataList") List<${entity}> dataList);

    @Select({"select * from ${table.name} ${ew.customSqlSegment}"})
    List<${entity}> listWithoutLogic(@Param("ew") LambdaQueryWrapper qw);

    @Select({"select * from ${table.name} ${ew.customSqlSegment}"})
    IPage<${entity}> pageWithoutLogic(Page page, @Param("ew") LambdaQueryWrapper qw);
}
#end
