package com.hzjm.service.model.VO;

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
#if(${swagger2})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
#if(${entityLombokModel})
import lombok.Data;
#end
import java.util.Date;
import com.hzjm.common.utils.DateTimeUtils;

/**
 * $!{table.comment}-详情
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
#if(${swagger2})
#end
public class ${entity}Vo implements Serializable {

#if(${entitySerialVersionUID})
    private static final long serialVersionUID=1L;
#end
#set($hasDelete=false)
## ----------  BEGIN 字段循环遍历  ----------
#foreach($field in ${table.fields})

    #if(${field.keyFlag})
        #set($keyPropertyName=${field.propertyName})
    #end
    #if("$!field.comment" != "")
        #if(${swagger2})
    @ApiModelProperty(value = "${field.comment}")
        #else
    /**
     * ${field.comment}
     */
        #end
    #end
## 逻辑删除注解
    #if(${logicDeleteFieldName}==${field.name})
        #set($hasDelete=true)
    @TableLogic
    #end
    #if(${field.propertyType.equals("LocalDateTime")} || ${field.propertyType.equals("LocalDate")})
    public Date ${field.propertyName};
    #else
    public ${field.propertyType} ${field.propertyName};
    #end
#end
## ----------  END 字段循环遍历  ----------

#if($hasDelete == false)
    // 不存在逻辑删除字段时，生成删除标识
    @TableField(exist = false)
    @ApiModelProperty("0-正常，-1-删除")
    private Integer delFlag = 0;
#end

#if(!${entityLombokModel})
    #foreach($field in ${table.fields})
    #if(${field.propertyType.equals("boolean")})
        #set($getprefix="is")
    #else
        #set($getprefix="get")
    #end

    public ${field.propertyType} ${getprefix}${field.capitalName}() {
        return ${field.propertyName};
    }

    #if(${entityBuilderModel})
    public ${entity} set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
    #else
    public void set${field.capitalName}(${field.propertyType} ${field.propertyName}) {
    #end
        this.${field.propertyName} = ${field.propertyName};
    #if(${entityBuilderModel})
        return this;
    #end
    }
    #end
#end

#if(${entityColumnConstant})
    #foreach($field in ${table.fields})
    public static final String ${field.name.toUpperCase()} = "${field.name}";

    #end
#end

#if(!${entityLombokModel})
    @Override
    public String toString() {
        return "${entity}{" +
        #foreach($field in ${table.fields})
        #if($!{foreach.index}==0)
            "${field.propertyName}=" + ${field.propertyName} +
        #else
            ", ${field.propertyName}=" + ${field.propertyName} +
        #end
        #end
        "}";
    }
#end
}
