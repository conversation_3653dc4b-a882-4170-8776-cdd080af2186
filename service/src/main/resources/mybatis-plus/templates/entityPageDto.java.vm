package com.hzjm.service.model.DTO;

#foreach($pkg in ${table.importPackages})
import ${pkg};
#end
#if(${swagger2})
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
#end
import lombok.Data;
import com.hzjm.service.model.DTO.PageBaseSearchDto;

/**
 * $!{table.comment}-列表查询
 *
 * <AUTHOR>
 * @since ${date}
 */
@Data
#if(${swagger2})
#end
public class ${entity}PageDto extends PageBaseSearchDto {

}
