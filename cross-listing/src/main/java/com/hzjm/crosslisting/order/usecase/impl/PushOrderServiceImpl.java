package com.hzjm.crosslisting.order.usecase.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hzjm.common.model.BaseException;
import com.hzjm.common.utils.DateTimeUtils;
import com.hzjm.crosslisting.common.AccountConfig;
import com.hzjm.crosslisting.enums.ListingAccount;
import com.hzjm.crosslisting.enums.PlatformOrderStatus;
import com.hzjm.crosslisting.enums.SourcePlatform;
import com.hzjm.crosslisting.listing.repository.IPlatformListingService;
import com.hzjm.crosslisting.order.entity.PlatformOrder;
import com.hzjm.crosslisting.order.repository.IPlatformOrderService;
import com.hzjm.crosslisting.order.usecase.IOrderLifeCycleService;
import com.hzjm.crosslisting.order.usecase.IPushOrderService;
import com.hzjm.crosslisting.order.usecase.StockXOrderLogisticsService;
import com.hzjm.crosslisting.pricing.usecase.revenue.PricingDetailStrategyContext;
import com.hzjm.poizon.common.AlreadyShippedException;
import com.hzjm.poizon.order.request.GetPoizonOrderRequest;
import com.hzjm.poizon.order.request.ObtainShippingLabelRequest;
import com.hzjm.poizon.order.request.ShipOrderRequest;
import com.hzjm.poizon.order.response.*;
import com.hzjm.poizon.order.usecase.IPoizonOrderService;
import com.hzjm.service.entity.ShopUserPlat;
import com.hzjm.service.entity.SysProd;
import com.hzjm.service.entity.SysProdDeal;
import com.hzjm.service.entity.SysProdSale;
import com.hzjm.service.model.VO.ThirdPlatProdListVo;
import com.hzjm.service.model.touch.TouchSettleRequest;
import com.hzjm.service.service.*;
import com.hzjm.stockx.order.usecase.IStockXOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PushOrderServiceImpl implements IPushOrderService {

    @Autowired
    IPlatformListingService platformListingService;

    @Autowired
    IPlatformOrderService platformOrderService;

    @Autowired
    ISysProdSaleService prodSaleService;

    @Autowired
    PricingDetailStrategyContext pricingCalculator;

    @Autowired
    IStockXOrderService stockOrderService;

    @Autowired
    IPoizonOrderService poizonOrderService;

    @Autowired
    IOrderLifeCycleService orderLifeCycleService;

    @Autowired
    ISysProdService sysProdService;

    @Autowired
    ISysProdDealService sysProdDealService;

    @Autowired
    IShopUserPlatService shopUserPlatService;

    @Autowired
    AccountConfig accountConfig;

    @Autowired
    ISysWareOutService sysWareOutService;

    @Autowired
    StockXOrderLogisticsService stockXOrderLogisticsService;

    /**
     * 订单 以 多合一 的方式 推送至 knet 系统
     * @param orders
     */
    @Override
    public void pushOrderMultiToOnePackage(List<PlatformOrder> orders) {
        // one Id 查重
        Set<String> uniqueOneIds = new HashSet<>();
        boolean hasDuplicate = orders.stream().anyMatch(order -> !uniqueOneIds.add(order.getOneId()));
        if (hasDuplicate) {
            String errorInfo = "向 knet 系统 推送打包 商品时发现有重复的 one Id，请进行检查后再打包";
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        assembleSysProdDeal(orders);

        SysProdSale entity = new SysProdSale();
        entity.setPlatId(orders.get(0).getAccount().getPlatId());
        entity.setPlatName(orders.get(0).getAccount().presentation());
        entity.setNote("#Order From CrossListing# \n" + "BoxingId: \n" + orders.get(0).getBoxingId());
        entity.setLogNo(orders.get(0).getTrackingCode());
        entity.setPlatOrderNo(orders.get(0).getOrderNumber());
        entity.setLabelImg(orders.get(0).getShippingLabel());
        // 在这里是一个包裹里的 订单组
        List<ThirdPlatProdListVo> prodList = new ArrayList<>();

        for (PlatformOrder order: orders) {
            ThirdPlatProdListVo prod = new ThirdPlatProdListVo();
            prod.setOneId(order.getOneId());
            prod.setOrderNo(order.getOrderNumber());
//            double sellerOwning = order.getSellerOwning();
//            prod.setPlatSoldPrice(BigDecimal.valueOf(sellerOwning));
            prodList.add(prod);
            order.setKnetOrderStatus(PlatformOrderStatus.BOXED);
        }

        entity.setProdList(prodList);
        prodSaleService.saveSysProdSale(entity);
    }

    /**
     *  订单 以 一对一 的方式 推送 knet原系统
     */
    @Override
    public void pushOrderMultiToMultiPackage(List<PlatformOrder> orders, boolean ignoreError) {
        // one Id 查重
        Set<String> uniqueOneIds = new HashSet<>();
        boolean hasDuplicate = orders.stream().anyMatch(order -> !uniqueOneIds.add(order.getOneId()));
        if (hasDuplicate) {
            String errorInfo = "向 knet 系统 推送打包 商品时发现有重复的 one Id，请进行检查后再打包";
            log.error(errorInfo);
            throw new BaseException(errorInfo);
        }

        // 没有 shipping Label 且不是 ebay 平台的订单 不会去自动推送
        List<PlatformOrder> noShippingLabelOrder = orders
                .stream()
                .filter(a -> (ObjectUtils.isEmpty(a.getShippingLabel()) || ObjectUtils.isEmpty(a.getTrackingCode()))
                        && !Objects.equals(a.getPlatform(), SourcePlatform.EBAY))
                .collect(Collectors.toList());

        // 打印 log
        if (!noShippingLabelOrder.isEmpty()) {
            log.info("执行 Push Order to Knet 时，发现 有 shipping Label 或 tracking code 信息不全的订单信息，这些订单将会留滞等到下一次推送处理, 订单信息: "
                    + noShippingLabelOrder.toString()
            );
        }

        // 获取 具有完整 shipping label 信息的或者 直接 就是 ebay 的订单
        List<PlatformOrder> hasShippingLabelOrder = orders
                .stream()
                .filter(a ->
                        (!ObjectUtils.isEmpty(a.getShippingLabel()) && !ObjectUtils.isEmpty(a.getTrackingCode()))
                        || Objects.equals(SourcePlatform.EBAY, a.getPlatform()) // ebay 平台的单子可以推送
                        || Objects.equals(ListingAccount.STOCKX_FLEX, a.getAccount()) // stockX flex 单子 可以推送
                )
                .collect(Collectors.toList());

        assembleSysProdDeal(hasShippingLabelOrder);

        // 批量组装 三方单子 数据
        List<PlatformOrder> pushedOrder = new ArrayList<>();
        for (PlatformOrder order: hasShippingLabelOrder) {
            try {
                if (Objects.equals(order.getPlatform(), SourcePlatform.STOCKX_DIRECT)) {
                    String pdfFormatDocument = stockXOrderLogisticsService.fetchOrderPdfFormatDocument(order.getOrderNumber(), order.getAccount().getAccountEmail(accountConfig));
                    order.setShippingLabel(pdfFormatDocument);
                }

                SysProdSale entity = new SysProdSale();
                //entity.setPlatId(10000);
                Integer platId = order.getAccount().getPlatId();
                entity.setPlatId(platId);
                entity.setPlatName(order.getAccount().presentation());

                // 推送时有 order 备注 信息备注order 信息
                if (!ObjectUtils.isEmpty(order.getNote())) {
                    entity.setNote(order.getNote());
                }else{
                    entity.setNote("#Order From CrossListing#");
                }

                entity.setPlatOrderNo(order.getOrderNumber());
                entity.setLabelImg(order.getShippingLabel());
                entity.setLogNo(order.getTrackingCode());

                List<ThirdPlatProdListVo> prodList = new ArrayList<>();
                ThirdPlatProdListVo prod = new ThirdPlatProdListVo();
                prod.setOneId(order.getOneId());
                prod.setOrderNo(order.getOrderNumber());
//            double sellerOwning = order.getSellerOwning();
//            prod.setPlatSoldPrice(BigDecimal.valueOf(sellerOwning));
                prodList.add(prod);
                entity.setProdList(prodList);
                // 推送三方寄售但
                boolean createResult = prodSaleService.saveSysProdSale(entity);

                // 如果是 stockX flex 的单子直接将 对应的 库存出库
                if (Objects.equals(ListingAccount.STOCKX_FLEX, order.getAccount()) && createResult) {
                    List<String> oneIds = new ArrayList<>();
                    oneIds.add(order.getOneId());
                    sysWareOutService.stocksFlexWareOut(oneIds);
                }

                order.setKnetOrderStatus(PlatformOrderStatus.BOXED);

                platformOrderService.updateById(order);
            }catch (Exception e) {
                if (ignoreError) {
                    // 如果忽略错误，继续处理下一个订单
                    log.error("订单号为: " + order.getOrderNumber() + " 的订单推送失败， 原因: " + e.getMessage() + "stack trace: " + Arrays.toString(e.getStackTrace()));
                } else {
                    // 如果不忽略错误，抛出异常
                    throw new BaseException("订单号为: " + order.getOrderNumber() + " 的订单推送失败， 原因: " + e);
                }
            }
        }
    }

    // 在 将订单存入 SysProdSale 之前 伪造 SysProdDeal
    private void assembleSysProdDeal(List<PlatformOrder> orders) {
        // 获取三方寄售数组的所有 oneId
        List<String> oneIdList = orders.stream().map(PlatformOrder::getOneId).collect(Collectors.toList());

        // 以 one Id 为 Key， PlatformOrder 为值
        Map<String, PlatformOrder> orderMap = orders
                .stream()
                .collect(Collectors.toMap(PlatformOrder::getOneId, a -> a));

        // 根据这个 oneID List 去找 处在寄卖中状态的 SysProd
        List<SysProd> sysProdList = sysProdService.list(Wrappers.<SysProd>lambdaQuery()
                .eq(SysProd::getStatus, 2)
                .in(SysProd::getOneId, oneIdList));

        // 以 one Id 为 Key， Sys Prod 为值
        Map<String, SysProd> oneIdMap = sysProdList
                .stream()
                .collect(Collectors.toMap(SysProd::getOneId, a -> a));

        List<SysProdDeal> needUpdateDeal = new ArrayList<>();

        for (String oneId: oneIdMap.keySet()) {
            // 对应的 SysProd 数据
            SysProd prod = oneIdMap.get(oneId);

            // 根据 所属人， 寄卖中， 且 SysProd 关联的数据去找 SysProdDeal 数据
            List<SysProdDeal> sysProdDeals = sysProdDealService.list(Wrappers.<SysProdDeal>lambdaQuery()
                    .eq(SysProdDeal::getProdId, oneIdMap.get(oneId).getId())
            );

            //没有 处于寄卖的数据 就手动创建一个
            if (sysProdDeals.isEmpty()
                    || sysProdDeals.stream().noneMatch(prodDeal -> Objects.equals(prodDeal.getType(), 6))
            ) {
                // 添加关联商品 手造一个
                SysProdDeal deal = new SysProdDeal();
                deal.setProdId(prod.getId());
                deal.setType(6);
                deal.setRelationId(0);
                deal.setStatus(1);
                deal.setSalePrice(BigDecimal.valueOf(orderMap.get(oneId).salePrice / 100));
                if (!ObjectUtils.isEmpty(prod)) {
                    deal.setGmtIn(prod.getGmtIn());
                    deal.setShopId(prod.getShopId());
                    deal.setWareId(prod.getWareId());
                    deal.setPku(prod.getPku());
                    deal.setSku(prod.getSku());
                    deal.setCostPrice(prod.getCostPrice());
                    deal.setSupply(prod.getSupply());
                }
                sysProdDealService.getBaseMapper().insert(deal);
            } else {
                // 同时满足条件的 sys prod deal 可能有多个 ，所以我们需要去处理这些多余的数据
                Map<Boolean, List<SysProdDeal>> partitionedDeals = sysProdDeals
                        .stream()
                        .collect(Collectors.partitioningBy(deal -> Objects.equals(deal.getType(), 6)));

                // 非寄卖类型的数据
//                List<SysProdDeal> nonSaleTypeDeals = partitionedDeals.get(false);
//                if (!nonSaleTypeDeals.isEmpty()) {
//                    nonSaleTypeDeals.forEach(deal -> {
//                        if (Objects.equals(deal.getStatus(), 1)) {
//                            deal.setStatus(3); // 设置状态已完成, 以免影响到后续的存储
//                        }
//                    });
//                    sysProdDealService.saveOrUpdateBatch(nonSaleTypeDeals);
//                }

                // 寄卖类型的 Deal，如果 有多个 我们只修正第一个 ，其他的类型我们直接 设置已完成来规避后续的错误
                List<SysProdDeal> saleTypeDeals = partitionedDeals.get(true);
                if (!saleTypeDeals.isEmpty()) {
                    for (int i = 0; i < saleTypeDeals.size(); i++) {
                        SysProdDeal sysProdDeal = saleTypeDeals.get(i);
                        if (i == 0) { // 对第一个元素进行特殊处理
                            sysProdDeal.setStatus(1);
                            sysProdDeal.setGmtModify(DateTimeUtils.getNow());
                            sysProdDeal.setSku(orderMap.get(oneId).getSku());
                            sysProdDeal.setSalePrice(BigDecimal.valueOf(orderMap.get(oneId).salePrice / 100));
                            sysProdDeal.setWareId(Integer.valueOf(orderMap.get(oneId).getWareId()));
                            sysProdDeal.setSupply(oneIdMap.get(oneId).getSupply());
                            sysProdDeal.setShopId(Integer.valueOf(orderMap.get(oneId).getShopId()));
                            sysProdDeal.setType(6);
                        } else { // 对其他重复的元素数据进行不同的处理
                            if (Objects.equals(sysProdDeal.getStatus(), 1)) {
                                sysProdDeal.setStatus(2); // 设置状态已失效, 以免影响到后续的存储
                            }
                            sysProdDeal.setPlatProdStatus(4); // 三方寄售状态设为已关闭
                        }
                    }
                    sysProdDealService.saveOrUpdateBatch(saleTypeDeals);
                }
            }
        }

    }

    /**
     * 推送账单
     * @param orders
     */
    @Override
    public void pushSettleToKnet(List<PlatformOrder> orders) {
        // 将全平台的订单组成数组
        if (orders.isEmpty()) {
            log.info("There's no settle push to knet system.");
            return;
        }
        // 组装 账单推送请求头
        List<TouchSettleRequest> settleRequests = new ArrayList<>();

        // 循环 拉取到的所有订单
        for (PlatformOrder order: orders) {
            // 推送账单成功后，睡一秒再推下一单
            try {
                TouchSettleRequest touchSettleRequest = new TouchSettleRequest();
                touchSettleRequest.setTouchUserId(order.getShopId());
                touchSettleRequest.setErpProductId(order.getOneId());
                touchSettleRequest.setSaleSource(order.getSaleSource());

                // 出售价格
                double salePrice = (double) order.getSalePrice() / 100;
                touchSettleRequest.setSalePrice(BigDecimal.valueOf(salePrice));

                // 获取商家的 服务费
                ShopUserPlat shopUserPlat = shopUserPlatService.getOne(Wrappers.<ShopUserPlat>lambdaQuery()
                        .eq(ShopUserPlat::getShopId, order.getShopId()).last("LIMIT 1"));
                BigDecimal ocFee = BigDecimal.ZERO;
                if (shopUserPlat != null) {
                    ocFee = shopUserPlat.getOcFee();
                    if (ocFee == null) {
                        ocFee = BigDecimal.ZERO;
                    }
                }

                //计算的到手价
                double sellerOwning = (double) (order.getSellerOwning())/ 100;

                // 卖家最终到手价 = 计算的到手价 + 货品出库时已扣除的服务费
                // stockX 不收 出库费因此无需添加 预先扣除的 出库费用
                if (Objects.equals(ListingAccount.STOCKX_FLEX, order.getAccount())) {
                    touchSettleRequest.setAmount(BigDecimal.valueOf(sellerOwning));
                }else {
                    touchSettleRequest.setAmount(BigDecimal.valueOf(sellerOwning).add(ocFee));
                }

                // 如果是 Kicks Crew 平台 设置 延迟打款的时间
                if (Objects.equals(SourcePlatform.KICKS_CREW, order.getPlatform())) {
                    touchSettleRequest.setDelaySettleDays(5); // 延迟打款 5 天
                }

                boolean isSuccess = prodSaleService.settle(touchSettleRequest);
                if (isSuccess) {
                    // 如果是 TIK TOK 的订单 是 完成但 订单状态是 RETURNED 的话，意味买家已经退款了，设置完成但退款状态
                    boolean returned = (order.getOrderStatus() == PlatformOrderStatus.RETURNED || order.getOrderStatus() == PlatformOrderStatus.RETURNING);
                    if (returned) {
                        order.setKnetOrderStatus(Objects.equals(SourcePlatform.TIKTOK_SHOP, order.getPlatform())
                                ? PlatformOrderStatus.COMPLETED_WITH_REFUND
                                : PlatformOrderStatus.RETURNING
                        );
                    }else {
                        //设置Knet 状态 为 payout completed
                        order.setKnetOrderStatus(PlatformOrderStatus.PAYOUT_COMPLETED);
                    }

                    // 更新数据库订单状态 以及 记录订单生命周期
                    platformOrderService.saveOrUpdate(order);
                }

                Thread.sleep(500);
            } catch (Exception e) {
                log.error("尝试为 订单号 为: " + order.getOrderNumber() + " 的 订单打款失败， reason: " + e);
            }

        }
    }

    /**
     * 推送 poizon order 前的 前置动作
     * 1. 设置发货
     * 2. 然后获取 shipping label
     */
    @Override
    public void confirmShipAndObtainShippingLabel() {

        //1. 筛选需要确认发货且 没有 shipping label 没有超卖的数据
        List<PlatformOrder> pendingShipLabelOrders = platformOrderService.list(
                Wrappers.<PlatformOrder>lambdaQuery()
                        .eq(PlatformOrder::getPlatform, SourcePlatform.POIZON)
                        .ne(PlatformOrder::getOrderStatus, PlatformOrderStatus.CANCELED)
                        .isNull(PlatformOrder::getTrackingCode)
                        .isNull(PlatformOrder::getShippingLabel)
                        .apply("one_id IN (SELECT one_id FROM platform_order GROUP BY one_id HAVING COUNT(one_id) = 1)") // 确保 one Id 在数据库中唯一，表示没有超卖
        );

        if (ObjectUtils.isEmpty(pendingShipLabelOrders)) {
            return;
        }

        //2. 开始为每个订单确认发货并获取订单号
        pendingShipLabelOrders.forEach(order -> {

            String trackingCode = "";

            // 2.1 首先确认发货
            ShipOrderRequest shipOrderRequest = new ShipOrderRequest();
            // 设置发货订单号
            List<String> orderNoList = new ArrayList<>();
            orderNoList.add(order.getOrderNumber());
            shipOrderRequest.setOrder_no_list(orderNoList);
            // 设置发货仓库和发货地
            shipOrderRequest.setCarrier(CarrierCode.ONLINE_DELIVERY_EASTERN);
            shipOrderRequest.setDelivery_type(DeliveryType.ONLINE_EXPRESS_DELIVERY);
            shipOrderRequest.setDelivery_region("US");

            try {
                ShipOrderResponse response = poizonOrderService.shipOrder(shipOrderRequest, order.getAccount().getAccountEmail(accountConfig));

                // 发货失败了的数据
                if (!ObjectUtils.isEmpty(response.getFailed_item_list())) {
                    FailedShipItem failedShipItem = response.getFailed_item_list().get(0);
                    // 此 履约单的状态不能够 发货, 设置取消后 更新数据库 并进入下一次循环
                    if (Objects.equals(6000037, failedShipItem.getFailedCode())) {
                        order.setOrderStatus(PlatformOrderStatus.CANCELED);
                        platformOrderService.saveOrUpdate(order);
                        return;
                    }
                    return;
                }

                // 返回的 tracking no 为空 或 请求发货的 order number 不在成功的列表里都算作失败
                if (!response.getSuccess_order_no_list().contains(order.getOrderNumber())) {
                    log.error("try to set poizon order to ship error, order number: "
                            + order.getOrderNumber() + ", reason: request is success, but poizon return invalid info. empty tracking code or empty success order list");
                    return;
                }

                // 发货获取到的 logistics 数据为空
                if (ObjectUtils.isEmpty(response.getTracking_number())) {
                    log.error("try to set poizon order to ship error, order number: " + order.getOrderNumber() + ", reason: empty tracking code & label url");
                    return;
                }

                trackingCode = response.getTracking_number();

            } catch (AlreadyShippedException exception) {
                //2.2 通过查询 order 接口 来查询 单一 order 的 express no 数据
                GetPoizonOrderRequest getPoizonOrderRequest = new GetPoizonOrderRequest();
                getPoizonOrderRequest.setOrder_no(order.getOrderNumber());
                try {
                    PoizonOrderPagination pagination = poizonOrderService.getOrders(getPoizonOrderRequest, order.getAccount().getAccountEmail(accountConfig));
                    List<PoizonOrder> poizonOrders = pagination.getOrders();

                    // 获取 匹配 order no 的order 信息
                    Optional<PoizonOrder> matchedOrderOpt = poizonOrders.stream()
                            .filter(a -> Objects.equals(a.getOrder_no(), order.getOrderNumber()))
                            .findFirst();

                    if (!matchedOrderOpt.isPresent()) {
                        log.error("try to get matched poizon order error, order number: {}", order.getOrderNumber());
                        return;
                    }

                    PoizonOrder matchedOrder = matchedOrderOpt.get();

                    if (ObjectUtils.isEmpty(matchedOrder.getExpress_to_platform()) ||
                            ObjectUtils.isEmpty(matchedOrder.getExpress_to_platform().getExpress_no())) {
                        log.error("try to get matched poizon order express no error, order number: {}", order.getOrderNumber());
                        return;
                    }

                    trackingCode = matchedOrder.getExpress_to_platform().getExpress_no();
                }catch (Exception e) {
                    log.error("try to get poizon order express no error, order number: " + order.getOrderNumber() + ", reason: " + e);;
                }

            } catch (Exception e) {
                log.error("try to set poizon order to ship error, order number: " + order.getOrderNumber() + ", reason: " + e);;
            }

            if (ObjectUtils.isEmpty(trackingCode)) {
                return;
            }

            //2.3 根据上一步获取的  tracking code 来 去生成 shipping label
            ObtainShippingLabelRequest shippingLabelRequest = new ObtainShippingLabelRequest();
            shippingLabelRequest.setTrackingNumber(trackingCode);
            shippingLabelRequest.setCarrier(CarrierCode.ONLINE_DELIVERY_EASTERN.getRawValue());

            try {
                ObtainShippingLabelResponse response = poizonOrderService.obtainShippingLabel(shippingLabelRequest, order.getAccount().getAccountEmail(accountConfig));
                if (!ObjectUtils.isEmpty(response.getPackageLabelWrapUrl())) {
                    order.setShippingLabel(response.getPackageLabelWrapUrl());
                }else{
                    order.setShippingLabel(response.getLabel());
                }

                order.setTrackingCode(trackingCode);
            }catch (Exception e) {
                log.error("try to fetch poizon shipping label error, order number: " + order.getOrderNumber() + ", reason: " + e);
            }

            // 只要存在有更新的数据就存储下来
            if (!ObjectUtils.isEmpty(order.getTrackingCode()) && !ObjectUtils.isEmpty(order.getShippingLabel())) {
                platformOrderService.saveOrUpdate(order);
            }

        });
    }

}
