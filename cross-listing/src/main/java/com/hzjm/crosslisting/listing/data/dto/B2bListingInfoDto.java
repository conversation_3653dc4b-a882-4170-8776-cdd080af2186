package com.hzjm.crosslisting.listing.data.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/8/4
 * @description: B2B Listing信息DTO，用于获取改价历史所需的信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class B2bListingInfoDto {
    
    @ApiModelProperty(value = "knet_listing_id")
    private String knetListingId;
    
    @ApiModelProperty(value = "b2b_listing_id")
    private String listingId;
    
    @ApiModelProperty(value = "b2b_sale_price")
    private String salePrice;
    
    @ApiModelProperty(value = "shop_user_id")
    private Integer shopUserId;
    
    @ApiModelProperty(value = "one_id")
    private String oneId;
}
