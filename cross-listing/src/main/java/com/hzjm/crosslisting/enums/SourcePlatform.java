package com.hzjm.crosslisting.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SourcePlatform {

    /**
     * 新增平台时，需要同步 com.hzjm.service.model.enums.SourcePlatformService 枚举
     */

    STOCK_X("stockX"),
    STOCKX_DIRECT("stockxDirect"),
    GOAT_INSTANT_SHIP("goatIs"),
    GOAT_STV("goat"),
    KICKS_CREW("kc"),
    EBAY("ebay"),
    POIZON("poizon"),
    TIKTOK_SHOP("tts"),
    B2B_SHOP("b2b");

    @EnumValue
    @JsonValue
    private final String value;


    @Override
    public String toString() {
        return this.value;
    }

    public static SourcePlatform createFrom(String value) {
        switch (value) {
            case "stockX":
                return SourcePlatform.STOCK_X;
            case "stockxDirect":
                return SourcePlatform.STOCKX_DIRECT;
            case "goat":
                return SourcePlatform.GOAT_STV;
            case "goatIs":
                return SourcePlatform.GOAT_INSTANT_SHIP;
            case "kc":
                return SourcePlatform.KICKS_CREW;
            case "ebay":
                return SourcePlatform.EBAY;
            case "poizon":
                return SourcePlatform.POIZON;
            case "tts":
                return SourcePlatform.TIKTOK_SHOP;
            default:
                throw new IllegalArgumentException("Unsupported platform string value, value: " + value);
        }
    }

    public Integer basePlatformId() {
        switch (this) {
            case STOCK_X:
                return 11001;
            case GOAT_STV:
                return 11004;
            case GOAT_INSTANT_SHIP:
                return 11003;
            case KICKS_CREW:
                return 11005;
            case EBAY:
                return 11006;
            case POIZON:
                return 11007;
            case TIKTOK_SHOP:
                return 11010;
            case B2B_SHOP:
                return 11011;
            case STOCKX_DIRECT:
                return 11012;
            default:
                throw new IllegalArgumentException("unsupported platform to get plat id.");
        }
    }

    public String stringFormat() {
        switch (this) {
            case GOAT_INSTANT_SHIP:
                return "GOAT IS";
            case GOAT_STV:
                return "GOAT STV";
            case STOCK_X:
                return "StockX";
            case KICKS_CREW:
                return "Kicks Crew";
            case EBAY:
                return "ebay";
            case POIZON:
                return "poizon";
            case TIKTOK_SHOP:
                return "TikTok Shop";
            case STOCKX_DIRECT:
                return "SURGE";
            default:
                return "";
        }
    }


    public String stringFormatToEmail() {
        switch (this) {
            case GOAT_INSTANT_SHIP:
                return "KNET";
            case GOAT_STV:
                return "GOAT";
            case STOCK_X:
                return "StockX";
            case KICKS_CREW:
                return "Kicks Crew";
            case EBAY:
                return "EBay";
            case POIZON:
                return "POIZON";
            case TIKTOK_SHOP:
                return "TikTok Shop";
            case B2B_SHOP:
                return "KNET B2B";
            case STOCKX_DIRECT:
                return "SURGE";
            default:
                return "";
        }
    }


}
